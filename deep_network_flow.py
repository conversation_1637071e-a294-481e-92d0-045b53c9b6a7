import torch
import torch.nn as nn
import torch.nn.functional as F

print("=== 深度神经网络完整流程演示 ===")

# 定义网络结构（按照图片中的架构）
class DeepClassifier(nn.Module):
    def __init__(self):
        super(DeepClassifier, self).__init__()
        # 按照图片中的层次定义
        self.l1 = nn.Linear(784, 512)   # 第一层：784 → 512
        self.l2 = nn.Linear(512, 256)   # 第二层：512 → 256  
        self.l3 = nn.Linear(256, 128)   # 第三层：256 → 128
        self.l4 = nn.Linear(128, 64)    # 第四层：128 → 64
        self.l5 = nn.Linear(64, 10)     # 输出层：64 → 10 (10个类别)
        
    def forward(self, x):
        # 详细记录每一步的形状变化
        print(f"输入形状: {x.shape}")
        
        # Step 1: 展平图像
        x = x.view(-1, 784)
        print(f"展平后: {x.shape}")
        
        # Step 2: 第一个线性层 + ReLU
        x = self.l1(x)
        print(f"l1输出: {x.shape}")
        x = F.relu(x)
        print(f"ReLU后: {x.shape}")
        
        # Step 3: 第二个线性层 + ReLU
        x = self.l2(x)
        print(f"l2输出: {x.shape}")
        x = F.relu(x)
        print(f"ReLU后: {x.shape}")
        
        # Step 4: 第三个线性层 + ReLU
        x = self.l3(x)
        print(f"l3输出: {x.shape}")
        x = F.relu(x)
        print(f"ReLU后: {x.shape}")
        
        # Step 5: 第四个线性层 + ReLU
        x = self.l4(x)
        print(f"l4输出: {x.shape}")
        x = F.relu(x)
        print(f"ReLU后: {x.shape}")
        
        # Step 6: 输出层（不加激活函数）
        x = self.l5(x)
        print(f"最终输出: {x.shape}")
        
        return x

# 创建网络实例
model = DeepClassifier()

# 模拟输入数据：批次大小为3的MNIST图像
batch_size = 3
input_data = torch.randn(batch_size, 1, 28, 28)

print("=== 前向传播过程 ===")
with torch.no_grad():
    output = model(input_data)

print(f"\n=== 输出解释 ===")
print(f"最终输出形状: {output.shape}")
print(f"含义: {batch_size}个样本，每个样本对10个类别的预测分数")
print(f"输出数据:\n{output}")

print(f"\n=== 每层的作用详解 ===")
print("1. 输入层: (N,1,28,28) → view(-1,784) → (N,784)")
print("   作用: 将2D图像展平为1D向量")
print()
print("2. 第一层: Linear(784,512) + ReLU")
print("   作用: 784个像素特征 → 512个抽象特征")
print("   ReLU: 增加非线性，去除负值")
print()
print("3. 第二层: Linear(512,256) + ReLU") 
print("   作用: 512个特征 → 256个更抽象的特征")
print("   特征逐渐压缩和抽象化")
print()
print("4. 第三层: Linear(256,128) + ReLU")
print("   作用: 继续特征压缩和抽象")
print()
print("5. 第四层: Linear(128,64) + ReLU")
print("   作用: 进一步特征压缩")
print()
print("6. 输出层: Linear(64,10)")
print("   作用: 64个高级特征 → 10个类别的原始分数(logits)")
print("   注意: 输出层不使用激活函数!")

print(f"\n=== 参数数量计算 ===")
total_params = 0
for name, param in model.named_parameters():
    param_count = param.numel()
    total_params += param_count
    print(f"{name}: {param.shape} → {param_count:,} 参数")

print(f"总参数数量: {total_params:,}")

print(f"\n=== 为什么这样设计？ ===")
print("1. 逐层降维: 784→512→256→128→64→10")
print("   - 逐步提取和压缩特征")
print("   - 从低级特征到高级语义特征")
print()
print("2. ReLU激活函数:")
print("   - 增加网络的非线性表达能力")
print("   - 解决梯度消失问题")
print("   - 计算简单高效")
print()
print("3. 深度设计:")
print("   - 多层可以学习复杂的特征组合")
print("   - 每层学习不同抽象级别的特征")
print()
print("4. 输出层设计:")
print("   - 10个神经元对应10个类别")
print("   - 输出原始分数，后续用softmax转换为概率")

print(f"\n=== 训练时的完整流程 ===")
print("1. 前向传播: 输入 → 网络 → logits输出")
print("2. 损失计算: CrossEntropyLoss(logits, 真实标签)")
print("3. 反向传播: 计算梯度")
print("4. 参数更新: 优化器更新权重")
print("5. 重复上述过程")

print(f"\n=== 推理时的完整流程 ===")
print("1. 输入图像 → 网络 → logits")
print("2. Softmax(logits) → 概率分布")
print("3. argmax(概率) → 预测类别")

# 演示推理过程
print(f"\n=== 推理演示 ===")
with torch.no_grad():
    logits = model(input_data)
    probabilities = F.softmax(logits, dim=1)
    predictions = torch.argmax(probabilities, dim=1)
    
    for i in range(batch_size):
        print(f"样本{i+1}:")
        print(f"  Logits: {logits[i].numpy()}")
        print(f"  概率分布: {probabilities[i].numpy()}")
        print(f"  预测类别: {predictions[i].item()}")
        print(f"  最高概率: {probabilities[i].max().item():.4f}")
        print()
