---
tags:
  - fleeting
CreatedTime:  2025-07-19
modified_time:  2025-07-19 11:20:56
---
# 不失真的裁切
📦 LetterBox 的作用
LetterBox 的作用非常简单明确：

🎯 核心作用
保持图像宽高比的同时，将任意尺寸的图像统一调整到固定尺寸

🔍 具体说明
问题背景：
神经网络需要固定尺寸输入（如 640 x 640）
实际图像尺寸各不相同（如 1920 x 1080, 800 x 600 等）
LetterBox 解决方案：
原图 (1920 x 1080) → 缩放到 (640 x 360) → 上下填充 → 最终 (640 x 640)
原图 (800 x 1200)  → 缩放到 (426 x 640) → 左右填充 → 最终 (640 x 640)
关键特点：
✅ 保持宽高比：图像不会变形
✅ 统一尺寸：所有图像都变成 640 x 640
✅ 填充区域：用灰色（114,114,114）填充空白区域
🆚 对比其他方法
方法结果问题
直接拉伸	640 x 640	❌ 图像变形，影响检测精度
裁剪	640 x 640	❌ 丢失图像信息
LetterBox	640 x 640	✅ 保持原图比例，不丢失信息
🎨 视觉效果
```
原图 (宽图)：  [████████████████]
                    1920 x 1080

LetterBox 后：   [░░░░░░░░░░░░░░░░]  ← 灰色填充
               [████████████████]  ← 原图内容
               [░░░░░░░░░░░░░░░░]  ← 灰色填充
                    640 x 640
```


💡 为什么叫"LetterBox"
就像老式电视播放宽屏电影时，上下会有黑边一样，这种处理方式被称为"信箱模式"（LetterBox）。

总结：LetterBox 让所有图像都能"装进"固定尺寸的"盒子"里，同时保持原有的长宽比例不变形。- [ ] 

# 线性差值
🔍 双线性插值的作用
双线性插值的作用是：在图像缩放过程中，计算新像素位置的颜色值

🎯 核心问题
当图像从 1920 x 1080 缩放到 640 x 360 时：

原图有 1920×1080 = 2,073,600 个像素
新图只有 640×360 = 230,400 个像素
问题：新图像的每个像素应该是什么颜色？
📐 具体场景
原图坐标系：     新图坐标系：
```
0   1   2   3    0.0  1.5  3.0
┌───┬───┬───┐    ┌─────┬─────┐
│ A │ B │ C │    │  ?  │  ?  │
├───┼───┼───┤    └─────┴─────┘
│ D │ E │ F │    
└───┴───┴───┘    ```

新图像素 (0,0) 对应原图坐标 (0.0, 0.0) → 直接取 A 的颜色
新图像素 (1,0) 对应原图坐标 (1.5, 0.0) → 在 A 和 B 之间，需要插值！
```

🔢 双线性插值计算
当新像素对应原图坐标 (1.5, 0.5) 时：
```
原图中的四个邻近像素：
┌─────┬─────┐
│  A  │  B  │  ← y=0
├─────┼─────┤
│  D  │  E  │  ← y=1
└─────┴─────┘
x=1   x=2

目标点(1.5, 0.5)正好在ABDE四个像素的中心
```
计算过程：
```
# 权重计算
wx = 1.5 - 1 = 0.5  # X方向权重
wy = 0.5 - 0 = 0.5  # Y方向权重

# 双线性插值
result = (1-wx)*(1-wy)*A + wx*(1-wy)*B + (1-wx)*wy*D + wx*wy*E
       = 0.5*0.5*A + 0.5*0.5*B + 0.5*0.5*D + 0.5*0.5*E
       = 0.25*(A + B + D + E)  # 四个像素的平均值
```
🎨 视觉效果对比
插值方法效果质量
最近邻插值像素化，有锯齿	❌ 低质量
双线性插值平滑过渡	✅ 高质量
双三次插值更平滑，但计算复杂	✅ 最高质量
🚀 在 TensorRT 中的实现
TensorRT-YOLO/deploy/infer
💡 为什么需要双线性插值？
避免锯齿：让缩放后的图像看起来平滑
保持细节：比简单的像素复制保留更多信息
数学精确：每个新像素都有明确的计算依据
GPU 友好：计算简单，适合并行处理
🎯 总结
双线性插值 = 图像缩放时的"颜色计算器"

它解决了"当新像素位置落在原图像素之间时，应该用什么颜色"这个问题，通过对周围 4 个像素进行加权平均，得到平滑、高质量的缩放结果。