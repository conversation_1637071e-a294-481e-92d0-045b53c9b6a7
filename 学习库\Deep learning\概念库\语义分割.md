---
tags:
  - 学习
  - segmentation
---

# 语义分割

语义分割是指对图片中的像素进行类别上的分类，要理解语义分割就要把它拆成两个部分，一个是语义，一个是分割

```ad-abstract
title: 分割
所谓分割就是按照类别的不同划分为不同的块，本质是对每个像素进行标注，也就是对每个像素打上标签，`掩码（mask）`就是和原图大小相同的矩阵，在这个矩阵中不同的值代表不同的类别
![[语义分割-2025-03-16-08-42-15.png]]
```

```ad-abstract
title: 语义
一句话或者一篇文章都有其表达的意思，这就是语义。同样，一张图片，我们看到以后也能知道它的意思，比如哪些表示的是车，哪些表示的人，哪些表示的是房子..... 图像表达出的意思就是图像的语义
所以语义分割就是将一张图片，表达成机器能够读懂的意思，将图片中的不同类别进行分类
```


## 常见的分割任务

| <center>任务</center>          | <center>简介</center>                                   | <center>经典模型</center> | <center>实现效果</center>                |
| ---------------------------- | ----------------------------------------------------- | --------------------- | ------------------------------------ |
| 语义分割（semantic segmentation）  | 对一张图片里面的各个像素所属的类别进行分类（如图中的所有飞机都是一个类别，房子是一个类别）         | FCN                   | ![[Pasted image 20240729095613.png]] |
| 实例分割（Instance segmentation）  | 语义分割的更进一步，在某一个类别进行更加精细的分类（如图中正在起飞的飞机是一个类别，停在地上的是一个类别） | Mask R-CNN            | ![[Pasted image 20240729095710.png]] |
| 全景分割（Panoramic segmentation） | 以上两种分割是不会对背景进行分类的，而全景分割会将背景也考虑进去                      | Panoptic FPN          |                                      |



## 常见的数据集格式

```ad-info
title: PASCAL_VOC
color:178,22,164

**标注格式**：`.xml`

![[Quicker_20240729_101434.png]]

```


```ad-info
title: MS COCO
color:178,22,164

**标注格式**：`.json`

![[Quicker_20240729_102754.png|711x478]]

```

