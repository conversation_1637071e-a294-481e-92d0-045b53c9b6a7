---
tags:
  - 学习
  - deep_learning
  - pytorch
---

# 模型训练目标与挑战

![[3. 反向传播（Back Propagation）-2025-04-28-16-12-33.png]]
在训练神经网络的时候，核心目标是找到最优的 [[1. 线性模型（Linear Model）#模型设计|权重]] ($\omega$)，使 [[2. 梯度下降算法（Gradient Descent Algorithm）#^bwp6bp|loss]] 降至最低。
对于一个简单的线性模型 ：
$$\hat{y} = \omega * x $$ 
我们可以通过推到损失函数对于权重s的导数（梯度）来找到最优的权重值。
$$
 \\
\begin{Bmatrix}
\omega = \omega - \alpha \frac{\partial loss}{\partial \omega} \\
\frac{\partial loss}{\partial \omega} = \frac{\partial (\hat{y}-y)^2}{\partial \omega} = \frac{\partial (\omega * x - y)^2}{\partial \omega} = 2 * (\omega * x - y) * x
\end{Bmatrix}
$$
- 其中 $\hat{y}$ 是预测值，$y$ 是真实值，$x$ 是输入值

然而对于复杂的神经网络模型，具有多个层和多个参数，直接推导每个参数的梯度是非常复杂的。为了解决这个问题，我们使用了反向传播算法（Back Propagation Algorithm）

# 反向传播算法（Back Propagation Algorithm）

## 计算图

- 反向传播算法的核心思想是使用计算图（Computational Graph）来表示神经网络的计算过程。
- 计算图是一个有向无环图（Directed Acyclic Graph, DAG），其中节点表示操作或变量，边表示数据流动。
对于下面这个例子来说，是没有使用激活函数(activation function)的线性模型，所以这个2层神经网络其实就等于一个1层的神经网络，也就是只能线性变换，整体是一个线性模型，只能简单的学习输入和输出之间的线性关系

```ad-flex
title: 线性模型
color: 255,150,128
![[Lecture_04_Back_Propagation.pdf#page=5&rect=10,138,951,475&color=note|Lecture_04_Back_Propagation, p.5]]
![[Lecture_04_Back_Propagation.pdf#page=10&rect=25,39,867,478&color=red|Lecture_04_Back_Propagation, p.10]]
```

所以，为了提高模型的表达能力，我们需要使用 [[激活函数 | 非线性激活函数]] (Non-activation function，可以简称为激活函数)，将线性模型变成非线性模型。这个非线性变换会应用于向量或张量中的每一个元素。
![[Lecture_04_Back_Propagation.pdf#page=11&rect=423,37,911,481&color=red|Lecture_04_Back_Propagation, p.11]]

## 链式法则（Chain Rule）

- 链式法则是微积分中的一个重要法则，用于计算复合函数的导数，例如$f(g(x))$的导数可以表示为$f' =\frac{\partial f}{\partial g} \cdot \frac{\partial g}{\partial x}$
- 在反向传播算法中，需要==计算损失函数对当前层输入的梯度==，通过使用链式法则来计算最终损失 (loss) 对当前层输出的梯度（求导）与当前层输出对当前层输入的梯度（局部导数）的乘积。

## 正向传播（Forward Propagation）

- 正向传播是指将输入数据通过神经网络的每一层进行计算（沿着计算图的箭头方向），直到得到最终的输出结果和损失值（Loss）。
- 在正向传播的过程中，除了==计算输出值==，还可以==同时计算每一层的梯度值==（每一层输出对于输入的梯度，局部导数），这些梯度值在反向传播中会用到。
如下图所示，对于这个线性模型 $y = \omega \times x$ ，其输入是 $x = 1$，权重 $\omega = 1$ ^w7plgs
- 第一层：$\hat{y} = x * \omega = 1$ ， $\frac{\hat{y}}{\partial \omega}= \frac{\partial{x * \omega}}{\partial{\omega}} = x = 1$
- 第二层：$r=\hat{y}-y = -1$， $\frac{\partial{r}}{\partial{\hat{y}}} = \frac{\partial{(\hat{y}-y})}{\partial{\hat{y}}} = 1$
- 最终输入：$loss = r^{2} = 1$
![[Lecture_04_Back_Propagation.pdf#page=28&rect=50,157,890,477&color=red|Lecture_04_Back_Propagation, p.28]]

## 反向传播（Backward Propagation）

- 在正向传播完成后，我们会得到最终的损失值（Loss），接下来我们需要通过反向传播算法来计算损失函数对每一层输入的梯度，最终得到损失函数对于输入的梯度（$\frac{\partial{loss}}{\omega}$），然后完成参数（$\omega$）的更新 [[2. 梯度下降算法（Gradient Descent Algorithm）#随机梯度下降|随机梯度下降]]
- 可以使用链式法则将损失函数对每一层输入的梯度表示为==损失函数对当前层输出的梯度==与==当前层输出对当前层输入的梯度（[[3. 反向传播（Back Propagation）#^w7plgs|在正向传播的时候计算的]]）==的==乘积==

如下图所示：
- loss对r的梯度：$\frac{\partial{loss}}{r} = \frac{r^{2}}{r} = 2r = -2$
- loss对$\hat{y}$的梯度，使用链式法则可表示为：$\frac{\partial{loss}}{\hat{y}} = \frac{\partial{loss}}{r} * \frac{\partial{r}}{\hat{y}} = -2 * 1 = -2$
- loss对$\omega$的梯度，使用链式法则可表示为：$\frac{\partial{loss}}{\omega} = \frac{\partial{loss}}{\hat{y}} * \frac{\partial{\hat{y}}}{\omega} = -2 * x = -2$

![[Lecture_04_Back_Propagation.pdf#page=32&rect=9,40,921,473&color=red|Lecture_04_Back_Propagation, p.32]]

## 练习

对于加入偏置(bias)的仿射模型(Affine Model) $y = \omega * x + b$，请计算损失函数对权重和偏置的梯度

![[Lecture_04_Back_Propagation.pdf#page=34&rect=13,46,920,474&color=red|Lecture_04_Back_Propagation, p.34]]

- 正向传播：
    - 第一层：$x * \omega  = 1 * 1 = 1$, $\frac{\hat{y}}{\partial \omega}= \frac{\partial{x * \omega}}{\partial{\omega}} = x = 1$
    - 第二层：$\hat{y} = x * \omega + b = 1 + 2 = 3$, $\frac{\partial{\hat{y}}}{\partial{\omega * x}} = \frac{\partial({\omega * x}+b)}{\partial{\omega * x}} = 1$，$\frac{\partial{\hat{y}}}{\partial{b}} = \frac{\partial({\omega * x}+b)}{\partial{b}} = 1$
    - 最终输入：$loss = r^{2} = (\hat{y} - y)^2 = 1$

- 反向传播：
    - loss 对 $\hat{y}$ 的梯度：$\frac{\partial{loss}}{\hat{y}} = \frac{\partial{loss}}{r} * \frac{\partial{r}}{\hat{y}} = 2 * 1 = 2$
    - loss 对 $\omega * x$ 的梯度：$\frac{\partial{loss}}{\hat{y}} * \frac{\partial{\hat{y}}}{\omega * x} = 2 * 1 = 2$
    - loss 对 $\omega$ 的梯度：$\frac{\partial{loss}}{\omega * x} * \frac{\partial{\omega * x}}{\omega} = 2 * 1 = 2$ 
    - loss 对 $b$ 的梯度：$\frac{\partial{loss}}{\partial{\hat{y}}} * \frac{\partial{\hat{y}}}{b} = 2 * 1 = 2$