---
tags:
  - 学习
  - deep_learning
---

## 验证（validation）

### 留一法交叉验证 (Leave-one-subject-out Cross-validation)

```ad-note
title: 留一法交叉验证
- **工作原理：** 每次从数据集中选择一个样本作为验证集，其余样本作为训练集。这个过程会重复进行，直到每个样本都被用作验证集一次。
- **优点：** 充分利用了所有数据，适用于小数据集。
- **缺点：** 计算成本高，特别是在数据集较大时。同时，如果数据集中的样本存在相关性，那么留一法可能会导致验证集和训练集之间的相关性过高，从而影响模型的泛化能力。
```

#### 留一法交叉验证的步骤

假设一个数据集中有 N 个独立的病人数据。
1. **第一次迭代**：将病人 1 的数据作为验证集，将病人 2 到病人 N 的所有数据作为训练集来训练模型，并记录模型在病人 1 上的表现。

2. **第二次迭代**：将病人 2 的数据作为验证集，将病人 1 以及病人 3 到病人 N 的数据作为训练集，并记录模型在病人 2 上的表现。

3. 依此类推，直到第 N 次迭代。

4. 最终，将 N 次迭代得到的性能指标（如准确率、精确率等）进行平均，作为模型的最终评估结果。

### K 折交叉验证 (K-fold Cross-validation)

```ad-note
title: k折交叉验证
- **工作原理：** 将原始数据集随机分成K个大小相似的子集。在每一轮中，一个子集被用作验证集，其余K-1个子集被用作训练集。这个过程会重复K次，确保每个子集都做过一次验证集。
    
- **优点：** 通过让每个样本都有机会用于训练和验证，提高了评估结果的准确性和稳定性。
    
- **缺点：** 对于大型数据集，这种方法可能会消耗大量的计算资源和时间。如果数据分布不均，某些类别的样本数量很少，它们可能在某些训练集或验证集中缺失，从而影响模型的性能。
```

#### K 折交叉验证的步骤

假设原始的裂纹舌象数据集中，总样本数为684，裂纹舌的数量为348，非裂纹舌的数量为336。进行五折划分后的结果为：

| 折数  | 训练集样本数 | 训练集裂纹舌样本数 | 训练集非裂纹舌样本数 | 测试集样本数 | 测试集裂纹舌样本数 | 测试集非裂纹舌样本数 |
| --- | ------ | --------- | ---------- | ------ | --------- | ---------- |
| 1   | 547    | 274       | 273        | 137    | 74        | 63         |
| 2   | 547    | 274       | 273        | 137    | 74        | 63         |
| 3   | 547    | 274       | 273        | 137    | 74        | 63         |
| 4   | 547    | 274       | 273        | 137    | 74        | 63         |
| 5   | 547    | 274       | 273        | 137    | 74        | 63         |

需要注意以下：

1. **数据不重复**：虽然每折的数量相同，但==具体的图像是不同==的，确保了数据的多样性和代表性。 例如，在FOLD 1 中 验证集用图像 1-137，训练集用图像 138-684，在FOLD 2 中 验证集用图像 138-274，训练集用图像 1-137 和 275-684，以此类推。
2. **每张图像只验证一次**：图像A在FOLD 1 中作为验证集，FOLD 2,3,4,5 中作为训练集；图像B在FOLD 2 中做验证，在FOLD 1,3,4,5 中作为训练集，以此类推。 确保每张图像只参与一次验证，避免了数据泄漏。
3. **样本均衡**：每折的训练集和测试集中的裂纹舌和非裂纹舌样本比例保持一致，确保了模型训练和测试的公平性。



### 保留验证 (Hold-out)

```ad-note
title: 保留验证
- **工作原理：** 将数据集随机一次性划分为训练集和验证集
- **优点：** 实现简单，计算效率高，适用于大数据集。
- **缺点：** 评估结果严重依赖于数据的划分方式。如果训练集和验证集的数据分布存在显著差异，可能会导致评估结果出现偏差，不能准确反映模型的真实性能。
```

#### 保留验证的步骤

假设原始数据集共有 1000 张图像。
1. **数据划分**：按照预设的比例（例如，80% 用于训练，20% 用于验证）将数据集随机分成两部分。
  - 训练集：包含 800 张图像，用于训练模型参数。
  - 验证集：包含 200 张图像，用于评估模型的性能。

1. **模型训练**：仅使用 800 张图像的训练集来训练模型。

2. **模型验证**：使用训练好的模型在 200 张图像的验证集上进行预测，并计算性能指标。


### 外部验证 (External Validation)

```ad-note
title: 外部验证
- **工作原理：** 使用一个完全独立的数据集来评估模型的性能。这个数据集通常来自不同的来源或实验条件，确保与训练集没有任何重叠。
- **优点：** 提供了对模型泛化能力的强有力验证，能够更真实地反映模型在实际应用中的表现。
- **缺点：** 需要额外的数据集，可能难以获取，且不同数据源之间可能存在数据偏差（Data Bias），数据质量和可用性也可能存在差异，需要对数据进行仔细的选择和预处理。
```

#### 外部验证的步骤

假设模型的目标是诊断某种疾病。
1. **数据准备**：收集一个独立的外部数据集，这个数据集包含了与训练集不同来源的样本。例如，训练集可能来自医院A，而外部验证集来自医院B，折两个数据集是完全独立的
2. **模型训练**：使用训练集（医院A的数据）来训练模型。
3. **模型验证**：将训练好的模型应用于外部验证集（医院B的数据），并评估其性能指标，如准确率、召回率等。

