2025-07-09 12:36:44.467 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-09 12:36:44.513 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-09 12:36:44.573 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-07-09 12:36:44.574 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-07-09 12:36:44.575 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-07-09 12:36:44.577 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-07-09 12:36:45.830 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-09 12:36:46.912 [info] script extension loaded 0 functions from components/script  [object Map] 
2025-07-09 12:36:47.000 [info] components database created cost 89 ms   
2025-07-09 12:36:47.001 [info] components index initializing...   
2025-07-09 12:36:47.020 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-09 12:36:47.029 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-09 12:36:47.092 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-09 12:36:47.177 [info] start to batch put pages: 6   
2025-07-09 12:36:47.180 [info] batch persist cost 6  3 
2025-07-09 12:36:47.222 [info] components index initialized, 953 files cost 311 ms   
2025-07-09 12:36:47.223 [info] refresh page data from init listeners 0 953   
2025-07-09 12:36:47.908 [info] indexing created file components/logs/2025-07-09.components.log  [object Object] 
2025-07-09 12:36:47.912 [info] refresh page data from created listeners 0 954   
2025-07-09 12:36:47.926 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-09 12:36:47.929 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-09 12:36:47.932 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-09 12:36:47.935 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-09 12:36:48.146 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-09 12:36:49.007 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-09 12:36:49.011 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-09 12:36:49.019 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-09 12:36:49.024 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-09 12:36:50.628 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-09 12:36:50.650 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-09 12:36:50.654 [info] index finished after resolve  [object Object] 
2025-07-09 12:36:50.656 [info] refresh page data from resolve listeners 0 954   
2025-07-09 12:36:51.380 [debug] ignore file modify evnet 日记库/读博.md   
2025-07-09 12:36:51.423 [info] trigger 日记库/读博.md resolve  [object Object] 
2025-07-09 12:36:51.424 [info] index finished after resolve  [object Object] 
2025-07-09 12:36:51.427 [info] refresh page data from resolve listeners 0 954   
2025-07-09 12:36:54.497 [info] indexing created file components/logs/2025-07-08.components.log  [object Object] 
2025-07-09 12:36:54.502 [info] refresh page data from created listeners 0 955   
2025-07-09 12:36:55.288 [info] indexing created file 学习库/Deep learning/概念库/attachments/上采样；下采样-2025-07-09-08-32-42.png  [object Object] 
2025-07-09 12:36:55.293 [info] refresh page data from created listeners 0 956   
2025-07-09 12:36:55.997 [debug] ignore file modify evnet 学习库/Deep learning/概念库/上采样；下采样.md   
2025-07-09 12:36:56.018 [info] trigger 学习库/Deep learning/概念库/上采样；下采样.md resolve  [object Object] 
2025-07-09 12:36:56.019 [info] index finished after resolve  [object Object] 
2025-07-09 12:36:56.020 [info] refresh page data from resolve listeners 0 956   
2025-07-09 12:36:56.668 [info] indexing created file 学习库/Deep learning/概念库/交叉验证.md  [object Object] 
2025-07-09 12:36:56.668 [info] indexing created ignore file 学习库/Deep learning/概念库/交叉验证.md   
2025-07-09 12:36:56.691 [info] trigger 学习库/Deep learning/概念库/交叉验证.md resolve  [object Object] 
2025-07-09 12:36:56.693 [info] index finished after resolve  [object Object] 
2025-07-09 12:36:56.695 [info] refresh page data from resolve listeners 0 957   
2025-07-09 12:36:57.356 [debug] ignore file modify evnet 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md   
2025-07-09 12:36:57.381 [info] trigger 学习库/Deep learning/pytorch/9. 卷积神经网络（Convolutional Neural Network）.md resolve  [object Object] 
2025-07-09 12:36:57.383 [info] index finished after resolve  [object Object] 
2025-07-09 12:36:57.384 [info] refresh page data from resolve listeners 0 957   
2025-07-09 12:36:59.041 [info] indexing created file 学习库/Deep learning/pytorch/attachments/10. 循环神经网络（RNN）-2025-07-09-10-36-54.png  [object Object] 
2025-07-09 12:36:59.046 [info] refresh page data from created listeners 0 958   
2025-07-09 12:36:59.903 [info] indexing created file 学习库/Deep learning/pytorch/10. 循环神经网络（Recurrent Neural Network）.md  [object Object] 
2025-07-09 12:36:59.904 [info] indexing created ignore file 学习库/Deep learning/pytorch/10. 循环神经网络（Recurrent Neural Network）.md   
2025-07-09 12:36:59.928 [info] trigger 学习库/Deep learning/pytorch/10. 循环神经网络（Recurrent Neural Network）.md resolve  [object Object] 
2025-07-09 12:36:59.932 [info] index finished after resolve  [object Object] 
2025-07-09 12:36:59.937 [info] refresh page data from resolve listeners 0 959   
2025-07-09 12:39:48.457 [debug] ignore file modify evnet 日记库/fleeting_notes/闪念.md   
2025-07-09 12:39:48.471 [info] trigger 日记库/fleeting_notes/闪念.md resolve  [object Object] 
2025-07-09 12:39:48.473 [info] index finished after resolve  [object Object] 
2025-07-09 12:39:48.475 [info] refresh page data from resolve listeners 0 959   
2025-07-09 12:40:22.164 [debug] ignore file modify evnet 日记库/fleeting_notes/闪念.md   
2025-07-09 12:40:22.262 [info] trigger 日记库/fleeting_notes/闪念.md resolve  [object Object] 
2025-07-09 12:40:22.269 [info] index finished after resolve  [object Object] 
2025-07-09 12:40:22.272 [info] refresh page data from resolve listeners 0 959   
2025-07-09 12:40:24.249 [debug] ignore file modify evnet 日记库/fleeting_notes/闪念.md   
2025-07-09 12:40:24.312 [info] trigger 日记库/fleeting_notes/闪念.md resolve  [object Object] 
2025-07-09 12:40:24.317 [info] index finished after resolve  [object Object] 
2025-07-09 12:40:24.319 [info] refresh page data from resolve listeners 0 959   
2025-07-09 12:40:30.481 [debug] ignore file modify evnet 日记库/fleeting_notes/闪念.md   
2025-07-09 12:40:30.499 [info] trigger 日记库/fleeting_notes/闪念.md resolve  [object Object] 
2025-07-09 12:40:30.503 [info] index finished after resolve  [object Object] 
2025-07-09 12:40:30.505 [info] refresh page data from resolve listeners 0 959   
2025-07-09 12:40:36.149 [debug] ignore file modify evnet 日记库/fleeting_notes/闪念.md   
2025-07-09 12:40:36.165 [info] trigger 日记库/fleeting_notes/闪念.md resolve  [object Object] 
2025-07-09 12:40:36.167 [info] index finished after resolve  [object Object] 
2025-07-09 12:40:36.169 [info] refresh page data from resolve listeners 0 959   
2025-07-09 12:41:26 [info] ignore file modify evnet 日记库/fleeting_notes/闪念.md   
2025-07-09 12:41:26 [info] trigger 日记库/fleeting_notes/闪念.md resolve  [object Object] 
2025-07-09 12:41:26 [info] index finished after resolve  [object Object] 
2025-07-09 12:41:26 [info] refresh page data from resolve listeners 0 965   
2025-07-09 17:29:02.342 [info] script extension loaded 0 functions from components/script  [object Map] 
2025-07-09 17:29:02.344 [info] components database created cost 3 ms   
2025-07-09 17:29:02.345 [info] components index initializing...   
2025-07-09 17:29:02.540 [info] start to batch put pages: 8   
2025-07-09 17:29:02.554 [info] batch persist cost 8  14 
2025-07-09 17:29:02.606 [info] components index initialized, 959 files cost 265 ms   
2025-07-09 17:29:02.607 [info] refresh page data from init listeners 0 959   
2025-07-09 17:29:04.073 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-09 17:29:04.361 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-09 17:29:04.861 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-09 17:29:04.865 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-09 17:29:04.879 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-09 17:29:04.894 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-09 17:29:49.175 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-09 17:29:49.336 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-09 17:29:49.342 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-09 17:29:49.548 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-09 17:29:49.596 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-09 17:29:49.773 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-09 17:29:49.841 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-09 17:29:49.883 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-09 17:29:49.915 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-09 17:29:49.926 [debug] ignore file modify evnet Home/components/view/文件检索.components   
