# 单层神经网络
简单的神经网络的模型如下图所示，其中隐藏层的作用是让模型更加的复杂，也就是将输入数据进行升维（这里的隐藏层有 10 个神经元，也就是将输入层从原来的二维升到了 10 维）。
![[Pasted image 20241008095639.png]]
# 多层神经网络
而对于下面这张人脸图片，输入层就是一张图片中的所有像素，隐藏层可以表示为对不同像素进行综合判断的各个动作特征，最后在输出层中进行综合判断得到人的心情 ![[Pasted image 20241008100523.png]] 但这样是无法解释为什么神经网络需要这么多隐藏层。下面的 GIF 是数字识别任务，输入同样是图片的各个像素，对于一个数字 8可以看出它最主要的特征是上面和下面的两个圆圈，这两个圆圈又分别由各自的四个子特征（浅层特征）所组成，这样的好处就是对于其它的数字，比如数字 6，下面的圈可以和 8 公用子特征，属于它独有的子特征也就只有上面的竖。所以隐藏层越深，神经网络的抽象程度越高。  ![[GIF 2024-10-8 10-21-33.gif]]

