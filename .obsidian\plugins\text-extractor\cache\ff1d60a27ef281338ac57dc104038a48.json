{"path": "Deep learning/训练实践/mmsegmentation/Attachments/Pasted image 20250227165524.png", "text": "Lo e UL SUEH SEpET L TRt EsEr from .refuge import REFUGEDataset from .stare import STAREDataset from .synapse import SynapseDataset from .tongue import TongueDataset from .note import notedateset ¢ yapf: disable from .transforms import (CLAHE, AdjustGamma, Albu, BioMedical3DPad, BioMedical3DRandonCrop, BioMedical3DRandomFlip, BioMedicalGaussianBlur, BioMedicalGaussianNoise, BioMedicalRandonGamma, ConcatCDInput, GenerateEdge, LoadAnnotations, LoadBiomedicalAnnotation, LoadBiomedicalData, LoadBiomedicalImageFromFile, LoadImageFromNDArray, LoadMultipleRSImageFromFile, LoadSingleRSImageFronFile, PackSegInputs, PhotoMetricDistortion, RandomCrop, RandomCutOut, RandomMosaic, RandomRotate, RandomRotFlip, Rerange, ResizeShortestEdge, ResizeToMultiple, RGB2Gray, [ SegRescale)| from .voc import PascalVOCDataset ‘PascalContextataset’, ‘PascalContextbatasetsy’, ‘Chasebblbataset, ‘DRIVEDataset’, 'HRFDataset’, 'STAREDataset’, 'DarkZurichDataset’, *NightDrivingDataset’, 'COCOStuffDataset’, 'LoveDADataset’, *MultiImageMixDataset', 'iSAIDDataset', 'ISPRSDataset', 'PotsdamDataset’, *LoadAnnotations', 'RandomCrop’, 'SegRescale’, 'PhotoMetricDistortion', *RandomRotate’, 'AdjustGamma’, 'CLAHE', 'Rerange’, 'RGB2Gray’, *RandomCutOut®, 'RandomMosaic', 'PackSegInputs’, 'ResizeToMultiple', *LoadImageFromNDArray ', ‘LoadBiomedicalImageFromFile’, *LoadBiomedicalAnnotation’, ‘LoadBiomedicalData’, 'GenerateEdge', ‘DecathlonDataset’, 'LIPDataset’, 'ResizeShortestEdge’, *BioMedicalGaussianNoise’, 'BioMedicalGaussianBlur', *BioMedicalRandomGamma' , 'BioMedical3DPad’, 'RandomRotFlip’, *SynapseDataset’, 'REFUGEDataset’, 'MapillaryDataset vi', ‘MapillaryDataset_v2', 'Albu’, 'LEVIRCDDataset’, *LoadMultipleRSImageFromFile’, 'LoadSingleRSImageFromFile’, *ConcatCDInput', 'BaseCDDataset’, 'DSDLSegDataset’, 'BDD10OKDataset', *NYUDataset', 'HSIDrive2eDataset’, 'TongueDataset', ]", "libVersion": "0.3.2", "langs": "eng"}