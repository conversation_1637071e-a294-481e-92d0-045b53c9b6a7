---
tags:
  - 学习
  - python
  - Numpy
---

# 访问数组元素

与python列表类似，Numpy数组的元素也可以通过索引访问，索引从0开始

```python
import numpy as np
# 创建一维数组-向量
arr1 = np.array([1, 2, 3])
print(arr1[0])  # 输出结果为1，表示访问第一个元素
print(arr1[1])  # 输出结果为2，表示访问第二个元素
print(arr1[2])  # 输出结果为3，表示访问第三个元素
print(arr1[-1])  # 输出结果为3，表示访问最后一个元素

# 创建二维数组-矩阵
arr2 = np.array([[1,2,3],[4,5,6]])
print(arr2[0,0])  # 输出结果为1，表示访问第一行第一列的元素
print(arr2[0,1])  # 输出结果为2，表示访问第一行第二列的元素
print(arr2[0,2])  # 输出结果为3，表示访问第一行第三列的元素

```

# 花式索引

花式索引是指使用整数数组或布尔数组来访问Numpy数组的元素(使用两层中括号)。花式索引可以用于一维和多维数组。花式索引输出的仍然是一个向量

```python
import numpy as np
# 创建一维数组-向量
arr1 = np.arange(0,90,10)
print(arr1)  # 输出结果为[0 10 20 30 40 50 60 70 80]

# 花式索引
print(arr1[[0,2,4]])  # 输出结果为[0 20 40]，表示访问第0、2、4个元素

# 创建二维数组-矩阵
arr2 = np.arange(1,17).reshape(4,4)
print(arr2)  # 输出结果为[[ 1  2  3  4] [ 5  6  7  8] [ 9 10 11 12] [13 14 15 16]]

# 花式索引修改数组元素
arr2[[0,2],[1,3]] = 99  # 修改第0行第1列和第2行第3列的元素为99
print(arr2)  # 输出结果为[[ 1 99  3  4] [ 5  6  7  8] [ 9 10 11 99] [13 14 15 16]]
```


| 索引方式 | 向量                          | 矩阵                                             |
| ---- | --------------------------- | ---------------------------------------------- |
| 普通索引 | $arr1[x_{1}]$               | $arr2[x_{1},y_{1}]$                            |
| 花式索引 | $arr1[[x_{1},x_{2},x_{3}]]$ | $arr2[[x_{1},x_{2},x_{3}],[y_{1},y_{2},y_{3}]]$ |


# 访问数组切片

向量与列表的切片完全一致

```python
import numpy as np
# 一维数组-向量的切片
arr1 = np.arange(10)
print(arr1)  # 输出结果为[0 1 2 3 4 5 6 7 8 9]
print(arr1[0:5])  # 输出结果为[0 1 2 3 4]，表示访问第0到第4个元素
print(arr1[5:])  # 输出结果为[5 6 7 8 9]，表示访问第5到最后一个元素
print(arr1[:5])  # 输出结果为[0 1 2 3 4]，表示访问第0到第4个元素
print(arr1[::2])  # 输出结果为[0 2 4 6 8]，表示访问第0到最后一个元素，步长为2

# 二维数组-矩阵的切片
arr2 = np.arange(1,21).reshape(4,5)
print(arr2)  # 输出结果为[[ 1  2  3  4  5] [ 6  7  8  9 10] [11 12 13 14 15] [16 17 18 19 20]]
print(arr2[0:2, 0:3])  # 输出结果为[[1 2 3] [6 7 8]]，表示访问第0到第1行，第0到第2列的元素
print(arr2[0:2, ::2])  # 输出结果为[[1 3 5] [6 8 10]]，表示访问第0到第1行，第0到最后一列，步长为2的元素

# 提取列矩阵
arr2[:, 1]  # 输出结果为[ 2  7 12 17]，是一个向量
arr2[:.1:3]  # 输出结果为[[ 2] [ 7] [12] [17]]，是一个矩阵

# 如果想提取列矩阵
arr2[:, 1:2]  # 输出结果为[[ 2] [ 7] [12] [17]]，是一个矩阵
arr2[:, 1].reshape(1,-1).T  # 先转换为行矩阵，再进行转置，输出结果也为[[ 2] [ 7] [12] [17]]
```

# 数组的切片只是一个视图

数组的切片只是一个视图，不会创建新的数组，修改切片中的元素会影响原数组

```python
import numpy as np
# 创建一维数组-向量
arr1 = np.arange(10)
print(arr1)  # 输出结果为[0 1 2 3 4 5 6 7 8 9]
cut1 = arr1[0:5]  # 切片
print(cut1)  # 输出结果为[0 1 2 3 4]
cut1[0] = 99  # 修改切片中的元素
print(cut1)  # 输出结果为[99  1  2  3  4]，表示切片中的第一个元素被修改为99
print(arr1)  # 输出结果为[99  1  2  3  4  5  6  7  8  9]，表示原数组中的第一个元素也被修改为99

# 如果想创建新的数组，可以使用`copy()`方法
arr2 = arr1[0:5].copy()  # 切片
print(arr2)  # 输出结果为[99  1  2  3  4]
arr2[0] = 100  # 修改切片中的元素
print(arr2)  # 输出结果为[100  1  2  3  4]，表示切片中的第一个元素被修改为100
print(arr1)  # 输出结果为[99  1  2  3  4  5  6  7  8  9]，表示原数组中的第一个元素仍然为99
```

# 数组赋值仅是绑定

数组赋值仅是绑定，不会创建新的数组，修改赋值后的数组会影响原数组

```python
import numpy as np
# 创建一维数组-向量
arr1 = np.arange(10)
print(arr1)  # 输出结果为[0 1 2 3 4 5 6 7 8 9]
arr2 = arr1  # 赋值
print(arr2)  # 输出结果为[0 1 2 3 4 5 6 7 8 9]
arr2[0] = 99  # 修改赋值后的数组
print(arr2)  # 输出结果为[99  1  2  3  4  5  6  7  8  9]，表示赋值后的第一个元素被修改为99
print(arr1)  # 输出结果为[99  1  2  3  4  5  6  7  8  9]，表示原数组中的第一个元素也被修改为99

# 如果想创建新的数组，可以使用`copy()`方法
```

