import torch
import torch.nn.functional as F

# 设置数据
criterion = torch.nn.CrossEntropyLoss()
Y = torch.LongTensor([2, 0, 1])

Y_pred1 = torch.Tensor([[0.1, 0.2, 0.9],
                        [1.1, 0.1, 0.2], 
                        [0.2, 2.1, 0.1]])

Y_pred2 = torch.Tensor([[0.8, 0.2, 0.3],
                        [0.2, 0.3, 0.5],
                        [0.2, 0.2, 0.5]])

# 计算损失
l1 = criterion(Y_pred1, Y)
l2 = criterion(Y_pred2, Y)

print("=== 损失比较 ===")
print(f"Y_pred1 损失: {l1.item():.4f}")
print(f"Y_pred2 损失: {l2.item():.4f}")
print(f"l1 > l2: {l1 > l2}")

print("\n=== Y_pred1 详细分析 ===")
# 计算softmax概率
softmax1 = F.softmax(Y_pred1, dim=1)
print("Softmax概率:")
for i in range(3):
    print(f"样本{i+1}: {softmax1[i].numpy()}")
    true_class = Y[i].item()
    prob_true_class = softmax1[i][true_class].item()
    print(f"  真实类别{true_class}的概率: {prob_true_class:.4f}")
    print(f"  负对数似然: {-torch.log(softmax1[i][true_class]).item():.4f}")

print("\n=== Y_pred2 详细分析 ===")
softmax2 = F.softmax(Y_pred2, dim=1)
print("Softmax概率:")
for i in range(3):
    print(f"样本{i+1}: {softmax2[i].numpy()}")
    true_class = Y[i].item()
    prob_true_class = softmax2[i][true_class].item()
    print(f"  真实类别{true_class}的概率: {prob_true_class:.4f}")
    print(f"  负对数似然: {-torch.log(softmax2[i][true_class]).item():.4f}")

print("\n=== 预测准确性 ===")
pred1_classes = torch.argmax(Y_pred1, dim=1)
pred2_classes = torch.argmax(Y_pred2, dim=1)

print(f"真实标签:     {Y.numpy()}")
print(f"Y_pred1预测:  {pred1_classes.numpy()}")
print(f"Y_pred2预测:  {pred2_classes.numpy()}")

accuracy1 = (pred1_classes == Y).float().mean()
accuracy2 = (pred2_classes == Y).float().mean()

print(f"Y_pred1准确率: {accuracy1:.2f}")
print(f"Y_pred2准确率: {accuracy2:.2f}")
