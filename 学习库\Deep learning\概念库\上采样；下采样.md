# 上采样

^0adbc0

## 为什么要进行上采样
通常对图像进行 [[卷积和转置卷积#^44488d|卷积操作]]（下采样）会进行多层的卷积运算，从而导致输出图像的尺寸变得很小，但我们往往最后需要高清的图像，所以我们可以通过上采样（up-sampling）可以提高图片的分辨率
![[Pasted image 20240730155551.png]]
上采用有很多方法，但在深度学习中最常见的是使用 [[卷积和转置卷积#^f20300|转置卷积]]
## 使用转置卷积进行上采样
转置卷积的操作如下图所示：
![[20190101220640484.gif]]
上采样通过padding来实现，将小尺寸的图像或特征变成大尺寸
如图所示，stride=2；padding=1；kernel=3\*3；input=3\*3
注：这里的stride是指在原图中的步长，在padding后的图片中卷积核走一步，相当于在原图中走两步。
![[Pasted image 20240120121420.png]]
## 目的
调整图像或特征图的分辨率，用于恢复细节信息
## 缺点
增加计算量

## 公式
$$
i=(o-1)*s+k-2*p
$$
# 下采样

^219591
减少计算量，提高感受野，抑制噪声，提取==高层特征==，但会<span style="background:#ff4d4f">丢失细节信息</span>
> **高层特征**：能够描述图像或视频中的语义信息的特征，比如物体的类别，颜色，形状。高层特征通常是从底层特征（像素，纹理）中提取出来的
> **举例**：识别图像中的人脸，利用卷积神经网络，从最底层的特征（可能是简单的线条或形状）逐步向下采样得到高级特征（眼睛，鼻子，嘴巴），通过这些高级特征来判断图像中是否有人脸
```ad-col2
title: 下采样
color:178,22,164
![[Pasted image 20240120113636.png]]
![[20190101212350912.gif|256]]

```


```ad-thm
title: 下采样输出尺寸公式
color: 140, 122, 230
$$
o=\frac{n-f+2p}{s} +1
$$
其中$n$=输入大小，$0$=输出大小，$p$=padding，$f$=卷积核大小，$s$=步长
```

## 池化

通过池化操作，对输入==**特征图**==进行下采样，用于压缩特征

### 最大池化 (max pooling)

![[Pasted image 20240124210626.png]]

设置 `kernel_size = 3`， `stride = 1`，`filter` 滑动到矩阵的某一部分时，**选取这部分的最大值**（进行最大池化），再往下滑动一个（步幅为 1）进行相同的操作，直至遍历整个矩阵。
使用 `maxpooling` 会改变图像的尺寸

```python
import torch

input = [
    3, 4, 6, 5,
    2, 4, 6, 8,
    1, 6, 7, 8,
    9, 7, 4, 6,
]

# 将input这个列表转换成batch_size=1, in_channel=1, height=4, width=4的四维张量
input_tensor = torch.Tensor(input).view(1, 1, 4, 4)

# 定义最大池化层，窗口大小为2x2, stride默认值为2
maxpool_layer = torch.nn.MaxPool2d(kernel_size=2)

# 前向传播
output_tensor = maxpool_layer(input_tensor)

print(input_tensor.shape) # (1, 1, 4, 4)
print(output_tensor.shape) # (1, 1, 2, 2)，图像的长宽的计算过程是 (4-2+1, 4-2+1) = (2, 2)
print(output_tensor)
```

### 平均池化 (average pooling)

![[上采样；下采样-2025-07-09-08-32-42.png]]

设置 `kernel_size = 3`， `stride = 2`

```python
input = [
    1, 5, 2, 7,
    6, 1, 8, 4,
    5, 6, 0, 1,
    1, 4, 3, 4
]                               

import torch
input_tensor = torch.Tensor(input).view(1, 1, 4, 4)  # 将input这个列表转换成batch_size=1, in_channel=1, height=4, width=4的四维张量
avgpool_layer = torch.nn.AvgPool2d(kernel_size=3, stride=2)  # 定义平均池化层，窗口大小为3x3, 步幅=2
output_tensor = avgpool_layer(input_tensor)  # 前向传播
print(input_tensor.shape)  # (1, 1, 4, 4)
print(output_tensor.shape)  # (1, 1, 2, 2)，图像的长宽的计算过程是 (4-3)/2+1 = 2
print(output_tensor)    
```


