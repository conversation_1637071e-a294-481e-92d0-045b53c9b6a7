import torch

print("=== 张量形状变换演示 ===")

# 模拟MNIST数据：批次大小为3的28x28灰度图像
N = 3  # 批次大小
batch_images = torch.randn(N, 1, 28, 28)

print(f"原始形状: {batch_images.shape}")
print(f"含义: ({N}个样本, 1个通道, 28高度, 28宽度)")

# 计算总的像素数
total_pixels = 1 * 28 * 28
print(f"\n每张图像的像素总数: 1 × 28 × 28 = {total_pixels}")

# 使用view(-1, 784)进行形状变换
reshaped = batch_images.view(-1, 784)
print(f"\n变换后形状: {reshaped.shape}")
print(f"含义: ({N}个样本, 每个样本{total_pixels}个特征)")

print("\n=== view(-1, 784) 详解 ===")
print("参数说明:")
print("- 第一个参数 -1: 让PyTorch自动计算这个维度的大小")
print("- 第二个参数 784: 指定第二个维度为784")
print()
print("计算过程:")
print(f"- 原始总元素数: {N} × 1 × 28 × 28 = {N * 1 * 28 * 28}")
print(f"- 新形状总元素数: ? × 784")
print(f"- 自动计算: ? = {N * 1 * 28 * 28} ÷ 784 = {N}")
print(f"- 所以最终形状: ({N}, 784)")

print("\n=== 实际应用场景 ===")
print("这种变换常用于:")
print("1. 卷积层 → 全连接层的过渡")
print("2. 将2D图像数据展平为1D向量")
print("3. 为分类器准备输入数据")

print("\n=== 具体例子 ===")
# 创建一个简单的例子
small_batch = torch.randn(2, 1, 3, 3)  # 2张3x3的图像
print(f"小例子原始形状: {small_batch.shape}")
print(f"原始数据:\n{small_batch}")

flattened = small_batch.view(-1, 9)  # 3x3=9个像素
print(f"\n展平后形状: {flattened.shape}")
print(f"展平后数据:\n{flattened}")

print("\n=== 为什么要这样做？ ===")
print("全连接层需要1D输入:")
print("- 卷积层输出: (N, C, H, W) - 4维")
print("- 全连接层需要: (N, features) - 2维")
print("- view操作将4维变为2维，保持批次维度不变")

print("\n=== 等价操作 ===")
original = torch.randn(4, 1, 28, 28)
print(f"原始: {original.shape}")

# 方法1: view(-1, 784)
method1 = original.view(-1, 784)
print(f"方法1 view(-1, 784): {method1.shape}")

# 方法2: view(N, -1)
method2 = original.view(4, -1)
print(f"方法2 view(4, -1): {method2.shape}")

# 方法3: flatten(start_dim=1)
method3 = torch.flatten(original, start_dim=1)
print(f"方法3 flatten(start_dim=1): {method3.shape}")

print(f"\n所有方法结果相同: {method1.shape == method2.shape == method3.shape}")
