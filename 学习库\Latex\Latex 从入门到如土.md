# Latex 从入门到如土

## Latex 中的特殊符号

以下符号有特殊含义，所以不能当成普通文本直接打出来

- `$`：这个符号被用作数学公式的标记符，被框在 `$...$` 之间的内容会被渲染为数学公式
- `\`：这个符号用作转义字符，可以用来转义特殊符号，使其被当作普通文本渲染
- `%`：这个符号用作注释符，后面的内容会被忽略
- `&`：这个符号用作表格或者数学公式中的位置对齐符号
- `{` 和 `}`：左右大括号，用于分组和限制作用范围
- `~`：这个符号用作不间断空格，防止换行
- `^` 和 `_`：这两个符号用作上下标，分别表示上标和下标

## 正文

### 设定区域和正文区域

- 设定区域
    - `\documentclass{...}`、`\usepackage{...}` 等为设定区域，主要用于设置文档的类型和引入所需的宏包。
    - 一般不对生产的PDF产生影响

- 正文区域
    - `\begin{document}` 和 `\end{document}` 之间的内容为正文区域，主要用于撰写文档的主体内容。

- 正文各级标题
    - `\section{...}`：用于创建一级标题
    - `\subsection{...}`：用于创建二级标题
    - `\subsubsection{...}`：用于创建三级标题

- 换行，换段，换页，首行缩进
    - `\\`：用于强制换行
    - `\\[offest]`：用于强制换行并设置偏移量
    - `\newline`：用于强制换段
    - `\pagebreak`：用于强制换页
    - `\indent`：用于首行缩进
    - `\par`：用于强制换段
    - `\linebreak`：用于强制换行
    - `\newpage`：用于强制换页
    - `\setlength{\parindent}{长度}`：用于设置段落缩进

## `bst` 文件

**BST (Bibliography Style)** 文件是BibTeX的样式文件，它的作用是：

1. **控制参考文献的格式**：决定参考文献在文档中如何显示
2. **定义引用样式**：控制正文中引用的显示方式（如author-year vs 数字）
3. **设置排序规则**：决定参考文献列表的排序方式（按字母、年份等）
4. **格式化细节**：控制作者名字格式、期刊名缩写、标点符号等

每次更换BST文件时，正确的操作步骤是：

1. **删除辅助文件**：删除 .aux, .bbl, .blg 文件
2. **重新编译**：pdflatex → bibtex → pdflatex → pdflatex



## 插入图片

````ad-example
title: 基本的图片插入
```latex
\begin{figure}[位置参数]
\centering
\includegraphics[选项]{图片文件名}
\caption{图片标题}\label{图片标签}
\end{figure}
```
位置参数设置：
- `[htbp]` 是用来控制图片浮动位置的选项
- `[h]`     here - 尽量放在当前位置
- `[t]`     top - 放在页面顶部
- `[b]`     bottom - 放在页面底部
- `[p]`     page - 单独放在一页
- `[H]`     强制放在当前位置（需要float包）
- `[!h]`    忽略LaTeX的美观规则，强制放在这里
- `[!htb]`

图片选项：
- `[width=0.5\textwidth]`     宽度为文本宽度的50%
- `[height=5cm]`              高度为5厘米
- `[scale=0.8]`               缩放为原大小的80%
- `[width=8cm,height=6cm]`    同时指定宽度和高度
- `[keepaspectratio]`         保持长宽比

图片要求：
- **PDFLaTeX编译**：使用 `.pdf`, `.jpg`, `.png` 格式
- **LaTeX编译**：使用 `.eps` 格式
````

## 标签命名规范

|元素类型|前缀|示例|说明|
|---|---|---|---|
|**章节**|`sec:`|`\label{sec:introduction}`|主要章节|
|**子章节**|`sec:`|`\label{sec:system-overview}`|子章节|
|**图片**|`fig:`|`\label{fig:robotic-system}`|图片|
|**表格**|`tab:`|`\label{tab:results}`|表格|
|**公式**|`eq:`|`\label{eq:main-formula}`|数学公式|
|**算法**|`alg:`|`\label{alg:detection}`|算法|


## 公式显示改进

### 调整矩阵的行间距（垂直间距）

````ad-example
title: `\arraystretch` 命令
使用 `\arraystretch` 命令 (全局或局部)，可以按比例缩放表格或者矩阵中行的高度，默认值为 1.0。
- **全局设置**：将命令放在文档的导言区 (preamble)，会影响文档中所有的 `tabular` 和 `array` 环境。
```latex
\documentclass{article}
\usepackage{amsmath}

\renewcommand{\arraystretch}{1.5} % 将行高增加到默认的1.5倍

\begin{document}
\[
    \begin{pmatrix}
        a & b \\
        c & d
    \end{pmatrix}
\]
\end{document}
```
</br>

- **局部设置**：为了避免影响其他表格或矩阵，可以将其放在一个环境中，或者使用大括号 `{}` 将其分组。
```latex
\documentclass{article}
\usepackage{amsmath}

\begin{document}
默认间距:
\[
    \begin{pmatrix}
        \frac{1}{2} & 0 \\
        0 & 1
    \end{pmatrix}
\]

调整后的间距:
{
\renewcommand{\arraystretch}{2.0}
\[
    \begin{pmatrix}
        \frac{1}{2} & 0 \\
        0 & 1
    \end{pmatrix}
\]
}
\end{document}
```
> `\renewcommand` 的主要作用是 **重新定义 (redefine)** 一个已经存在的命令，也就是改变一个 LaTeX 已有命令的功能或其输出的内容
````

````ad-example
title: 在特定行后添加额外空间
只想调整特定行的间距，可以在换行符 `\\` 后面添加一个可选参数，指定要增加的垂直距离。
```latex
\documentclass{article}
\usepackage{amsmath}

\begin{document}
\[
    \begin{pmatrix}
        a & b \\[1em] % 在第一行后增加 1em 的额外空间
        c & d \\ % 默认间距
        e & f
    \end{pmatrix}
\]
\end{document}
```
````

### 调整列间距（水平间距）

````ad-example
title:使用`\setlength{\arraycolsep}{}` 
- **局部修改**：与 `\arraystretch` 类似，可以将其放在一个环境中或用大括号分组。

```latex
% 在导言区确保 amsmath 已加载（Springer模板已自动加载）

% 使用花括号创建一个局部作用域
{
% 在这个作用域内，临时将列间距设置为10pt（默认为5pt）
\setlength{\arraycolsep}{10pt} 
\begin{equation}
M =
\begin{bmatrix}
s & 0 & -\frac{s \times w_{o}}{2} + \frac{w_{t}}{2} \\[1ex] 
0 & s & -\frac{s \times h_{o}}{2} + \frac{h_{t}}{2}
\end{bmatrix}
\label{eq:matrix-spaced}
\end{equation}
} % 作用域结束，\arraycolsep 的值自动恢复为默认的 5pt

% 下面的矩阵将不受影响，使用默认间距
\begin{equation}
A = 
\begin{bmatrix}
1 & 2 \\
3 & 4
\end{bmatrix}
\end{equation}
```
````

````ad-example
title:在特定列之间插入空白
```latex
\documentclass{article}
\usepackage{amsmath}

\begin{document}
\[
    \begin{pmatrix}
        a & \qquad & b & \! c
    \end{pmatrix}
\]
\end{document}
```
- `\,` (小间距)
    
- `\;` (中等间距)
    
- `\quad` (较大间距)
    
- `\qquad` (更大的间距)
    
- `\!` (负间距，用于收紧)
    
- `\hspace{<length>}` (指定任意长度的间距)
````


````ad-example
title: 分数显示选项
```latex
\frac{a}{b}     % 行内样式（小）
\dfrac{a}{b}    % 显示样式（大）
\tfrac{a}{b}    % 文本样式（小，强制）
```
````


## natbib命令集

**natbib** 是LaTeX中最强大的引用管理包，它提供了丰富的引用命令。"完整natbib命令集"指的是支持natbib包的所有引用命令。

- 基础引用命令
```latex
\cite{key}          → Jones et al. (1990)
\citep{key}         → (Jones et al. 1990)  
\citet{key}         → Jones et al. (1990)
```

- 完整作者列表
```latex
\cite*{key}         → Jones, Baker, and Smith (1990)
\citep*{key}        → (Jones, Baker, and Smith 1990)
\citet*{key}        → Jones, Baker, and Smith (1990)
```

- 带前缀/后缀的引用
```latex
\citep[p.~32]{key}              → (Jones et al. 1990, p. 32)
\citep[see][]{key}              → (see Jones et al. 1990)
\citep[see][p.~32]{key}         → (see Jones et al. 1990, p. 32)
\citep[e.g.,][chap.~2]{key}     → (e.g., Jones et al. 1990, chap. 2)
```

- 分离的引用元素
```latex
\citeauthor{key}    → Jones et al.
\citeauthor*{key}   → Jones, Baker, and Smith
\citeyear{key}      → 1990
\citeyearpar{key}   → (1990)
```

- 多重引用
```latex
\citep{key1,key2,key3}          → (Jones et al. 1990; Smith 1995; Brown 2000)
\citet{key1,key2}               → Jones et al. (1990), Smith (1995)
```

