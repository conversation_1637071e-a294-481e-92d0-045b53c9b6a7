import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F

x_train = torch.tensor([[1.0], [2.0], [3.0], [4.0]])  # 训练数据，形状为(batch_size, 1)
y_train = torch.tensor([[0.0], [0.0], [1.0], [1.0]])  # 训练标签，形状为(batch_size, 1)

# 定义逻辑回归模型
class LogisticRegressionModel(nn.Module):
    def __init__(self):
        super(LogisticRegressionModel, self).__init__()
        self.linear = nn.Linear(1, 1)  # 线性层

    def forward(self, x):
        y_pred = self.linear(x)  # 前向传播
        y_pred = torch.sigmoid(y_pred)  # Sigmoid激活函数
        return y_pred

# 实例化模型
model = LogisticRegressionModel()

# 损失函数
criterion = nn.BCELoss()  # 二元交叉熵损失函数`
# 优化器
optimizer = optim.SGD(model.parameters(), lr=0.01)  # 随机梯度下降优化器，lr是学习率

# 训练模型
num_epochs = 1000  # 训练轮数
for epoch in range(num_epochs):
    # 前向传播
    y_pred = model(x_train)  # x_train是训练数据，形状为(batch_size, 1)
    # 计算损失函数
    loss = criterion(y_pred, y_train)  # y_train是训练标签，形状为(batch_size, 1)
    # 清空梯度
    optimizer.zero_grad()
    # 反向传播
    loss.backward()  # 计算梯度
    # 更新权重
    optimizer.step()  # 更新权重
    # 打印损失
    if (epoch + 1) % 100 == 0:
        print(f'Epoch [{epoch + 1}/{num_epochs}], Loss: {loss.item():.4f}')
# 打印权重和偏置
print(f'Weight: {model.linear.weight.item():.4f}, Bias: {model.linear.bias.item():.4f}')