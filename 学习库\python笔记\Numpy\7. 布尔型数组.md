---
tags:
  - 学习
  - python
  - Numpy
---

# 创建布尔型数组

由于Numpy的主要数据类型是整数型数组和浮点型数组，因此，布尔型数组的产生离不开 大于、小于、等于等比较运算符。

```python
import numpy as np
# 创建数组
arr = np.arange(0,6).reshape(2,3)
print(arr)  # 输出结果为[[0 1 2]
             #          [3 4 5]]
print(arr > 2)  # 输出结果为[[False False False]
                #          [ True  True  True]]
```

# 比较多个条件

在布尔型数组中，`&` ， `|` ， `~` 分别表示与、或、非运算符。

```python
import numpy as np
# 创建数组
arr = np.arange(0,6).reshape(2,3)
print(arr)  # 输出结果为[[0 1 2]
             #          [3 4 5]]
print((arr > 2) | (arr < 4))  # 输出结果为[[False False False]
                #          [ True  True False]]

```

# 布尔型数组做掩码

布尔型数组可以用来做掩码，掩码的作用是将数组中符合条件的元素提取出来。

```python
import numpy as np
# 创建数组
arr = np.arange(0,6).reshape(2,3)
print(arr)  # 输出结果为[[0 1 2]
             #          [3 4 5]]
print(arr > 2)  # 输出结果为[[False False False]
                #          [ True  True  True]]
print(arr[arr > 2])  # 得到一个向量，将满足的元素提取出来 [3 4 5]
```

# 满足条件的元素所在位置

满足条件的元素所在位置可以用 `np.where()` 函数来实现。

```python   
import numpy as np
# 创建数组
arr = np.arange(0,10).reshape(5,2)
print(arr) 
# 输出结果为[[0 1]
             #          [2 3]
             #          [4 5]
             #          [6 7]
             #          [8 9]]
print(np.where(arr > 5))  # 输出结果为(array([3, 3, 4, 4], dtype=int64), array([0, 1, 0, 1], dtype=int64))，是一个元组，分别表示行索引加上数据类型和列索引加上数据类型
row = np.where(arr > 5)[0]  # 得到行索引
print(row)  # 输出结果为[3 3 4 4]
col = np.where(arr > 5)[1]  # 得到列索引
print(col)  # 输出结果为[0 1 0 1]
print(arr[row, col])  # 输出结果为[6 7 8 9]，得到满足条件的元素
```