{"path": "obsidian 插件使用说明/Attachments/未命名/QuickAdd-2.png", "text": "X MLRIR Capture think B AEBH [ i File to capture to. Supports some format syntax. Create Tl 1t dossn exiet [ d Capture to active file 71111 ° Create e with given tempiste. CURMIIERMBIRIRIE o . MEMA RS File name format ;75N ZIERE A 3214 - PR B ah SR A1{{DATE} /77 Create file if it doesn'texist 0 @ Task o e —— Formats the value as atask. ||l - - | oor captrs e spotd e Acopts fornat . - Write to bottom of file Insert after 75 A5 o S BRI _(B7E &% Rl [ET) Put value at the bottom of the file - otherwise at the top. gEIﬁE' L B Insertatend of section 1@ \\E| A A AR ° Insert the text at he end o the section, rather than at he op. Append link FRERN—ERMEAE X R ° _ _ Add a link on your current cursor position, linking to the file you're capturing to. Create ne f not found ASRIE E N B T D ZLE reate line if not foun e (o e — Createsthe nsert ater I s notfouna, WICIAEE @ | Top Insertafter FFFEIEX{FRIEEMERA ° o Insert capture after specified line. Accepts format syntax. Open ® | Defautt Open the file that is capturedto. 1 [~/ L Open et P TRTR—. @ | Ocrout Capture format FERE, BERRNNE ° New Tab FEFERITHF E‘.ﬂﬁ Set the format of the capture. 5= | {(\\/ALUE}} Open the file thatis captured to n a new tab. e . Focus new pane or Tosos oo ey $5 SRR — AWM u/_\\ t\\h t;\\‘l X Jha", "libVersion": "0.3.2", "langs": "eng"}