/* @settings

name: Buttons
id: buttons-styles
settings:
    - 
        id: button-background
        title: Background
        type: variable-themed-color
        format: hex
        opacity: false
        default-light: '#f5f6f8'
        default-dark: '#1b1b1b'
    - 
        id: button-text
        title: Text
        type: variable-themed-color
        format: hex
        opacity: false
        default-light: '#1b1b1b'
        default-dark: '#f5f6f8'
    - 
        id: button-border
        title: Border
        type: variable-themed-color
        format: hex
        opacity: false
        default-light: '#7a9486'
        default-dark: '#84a83a'
    - 
        id: button-box-shadow
        title: Box Shadow
        type: variable-themed-color
        format: rgb
        opacity: true
        default-light: '#1b1b1b'
        default-dark: '#f5f6f8'
    -
        id: button-border-radius
        title: Border Radius
        type: variable-number
        format: px
        default: 5
    -
        id: button-size
        title: Font Size
        type: variable-number
        format: em
        default: 1

*/

.block-language-button {
  padding: 5px;
}

button.button-default {
  border: 0.5px solid var(--button-border, #7a9486);
  border-radius: var(--button-border-radius, 5px);
  background-color: var(--button-background);
  padding: 10px 30px;
  color: var(--button-text);
  text-decoration: none;
  font-size: var(--button-size);
  margin: 0 5px;
  box-shadow: 0 1px 3px var(--button-box-shadow, rgba(0, 0, 0, 0.12)),
    0 1px 2px var(--button-box-shadow, rgba(0, 0, 0, 0.24));
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

button.button-default:hover {
  z-index: 100;
  box-shadow: 0 4px 4px var(--button-box-shadow, rgba(0, 0, 0, 0.25)),
    0 10px 10px var(--button-box-shadow, rgba(0, 0, 0, 0.22));
  transform: translate3d(0px, -1.5px, 0px);
  background-color: var(--button-background);
}

.theme-dark button.button-default {
  border: 0.5px solid var(--button-border, #84a83a);
}

.theme-dark button.button-default:hover {
  z-index: 100;
  box-shadow: 0 4px 4px var(--button-box-shadow, rgba(210, 210, 210, 0.25)),
    0 10px 10px var(--button-box-shadow, rgba(210, 210, 210, 0.22));
  transform: translate3d(0px, -1.5px, 0px);
}

button.button-inline {
  width: unset;
  height: unset;
  padding: 0 8px;
}

button.blue {
  background: #76b3fa;
  color: black;
}

button.red {
  background-color: red;
}

button.green {
  background: green;
}

button.yellow {
  background: yellow;
  color: black;
}

button.purple {
  background: #725585;
}

button.blue:hover {
  background: #76b3fa;
  color: black;
}

button.red:hover {
  background: red;
}

button.green:hover {
  background: green;
}

button.yellow:hover {
  background: yellow;
  color: black;
}

button.purple:hover {
  background: #725585;
}

.button-maker {
  max-width: 35rem;
  width: 35rem;
  overflow-y: auto;
  max-height: 30rem;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  overflow-x: hidden;
}
