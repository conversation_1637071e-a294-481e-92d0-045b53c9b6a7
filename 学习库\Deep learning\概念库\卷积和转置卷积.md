---
tags:
  - 学习
  - deep_learning
---
# 卷积操作
卷积操作是一个线性运算，在深度学习领域主要有三个步骤：位移，相乘，和相加，也就是**输入和卷积核的内积运算**。
学习时需记住以下要点： ^nlhi3f
- 最终得到的输出（feature map）通道数与卷积核（kernel）的数量（卷积核通道数）相同
- 可以通过卷积进行 [[上采样；下采样#^219591|下采样]]（但**下采样不等于卷积**，池化同样也可以进行下采样）
- 正向卷积操作通过卷积核建立了多对一的关系（如在下面提到的 [[卷积和转置卷积#^88a369|二维单通道卷积]]中建立了输入矩阵中的 9 个值对应输出矩阵中 1 个值的对应关系）  ^1ef9f2

## 二维单通道单卷积核
 
```ad-note
title: 公式
1. 输入为5×5的图片，其像素值如下所示：
$$
\begin{bmatrix}
  0 & 0 & 75 & 80 & 80 \\
  0 & 75 & 80 & 80 & 80 \\
  0 & 75 & 80 & 80 & 80 \\
  0 & 75 & 75 & 80 & 80 \\
  0 & 0 & 0 & 0 & 0 
\end{bmatrix}
$$
1. 采用3×3的卷积核（Kernel），其值如下：
$$
\begin{bmatrix}
1 & 0 & 1 \\
0 & 1 & 0 \\
1 & 0 & 1 \\
\end{bmatrix}
$$
1. 经过窗口和卷积核的内积操作得到的结果就是feature map，其中stride = 1, padding = 0。
![[bda132ec94ebe94bd598dd9ae9fa04d5.gif]]
```

## 二维多通道单卷积核

若输入有多个通道，但只有单个卷积核，那么分别对每个通道求 featuremap 后将对应位置相加得到最终的 feature map。
![[Pasted image 20240730110119.png]]

## 二维单通道多卷积核

若有==多个卷积核，则会生成对应数量的 feature map==，即下一输入层的通道数。

![[Pasted image 20240730110336.png]]
![[卷积和转置卷积-2025-07-07-10-01-26.png]]

## padding

假设输入特征为 `5*5`，要想通过 `3*3` 的kernel 来输出 `5*5` 的特征，可以将输入外面填充一圈 0，以得到尺寸为 `5*5` 的特征。
需要 padding 的尺寸，只需要使用 $\frac{输入}{输出}$ 然后取整即可，如 $\frac{5}{3}=1$，即需要padding 一圈

```ad-flex
color: 72, 219, 251
title: padding
![[卷积和转置卷积-2025-07-07-10-25-06.png]]
![[卷积和转置卷积-2025-07-07-10-28-53.png]]
```

```python
import torch

input = [3,4,6,5,7,
2,4,6,8,2,
1,6,7,8,4,
9,7,4,6,2,
3,7,5,4,7
]

input = torch.Tensor(input).view(1, 1, 5, 5)  # 将input这个列表转换成batch_size=1, in_channel=1, height=5, width=5的四维张量

conv_layer = torch.nn.Conv2d(in_channels=1, out_channels=1, kernel_size=3, padding=1, bias=False)

kernel = torch.Tensor([1,2,3,4,5,6,7,8,9]).view(1, 1, 3, 3)  # 将卷积核转换成out_channel=1, in_channel=1, height=3, width=3的四维张量
conv_layer.weight.data = kernel.data # 将卷积核的权重设置为自定义的卷积核

output = conv_layer(input)
print(output)  # (1, 1, 5, 5)
```


## 步长（stride）与特征图（feature map）的关系
假设输入的大小为 `5×5`，卷积核的大小为 `3×3`，设置步长为 `2`，则每一步的滑动窗口如下所示，最终得到的 feature map 的大小为 `2×2`。
![[Pasted image 20240730111724.png]]
所以步长与输出的关系（<font color="#d83931">无 padding</font>）如下：
```ad-thm
$$
 o =\left [\frac{n-f}{s}\right ] +1
$$
其中，输入大小是$n×n$，卷积核大小是$f×f$，步长是$s$，最后输出的尺寸是$o×o$
```

## 三种卷积模式

![[Pasted image 20240730145412.png]]
如上图所示，3 种模式的主要区别是从哪部分边缘开始滑动窗口进行卷积操作
### FULL 模式
第一个窗口只包含一个输入的元素，即从卷积核和输入刚开始相交开始做卷积，没有元素的部分做补 0 操作。如上图 FULL 所示，卷积核刚开始做卷积时只与输入的一个元素‘’3‘’相交。
输出 feature map 的大小计算公式如下：
```ad-thm
$$
o=\frac{n-f+2p}{s} +1
$$
其中$n$=输入大小，$0$=输出大小，$p$=padding，$f$=卷积核大小，$s$=步长
```
### Valid 模式
卷积核和输入完全相交后开始进行卷积操作，这种模式不需要补 0
输出 feature map 的大小计算公式如下：
```ad-thm
$$
o =\left [\frac{n-f}{s}\right ] +1
$$
其中，输入大小是$n×n$，卷积核大小是$f×f$，步长是$s$，最后输出的尺寸是$o×o$
```
### Same 模式
当卷积核的中心 c 和输入开始相交时才进行卷积，没有的元素进行补 0 操作
输出 feature map 的大小计算公式如下：
```ad-thm
$$
o=\frac{n-f+2p}{s} +1
$$
其中$n$=输入大小，$0$=输出大小，$p$=padding，$f$=卷积核大小，$s$=步长
```

# 转置卷积
^f20300
转置卷积（Transposed Convolution）的主要作用就是进行 [[上采样；下采样#^0adbc0|上采样]]，但需要注意如下：
- <font color="#d83931">转置卷积不是卷积的逆运算，即转置卷积不是使用输出矩阵和卷积核计算原始的输入矩阵，而是 计算得到原始大小的矩阵，但数值是不同的</font>
- <font color="#d83931">转置卷积也是卷积</font>
- 转置卷积通过卷积核建立了一对多的关系
## 转置卷积和卷积的关系
转置卷积不是卷积的逆运算，而是一种特殊的卷积
- 普通卷积相当于是用一个“小窗户”（卷积核）去看一个“大世界”（输入图），最终我们看得到的东西（输出图）也只有“小窗户”这么大
- 转置卷积相当于使用一个“大窗户”（卷积核）去看一个“小世界”，最终我们看见东西也不仅仅局限于这个“小世界”
就如下图所示，普通卷积是将大图变小图，转置卷积则是将小图变大图
![[tmp6067.png]]
## 转置卷积操作
通过转置卷积，将图片还原为原始尺寸一般都需要 padding 操作，如下图所示：
![[Pasted image 20240730171955.png]]
转置卷积的运算步骤可以归为以下几步：
1. 将输入特征图==元素==间填充 s-1 行 0 元素，以及填充 s-1 列 0 元素
2. 将输入特征图==四周==填充 k-p-1 行 0 元素，以及填充 k-p-1 列元素
3. 将卷积核进行上下，左右翻转（180°翻转）
4. 完成上面操作后进行正常的卷积
![[Pasted image 20240730172720.png]]
上图根据运算步骤：
1. 将输入特征图元素间填充 s-1（1-1=0） 行 0 元素，s-1（1-1=0） 列 0 元素
2. 将输入特征图四周填充 k-p-1（3-0-1=2）行 0 元素，k-p-1（3-0-1=2）列 0 元素
3. 将卷积核进行上下，左右翻转
4. 最后得到 4×4 的 feature map
### 转置卷积中不同 s 和 p 的情况
`````ad-flex
collapse: open
title: 不同的s和p导致不同的情况
color: 178,22,164
```ad-note
title: s=1, p=0, k=3
![[3d8813449101f99a106a74cee1754184.gif]]
```
```ad-note
title:s=2, p=0, k=3
![[3bb7fc0d90165becc7ed5033534fdac4.gif]]
```
```ad-note
title:s=1, p=1, k=3
![[b6ca1dfbf6eacd96531ac2fd26a5eec9.gif]]
```
`````
计算公式如下所示 ^5vt37l
```ad-thm
$$
H_{out}=(H_{in}-1)×s_{0}-2×p_{0}+k_{0}
$$
$$
W_{out}=(W_{in}-1)×s_{1}-2×p_{1}+k_{1}
$$
其中H代表高度，W代表宽度，s表示步长，p表示padding，k表示卷积核的尺寸，0表示高度方向，1表示宽度方向
```


测试以下[[卷积和转置卷积#^5vt37l]] ^1avrpu