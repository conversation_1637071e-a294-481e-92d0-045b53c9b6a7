/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var Od=Object.create;var yi=Object.defineProperty;var Rd=Object.getOwnPropertyDescriptor;var Nd=Object.getOwnPropertyNames;var Gd=Object.getPrototypeOf,Vd=Object.prototype.hasOwnProperty;var _d=(M,s)=>()=>(s||M((s={exports:{}}).exports,s),s.exports),Xd=(M,s)=>{for(var r in s)yi(M,r,{get:s[r],enumerable:!0})},ca=(M,s,r,n)=>{if(s&&typeof s=="object"||typeof s=="function")for(let a of Nd(s))!Vd.call(M,a)&&a!==r&&yi(M,a,{get:()=>s[a],enumerable:!(n=Rd(s,a))||n.enumerable});return M};var ha=(M,s,r)=>(r=M!=null?Od(Gd(M)):{},ca(s||!M||!M.__esModule?yi(r,"default",{value:M,enumerable:!0}):r,M)),Jd=M=>ca(yi({},"__esModule",{value:!0}),M);var cs=_d((os,ls)=>{(function(M,s){typeof os=="object"&&typeof ls!="undefined"?ls.exports=s():typeof define=="function"&&define.amd?define(s):(M=typeof globalThis!="undefined"?globalThis:M||self,M.html2canvas=s())})(os,function(){"use strict";var M=function(e,A){return M=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,t){i.__proto__=t}||function(i,t){for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(i[o]=t[o])},M(e,A)};function s(e,A){if(typeof A!="function"&&A!==null)throw new TypeError("Class extends value "+String(A)+" is not a constructor or null");M(e,A);function i(){this.constructor=e}e.prototype=A===null?Object.create(A):(i.prototype=A.prototype,new i)}var r=function(){return r=Object.assign||function(A){for(var i,t=1,o=arguments.length;t<o;t++){i=arguments[t];for(var c in i)Object.prototype.hasOwnProperty.call(i,c)&&(A[c]=i[c])}return A},r.apply(this,arguments)};function n(e,A,i,t){function o(c){return c instanceof i?c:new i(function(d){d(c)})}return new(i||(i=Promise))(function(c,d){function B(v){try{C(t.next(v))}catch(F){d(F)}}function u(v){try{C(t.throw(v))}catch(F){d(F)}}function C(v){v.done?c(v.value):o(v.value).then(B,u)}C((t=t.apply(e,A||[])).next())})}function a(e,A){var i={label:0,sent:function(){if(c[0]&1)throw c[1];return c[1]},trys:[],ops:[]},t,o,c,d;return d={next:B(0),throw:B(1),return:B(2)},typeof Symbol=="function"&&(d[Symbol.iterator]=function(){return this}),d;function B(C){return function(v){return u([C,v])}}function u(C){if(t)throw new TypeError("Generator is already executing.");for(;i;)try{if(t=1,o&&(c=C[0]&2?o.return:C[0]?o.throw||((c=o.return)&&c.call(o),0):o.next)&&!(c=c.call(o,C[1])).done)return c;switch(o=0,c&&(C=[C[0]&2,c.value]),C[0]){case 0:case 1:c=C;break;case 4:return i.label++,{value:C[1],done:!1};case 5:i.label++,o=C[1],C=[0];continue;case 7:C=i.ops.pop(),i.trys.pop();continue;default:if(c=i.trys,!(c=c.length>0&&c[c.length-1])&&(C[0]===6||C[0]===2)){i=0;continue}if(C[0]===3&&(!c||C[1]>c[0]&&C[1]<c[3])){i.label=C[1];break}if(C[0]===6&&i.label<c[1]){i.label=c[1],c=C;break}if(c&&i.label<c[2]){i.label=c[2],i.ops.push(C);break}c[2]&&i.ops.pop(),i.trys.pop();continue}C=A.call(e,i)}catch(v){C=[6,v],o=0}finally{t=c=0}if(C[0]&5)throw C[1];return{value:C[0]?C[1]:void 0,done:!0}}}function l(e,A,i){if(i||arguments.length===2)for(var t=0,o=A.length,c;t<o;t++)(c||!(t in A))&&(c||(c=Array.prototype.slice.call(A,0,t)),c[t]=A[t]);return e.concat(c||A)}for(var h=function(){function e(A,i,t,o){this.left=A,this.top=i,this.width=t,this.height=o}return e.prototype.add=function(A,i,t,o){return new e(this.left+A,this.top+i,this.width+t,this.height+o)},e.fromClientRect=function(A,i){return new e(i.left+A.windowBounds.left,i.top+A.windowBounds.top,i.width,i.height)},e.fromDOMRectList=function(A,i){var t=Array.from(i).find(function(o){return o.width!==0});return t?new e(t.left+A.windowBounds.left,t.top+A.windowBounds.top,t.width,t.height):e.EMPTY},e.EMPTY=new e(0,0,0,0),e}(),g=function(e,A){return h.fromClientRect(e,A.getBoundingClientRect())},m=function(e){var A=e.body,i=e.documentElement;if(!A||!i)throw new Error("Unable to get document size");var t=Math.max(Math.max(A.scrollWidth,i.scrollWidth),Math.max(A.offsetWidth,i.offsetWidth),Math.max(A.clientWidth,i.clientWidth)),o=Math.max(Math.max(A.scrollHeight,i.scrollHeight),Math.max(A.offsetHeight,i.offsetHeight),Math.max(A.clientHeight,i.clientHeight));return new h(0,0,t,o)},w=function(e){for(var A=[],i=0,t=e.length;i<t;){var o=e.charCodeAt(i++);if(o>=55296&&o<=56319&&i<t){var c=e.charCodeAt(i++);(c&64512)===56320?A.push(((o&1023)<<10)+(c&1023)+65536):(A.push(o),i--)}else A.push(o)}return A},f=function(){for(var e=[],A=0;A<arguments.length;A++)e[A]=arguments[A];if(String.fromCodePoint)return String.fromCodePoint.apply(String,e);var i=e.length;if(!i)return"";for(var t=[],o=-1,c="";++o<i;){var d=e[o];d<=65535?t.push(d):(d-=65536,t.push((d>>10)+55296,d%1024+56320)),(o+1===i||t.length>16384)&&(c+=String.fromCharCode.apply(String,t),t.length=0)}return c},U="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",H=typeof Uint8Array=="undefined"?[]:new Uint8Array(256),I=0;I<U.length;I++)H[U.charCodeAt(I)]=I;for(var Q="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",S=typeof Uint8Array=="undefined"?[]:new Uint8Array(256),b=0;b<Q.length;b++)S[Q.charCodeAt(b)]=b;for(var y=function(e){var A=e.length*.75,i=e.length,t,o=0,c,d,B,u;e[e.length-1]==="="&&(A--,e[e.length-2]==="="&&A--);var C=typeof ArrayBuffer!="undefined"&&typeof Uint8Array!="undefined"&&typeof Uint8Array.prototype.slice!="undefined"?new ArrayBuffer(A):new Array(A),v=Array.isArray(C)?C:new Uint8Array(C);for(t=0;t<i;t+=4)c=S[e.charCodeAt(t)],d=S[e.charCodeAt(t+1)],B=S[e.charCodeAt(t+2)],u=S[e.charCodeAt(t+3)],v[o++]=c<<2|d>>4,v[o++]=(d&15)<<4|B>>2,v[o++]=(B&3)<<6|u&63;return C},V=function(e){for(var A=e.length,i=[],t=0;t<A;t+=2)i.push(e[t+1]<<8|e[t]);return i},R=function(e){for(var A=e.length,i=[],t=0;t<A;t+=4)i.push(e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]);return i},N=5,_=11,Z=2,cA=_-N,mA=65536>>N,rA=1<<N,uA=rA-1,yA=1024>>N,_A=mA+yA,XA=_A,aA=32,oA=XA+aA,JA=65536>>_,kA=1<<cA,Ue=kA-1,nt=function(e,A,i){return e.slice?e.slice(A,i):new Uint16Array(Array.prototype.slice.call(e,A,i))},Pt=function(e,A,i){return e.slice?e.slice(A,i):new Uint32Array(Array.prototype.slice.call(e,A,i))},IA=function(e,A){var i=y(e),t=Array.isArray(i)?R(i):new Uint32Array(i),o=Array.isArray(i)?V(i):new Uint16Array(i),c=24,d=nt(o,c/2,t[4]/2),B=t[5]===2?nt(o,(c+t[4])/2):Pt(t,Math.ceil((c+t[4])/4));return new at(t[0],t[1],t[2],t[3],d,B)},at=function(){function e(A,i,t,o,c,d){this.initialValue=A,this.errorValue=i,this.highStart=t,this.highValueIndex=o,this.index=c,this.data=d}return e.prototype.get=function(A){var i;if(A>=0){if(A<55296||A>56319&&A<=65535)return i=this.index[A>>N],i=(i<<Z)+(A&uA),this.data[i];if(A<=65535)return i=this.index[mA+(A-55296>>N)],i=(i<<Z)+(A&uA),this.data[i];if(A<this.highStart)return i=oA-JA+(A>>_),i=this.index[i],i+=A>>N&Ue,i=this.index[i],i=(i<<Z)+(A&uA),this.data[i];if(A<=1114111)return this.data[this.highValueIndex]}return this.errorValue},e}(),Pe="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",or=typeof Uint8Array=="undefined"?[]:new Uint8Array(256),kt=0;kt<Pe.length;kt++)or[Pe.charCodeAt(kt)]=kt;var Ma="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",ds=50,La=1,gs=2,us=3,Ta=4,Da=5,Bs=7,ps=8,ms=9,oe=10,lr=11,fs=12,cr=13,Ka=14,ot=15,hr=16,Ot=17,lt=18,Pa=19,ws=20,dr=21,ct=22,gr=23,ke=24,LA=25,ht=26,dt=27,Oe=28,ka=29,Ee=30,Oa=31,Rt=32,Nt=33,ur=34,Br=35,pr=36,gt=37,mr=38,Gt=39,Vt=40,fr=41,Cs=42,Ra=43,Na=[9001,65288],vs="!",$="\xD7",_t="\xF7",wr=IA(Ma),Ae=[Ee,pr],Cr=[La,gs,us,Da],Qs=[oe,ps],Fs=[dt,ht],Ga=Cr.concat(Qs),Us=[mr,Gt,Vt,ur,Br],Va=[ot,cr],_a=function(e,A){A===void 0&&(A="strict");var i=[],t=[],o=[];return e.forEach(function(c,d){var B=wr.get(c);if(B>ds?(o.push(!0),B-=ds):o.push(!1),["normal","auto","loose"].indexOf(A)!==-1&&[8208,8211,12316,12448].indexOf(c)!==-1)return t.push(d),i.push(hr);if(B===Ta||B===lr){if(d===0)return t.push(d),i.push(Ee);var u=i[d-1];return Ga.indexOf(u)===-1?(t.push(t[d-1]),i.push(u)):(t.push(d),i.push(Ee))}if(t.push(d),B===Oa)return i.push(A==="strict"?dr:gt);if(B===Cs||B===ka)return i.push(Ee);if(B===Ra)return c>=131072&&c<=196605||c>=196608&&c<=262141?i.push(gt):i.push(Ee);i.push(B)}),[t,i,o]},vr=function(e,A,i,t){var o=t[i];if(Array.isArray(e)?e.indexOf(o)!==-1:e===o)for(var c=i;c<=t.length;){c++;var d=t[c];if(d===A)return!0;if(d!==oe)break}if(o===oe)for(var c=i;c>0;){c--;var B=t[c];if(Array.isArray(e)?e.indexOf(B)!==-1:e===B)for(var u=i;u<=t.length;){u++;var d=t[u];if(d===A)return!0;if(d!==oe)break}if(B!==oe)break}return!1},Es=function(e,A){for(var i=e;i>=0;){var t=A[i];if(t===oe)i--;else return t}return 0},Xa=function(e,A,i,t,o){if(i[t]===0)return $;var c=t-1;if(Array.isArray(o)&&o[c]===!0)return $;var d=c-1,B=c+1,u=A[c],C=d>=0?A[d]:0,v=A[B];if(u===gs&&v===us)return $;if(Cr.indexOf(u)!==-1)return vs;if(Cr.indexOf(v)!==-1||Qs.indexOf(v)!==-1)return $;if(Es(c,A)===ps)return _t;if(wr.get(e[c])===lr||(u===Rt||u===Nt)&&wr.get(e[B])===lr||u===Bs||v===Bs||u===ms||[oe,cr,ot].indexOf(u)===-1&&v===ms||[Ot,lt,Pa,ke,Oe].indexOf(v)!==-1||Es(c,A)===ct||vr(gr,ct,c,A)||vr([Ot,lt],dr,c,A)||vr(fs,fs,c,A))return $;if(u===oe)return _t;if(u===gr||v===gr)return $;if(v===hr||u===hr)return _t;if([cr,ot,dr].indexOf(v)!==-1||u===Ka||C===pr&&Va.indexOf(u)!==-1||u===Oe&&v===pr||v===ws||Ae.indexOf(v)!==-1&&u===LA||Ae.indexOf(u)!==-1&&v===LA||u===dt&&[gt,Rt,Nt].indexOf(v)!==-1||[gt,Rt,Nt].indexOf(u)!==-1&&v===ht||Ae.indexOf(u)!==-1&&Fs.indexOf(v)!==-1||Fs.indexOf(u)!==-1&&Ae.indexOf(v)!==-1||[dt,ht].indexOf(u)!==-1&&(v===LA||[ct,ot].indexOf(v)!==-1&&A[B+1]===LA)||[ct,ot].indexOf(u)!==-1&&v===LA||u===LA&&[LA,Oe,ke].indexOf(v)!==-1)return $;if([LA,Oe,ke,Ot,lt].indexOf(v)!==-1)for(var F=c;F>=0;){var E=A[F];if(E===LA)return $;if([Oe,ke].indexOf(E)!==-1)F--;else break}if([dt,ht].indexOf(v)!==-1)for(var F=[Ot,lt].indexOf(u)!==-1?d:c;F>=0;){var E=A[F];if(E===LA)return $;if([Oe,ke].indexOf(E)!==-1)F--;else break}if(mr===u&&[mr,Gt,ur,Br].indexOf(v)!==-1||[Gt,ur].indexOf(u)!==-1&&[Gt,Vt].indexOf(v)!==-1||[Vt,Br].indexOf(u)!==-1&&v===Vt||Us.indexOf(u)!==-1&&[ws,ht].indexOf(v)!==-1||Us.indexOf(v)!==-1&&u===dt||Ae.indexOf(u)!==-1&&Ae.indexOf(v)!==-1||u===ke&&Ae.indexOf(v)!==-1||Ae.concat(LA).indexOf(u)!==-1&&v===ct&&Na.indexOf(e[B])===-1||Ae.concat(LA).indexOf(v)!==-1&&u===lt)return $;if(u===fr&&v===fr){for(var K=i[c],x=1;K>0&&(K--,A[K]===fr);)x++;if(x%2!==0)return $}return u===Rt&&v===Nt?$:_t},Ja=function(e,A){A||(A={lineBreak:"normal",wordBreak:"normal"});var i=_a(e,A.lineBreak),t=i[0],o=i[1],c=i[2];(A.wordBreak==="break-all"||A.wordBreak==="break-word")&&(o=o.map(function(B){return[LA,Ee,Cs].indexOf(B)!==-1?gt:B}));var d=A.wordBreak==="keep-all"?c.map(function(B,u){return B&&e[u]>=19968&&e[u]<=40959}):void 0;return[t,o,d]},Wa=function(){function e(A,i,t,o){this.codePoints=A,this.required=i===vs,this.start=t,this.end=o}return e.prototype.slice=function(){return f.apply(void 0,this.codePoints.slice(this.start,this.end))},e}(),Ya=function(e,A){var i=w(e),t=Ja(i,A),o=t[0],c=t[1],d=t[2],B=i.length,u=0,C=0;return{next:function(){if(C>=B)return{done:!0,value:null};for(var v=$;C<B&&(v=Xa(i,c,o,++C,d))===$;);if(v!==$||C===B){var F=new Wa(i,v,u,C);return u=C,{value:F,done:!1}}return{done:!0,value:null}}}},Za=1,$a=2,ut=4,ys=8,Xt=10,Is=47,Bt=92,qa=9,ja=32,Jt=34,pt=61,za=35,Ao=36,eo=37,Wt=39,Yt=40,mt=41,to=95,HA=45,io=33,ro=60,so=62,no=64,ao=91,oo=93,lo=61,co=123,Zt=63,ho=125,Hs=124,go=126,uo=128,Ss=65533,Qr=42,ye=43,Bo=44,po=58,mo=59,ft=46,fo=0,wo=8,Co=11,vo=14,Qo=31,Fo=127,YA=-1,bs=48,xs=97,Ms=101,Uo=102,Eo=117,yo=122,Ls=65,Ts=69,Ds=70,Io=85,Ho=90,QA=function(e){return e>=bs&&e<=57},So=function(e){return e>=55296&&e<=57343},Re=function(e){return QA(e)||e>=Ls&&e<=Ds||e>=xs&&e<=Uo},bo=function(e){return e>=xs&&e<=yo},xo=function(e){return e>=Ls&&e<=Ho},Mo=function(e){return bo(e)||xo(e)},Lo=function(e){return e>=uo},$t=function(e){return e===Xt||e===qa||e===ja},qt=function(e){return Mo(e)||Lo(e)||e===to},Ks=function(e){return qt(e)||QA(e)||e===HA},To=function(e){return e>=fo&&e<=wo||e===Co||e>=vo&&e<=Qo||e===Fo},le=function(e,A){return e!==Bt?!1:A!==Xt},jt=function(e,A,i){return e===HA?qt(A)||le(A,i):qt(e)?!0:!!(e===Bt&&le(e,A))},Fr=function(e,A,i){return e===ye||e===HA?QA(A)?!0:A===ft&&QA(i):QA(e===ft?A:e)},Do=function(e){var A=0,i=1;(e[A]===ye||e[A]===HA)&&(e[A]===HA&&(i=-1),A++);for(var t=[];QA(e[A]);)t.push(e[A++]);var o=t.length?parseInt(f.apply(void 0,t),10):0;e[A]===ft&&A++;for(var c=[];QA(e[A]);)c.push(e[A++]);var d=c.length,B=d?parseInt(f.apply(void 0,c),10):0;(e[A]===Ts||e[A]===Ms)&&A++;var u=1;(e[A]===ye||e[A]===HA)&&(e[A]===HA&&(u=-1),A++);for(var C=[];QA(e[A]);)C.push(e[A++]);var v=C.length?parseInt(f.apply(void 0,C),10):0;return i*(o+B*Math.pow(10,-d))*Math.pow(10,u*v)},Ko={type:2},Po={type:3},ko={type:4},Oo={type:13},Ro={type:8},No={type:21},Go={type:9},Vo={type:10},_o={type:11},Xo={type:12},Jo={type:14},zt={type:23},Wo={type:1},Yo={type:25},Zo={type:24},$o={type:26},qo={type:27},jo={type:28},zo={type:29},Al={type:31},Ur={type:32},Ps=function(){function e(){this._value=[]}return e.prototype.write=function(A){this._value=this._value.concat(w(A))},e.prototype.read=function(){for(var A=[],i=this.consumeToken();i!==Ur;)A.push(i),i=this.consumeToken();return A},e.prototype.consumeToken=function(){var A=this.consumeCodePoint();switch(A){case Jt:return this.consumeStringToken(Jt);case za:var i=this.peekCodePoint(0),t=this.peekCodePoint(1),o=this.peekCodePoint(2);if(Ks(i)||le(t,o)){var c=jt(i,t,o)?$a:Za,d=this.consumeName();return{type:5,value:d,flags:c}}break;case Ao:if(this.peekCodePoint(0)===pt)return this.consumeCodePoint(),Oo;break;case Wt:return this.consumeStringToken(Wt);case Yt:return Ko;case mt:return Po;case Qr:if(this.peekCodePoint(0)===pt)return this.consumeCodePoint(),Jo;break;case ye:if(Fr(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case Bo:return ko;case HA:var B=A,u=this.peekCodePoint(0),C=this.peekCodePoint(1);if(Fr(B,u,C))return this.reconsumeCodePoint(A),this.consumeNumericToken();if(jt(B,u,C))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();if(u===HA&&C===so)return this.consumeCodePoint(),this.consumeCodePoint(),Zo;break;case ft:if(Fr(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case Is:if(this.peekCodePoint(0)===Qr)for(this.consumeCodePoint();;){var v=this.consumeCodePoint();if(v===Qr&&(v=this.consumeCodePoint(),v===Is))return this.consumeToken();if(v===YA)return this.consumeToken()}break;case po:return $o;case mo:return qo;case ro:if(this.peekCodePoint(0)===io&&this.peekCodePoint(1)===HA&&this.peekCodePoint(2)===HA)return this.consumeCodePoint(),this.consumeCodePoint(),Yo;break;case no:var F=this.peekCodePoint(0),E=this.peekCodePoint(1),K=this.peekCodePoint(2);if(jt(F,E,K)){var d=this.consumeName();return{type:7,value:d}}break;case ao:return jo;case Bt:if(le(A,this.peekCodePoint(0)))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();break;case oo:return zo;case lo:if(this.peekCodePoint(0)===pt)return this.consumeCodePoint(),Ro;break;case co:return _o;case ho:return Xo;case Eo:case Io:var x=this.peekCodePoint(0),L=this.peekCodePoint(1);return x===ye&&(Re(L)||L===Zt)&&(this.consumeCodePoint(),this.consumeUnicodeRangeToken()),this.reconsumeCodePoint(A),this.consumeIdentLikeToken();case Hs:if(this.peekCodePoint(0)===pt)return this.consumeCodePoint(),Go;if(this.peekCodePoint(0)===Hs)return this.consumeCodePoint(),No;break;case go:if(this.peekCodePoint(0)===pt)return this.consumeCodePoint(),Vo;break;case YA:return Ur}return $t(A)?(this.consumeWhiteSpace(),Al):QA(A)?(this.reconsumeCodePoint(A),this.consumeNumericToken()):qt(A)?(this.reconsumeCodePoint(A),this.consumeIdentLikeToken()):{type:6,value:f(A)}},e.prototype.consumeCodePoint=function(){var A=this._value.shift();return typeof A=="undefined"?-1:A},e.prototype.reconsumeCodePoint=function(A){this._value.unshift(A)},e.prototype.peekCodePoint=function(A){return A>=this._value.length?-1:this._value[A]},e.prototype.consumeUnicodeRangeToken=function(){for(var A=[],i=this.consumeCodePoint();Re(i)&&A.length<6;)A.push(i),i=this.consumeCodePoint();for(var t=!1;i===Zt&&A.length<6;)A.push(i),i=this.consumeCodePoint(),t=!0;if(t){var o=parseInt(f.apply(void 0,A.map(function(u){return u===Zt?bs:u})),16),c=parseInt(f.apply(void 0,A.map(function(u){return u===Zt?Ds:u})),16);return{type:30,start:o,end:c}}var d=parseInt(f.apply(void 0,A),16);if(this.peekCodePoint(0)===HA&&Re(this.peekCodePoint(1))){this.consumeCodePoint(),i=this.consumeCodePoint();for(var B=[];Re(i)&&B.length<6;)B.push(i),i=this.consumeCodePoint();var c=parseInt(f.apply(void 0,B),16);return{type:30,start:d,end:c}}else return{type:30,start:d,end:d}},e.prototype.consumeIdentLikeToken=function(){var A=this.consumeName();return A.toLowerCase()==="url"&&this.peekCodePoint(0)===Yt?(this.consumeCodePoint(),this.consumeUrlToken()):this.peekCodePoint(0)===Yt?(this.consumeCodePoint(),{type:19,value:A}):{type:20,value:A}},e.prototype.consumeUrlToken=function(){var A=[];if(this.consumeWhiteSpace(),this.peekCodePoint(0)===YA)return{type:22,value:""};var i=this.peekCodePoint(0);if(i===Wt||i===Jt){var t=this.consumeStringToken(this.consumeCodePoint());return t.type===0&&(this.consumeWhiteSpace(),this.peekCodePoint(0)===YA||this.peekCodePoint(0)===mt)?(this.consumeCodePoint(),{type:22,value:t.value}):(this.consumeBadUrlRemnants(),zt)}for(;;){var o=this.consumeCodePoint();if(o===YA||o===mt)return{type:22,value:f.apply(void 0,A)};if($t(o))return this.consumeWhiteSpace(),this.peekCodePoint(0)===YA||this.peekCodePoint(0)===mt?(this.consumeCodePoint(),{type:22,value:f.apply(void 0,A)}):(this.consumeBadUrlRemnants(),zt);if(o===Jt||o===Wt||o===Yt||To(o))return this.consumeBadUrlRemnants(),zt;if(o===Bt)if(le(o,this.peekCodePoint(0)))A.push(this.consumeEscapedCodePoint());else return this.consumeBadUrlRemnants(),zt;else A.push(o)}},e.prototype.consumeWhiteSpace=function(){for(;$t(this.peekCodePoint(0));)this.consumeCodePoint()},e.prototype.consumeBadUrlRemnants=function(){for(;;){var A=this.consumeCodePoint();if(A===mt||A===YA)return;le(A,this.peekCodePoint(0))&&this.consumeEscapedCodePoint()}},e.prototype.consumeStringSlice=function(A){for(var i=5e4,t="";A>0;){var o=Math.min(i,A);t+=f.apply(void 0,this._value.splice(0,o)),A-=o}return this._value.shift(),t},e.prototype.consumeStringToken=function(A){var i="",t=0;do{var o=this._value[t];if(o===YA||o===void 0||o===A)return i+=this.consumeStringSlice(t),{type:0,value:i};if(o===Xt)return this._value.splice(0,t),Wo;if(o===Bt){var c=this._value[t+1];c!==YA&&c!==void 0&&(c===Xt?(i+=this.consumeStringSlice(t),t=-1,this._value.shift()):le(o,c)&&(i+=this.consumeStringSlice(t),i+=f(this.consumeEscapedCodePoint()),t=-1))}t++}while(!0)},e.prototype.consumeNumber=function(){var A=[],i=ut,t=this.peekCodePoint(0);for((t===ye||t===HA)&&A.push(this.consumeCodePoint());QA(this.peekCodePoint(0));)A.push(this.consumeCodePoint());t=this.peekCodePoint(0);var o=this.peekCodePoint(1);if(t===ft&&QA(o))for(A.push(this.consumeCodePoint(),this.consumeCodePoint()),i=ys;QA(this.peekCodePoint(0));)A.push(this.consumeCodePoint());t=this.peekCodePoint(0),o=this.peekCodePoint(1);var c=this.peekCodePoint(2);if((t===Ts||t===Ms)&&((o===ye||o===HA)&&QA(c)||QA(o)))for(A.push(this.consumeCodePoint(),this.consumeCodePoint()),i=ys;QA(this.peekCodePoint(0));)A.push(this.consumeCodePoint());return[Do(A),i]},e.prototype.consumeNumericToken=function(){var A=this.consumeNumber(),i=A[0],t=A[1],o=this.peekCodePoint(0),c=this.peekCodePoint(1),d=this.peekCodePoint(2);if(jt(o,c,d)){var B=this.consumeName();return{type:15,number:i,flags:t,unit:B}}return o===eo?(this.consumeCodePoint(),{type:16,number:i,flags:t}):{type:17,number:i,flags:t}},e.prototype.consumeEscapedCodePoint=function(){var A=this.consumeCodePoint();if(Re(A)){for(var i=f(A);Re(this.peekCodePoint(0))&&i.length<6;)i+=f(this.consumeCodePoint());$t(this.peekCodePoint(0))&&this.consumeCodePoint();var t=parseInt(i,16);return t===0||So(t)||t>1114111?Ss:t}return A===YA?Ss:A},e.prototype.consumeName=function(){for(var A="";;){var i=this.consumeCodePoint();if(Ks(i))A+=f(i);else if(le(i,this.peekCodePoint(0)))A+=f(this.consumeEscapedCodePoint());else return this.reconsumeCodePoint(i),A}},e}(),ks=function(){function e(A){this._tokens=A}return e.create=function(A){var i=new Ps;return i.write(A),new e(i.read())},e.parseValue=function(A){return e.create(A).parseComponentValue()},e.parseValues=function(A){return e.create(A).parseComponentValues()},e.prototype.parseComponentValue=function(){for(var A=this.consumeToken();A.type===31;)A=this.consumeToken();if(A.type===32)throw new SyntaxError("Error parsing CSS component value, unexpected EOF");this.reconsumeToken(A);var i=this.consumeComponentValue();do A=this.consumeToken();while(A.type===31);if(A.type===32)return i;throw new SyntaxError("Error parsing CSS component value, multiple values found when expecting only one")},e.prototype.parseComponentValues=function(){for(var A=[];;){var i=this.consumeComponentValue();if(i.type===32)return A;A.push(i),A.push()}},e.prototype.consumeComponentValue=function(){var A=this.consumeToken();switch(A.type){case 11:case 28:case 2:return this.consumeSimpleBlock(A.type);case 19:return this.consumeFunction(A)}return A},e.prototype.consumeSimpleBlock=function(A){for(var i={type:A,values:[]},t=this.consumeToken();;){if(t.type===32||tl(t,A))return i;this.reconsumeToken(t),i.values.push(this.consumeComponentValue()),t=this.consumeToken()}},e.prototype.consumeFunction=function(A){for(var i={name:A.value,values:[],type:18};;){var t=this.consumeToken();if(t.type===32||t.type===3)return i;this.reconsumeToken(t),i.values.push(this.consumeComponentValue())}},e.prototype.consumeToken=function(){var A=this._tokens.shift();return typeof A=="undefined"?Ur:A},e.prototype.reconsumeToken=function(A){this._tokens.unshift(A)},e}(),wt=function(e){return e.type===15},Ne=function(e){return e.type===17},eA=function(e){return e.type===20},el=function(e){return e.type===0},Er=function(e,A){return eA(e)&&e.value===A},Os=function(e){return e.type!==31},Ge=function(e){return e.type!==31&&e.type!==4},ZA=function(e){var A=[],i=[];return e.forEach(function(t){if(t.type===4){if(i.length===0)throw new Error("Error parsing function args, zero tokens for arg");A.push(i),i=[];return}t.type!==31&&i.push(t)}),i.length&&A.push(i),A},tl=function(e,A){return A===11&&e.type===12||A===28&&e.type===29?!0:A===2&&e.type===3},ce=function(e){return e.type===17||e.type===15},hA=function(e){return e.type===16||ce(e)},Rs=function(e){return e.length>1?[e[0],e[1]]:[e[0]]},fA={type:17,number:0,flags:ut},yr={type:16,number:50,flags:ut},he={type:16,number:100,flags:ut},Ct=function(e,A,i){var t=e[0],o=e[1];return[iA(t,A),iA(typeof o!="undefined"?o:t,i)]},iA=function(e,A){if(e.type===16)return e.number/100*A;if(wt(e))switch(e.unit){case"rem":case"em":return 16*e.number;case"px":default:return e.number}return e.number},Ns="deg",Gs="grad",Vs="rad",_s="turn",Ai={name:"angle",parse:function(e,A){if(A.type===15)switch(A.unit){case Ns:return Math.PI*A.number/180;case Gs:return Math.PI/200*A.number;case Vs:return A.number;case _s:return Math.PI*2*A.number}throw new Error("Unsupported angle type")}},Xs=function(e){return e.type===15&&(e.unit===Ns||e.unit===Gs||e.unit===Vs||e.unit===_s)},Js=function(e){var A=e.filter(eA).map(function(i){return i.value}).join(" ");switch(A){case"to bottom right":case"to right bottom":case"left top":case"top left":return[fA,fA];case"to top":case"bottom":return OA(0);case"to bottom left":case"to left bottom":case"right top":case"top right":return[fA,he];case"to right":case"left":return OA(90);case"to top left":case"to left top":case"right bottom":case"bottom right":return[he,he];case"to bottom":case"top":return OA(180);case"to top right":case"to right top":case"left bottom":case"bottom left":return[he,fA];case"to left":case"right":return OA(270)}return 0},OA=function(e){return Math.PI*e/180},de={name:"color",parse:function(e,A){if(A.type===18){var i=il[A.name];if(typeof i=="undefined")throw new Error('Attempting to parse an unsupported color function "'+A.name+'"');return i(e,A.values)}if(A.type===5){if(A.value.length===3){var t=A.value.substring(0,1),o=A.value.substring(1,2),c=A.value.substring(2,3);return ue(parseInt(t+t,16),parseInt(o+o,16),parseInt(c+c,16),1)}if(A.value.length===4){var t=A.value.substring(0,1),o=A.value.substring(1,2),c=A.value.substring(2,3),d=A.value.substring(3,4);return ue(parseInt(t+t,16),parseInt(o+o,16),parseInt(c+c,16),parseInt(d+d,16)/255)}if(A.value.length===6){var t=A.value.substring(0,2),o=A.value.substring(2,4),c=A.value.substring(4,6);return ue(parseInt(t,16),parseInt(o,16),parseInt(c,16),1)}if(A.value.length===8){var t=A.value.substring(0,2),o=A.value.substring(2,4),c=A.value.substring(4,6),d=A.value.substring(6,8);return ue(parseInt(t,16),parseInt(o,16),parseInt(c,16),parseInt(d,16)/255)}}if(A.type===20){var B=ee[A.value.toUpperCase()];if(typeof B!="undefined")return B}return ee.TRANSPARENT}},ge=function(e){return(255&e)===0},pA=function(e){var A=255&e,i=255&e>>8,t=255&e>>16,o=255&e>>24;return A<255?"rgba("+o+","+t+","+i+","+A/255+")":"rgb("+o+","+t+","+i+")"},ue=function(e,A,i,t){return(e<<24|A<<16|i<<8|Math.round(t*255)<<0)>>>0},Ws=function(e,A){if(e.type===17)return e.number;if(e.type===16){var i=A===3?1:255;return A===3?e.number/100*i:Math.round(e.number/100*i)}return 0},Ys=function(e,A){var i=A.filter(Ge);if(i.length===3){var t=i.map(Ws),o=t[0],c=t[1],d=t[2];return ue(o,c,d,1)}if(i.length===4){var B=i.map(Ws),o=B[0],c=B[1],d=B[2],u=B[3];return ue(o,c,d,u)}return 0};function Ir(e,A,i){return i<0&&(i+=1),i>=1&&(i-=1),i<1/6?(A-e)*i*6+e:i<1/2?A:i<2/3?(A-e)*6*(2/3-i)+e:e}var Zs=function(e,A){var i=A.filter(Ge),t=i[0],o=i[1],c=i[2],d=i[3],B=(t.type===17?OA(t.number):Ai.parse(e,t))/(Math.PI*2),u=hA(o)?o.number/100:0,C=hA(c)?c.number/100:0,v=typeof d!="undefined"&&hA(d)?iA(d,1):1;if(u===0)return ue(C*255,C*255,C*255,1);var F=C<=.5?C*(u+1):C+u-C*u,E=C*2-F,K=Ir(E,F,B+1/3),x=Ir(E,F,B),L=Ir(E,F,B-1/3);return ue(K*255,x*255,L*255,v)},il={hsl:Zs,hsla:Zs,rgb:Ys,rgba:Ys},vt=function(e,A){return de.parse(e,ks.create(A).parseComponentValue())},ee={ALICEBLUE:4042850303,ANTIQUEWHITE:4209760255,AQUA:16777215,AQUAMARINE:2147472639,AZURE:4043309055,BEIGE:4126530815,BISQUE:4293182719,BLACK:255,BLANCHEDALMOND:4293643775,BLUE:65535,BLUEVIOLET:2318131967,BROWN:2771004159,BURLYWOOD:3736635391,CADETBLUE:1604231423,CHARTREUSE:2147418367,CHOCOLATE:3530104575,CORAL:4286533887,CORNFLOWERBLUE:1687547391,CORNSILK:4294499583,CRIMSON:3692313855,CYAN:16777215,DARKBLUE:35839,DARKCYAN:9145343,DARKGOLDENROD:3095837695,DARKGRAY:2846468607,DARKGREEN:6553855,DARKGREY:2846468607,DARKKHAKI:3182914559,DARKMAGENTA:2332068863,DARKOLIVEGREEN:1433087999,DARKORANGE:4287365375,DARKORCHID:2570243327,DARKRED:2332033279,DARKSALMON:3918953215,DARKSEAGREEN:2411499519,DARKSLATEBLUE:1211993087,DARKSLATEGRAY:793726975,DARKSLATEGREY:793726975,DARKTURQUOISE:13554175,DARKVIOLET:2483082239,DEEPPINK:4279538687,DEEPSKYBLUE:12582911,DIMGRAY:1768516095,DIMGREY:1768516095,DODGERBLUE:512819199,FIREBRICK:2988581631,FLORALWHITE:4294635775,FORESTGREEN:579543807,FUCHSIA:4278255615,GAINSBORO:3705462015,GHOSTWHITE:4177068031,GOLD:4292280575,GOLDENROD:3668254975,GRAY:2155905279,GREEN:8388863,GREENYELLOW:2919182335,GREY:2155905279,HONEYDEW:4043305215,HOTPINK:4285117695,INDIANRED:3445382399,INDIGO:1258324735,IVORY:4294963455,KHAKI:4041641215,LAVENDER:3873897215,LAVENDERBLUSH:4293981695,LAWNGREEN:2096890111,LEMONCHIFFON:4294626815,LIGHTBLUE:2916673279,LIGHTCORAL:4034953471,LIGHTCYAN:3774873599,LIGHTGOLDENRODYELLOW:4210742015,LIGHTGRAY:3553874943,LIGHTGREEN:2431553791,LIGHTGREY:3553874943,LIGHTPINK:4290167295,LIGHTSALMON:4288707327,LIGHTSEAGREEN:548580095,LIGHTSKYBLUE:2278488831,LIGHTSLATEGRAY:2005441023,LIGHTSLATEGREY:2005441023,LIGHTSTEELBLUE:2965692159,LIGHTYELLOW:4294959359,LIME:16711935,LIMEGREEN:852308735,LINEN:4210091775,MAGENTA:4278255615,MAROON:2147483903,MEDIUMAQUAMARINE:1724754687,MEDIUMBLUE:52735,MEDIUMORCHID:3126187007,MEDIUMPURPLE:2473647103,MEDIUMSEAGREEN:1018393087,MEDIUMSLATEBLUE:2070474495,MEDIUMSPRINGGREEN:16423679,MEDIUMTURQUOISE:1221709055,MEDIUMVIOLETRED:3340076543,MIDNIGHTBLUE:421097727,MINTCREAM:4127193855,MISTYROSE:4293190143,MOCCASIN:4293178879,NAVAJOWHITE:4292783615,NAVY:33023,OLDLACE:4260751103,OLIVE:2155872511,OLIVEDRAB:1804477439,ORANGE:4289003775,ORANGERED:4282712319,ORCHID:3664828159,PALEGOLDENROD:4008225535,PALEGREEN:2566625535,PALETURQUOISE:2951671551,PALEVIOLETRED:3681588223,PAPAYAWHIP:4293907967,PEACHPUFF:4292524543,PERU:3448061951,PINK:4290825215,PLUM:3718307327,POWDERBLUE:2967529215,PURPLE:2147516671,REBECCAPURPLE:1714657791,RED:4278190335,ROSYBROWN:3163525119,ROYALBLUE:1097458175,SADDLEBROWN:2336560127,SALMON:4202722047,SANDYBROWN:4104413439,SEAGREEN:780883967,SEASHELL:4294307583,SIENNA:2689740287,SILVER:3233857791,SKYBLUE:2278484991,SLATEBLUE:1784335871,SLATEGRAY:1887473919,SLATEGREY:1887473919,SNOW:4294638335,SPRINGGREEN:16744447,STEELBLUE:1182971135,TAN:3535047935,TEAL:8421631,THISTLE:3636451583,TOMATO:4284696575,TRANSPARENT:0,TURQUOISE:1088475391,VIOLET:4001558271,WHEAT:4125012991,WHITE:**********,WHITESMOKE:4126537215,YELLOW:4294902015,YELLOWGREEN:2597139199},rl={name:"background-clip",initialValue:"border-box",prefix:!1,type:1,parse:function(e,A){return A.map(function(i){if(eA(i))switch(i.value){case"padding-box":return 1;case"content-box":return 2}return 0})}},sl={name:"background-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},ei=function(e,A){var i=de.parse(e,A[0]),t=A[1];return t&&hA(t)?{color:i,stop:t}:{color:i,stop:null}},$s=function(e,A){var i=e[0],t=e[e.length-1];i.stop===null&&(i.stop=fA),t.stop===null&&(t.stop=he);for(var o=[],c=0,d=0;d<e.length;d++){var B=e[d].stop;if(B!==null){var u=iA(B,A);u>c?o.push(u):o.push(c),c=u}else o.push(null)}for(var C=null,d=0;d<o.length;d++){var v=o[d];if(v===null)C===null&&(C=d);else if(C!==null){for(var F=d-C,E=o[C-1],K=(v-E)/(F+1),x=1;x<=F;x++)o[C+x-1]=K*x;C=null}}return e.map(function(L,J){var O=L.color;return{color:O,stop:Math.max(Math.min(1,o[J]/A),0)}})},nl=function(e,A,i){var t=A/2,o=i/2,c=iA(e[0],A)-t,d=o-iA(e[1],i);return(Math.atan2(d,c)+Math.PI*2)%(Math.PI*2)},al=function(e,A,i){var t=typeof e=="number"?e:nl(e,A,i),o=Math.abs(A*Math.sin(t))+Math.abs(i*Math.cos(t)),c=A/2,d=i/2,B=o/2,u=Math.sin(t-Math.PI/2)*B,C=Math.cos(t-Math.PI/2)*B;return[o,c-C,c+C,d-u,d+u]},WA=function(e,A){return Math.sqrt(e*e+A*A)},qs=function(e,A,i,t,o){var c=[[0,0],[0,A],[e,0],[e,A]];return c.reduce(function(d,B){var u=B[0],C=B[1],v=WA(i-u,t-C);return(o?v<d.optimumDistance:v>d.optimumDistance)?{optimumCorner:B,optimumDistance:v}:d},{optimumDistance:o?1/0:-1/0,optimumCorner:null}).optimumCorner},ol=function(e,A,i,t,o){var c=0,d=0;switch(e.size){case 0:e.shape===0?c=d=Math.min(Math.abs(A),Math.abs(A-t),Math.abs(i),Math.abs(i-o)):e.shape===1&&(c=Math.min(Math.abs(A),Math.abs(A-t)),d=Math.min(Math.abs(i),Math.abs(i-o)));break;case 2:if(e.shape===0)c=d=Math.min(WA(A,i),WA(A,i-o),WA(A-t,i),WA(A-t,i-o));else if(e.shape===1){var B=Math.min(Math.abs(i),Math.abs(i-o))/Math.min(Math.abs(A),Math.abs(A-t)),u=qs(t,o,A,i,!0),C=u[0],v=u[1];c=WA(C-A,(v-i)/B),d=B*c}break;case 1:e.shape===0?c=d=Math.max(Math.abs(A),Math.abs(A-t),Math.abs(i),Math.abs(i-o)):e.shape===1&&(c=Math.max(Math.abs(A),Math.abs(A-t)),d=Math.max(Math.abs(i),Math.abs(i-o)));break;case 3:if(e.shape===0)c=d=Math.max(WA(A,i),WA(A,i-o),WA(A-t,i),WA(A-t,i-o));else if(e.shape===1){var B=Math.max(Math.abs(i),Math.abs(i-o))/Math.max(Math.abs(A),Math.abs(A-t)),F=qs(t,o,A,i,!1),C=F[0],v=F[1];c=WA(C-A,(v-i)/B),d=B*c}break}return Array.isArray(e.size)&&(c=iA(e.size[0],t),d=e.size.length===2?iA(e.size[1],o):c),[c,d]},ll=function(e,A){var i=OA(180),t=[];return ZA(A).forEach(function(o,c){if(c===0){var d=o[0];if(d.type===20&&d.value==="to"){i=Js(o);return}else if(Xs(d)){i=Ai.parse(e,d);return}}var B=ei(e,o);t.push(B)}),{angle:i,stops:t,type:1}},ti=function(e,A){var i=OA(180),t=[];return ZA(A).forEach(function(o,c){if(c===0){var d=o[0];if(d.type===20&&["top","left","right","bottom"].indexOf(d.value)!==-1){i=Js(o);return}else if(Xs(d)){i=(Ai.parse(e,d)+OA(270))%OA(360);return}}var B=ei(e,o);t.push(B)}),{angle:i,stops:t,type:1}},cl=function(e,A){var i=OA(180),t=[],o=1,c=0,d=3,B=[];return ZA(A).forEach(function(u,C){var v=u[0];if(C===0){if(eA(v)&&v.value==="linear"){o=1;return}else if(eA(v)&&v.value==="radial"){o=2;return}}if(v.type===18){if(v.name==="from"){var F=de.parse(e,v.values[0]);t.push({stop:fA,color:F})}else if(v.name==="to"){var F=de.parse(e,v.values[0]);t.push({stop:he,color:F})}else if(v.name==="color-stop"){var E=v.values.filter(Ge);if(E.length===2){var F=de.parse(e,E[1]),K=E[0];Ne(K)&&t.push({stop:{type:16,number:K.number*100,flags:K.flags},color:F})}}}}),o===1?{angle:(i+OA(180))%OA(360),stops:t,type:o}:{size:d,shape:c,stops:t,position:B,type:o}},js="closest-side",zs="farthest-side",An="closest-corner",en="farthest-corner",tn="circle",rn="ellipse",sn="cover",nn="contain",hl=function(e,A){var i=0,t=3,o=[],c=[];return ZA(A).forEach(function(d,B){var u=!0;if(B===0){var C=!1;u=d.reduce(function(F,E){if(C)if(eA(E))switch(E.value){case"center":return c.push(yr),F;case"top":case"left":return c.push(fA),F;case"right":case"bottom":return c.push(he),F}else(hA(E)||ce(E))&&c.push(E);else if(eA(E))switch(E.value){case tn:return i=0,!1;case rn:return i=1,!1;case"at":return C=!0,!1;case js:return t=0,!1;case sn:case zs:return t=1,!1;case nn:case An:return t=2,!1;case en:return t=3,!1}else if(ce(E)||hA(E))return Array.isArray(t)||(t=[]),t.push(E),!1;return F},u)}if(u){var v=ei(e,d);o.push(v)}}),{size:t,shape:i,stops:o,position:c,type:2}},ii=function(e,A){var i=0,t=3,o=[],c=[];return ZA(A).forEach(function(d,B){var u=!0;if(B===0?u=d.reduce(function(v,F){if(eA(F))switch(F.value){case"center":return c.push(yr),!1;case"top":case"left":return c.push(fA),!1;case"right":case"bottom":return c.push(he),!1}else if(hA(F)||ce(F))return c.push(F),!1;return v},u):B===1&&(u=d.reduce(function(v,F){if(eA(F))switch(F.value){case tn:return i=0,!1;case rn:return i=1,!1;case nn:case js:return t=0,!1;case zs:return t=1,!1;case An:return t=2,!1;case sn:case en:return t=3,!1}else if(ce(F)||hA(F))return Array.isArray(t)||(t=[]),t.push(F),!1;return v},u)),u){var C=ei(e,d);o.push(C)}}),{size:t,shape:i,stops:o,position:c,type:2}},dl=function(e){return e.type===1},gl=function(e){return e.type===2},Hr={name:"image",parse:function(e,A){if(A.type===22){var i={url:A.value,type:0};return e.cache.addImage(A.value),i}if(A.type===18){var t=an[A.name];if(typeof t=="undefined")throw new Error('Attempting to parse an unsupported image function "'+A.name+'"');return t(e,A.values)}throw new Error("Unsupported image type "+A.type)}};function ul(e){return!(e.type===20&&e.value==="none")&&(e.type!==18||!!an[e.name])}var an={"linear-gradient":ll,"-moz-linear-gradient":ti,"-ms-linear-gradient":ti,"-o-linear-gradient":ti,"-webkit-linear-gradient":ti,"radial-gradient":hl,"-moz-radial-gradient":ii,"-ms-radial-gradient":ii,"-o-radial-gradient":ii,"-webkit-radial-gradient":ii,"-webkit-gradient":cl},Bl={name:"background-image",initialValue:"none",type:1,prefix:!1,parse:function(e,A){if(A.length===0)return[];var i=A[0];return i.type===20&&i.value==="none"?[]:A.filter(function(t){return Ge(t)&&ul(t)}).map(function(t){return Hr.parse(e,t)})}},pl={name:"background-origin",initialValue:"border-box",prefix:!1,type:1,parse:function(e,A){return A.map(function(i){if(eA(i))switch(i.value){case"padding-box":return 1;case"content-box":return 2}return 0})}},ml={name:"background-position",initialValue:"0% 0%",type:1,prefix:!1,parse:function(e,A){return ZA(A).map(function(i){return i.filter(hA)}).map(Rs)}},fl={name:"background-repeat",initialValue:"repeat",prefix:!1,type:1,parse:function(e,A){return ZA(A).map(function(i){return i.filter(eA).map(function(t){return t.value}).join(" ")}).map(wl)}},wl=function(e){switch(e){case"no-repeat":return 1;case"repeat-x":case"repeat no-repeat":return 2;case"repeat-y":case"no-repeat repeat":return 3;case"repeat":default:return 0}},Ve;(function(e){e.AUTO="auto",e.CONTAIN="contain",e.COVER="cover"})(Ve||(Ve={}));var Cl={name:"background-size",initialValue:"0",prefix:!1,type:1,parse:function(e,A){return ZA(A).map(function(i){return i.filter(vl)})}},vl=function(e){return eA(e)||hA(e)},ri=function(e){return{name:"border-"+e+"-color",initialValue:"transparent",prefix:!1,type:3,format:"color"}},Ql=ri("top"),Fl=ri("right"),Ul=ri("bottom"),El=ri("left"),si=function(e){return{name:"border-radius-"+e,initialValue:"0 0",prefix:!1,type:1,parse:function(A,i){return Rs(i.filter(hA))}}},yl=si("top-left"),Il=si("top-right"),Hl=si("bottom-right"),Sl=si("bottom-left"),ni=function(e){return{name:"border-"+e+"-style",initialValue:"solid",prefix:!1,type:2,parse:function(A,i){switch(i){case"none":return 0;case"dashed":return 2;case"dotted":return 3;case"double":return 4}return 1}}},bl=ni("top"),xl=ni("right"),Ml=ni("bottom"),Ll=ni("left"),ai=function(e){return{name:"border-"+e+"-width",initialValue:"0",type:0,prefix:!1,parse:function(A,i){return wt(i)?i.number:0}}},Tl=ai("top"),Dl=ai("right"),Kl=ai("bottom"),Pl=ai("left"),kl={name:"color",initialValue:"transparent",prefix:!1,type:3,format:"color"},Ol={name:"direction",initialValue:"ltr",prefix:!1,type:2,parse:function(e,A){switch(A){case"rtl":return 1;case"ltr":default:return 0}}},Rl={name:"display",initialValue:"inline-block",prefix:!1,type:1,parse:function(e,A){return A.filter(eA).reduce(function(i,t){return i|Nl(t.value)},0)}},Nl=function(e){switch(e){case"block":case"-webkit-box":return 2;case"inline":return 4;case"run-in":return 8;case"flow":return 16;case"flow-root":return 32;case"table":return 64;case"flex":case"-webkit-flex":return 128;case"grid":case"-ms-grid":return 256;case"ruby":return 512;case"subgrid":return 1024;case"list-item":return 2048;case"table-row-group":return 4096;case"table-header-group":return 8192;case"table-footer-group":return 16384;case"table-row":return 32768;case"table-cell":return 65536;case"table-column-group":return 131072;case"table-column":return 262144;case"table-caption":return 524288;case"ruby-base":return 1048576;case"ruby-text":return 2097152;case"ruby-base-container":return 4194304;case"ruby-text-container":return 8388608;case"contents":return 16777216;case"inline-block":return 33554432;case"inline-list-item":return 67108864;case"inline-table":return 134217728;case"inline-flex":return 268435456;case"inline-grid":return 536870912}return 0},Gl={name:"float",initialValue:"none",prefix:!1,type:2,parse:function(e,A){switch(A){case"left":return 1;case"right":return 2;case"inline-start":return 3;case"inline-end":return 4}return 0}},Vl={name:"letter-spacing",initialValue:"0",prefix:!1,type:0,parse:function(e,A){return A.type===20&&A.value==="normal"?0:A.type===17||A.type===15?A.number:0}},oi;(function(e){e.NORMAL="normal",e.STRICT="strict"})(oi||(oi={}));var _l={name:"line-break",initialValue:"normal",prefix:!1,type:2,parse:function(e,A){switch(A){case"strict":return oi.STRICT;case"normal":default:return oi.NORMAL}}},Xl={name:"line-height",initialValue:"normal",prefix:!1,type:4},on=function(e,A){return eA(e)&&e.value==="normal"?1.2*A:e.type===17?A*e.number:hA(e)?iA(e,A):A},Jl={name:"list-style-image",initialValue:"none",type:0,prefix:!1,parse:function(e,A){return A.type===20&&A.value==="none"?null:Hr.parse(e,A)}},Wl={name:"list-style-position",initialValue:"outside",prefix:!1,type:2,parse:function(e,A){switch(A){case"inside":return 0;case"outside":default:return 1}}},Sr={name:"list-style-type",initialValue:"none",prefix:!1,type:2,parse:function(e,A){switch(A){case"disc":return 0;case"circle":return 1;case"square":return 2;case"decimal":return 3;case"cjk-decimal":return 4;case"decimal-leading-zero":return 5;case"lower-roman":return 6;case"upper-roman":return 7;case"lower-greek":return 8;case"lower-alpha":return 9;case"upper-alpha":return 10;case"arabic-indic":return 11;case"armenian":return 12;case"bengali":return 13;case"cambodian":return 14;case"cjk-earthly-branch":return 15;case"cjk-heavenly-stem":return 16;case"cjk-ideographic":return 17;case"devanagari":return 18;case"ethiopic-numeric":return 19;case"georgian":return 20;case"gujarati":return 21;case"gurmukhi":return 22;case"hebrew":return 22;case"hiragana":return 23;case"hiragana-iroha":return 24;case"japanese-formal":return 25;case"japanese-informal":return 26;case"kannada":return 27;case"katakana":return 28;case"katakana-iroha":return 29;case"khmer":return 30;case"korean-hangul-formal":return 31;case"korean-hanja-formal":return 32;case"korean-hanja-informal":return 33;case"lao":return 34;case"lower-armenian":return 35;case"malayalam":return 36;case"mongolian":return 37;case"myanmar":return 38;case"oriya":return 39;case"persian":return 40;case"simp-chinese-formal":return 41;case"simp-chinese-informal":return 42;case"tamil":return 43;case"telugu":return 44;case"thai":return 45;case"tibetan":return 46;case"trad-chinese-formal":return 47;case"trad-chinese-informal":return 48;case"upper-armenian":return 49;case"disclosure-open":return 50;case"disclosure-closed":return 51;case"none":default:return-1}}},li=function(e){return{name:"margin-"+e,initialValue:"0",prefix:!1,type:4}},Yl=li("top"),Zl=li("right"),$l=li("bottom"),ql=li("left"),jl={name:"overflow",initialValue:"visible",prefix:!1,type:1,parse:function(e,A){return A.filter(eA).map(function(i){switch(i.value){case"hidden":return 1;case"scroll":return 2;case"clip":return 3;case"auto":return 4;case"visible":default:return 0}})}},zl={name:"overflow-wrap",initialValue:"normal",prefix:!1,type:2,parse:function(e,A){switch(A){case"break-word":return"break-word";case"normal":default:return"normal"}}},ci=function(e){return{name:"padding-"+e,initialValue:"0",prefix:!1,type:3,format:"length-percentage"}},Ac=ci("top"),ec=ci("right"),tc=ci("bottom"),ic=ci("left"),rc={name:"text-align",initialValue:"left",prefix:!1,type:2,parse:function(e,A){switch(A){case"right":return 2;case"center":case"justify":return 1;case"left":default:return 0}}},sc={name:"position",initialValue:"static",prefix:!1,type:2,parse:function(e,A){switch(A){case"relative":return 1;case"absolute":return 2;case"fixed":return 3;case"sticky":return 4}return 0}},nc={name:"text-shadow",initialValue:"none",type:1,prefix:!1,parse:function(e,A){return A.length===1&&Er(A[0],"none")?[]:ZA(A).map(function(i){for(var t={color:ee.TRANSPARENT,offsetX:fA,offsetY:fA,blur:fA},o=0,c=0;c<i.length;c++){var d=i[c];ce(d)?(o===0?t.offsetX=d:o===1?t.offsetY=d:t.blur=d,o++):t.color=de.parse(e,d)}return t})}},ac={name:"text-transform",initialValue:"none",prefix:!1,type:2,parse:function(e,A){switch(A){case"uppercase":return 2;case"lowercase":return 1;case"capitalize":return 3}return 0}},oc={name:"transform",initialValue:"none",prefix:!0,type:0,parse:function(e,A){if(A.type===20&&A.value==="none")return null;if(A.type===18){var i=hc[A.name];if(typeof i=="undefined")throw new Error('Attempting to parse an unsupported transform function "'+A.name+'"');return i(A.values)}return null}},lc=function(e){var A=e.filter(function(i){return i.type===17}).map(function(i){return i.number});return A.length===6?A:null},cc=function(e){var A=e.filter(function(u){return u.type===17}).map(function(u){return u.number}),i=A[0],t=A[1];A[2],A[3];var o=A[4],c=A[5];A[6],A[7],A[8],A[9],A[10],A[11];var d=A[12],B=A[13];return A[14],A[15],A.length===16?[i,t,o,c,d,B]:null},hc={matrix:lc,matrix3d:cc},ln={type:16,number:50,flags:ut},dc=[ln,ln],gc={name:"transform-origin",initialValue:"50% 50%",prefix:!0,type:1,parse:function(e,A){var i=A.filter(hA);return i.length!==2?dc:[i[0],i[1]]}},uc={name:"visible",initialValue:"none",prefix:!1,type:2,parse:function(e,A){switch(A){case"hidden":return 1;case"collapse":return 2;case"visible":default:return 0}}},Qt;(function(e){e.NORMAL="normal",e.BREAK_ALL="break-all",e.KEEP_ALL="keep-all"})(Qt||(Qt={}));for(var Bc={name:"word-break",initialValue:"normal",prefix:!1,type:2,parse:function(e,A){switch(A){case"break-all":return Qt.BREAK_ALL;case"keep-all":return Qt.KEEP_ALL;case"normal":default:return Qt.NORMAL}}},pc={name:"z-index",initialValue:"auto",prefix:!1,type:0,parse:function(e,A){if(A.type===20)return{auto:!0,order:0};if(Ne(A))return{auto:!1,order:A.number};throw new Error("Invalid z-index number parsed")}},cn={name:"time",parse:function(e,A){if(A.type===15)switch(A.unit.toLowerCase()){case"s":return 1e3*A.number;case"ms":return A.number}throw new Error("Unsupported time type")}},mc={name:"opacity",initialValue:"1",type:0,prefix:!1,parse:function(e,A){return Ne(A)?A.number:1}},fc={name:"text-decoration-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},wc={name:"text-decoration-line",initialValue:"none",prefix:!1,type:1,parse:function(e,A){return A.filter(eA).map(function(i){switch(i.value){case"underline":return 1;case"overline":return 2;case"line-through":return 3;case"none":return 4}return 0}).filter(function(i){return i!==0})}},Cc={name:"font-family",initialValue:"",prefix:!1,type:1,parse:function(e,A){var i=[],t=[];return A.forEach(function(o){switch(o.type){case 20:case 0:i.push(o.value);break;case 17:i.push(o.number.toString());break;case 4:t.push(i.join(" ")),i.length=0;break}}),i.length&&t.push(i.join(" ")),t.map(function(o){return o.indexOf(" ")===-1?o:"'"+o+"'"})}},vc={name:"font-size",initialValue:"0",prefix:!1,type:3,format:"length"},Qc={name:"font-weight",initialValue:"normal",type:0,prefix:!1,parse:function(e,A){if(Ne(A))return A.number;if(eA(A))switch(A.value){case"bold":return 700;case"normal":default:return 400}return 400}},Fc={name:"font-variant",initialValue:"none",type:1,prefix:!1,parse:function(e,A){return A.filter(eA).map(function(i){return i.value})}},Uc={name:"font-style",initialValue:"normal",prefix:!1,type:2,parse:function(e,A){switch(A){case"oblique":return"oblique";case"italic":return"italic";case"normal":default:return"normal"}}},BA=function(e,A){return(e&A)!==0},Ec={name:"content",initialValue:"none",type:1,prefix:!1,parse:function(e,A){if(A.length===0)return[];var i=A[0];return i.type===20&&i.value==="none"?[]:A}},yc={name:"counter-increment",initialValue:"none",prefix:!0,type:1,parse:function(e,A){if(A.length===0)return null;var i=A[0];if(i.type===20&&i.value==="none")return null;for(var t=[],o=A.filter(Os),c=0;c<o.length;c++){var d=o[c],B=o[c+1];if(d.type===20){var u=B&&Ne(B)?B.number:1;t.push({counter:d.value,increment:u})}}return t}},Ic={name:"counter-reset",initialValue:"none",prefix:!0,type:1,parse:function(e,A){if(A.length===0)return[];for(var i=[],t=A.filter(Os),o=0;o<t.length;o++){var c=t[o],d=t[o+1];if(eA(c)&&c.value!=="none"){var B=d&&Ne(d)?d.number:0;i.push({counter:c.value,reset:B})}}return i}},Hc={name:"duration",initialValue:"0s",prefix:!1,type:1,parse:function(e,A){return A.filter(wt).map(function(i){return cn.parse(e,i)})}},Sc={name:"quotes",initialValue:"none",prefix:!0,type:1,parse:function(e,A){if(A.length===0)return null;var i=A[0];if(i.type===20&&i.value==="none")return null;var t=[],o=A.filter(el);if(o.length%2!==0)return null;for(var c=0;c<o.length;c+=2){var d=o[c].value,B=o[c+1].value;t.push({open:d,close:B})}return t}},hn=function(e,A,i){if(!e)return"";var t=e[Math.min(A,e.length-1)];return t?i?t.open:t.close:""},bc={name:"box-shadow",initialValue:"none",type:1,prefix:!1,parse:function(e,A){return A.length===1&&Er(A[0],"none")?[]:ZA(A).map(function(i){for(var t={color:255,offsetX:fA,offsetY:fA,blur:fA,spread:fA,inset:!1},o=0,c=0;c<i.length;c++){var d=i[c];Er(d,"inset")?t.inset=!0:ce(d)?(o===0?t.offsetX=d:o===1?t.offsetY=d:o===2?t.blur=d:t.spread=d,o++):t.color=de.parse(e,d)}return t})}},xc={name:"paint-order",initialValue:"normal",prefix:!1,type:1,parse:function(e,A){var i=[0,1,2],t=[];return A.filter(eA).forEach(function(o){switch(o.value){case"stroke":t.push(1);break;case"fill":t.push(0);break;case"markers":t.push(2);break}}),i.forEach(function(o){t.indexOf(o)===-1&&t.push(o)}),t}},Mc={name:"-webkit-text-stroke-color",initialValue:"currentcolor",prefix:!1,type:3,format:"color"},Lc={name:"-webkit-text-stroke-width",initialValue:"0",type:0,prefix:!1,parse:function(e,A){return wt(A)?A.number:0}},Tc=function(){function e(A,i){var t,o;this.animationDuration=P(A,Hc,i.animationDuration),this.backgroundClip=P(A,rl,i.backgroundClip),this.backgroundColor=P(A,sl,i.backgroundColor),this.backgroundImage=P(A,Bl,i.backgroundImage),this.backgroundOrigin=P(A,pl,i.backgroundOrigin),this.backgroundPosition=P(A,ml,i.backgroundPosition),this.backgroundRepeat=P(A,fl,i.backgroundRepeat),this.backgroundSize=P(A,Cl,i.backgroundSize),this.borderTopColor=P(A,Ql,i.borderTopColor),this.borderRightColor=P(A,Fl,i.borderRightColor),this.borderBottomColor=P(A,Ul,i.borderBottomColor),this.borderLeftColor=P(A,El,i.borderLeftColor),this.borderTopLeftRadius=P(A,yl,i.borderTopLeftRadius),this.borderTopRightRadius=P(A,Il,i.borderTopRightRadius),this.borderBottomRightRadius=P(A,Hl,i.borderBottomRightRadius),this.borderBottomLeftRadius=P(A,Sl,i.borderBottomLeftRadius),this.borderTopStyle=P(A,bl,i.borderTopStyle),this.borderRightStyle=P(A,xl,i.borderRightStyle),this.borderBottomStyle=P(A,Ml,i.borderBottomStyle),this.borderLeftStyle=P(A,Ll,i.borderLeftStyle),this.borderTopWidth=P(A,Tl,i.borderTopWidth),this.borderRightWidth=P(A,Dl,i.borderRightWidth),this.borderBottomWidth=P(A,Kl,i.borderBottomWidth),this.borderLeftWidth=P(A,Pl,i.borderLeftWidth),this.boxShadow=P(A,bc,i.boxShadow),this.color=P(A,kl,i.color),this.direction=P(A,Ol,i.direction),this.display=P(A,Rl,i.display),this.float=P(A,Gl,i.cssFloat),this.fontFamily=P(A,Cc,i.fontFamily),this.fontSize=P(A,vc,i.fontSize),this.fontStyle=P(A,Uc,i.fontStyle),this.fontVariant=P(A,Fc,i.fontVariant),this.fontWeight=P(A,Qc,i.fontWeight),this.letterSpacing=P(A,Vl,i.letterSpacing),this.lineBreak=P(A,_l,i.lineBreak),this.lineHeight=P(A,Xl,i.lineHeight),this.listStyleImage=P(A,Jl,i.listStyleImage),this.listStylePosition=P(A,Wl,i.listStylePosition),this.listStyleType=P(A,Sr,i.listStyleType),this.marginTop=P(A,Yl,i.marginTop),this.marginRight=P(A,Zl,i.marginRight),this.marginBottom=P(A,$l,i.marginBottom),this.marginLeft=P(A,ql,i.marginLeft),this.opacity=P(A,mc,i.opacity);var c=P(A,jl,i.overflow);this.overflowX=c[0],this.overflowY=c[c.length>1?1:0],this.overflowWrap=P(A,zl,i.overflowWrap),this.paddingTop=P(A,Ac,i.paddingTop),this.paddingRight=P(A,ec,i.paddingRight),this.paddingBottom=P(A,tc,i.paddingBottom),this.paddingLeft=P(A,ic,i.paddingLeft),this.paintOrder=P(A,xc,i.paintOrder),this.position=P(A,sc,i.position),this.textAlign=P(A,rc,i.textAlign),this.textDecorationColor=P(A,fc,(t=i.textDecorationColor)!==null&&t!==void 0?t:i.color),this.textDecorationLine=P(A,wc,(o=i.textDecorationLine)!==null&&o!==void 0?o:i.textDecoration),this.textShadow=P(A,nc,i.textShadow),this.textTransform=P(A,ac,i.textTransform),this.transform=P(A,oc,i.transform),this.transformOrigin=P(A,gc,i.transformOrigin),this.visibility=P(A,uc,i.visibility),this.webkitTextStrokeColor=P(A,Mc,i.webkitTextStrokeColor),this.webkitTextStrokeWidth=P(A,Lc,i.webkitTextStrokeWidth),this.wordBreak=P(A,Bc,i.wordBreak),this.zIndex=P(A,pc,i.zIndex)}return e.prototype.isVisible=function(){return this.display>0&&this.opacity>0&&this.visibility===0},e.prototype.isTransparent=function(){return ge(this.backgroundColor)},e.prototype.isTransformed=function(){return this.transform!==null},e.prototype.isPositioned=function(){return this.position!==0},e.prototype.isPositionedWithZIndex=function(){return this.isPositioned()&&!this.zIndex.auto},e.prototype.isFloating=function(){return this.float!==0},e.prototype.isInlineLevel=function(){return BA(this.display,4)||BA(this.display,33554432)||BA(this.display,268435456)||BA(this.display,536870912)||BA(this.display,67108864)||BA(this.display,134217728)},e}(),Dc=function(){function e(A,i){this.content=P(A,Ec,i.content),this.quotes=P(A,Sc,i.quotes)}return e}(),dn=function(){function e(A,i){this.counterIncrement=P(A,yc,i.counterIncrement),this.counterReset=P(A,Ic,i.counterReset)}return e}(),P=function(e,A,i){var t=new Ps,o=i!==null&&typeof i!="undefined"?i.toString():A.initialValue;t.write(o);var c=new ks(t.read());switch(A.type){case 2:var d=c.parseComponentValue();return A.parse(e,eA(d)?d.value:A.initialValue);case 0:return A.parse(e,c.parseComponentValue());case 1:return A.parse(e,c.parseComponentValues());case 4:return c.parseComponentValue();case 3:switch(A.format){case"angle":return Ai.parse(e,c.parseComponentValue());case"color":return de.parse(e,c.parseComponentValue());case"image":return Hr.parse(e,c.parseComponentValue());case"length":var B=c.parseComponentValue();return ce(B)?B:fA;case"length-percentage":var u=c.parseComponentValue();return hA(u)?u:fA;case"time":return cn.parse(e,c.parseComponentValue())}break}},Kc="data-html2canvas-debug",Pc=function(e){var A=e.getAttribute(Kc);switch(A){case"all":return 1;case"clone":return 2;case"parse":return 3;case"render":return 4;default:return 0}},br=function(e,A){var i=Pc(e);return i===1||A===i},$A=function(){function e(A,i){if(this.context=A,this.textNodes=[],this.elements=[],this.flags=0,br(i,3))debugger;this.styles=new Tc(A,window.getComputedStyle(i,null)),_r(i)&&(this.styles.animationDuration.some(function(t){return t>0})&&(i.style.animationDuration="0s"),this.styles.transform!==null&&(i.style.transform="none")),this.bounds=g(this.context,i),br(i,4)&&(this.flags|=16)}return e}(),kc="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",gn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Ft=typeof Uint8Array=="undefined"?[]:new Uint8Array(256),hi=0;hi<gn.length;hi++)Ft[gn.charCodeAt(hi)]=hi;for(var Oc=function(e){var A=e.length*.75,i=e.length,t,o=0,c,d,B,u;e[e.length-1]==="="&&(A--,e[e.length-2]==="="&&A--);var C=typeof ArrayBuffer!="undefined"&&typeof Uint8Array!="undefined"&&typeof Uint8Array.prototype.slice!="undefined"?new ArrayBuffer(A):new Array(A),v=Array.isArray(C)?C:new Uint8Array(C);for(t=0;t<i;t+=4)c=Ft[e.charCodeAt(t)],d=Ft[e.charCodeAt(t+1)],B=Ft[e.charCodeAt(t+2)],u=Ft[e.charCodeAt(t+3)],v[o++]=c<<2|d>>4,v[o++]=(d&15)<<4|B>>2,v[o++]=(B&3)<<6|u&63;return C},Rc=function(e){for(var A=e.length,i=[],t=0;t<A;t+=2)i.push(e[t+1]<<8|e[t]);return i},Nc=function(e){for(var A=e.length,i=[],t=0;t<A;t+=4)i.push(e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]);return i},Ie=5,xr=11,Mr=2,Gc=xr-Ie,un=65536>>Ie,Vc=1<<Ie,Lr=Vc-1,_c=1024>>Ie,Xc=un+_c,Jc=Xc,Wc=32,Yc=Jc+Wc,Zc=65536>>xr,$c=1<<Gc,qc=$c-1,Bn=function(e,A,i){return e.slice?e.slice(A,i):new Uint16Array(Array.prototype.slice.call(e,A,i))},jc=function(e,A,i){return e.slice?e.slice(A,i):new Uint32Array(Array.prototype.slice.call(e,A,i))},zc=function(e,A){var i=Oc(e),t=Array.isArray(i)?Nc(i):new Uint32Array(i),o=Array.isArray(i)?Rc(i):new Uint16Array(i),c=24,d=Bn(o,c/2,t[4]/2),B=t[5]===2?Bn(o,(c+t[4])/2):jc(t,Math.ceil((c+t[4])/4));return new Ah(t[0],t[1],t[2],t[3],d,B)},Ah=function(){function e(A,i,t,o,c,d){this.initialValue=A,this.errorValue=i,this.highStart=t,this.highValueIndex=o,this.index=c,this.data=d}return e.prototype.get=function(A){var i;if(A>=0){if(A<55296||A>56319&&A<=65535)return i=this.index[A>>Ie],i=(i<<Mr)+(A&Lr),this.data[i];if(A<=65535)return i=this.index[un+(A-55296>>Ie)],i=(i<<Mr)+(A&Lr),this.data[i];if(A<this.highStart)return i=Yc-Zc+(A>>xr),i=this.index[i],i+=A>>Ie&qc,i=this.index[i],i=(i<<Mr)+(A&Lr),this.data[i];if(A<=1114111)return this.data[this.highValueIndex]}return this.errorValue},e}(),pn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",eh=typeof Uint8Array=="undefined"?[]:new Uint8Array(256),di=0;di<pn.length;di++)eh[pn.charCodeAt(di)]=di;var th=1,Tr=2,Dr=3,mn=4,fn=5,ih=7,wn=8,Kr=9,Pr=10,Cn=11,vn=12,Qn=13,Fn=14,kr=15,rh=function(e){for(var A=[],i=0,t=e.length;i<t;){var o=e.charCodeAt(i++);if(o>=55296&&o<=56319&&i<t){var c=e.charCodeAt(i++);(c&64512)===56320?A.push(((o&1023)<<10)+(c&1023)+65536):(A.push(o),i--)}else A.push(o)}return A},sh=function(){for(var e=[],A=0;A<arguments.length;A++)e[A]=arguments[A];if(String.fromCodePoint)return String.fromCodePoint.apply(String,e);var i=e.length;if(!i)return"";for(var t=[],o=-1,c="";++o<i;){var d=e[o];d<=65535?t.push(d):(d-=65536,t.push((d>>10)+55296,d%1024+56320)),(o+1===i||t.length>16384)&&(c+=String.fromCharCode.apply(String,t),t.length=0)}return c},nh=zc(kc),RA="\xD7",Or="\xF7",ah=function(e){return nh.get(e)},oh=function(e,A,i){var t=i-2,o=A[t],c=A[i-1],d=A[i];if(c===Tr&&d===Dr)return RA;if(c===Tr||c===Dr||c===mn||d===Tr||d===Dr||d===mn)return Or;if(c===wn&&[wn,Kr,Cn,vn].indexOf(d)!==-1||(c===Cn||c===Kr)&&(d===Kr||d===Pr)||(c===vn||c===Pr)&&d===Pr||d===Qn||d===fn||d===ih||c===th)return RA;if(c===Qn&&d===Fn){for(;o===fn;)o=A[--t];if(o===Fn)return RA}if(c===kr&&d===kr){for(var B=0;o===kr;)B++,o=A[--t];if(B%2===0)return RA}return Or},lh=function(e){var A=rh(e),i=A.length,t=0,o=0,c=A.map(ah);return{next:function(){if(t>=i)return{done:!0,value:null};for(var d=RA;t<i&&(d=oh(A,c,++t))===RA;);if(d!==RA||t===i){var B=sh.apply(null,A.slice(o,t));return o=t,{value:B,done:!1}}return{done:!0,value:null}}}},ch=function(e){for(var A=lh(e),i=[],t;!(t=A.next()).done;)t.value&&i.push(t.value.slice());return i},hh=function(e){var A=123;if(e.createRange){var i=e.createRange();if(i.getBoundingClientRect){var t=e.createElement("boundtest");t.style.height=A+"px",t.style.display="block",e.body.appendChild(t),i.selectNode(t);var o=i.getBoundingClientRect(),c=Math.round(o.height);if(e.body.removeChild(t),c===A)return!0}}return!1},dh=function(e){var A=e.createElement("boundtest");A.style.width="50px",A.style.display="block",A.style.fontSize="12px",A.style.letterSpacing="0px",A.style.wordSpacing="0px",e.body.appendChild(A);var i=e.createRange();A.innerHTML=typeof"".repeat=="function"?"&#128104;".repeat(10):"";var t=A.firstChild,o=w(t.data).map(function(u){return f(u)}),c=0,d={},B=o.every(function(u,C){i.setStart(t,c),i.setEnd(t,c+u.length);var v=i.getBoundingClientRect();c+=u.length;var F=v.x>d.x||v.y>d.y;return d=v,C===0?!0:F});return e.body.removeChild(A),B},gh=function(){return typeof new Image().crossOrigin!="undefined"},uh=function(){return typeof new XMLHttpRequest().responseType=="string"},Bh=function(e){var A=new Image,i=e.createElement("canvas"),t=i.getContext("2d");if(!t)return!1;A.src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg'></svg>";try{t.drawImage(A,0,0),i.toDataURL()}catch(o){return!1}return!0},Un=function(e){return e[0]===0&&e[1]===255&&e[2]===0&&e[3]===255},ph=function(e){var A=e.createElement("canvas"),i=100;A.width=i,A.height=i;var t=A.getContext("2d");if(!t)return Promise.reject(!1);t.fillStyle="rgb(0, 255, 0)",t.fillRect(0,0,i,i);var o=new Image,c=A.toDataURL();o.src=c;var d=Rr(i,i,0,0,o);return t.fillStyle="red",t.fillRect(0,0,i,i),En(d).then(function(B){t.drawImage(B,0,0);var u=t.getImageData(0,0,i,i).data;t.fillStyle="red",t.fillRect(0,0,i,i);var C=e.createElement("div");return C.style.backgroundImage="url("+c+")",C.style.height=i+"px",Un(u)?En(Rr(i,i,0,0,C)):Promise.reject(!1)}).then(function(B){return t.drawImage(B,0,0),Un(t.getImageData(0,0,i,i).data)}).catch(function(){return!1})},Rr=function(e,A,i,t,o){var c="http://www.w3.org/2000/svg",d=document.createElementNS(c,"svg"),B=document.createElementNS(c,"foreignObject");return d.setAttributeNS(null,"width",e.toString()),d.setAttributeNS(null,"height",A.toString()),B.setAttributeNS(null,"width","100%"),B.setAttributeNS(null,"height","100%"),B.setAttributeNS(null,"x",i.toString()),B.setAttributeNS(null,"y",t.toString()),B.setAttributeNS(null,"externalResourcesRequired","true"),d.appendChild(B),B.appendChild(o),d},En=function(e){return new Promise(function(A,i){var t=new Image;t.onload=function(){return A(t)},t.onerror=i,t.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent(new XMLSerializer().serializeToString(e))})},wA={get SUPPORT_RANGE_BOUNDS(){var e=hh(document);return Object.defineProperty(wA,"SUPPORT_RANGE_BOUNDS",{value:e}),e},get SUPPORT_WORD_BREAKING(){var e=wA.SUPPORT_RANGE_BOUNDS&&dh(document);return Object.defineProperty(wA,"SUPPORT_WORD_BREAKING",{value:e}),e},get SUPPORT_SVG_DRAWING(){var e=Bh(document);return Object.defineProperty(wA,"SUPPORT_SVG_DRAWING",{value:e}),e},get SUPPORT_FOREIGNOBJECT_DRAWING(){var e=typeof Array.from=="function"&&typeof window.fetch=="function"?ph(document):Promise.resolve(!1);return Object.defineProperty(wA,"SUPPORT_FOREIGNOBJECT_DRAWING",{value:e}),e},get SUPPORT_CORS_IMAGES(){var e=gh();return Object.defineProperty(wA,"SUPPORT_CORS_IMAGES",{value:e}),e},get SUPPORT_RESPONSE_TYPE(){var e=uh();return Object.defineProperty(wA,"SUPPORT_RESPONSE_TYPE",{value:e}),e},get SUPPORT_CORS_XHR(){var e="withCredentials"in new XMLHttpRequest;return Object.defineProperty(wA,"SUPPORT_CORS_XHR",{value:e}),e},get SUPPORT_NATIVE_TEXT_SEGMENTATION(){var e=!!(typeof Intl!="undefined"&&Intl.Segmenter);return Object.defineProperty(wA,"SUPPORT_NATIVE_TEXT_SEGMENTATION",{value:e}),e}},Ut=function(){function e(A,i){this.text=A,this.bounds=i}return e}(),mh=function(e,A,i,t){var o=Ch(A,i),c=[],d=0;return o.forEach(function(B){if(i.textDecorationLine.length||B.trim().length>0)if(wA.SUPPORT_RANGE_BOUNDS){var u=yn(t,d,B.length).getClientRects();if(u.length>1){var C=Nr(B),v=0;C.forEach(function(E){c.push(new Ut(E,h.fromDOMRectList(e,yn(t,v+d,E.length).getClientRects()))),v+=E.length})}else c.push(new Ut(B,h.fromDOMRectList(e,u)))}else{var F=t.splitText(B.length);c.push(new Ut(B,fh(e,t))),t=F}else wA.SUPPORT_RANGE_BOUNDS||(t=t.splitText(B.length));d+=B.length}),c},fh=function(e,A){var i=A.ownerDocument;if(i){var t=i.createElement("html2canvaswrapper");t.appendChild(A.cloneNode(!0));var o=A.parentNode;if(o){o.replaceChild(t,A);var c=g(e,t);return t.firstChild&&o.replaceChild(t.firstChild,t),c}}return h.EMPTY},yn=function(e,A,i){var t=e.ownerDocument;if(!t)throw new Error("Node has no owner document");var o=t.createRange();return o.setStart(e,A),o.setEnd(e,A+i),o},Nr=function(e){if(wA.SUPPORT_NATIVE_TEXT_SEGMENTATION){var A=new Intl.Segmenter(void 0,{granularity:"grapheme"});return Array.from(A.segment(e)).map(function(i){return i.segment})}return ch(e)},wh=function(e,A){if(wA.SUPPORT_NATIVE_TEXT_SEGMENTATION){var i=new Intl.Segmenter(void 0,{granularity:"word"});return Array.from(i.segment(e)).map(function(t){return t.segment})}return Qh(e,A)},Ch=function(e,A){return A.letterSpacing!==0?Nr(e):wh(e,A)},vh=[32,160,4961,65792,65793,4153,4241],Qh=function(e,A){for(var i=Ya(e,{lineBreak:A.lineBreak,wordBreak:A.overflowWrap==="break-word"?"break-word":A.wordBreak}),t=[],o,c=function(){if(o.value){var d=o.value.slice(),B=w(d),u="";B.forEach(function(C){vh.indexOf(C)===-1?u+=f(C):(u.length&&t.push(u),t.push(f(C)),u="")}),u.length&&t.push(u)}};!(o=i.next()).done;)c();return t},Fh=function(){function e(A,i,t){this.text=Uh(i.data,t.textTransform),this.textBounds=mh(A,this.text,t,i)}return e}(),Uh=function(e,A){switch(A){case 1:return e.toLowerCase();case 3:return e.replace(Eh,yh);case 2:return e.toUpperCase();default:return e}},Eh=/(^|\s|:|-|\(|\))([a-z])/g,yh=function(e,A,i){return e.length>0?A+i.toUpperCase():e},In=function(e){s(A,e);function A(i,t){var o=e.call(this,i,t)||this;return o.src=t.currentSrc||t.src,o.intrinsicWidth=t.naturalWidth,o.intrinsicHeight=t.naturalHeight,o.context.cache.addImage(o.src),o}return A}($A),Hn=function(e){s(A,e);function A(i,t){var o=e.call(this,i,t)||this;return o.canvas=t,o.intrinsicWidth=t.width,o.intrinsicHeight=t.height,o}return A}($A),Sn=function(e){s(A,e);function A(i,t){var o=e.call(this,i,t)||this,c=new XMLSerializer,d=g(i,t);return t.setAttribute("width",d.width+"px"),t.setAttribute("height",d.height+"px"),o.svg="data:image/svg+xml,"+encodeURIComponent(c.serializeToString(t)),o.intrinsicWidth=t.width.baseVal.value,o.intrinsicHeight=t.height.baseVal.value,o.context.cache.addImage(o.svg),o}return A}($A),bn=function(e){s(A,e);function A(i,t){var o=e.call(this,i,t)||this;return o.value=t.value,o}return A}($A),Gr=function(e){s(A,e);function A(i,t){var o=e.call(this,i,t)||this;return o.start=t.start,o.reversed=typeof t.reversed=="boolean"&&t.reversed===!0,o}return A}($A),Ih=[{type:15,flags:0,unit:"px",number:3}],Hh=[{type:16,flags:0,number:50}],Sh=function(e){return e.width>e.height?new h(e.left+(e.width-e.height)/2,e.top,e.height,e.height):e.width<e.height?new h(e.left,e.top+(e.height-e.width)/2,e.width,e.width):e},bh=function(e){var A=e.type===xh?new Array(e.value.length+1).join("\u2022"):e.value;return A.length===0?e.placeholder||"":A},gi="checkbox",ui="radio",xh="password",xn=707406591,Vr=function(e){s(A,e);function A(i,t){var o=e.call(this,i,t)||this;switch(o.type=t.type.toLowerCase(),o.checked=t.checked,o.value=bh(t),(o.type===gi||o.type===ui)&&(o.styles.backgroundColor=3739148031,o.styles.borderTopColor=o.styles.borderRightColor=o.styles.borderBottomColor=o.styles.borderLeftColor=2779096575,o.styles.borderTopWidth=o.styles.borderRightWidth=o.styles.borderBottomWidth=o.styles.borderLeftWidth=1,o.styles.borderTopStyle=o.styles.borderRightStyle=o.styles.borderBottomStyle=o.styles.borderLeftStyle=1,o.styles.backgroundClip=[0],o.styles.backgroundOrigin=[0],o.bounds=Sh(o.bounds)),o.type){case gi:o.styles.borderTopRightRadius=o.styles.borderTopLeftRadius=o.styles.borderBottomRightRadius=o.styles.borderBottomLeftRadius=Ih;break;case ui:o.styles.borderTopRightRadius=o.styles.borderTopLeftRadius=o.styles.borderBottomRightRadius=o.styles.borderBottomLeftRadius=Hh;break}return o}return A}($A),Mn=function(e){s(A,e);function A(i,t){var o=e.call(this,i,t)||this,c=t.options[t.selectedIndex||0];return o.value=c&&c.text||"",o}return A}($A),Ln=function(e){s(A,e);function A(i,t){var o=e.call(this,i,t)||this;return o.value=t.value,o}return A}($A),Tn=function(e){s(A,e);function A(i,t){var o=e.call(this,i,t)||this;o.src=t.src,o.width=parseInt(t.width,10)||0,o.height=parseInt(t.height,10)||0,o.backgroundColor=o.styles.backgroundColor;try{if(t.contentWindow&&t.contentWindow.document&&t.contentWindow.document.documentElement){o.tree=Kn(i,t.contentWindow.document.documentElement);var c=t.contentWindow.document.documentElement?vt(i,getComputedStyle(t.contentWindow.document.documentElement).backgroundColor):ee.TRANSPARENT,d=t.contentWindow.document.body?vt(i,getComputedStyle(t.contentWindow.document.body).backgroundColor):ee.TRANSPARENT;o.backgroundColor=ge(c)?ge(d)?o.styles.backgroundColor:d:c}}catch(B){}return o}return A}($A),Mh=["OL","UL","MENU"],Bi=function(e,A,i,t){for(var o=A.firstChild,c=void 0;o;o=c)if(c=o.nextSibling,Pn(o)&&o.data.trim().length>0)i.textNodes.push(new Fh(e,o,i.styles));else if(_e(o))if(Vn(o)&&o.assignedNodes)o.assignedNodes().forEach(function(B){return Bi(e,B,i,t)});else{var d=Dn(e,o);d.styles.isVisible()&&(Lh(o,d,t)?d.flags|=4:Th(d.styles)&&(d.flags|=2),Mh.indexOf(o.tagName)!==-1&&(d.flags|=8),i.elements.push(d),o.slot,o.shadowRoot?Bi(e,o.shadowRoot,d,t):!mi(o)&&!kn(o)&&!fi(o)&&Bi(e,o,d,t))}},Dn=function(e,A){return Jr(A)?new In(e,A):On(A)?new Hn(e,A):kn(A)?new Sn(e,A):Dh(A)?new bn(e,A):Kh(A)?new Gr(e,A):Ph(A)?new Vr(e,A):fi(A)?new Mn(e,A):mi(A)?new Ln(e,A):Nn(A)?new Tn(e,A):new $A(e,A)},Kn=function(e,A){var i=Dn(e,A);return i.flags|=4,Bi(e,A,i,i),i},Lh=function(e,A,i){return A.styles.isPositionedWithZIndex()||A.styles.opacity<1||A.styles.isTransformed()||Xr(e)&&i.styles.isTransparent()},Th=function(e){return e.isPositioned()||e.isFloating()},Pn=function(e){return e.nodeType===Node.TEXT_NODE},_e=function(e){return e.nodeType===Node.ELEMENT_NODE},_r=function(e){return _e(e)&&typeof e.style!="undefined"&&!pi(e)},pi=function(e){return typeof e.className=="object"},Dh=function(e){return e.tagName==="LI"},Kh=function(e){return e.tagName==="OL"},Ph=function(e){return e.tagName==="INPUT"},kh=function(e){return e.tagName==="HTML"},kn=function(e){return e.tagName==="svg"},Xr=function(e){return e.tagName==="BODY"},On=function(e){return e.tagName==="CANVAS"},Rn=function(e){return e.tagName==="VIDEO"},Jr=function(e){return e.tagName==="IMG"},Nn=function(e){return e.tagName==="IFRAME"},Gn=function(e){return e.tagName==="STYLE"},Oh=function(e){return e.tagName==="SCRIPT"},mi=function(e){return e.tagName==="TEXTAREA"},fi=function(e){return e.tagName==="SELECT"},Vn=function(e){return e.tagName==="SLOT"},_n=function(e){return e.tagName.indexOf("-")>0},Rh=function(){function e(){this.counters={}}return e.prototype.getCounterValue=function(A){var i=this.counters[A];return i&&i.length?i[i.length-1]:1},e.prototype.getCounterValues=function(A){var i=this.counters[A];return i||[]},e.prototype.pop=function(A){var i=this;A.forEach(function(t){return i.counters[t].pop()})},e.prototype.parse=function(A){var i=this,t=A.counterIncrement,o=A.counterReset,c=!0;t!==null&&t.forEach(function(B){var u=i.counters[B.counter];u&&B.increment!==0&&(c=!1,u.length||u.push(1),u[Math.max(0,u.length-1)]+=B.increment)});var d=[];return c&&o.forEach(function(B){var u=i.counters[B.counter];d.push(B.counter),u||(u=i.counters[B.counter]=[]),u.push(B.reset)}),d},e}(),Xn={integers:[1e3,900,500,400,100,90,50,40,10,9,5,4,1],values:["M","CM","D","CD","C","XC","L","XL","X","IX","V","IV","I"]},Jn={integers:[9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["\u0554","\u0553","\u0552","\u0551","\u0550","\u054F","\u054E","\u054D","\u054C","\u054B","\u054A","\u0549","\u0548","\u0547","\u0546","\u0545","\u0544","\u0543","\u0542","\u0541","\u0540","\u053F","\u053E","\u053D","\u053C","\u053B","\u053A","\u0539","\u0538","\u0537","\u0536","\u0535","\u0534","\u0533","\u0532","\u0531"]},Nh={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,400,300,200,100,90,80,70,60,50,40,30,20,19,18,17,16,15,10,9,8,7,6,5,4,3,2,1],values:["\u05D9\u05F3","\u05D8\u05F3","\u05D7\u05F3","\u05D6\u05F3","\u05D5\u05F3","\u05D4\u05F3","\u05D3\u05F3","\u05D2\u05F3","\u05D1\u05F3","\u05D0\u05F3","\u05EA","\u05E9","\u05E8","\u05E7","\u05E6","\u05E4","\u05E2","\u05E1","\u05E0","\u05DE","\u05DC","\u05DB","\u05D9\u05D8","\u05D9\u05D7","\u05D9\u05D6","\u05D8\u05D6","\u05D8\u05D5","\u05D9","\u05D8","\u05D7","\u05D6","\u05D5","\u05D4","\u05D3","\u05D2","\u05D1","\u05D0"]},Gh={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["\u10F5","\u10F0","\u10EF","\u10F4","\u10EE","\u10ED","\u10EC","\u10EB","\u10EA","\u10E9","\u10E8","\u10E7","\u10E6","\u10E5","\u10E4","\u10F3","\u10E2","\u10E1","\u10E0","\u10DF","\u10DE","\u10DD","\u10F2","\u10DC","\u10DB","\u10DA","\u10D9","\u10D8","\u10D7","\u10F1","\u10D6","\u10D5","\u10D4","\u10D3","\u10D2","\u10D1","\u10D0"]},Xe=function(e,A,i,t,o,c){return e<A||e>i?yt(e,o,c.length>0):t.integers.reduce(function(d,B,u){for(;e>=B;)e-=B,d+=t.values[u];return d},"")+c},Wn=function(e,A,i,t){var o="";do i||e--,o=t(e)+o,e/=A;while(e*A>=A);return o},lA=function(e,A,i,t,o){var c=i-A+1;return(e<0?"-":"")+(Wn(Math.abs(e),c,t,function(d){return f(Math.floor(d%c)+A)})+o)},He=function(e,A,i){i===void 0&&(i=". ");var t=A.length;return Wn(Math.abs(e),t,!1,function(o){return A[Math.floor(o%t)]})+i},Je=1,Be=2,pe=4,Et=8,te=function(e,A,i,t,o,c){if(e<-9999||e>9999)return yt(e,4,o.length>0);var d=Math.abs(e),B=o;if(d===0)return A[0]+B;for(var u=0;d>0&&u<=4;u++){var C=d%10;C===0&&BA(c,Je)&&B!==""?B=A[C]+B:C>1||C===1&&u===0||C===1&&u===1&&BA(c,Be)||C===1&&u===1&&BA(c,pe)&&e>100||C===1&&u>1&&BA(c,Et)?B=A[C]+(u>0?i[u-1]:"")+B:C===1&&u>0&&(B=i[u-1]+B),d=Math.floor(d/10)}return(e<0?t:"")+B},Yn="\u5341\u767E\u5343\u842C",Zn="\u62FE\u4F70\u4EDF\u842C",$n="\u30DE\u30A4\u30CA\u30B9",Wr="\uB9C8\uC774\uB108\uC2A4",yt=function(e,A,i){var t=i?". ":"",o=i?"\u3001":"",c=i?", ":"",d=i?" ":"";switch(A){case 0:return"\u2022"+d;case 1:return"\u25E6"+d;case 2:return"\u25FE"+d;case 5:var B=lA(e,48,57,!0,t);return B.length<4?"0"+B:B;case 4:return He(e,"\u3007\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D",o);case 6:return Xe(e,1,3999,Xn,3,t).toLowerCase();case 7:return Xe(e,1,3999,Xn,3,t);case 8:return lA(e,945,969,!1,t);case 9:return lA(e,97,122,!1,t);case 10:return lA(e,65,90,!1,t);case 11:return lA(e,1632,1641,!0,t);case 12:case 49:return Xe(e,1,9999,Jn,3,t);case 35:return Xe(e,1,9999,Jn,3,t).toLowerCase();case 13:return lA(e,2534,2543,!0,t);case 14:case 30:return lA(e,6112,6121,!0,t);case 15:return He(e,"\u5B50\u4E11\u5BC5\u536F\u8FB0\u5DF3\u5348\u672A\u7533\u9149\u620C\u4EA5",o);case 16:return He(e,"\u7532\u4E59\u4E19\u4E01\u620A\u5DF1\u5E9A\u8F9B\u58EC\u7678",o);case 17:case 48:return te(e,"\u96F6\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D",Yn,"\u8CA0",o,Be|pe|Et);case 47:return te(e,"\u96F6\u58F9\u8CB3\u53C3\u8086\u4F0D\u9678\u67D2\u634C\u7396",Zn,"\u8CA0",o,Je|Be|pe|Et);case 42:return te(e,"\u96F6\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D",Yn,"\u8D1F",o,Be|pe|Et);case 41:return te(e,"\u96F6\u58F9\u8D30\u53C1\u8086\u4F0D\u9646\u67D2\u634C\u7396",Zn,"\u8D1F",o,Je|Be|pe|Et);case 26:return te(e,"\u3007\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D","\u5341\u767E\u5343\u4E07",$n,o,0);case 25:return te(e,"\u96F6\u58F1\u5F10\u53C2\u56DB\u4F0D\u516D\u4E03\u516B\u4E5D","\u62FE\u767E\u5343\u4E07",$n,o,Je|Be|pe);case 31:return te(e,"\uC601\uC77C\uC774\uC0BC\uC0AC\uC624\uC721\uCE60\uD314\uAD6C","\uC2ED\uBC31\uCC9C\uB9CC",Wr,c,Je|Be|pe);case 33:return te(e,"\u96F6\u4E00\u4E8C\u4E09\u56DB\u4E94\u516D\u4E03\u516B\u4E5D","\u5341\u767E\u5343\u842C",Wr,c,0);case 32:return te(e,"\u96F6\u58F9\u8CB3\u53C3\u56DB\u4E94\u516D\u4E03\u516B\u4E5D","\u62FE\u767E\u5343",Wr,c,Je|Be|pe);case 18:return lA(e,2406,2415,!0,t);case 20:return Xe(e,1,19999,Gh,3,t);case 21:return lA(e,2790,2799,!0,t);case 22:return lA(e,2662,2671,!0,t);case 22:return Xe(e,1,10999,Nh,3,t);case 23:return He(e,"\u3042\u3044\u3046\u3048\u304A\u304B\u304D\u304F\u3051\u3053\u3055\u3057\u3059\u305B\u305D\u305F\u3061\u3064\u3066\u3068\u306A\u306B\u306C\u306D\u306E\u306F\u3072\u3075\u3078\u307B\u307E\u307F\u3080\u3081\u3082\u3084\u3086\u3088\u3089\u308A\u308B\u308C\u308D\u308F\u3090\u3091\u3092\u3093");case 24:return He(e,"\u3044\u308D\u306F\u306B\u307B\u3078\u3068\u3061\u308A\u306C\u308B\u3092\u308F\u304B\u3088\u305F\u308C\u305D\u3064\u306D\u306A\u3089\u3080\u3046\u3090\u306E\u304A\u304F\u3084\u307E\u3051\u3075\u3053\u3048\u3066\u3042\u3055\u304D\u3086\u3081\u307F\u3057\u3091\u3072\u3082\u305B\u3059");case 27:return lA(e,3302,3311,!0,t);case 28:return He(e,"\u30A2\u30A4\u30A6\u30A8\u30AA\u30AB\u30AD\u30AF\u30B1\u30B3\u30B5\u30B7\u30B9\u30BB\u30BD\u30BF\u30C1\u30C4\u30C6\u30C8\u30CA\u30CB\u30CC\u30CD\u30CE\u30CF\u30D2\u30D5\u30D8\u30DB\u30DE\u30DF\u30E0\u30E1\u30E2\u30E4\u30E6\u30E8\u30E9\u30EA\u30EB\u30EC\u30ED\u30EF\u30F0\u30F1\u30F2\u30F3",o);case 29:return He(e,"\u30A4\u30ED\u30CF\u30CB\u30DB\u30D8\u30C8\u30C1\u30EA\u30CC\u30EB\u30F2\u30EF\u30AB\u30E8\u30BF\u30EC\u30BD\u30C4\u30CD\u30CA\u30E9\u30E0\u30A6\u30F0\u30CE\u30AA\u30AF\u30E4\u30DE\u30B1\u30D5\u30B3\u30A8\u30C6\u30A2\u30B5\u30AD\u30E6\u30E1\u30DF\u30B7\u30F1\u30D2\u30E2\u30BB\u30B9",o);case 34:return lA(e,3792,3801,!0,t);case 37:return lA(e,6160,6169,!0,t);case 38:return lA(e,4160,4169,!0,t);case 39:return lA(e,2918,2927,!0,t);case 40:return lA(e,1776,1785,!0,t);case 43:return lA(e,3046,3055,!0,t);case 44:return lA(e,3174,3183,!0,t);case 45:return lA(e,3664,3673,!0,t);case 46:return lA(e,3872,3881,!0,t);case 3:default:return lA(e,48,57,!0,t)}},qn="data-html2canvas-ignore",jn=function(){function e(A,i,t){if(this.context=A,this.options=t,this.scrolledElements=[],this.referenceElement=i,this.counters=new Rh,this.quoteDepth=0,!i.ownerDocument)throw new Error("Cloned element does not have an owner document");this.documentElement=this.cloneNode(i.ownerDocument.documentElement,!1)}return e.prototype.toIFrame=function(A,i){var t=this,o=Vh(A,i);if(!o.contentWindow)return Promise.reject("Unable to find iframe window");var c=A.defaultView.pageXOffset,d=A.defaultView.pageYOffset,B=o.contentWindow,u=B.document,C=Jh(o).then(function(){return n(t,void 0,void 0,function(){var v,F;return a(this,function(E){switch(E.label){case 0:return this.scrolledElements.forEach($h),B&&(B.scrollTo(i.left,i.top),/(iPad|iPhone|iPod)/g.test(navigator.userAgent)&&(B.scrollY!==i.top||B.scrollX!==i.left)&&(this.context.logger.warn("Unable to restore scroll position for cloned document"),this.context.windowBounds=this.context.windowBounds.add(B.scrollX-i.left,B.scrollY-i.top,0,0))),v=this.options.onclone,F=this.clonedReferenceElement,typeof F=="undefined"?[2,Promise.reject("Error finding the "+this.referenceElement.nodeName+" in the cloned document")]:u.fonts&&u.fonts.ready?[4,u.fonts.ready]:[3,2];case 1:E.sent(),E.label=2;case 2:return/(AppleWebKit)/g.test(navigator.userAgent)?[4,Xh(u)]:[3,4];case 3:E.sent(),E.label=4;case 4:return typeof v=="function"?[2,Promise.resolve().then(function(){return v(u,F)}).then(function(){return o})]:[2,o]}})})});return u.open(),u.write(Yh(document.doctype)+"<html></html>"),Zh(this.referenceElement.ownerDocument,c,d),u.replaceChild(u.adoptNode(this.documentElement),u.documentElement),u.close(),C},e.prototype.createElementClone=function(A){if(br(A,2))debugger;if(On(A))return this.createCanvasClone(A);if(Rn(A))return this.createVideoClone(A);if(Gn(A))return this.createStyleClone(A);var i=A.cloneNode(!1);return Jr(i)&&(Jr(A)&&A.currentSrc&&A.currentSrc!==A.src&&(i.src=A.currentSrc,i.srcset=""),i.loading==="lazy"&&(i.loading="eager")),_n(i)?this.createCustomElementClone(i):i},e.prototype.createCustomElementClone=function(A){var i=document.createElement("html2canvascustomelement");return Yr(A.style,i),i},e.prototype.createStyleClone=function(A){try{var i=A.sheet;if(i&&i.cssRules){var t=[].slice.call(i.cssRules,0).reduce(function(c,d){return d&&typeof d.cssText=="string"?c+d.cssText:c},""),o=A.cloneNode(!1);return o.textContent=t,o}}catch(c){if(this.context.logger.error("Unable to access cssRules property",c),c.name!=="SecurityError")throw c}return A.cloneNode(!1)},e.prototype.createCanvasClone=function(A){var i;if(this.options.inlineImages&&A.ownerDocument){var t=A.ownerDocument.createElement("img");try{return t.src=A.toDataURL(),t}catch(C){this.context.logger.info("Unable to inline canvas contents, canvas is tainted",A)}}var o=A.cloneNode(!1);try{o.width=A.width,o.height=A.height;var c=A.getContext("2d"),d=o.getContext("2d");if(d)if(!this.options.allowTaint&&c)d.putImageData(c.getImageData(0,0,A.width,A.height),0,0);else{var B=(i=A.getContext("webgl2"))!==null&&i!==void 0?i:A.getContext("webgl");if(B){var u=B.getContextAttributes();(u==null?void 0:u.preserveDrawingBuffer)===!1&&this.context.logger.warn("Unable to clone WebGL context as it has preserveDrawingBuffer=false",A)}d.drawImage(A,0,0)}return o}catch(C){this.context.logger.info("Unable to clone canvas as it is tainted",A)}return o},e.prototype.createVideoClone=function(A){var i=A.ownerDocument.createElement("canvas");i.width=A.offsetWidth,i.height=A.offsetHeight;var t=i.getContext("2d");try{return t&&(t.drawImage(A,0,0,i.width,i.height),this.options.allowTaint||t.getImageData(0,0,i.width,i.height)),i}catch(c){this.context.logger.info("Unable to clone video as it is tainted",A)}var o=A.ownerDocument.createElement("canvas");return o.width=A.offsetWidth,o.height=A.offsetHeight,o},e.prototype.appendChildNode=function(A,i,t){(!_e(i)||!Oh(i)&&!i.hasAttribute(qn)&&(typeof this.options.ignoreElements!="function"||!this.options.ignoreElements(i)))&&(!this.options.copyStyles||!_e(i)||!Gn(i))&&A.appendChild(this.cloneNode(i,t))},e.prototype.cloneChildNodes=function(A,i,t){for(var o=this,c=A.shadowRoot?A.shadowRoot.firstChild:A.firstChild;c;c=c.nextSibling)if(_e(c)&&Vn(c)&&typeof c.assignedNodes=="function"){var d=c.assignedNodes();d.length&&d.forEach(function(B){return o.appendChildNode(i,B,t)})}else this.appendChildNode(i,c,t)},e.prototype.cloneNode=function(A,i){if(Pn(A))return document.createTextNode(A.data);if(!A.ownerDocument)return A.cloneNode(!1);var t=A.ownerDocument.defaultView;if(t&&_e(A)&&(_r(A)||pi(A))){var o=this.createElementClone(A);o.style.transitionProperty="none";var c=t.getComputedStyle(A),d=t.getComputedStyle(A,":before"),B=t.getComputedStyle(A,":after");this.referenceElement===A&&_r(o)&&(this.clonedReferenceElement=o),Xr(o)&&zh(o);var u=this.counters.parse(new dn(this.context,c)),C=this.resolvePseudoContent(A,o,d,It.BEFORE);_n(A)&&(i=!0),Rn(A)||this.cloneChildNodes(A,o,i),C&&o.insertBefore(C,o.firstChild);var v=this.resolvePseudoContent(A,o,B,It.AFTER);return v&&o.appendChild(v),this.counters.pop(u),(c&&(this.options.copyStyles||pi(A))&&!Nn(A)||i)&&Yr(c,o),(A.scrollTop!==0||A.scrollLeft!==0)&&this.scrolledElements.push([o,A.scrollLeft,A.scrollTop]),(mi(A)||fi(A))&&(mi(o)||fi(o))&&(o.value=A.value),o}return A.cloneNode(!1)},e.prototype.resolvePseudoContent=function(A,i,t,o){var c=this;if(t){var d=t.content,B=i.ownerDocument;if(!(!B||!d||d==="none"||d==="-moz-alt-content"||t.display==="none")){this.counters.parse(new dn(this.context,t));var u=new Dc(this.context,t),C=B.createElement("html2canvaspseudoelement");Yr(t,C),u.content.forEach(function(F){if(F.type===0)C.appendChild(B.createTextNode(F.value));else if(F.type===22){var E=B.createElement("img");E.src=F.value,E.style.opacity="1",C.appendChild(E)}else if(F.type===18){if(F.name==="attr"){var K=F.values.filter(eA);K.length&&C.appendChild(B.createTextNode(A.getAttribute(K[0].value)||""))}else if(F.name==="counter"){var x=F.values.filter(Ge),L=x[0],J=x[1];if(L&&eA(L)){var O=c.counters.getCounterValue(L.value),k=J&&eA(J)?Sr.parse(c.context,J.value):3;C.appendChild(B.createTextNode(yt(O,k,!1)))}}else if(F.name==="counters"){var z=F.values.filter(Ge),L=z[0],Y=z[1],J=z[2];if(L&&eA(L)){var G=c.counters.getCounterValues(L.value),D=J&&eA(J)?Sr.parse(c.context,J.value):3,q=Y&&Y.type===0?Y.value:"",j=G.map(function(SA){return yt(SA,D,!1)}).join(q);C.appendChild(B.createTextNode(j))}}}else if(F.type===20)switch(F.value){case"open-quote":C.appendChild(B.createTextNode(hn(u.quotes,c.quoteDepth++,!0)));break;case"close-quote":C.appendChild(B.createTextNode(hn(u.quotes,--c.quoteDepth,!1)));break;default:C.appendChild(B.createTextNode(F.value))}}),C.className=Zr+" "+$r;var v=o===It.BEFORE?" "+Zr:" "+$r;return pi(i)?i.className.baseValue+=v:i.className+=v,C}}},e.destroy=function(A){return A.parentNode?(A.parentNode.removeChild(A),!0):!1},e}(),It;(function(e){e[e.BEFORE=0]="BEFORE",e[e.AFTER=1]="AFTER"})(It||(It={}));var Vh=function(e,A){var i=e.createElement("iframe");return i.className="html2canvas-container",i.style.visibility="hidden",i.style.position="fixed",i.style.left="-10000px",i.style.top="0px",i.style.border="0",i.width=A.width.toString(),i.height=A.height.toString(),i.scrolling="no",i.setAttribute(qn,"true"),e.body.appendChild(i),i},_h=function(e){return new Promise(function(A){if(e.complete){A();return}if(!e.src){A();return}e.onload=A,e.onerror=A})},Xh=function(e){return Promise.all([].slice.call(e.images,0).map(_h))},Jh=function(e){return new Promise(function(A,i){var t=e.contentWindow;if(!t)return i("No window assigned for iframe");var o=t.document;t.onload=e.onload=function(){t.onload=e.onload=null;var c=setInterval(function(){o.body.childNodes.length>0&&o.readyState==="complete"&&(clearInterval(c),A(e))},50)}})},Wh=["all","d","content"],Yr=function(e,A){for(var i=e.length-1;i>=0;i--){var t=e.item(i);Wh.indexOf(t)===-1&&A.style.setProperty(t,e.getPropertyValue(t))}return A},Yh=function(e){var A="";return e&&(A+="<!DOCTYPE ",e.name&&(A+=e.name),e.internalSubset&&(A+=e.internalSubset),e.publicId&&(A+='"'+e.publicId+'"'),e.systemId&&(A+='"'+e.systemId+'"'),A+=">"),A},Zh=function(e,A,i){e&&e.defaultView&&(A!==e.defaultView.pageXOffset||i!==e.defaultView.pageYOffset)&&e.defaultView.scrollTo(A,i)},$h=function(e){var A=e[0],i=e[1],t=e[2];A.scrollLeft=i,A.scrollTop=t},qh=":before",jh=":after",Zr="___html2canvas___pseudoelement_before",$r="___html2canvas___pseudoelement_after",zn=`{
    content: "" !important;
    display: none !important;
}`,zh=function(e){Ad(e,"."+Zr+qh+zn+`
         .`+$r+jh+zn)},Ad=function(e,A){var i=e.ownerDocument;if(i){var t=i.createElement("style");t.textContent=A,e.appendChild(t)}},Aa=function(){function e(){}return e.getOrigin=function(A){var i=e._link;return i?(i.href=A,i.href=i.href,i.protocol+i.hostname+i.port):"about:blank"},e.isSameOrigin=function(A){return e.getOrigin(A)===e._origin},e.setContext=function(A){e._link=A.document.createElement("a"),e._origin=e.getOrigin(A.location.href)},e._origin="about:blank",e}(),ed=function(){function e(A,i){this.context=A,this._options=i,this._cache={}}return e.prototype.addImage=function(A){var i=Promise.resolve();return this.has(A)||(jr(A)||sd(A))&&(this._cache[A]=this.loadImage(A)).catch(function(){}),i},e.prototype.match=function(A){return this._cache[A]},e.prototype.loadImage=function(A){return n(this,void 0,void 0,function(){var i,t,o,c,d=this;return a(this,function(B){switch(B.label){case 0:return i=Aa.isSameOrigin(A),t=!qr(A)&&this._options.useCORS===!0&&wA.SUPPORT_CORS_IMAGES&&!i,o=!qr(A)&&!i&&!jr(A)&&typeof this._options.proxy=="string"&&wA.SUPPORT_CORS_XHR&&!t,!i&&this._options.allowTaint===!1&&!qr(A)&&!jr(A)&&!o&&!t?[2]:(c=A,o?[4,this.proxy(c)]:[3,2]);case 1:c=B.sent(),B.label=2;case 2:return this.context.logger.debug("Added image "+A.substring(0,256)),[4,new Promise(function(u,C){var v=new Image;v.onload=function(){return u(v)},v.onerror=C,(nd(c)||t)&&(v.crossOrigin="anonymous"),v.src=c,v.complete===!0&&setTimeout(function(){return u(v)},500),d._options.imageTimeout>0&&setTimeout(function(){return C("Timed out ("+d._options.imageTimeout+"ms) loading image")},d._options.imageTimeout)})];case 3:return[2,B.sent()]}})})},e.prototype.has=function(A){return typeof this._cache[A]!="undefined"},e.prototype.keys=function(){return Promise.resolve(Object.keys(this._cache))},e.prototype.proxy=function(A){var i=this,t=this._options.proxy;if(!t)throw new Error("No proxy defined");var o=A.substring(0,256);return new Promise(function(c,d){var B=wA.SUPPORT_RESPONSE_TYPE?"blob":"text",u=new XMLHttpRequest;u.onload=function(){if(u.status===200)if(B==="text")c(u.response);else{var F=new FileReader;F.addEventListener("load",function(){return c(F.result)},!1),F.addEventListener("error",function(E){return d(E)},!1),F.readAsDataURL(u.response)}else d("Failed to proxy resource "+o+" with status code "+u.status)},u.onerror=d;var C=t.indexOf("?")>-1?"&":"?";if(u.open("GET",""+t+C+"url="+encodeURIComponent(A)+"&responseType="+B),B!=="text"&&u instanceof XMLHttpRequest&&(u.responseType=B),i._options.imageTimeout){var v=i._options.imageTimeout;u.timeout=v,u.ontimeout=function(){return d("Timed out ("+v+"ms) proxying "+o)}}u.send()})},e}(),td=/^data:image\/svg\+xml/i,id=/^data:image\/.*;base64,/i,rd=/^data:image\/.*/i,sd=function(e){return wA.SUPPORT_SVG_DRAWING||!ad(e)},qr=function(e){return rd.test(e)},nd=function(e){return id.test(e)},jr=function(e){return e.substr(0,4)==="blob"},ad=function(e){return e.substr(-3).toLowerCase()==="svg"||td.test(e)},T=function(){function e(A,i){this.type=0,this.x=A,this.y=i}return e.prototype.add=function(A,i){return new e(this.x+A,this.y+i)},e}(),We=function(e,A,i){return new T(e.x+(A.x-e.x)*i,e.y+(A.y-e.y)*i)},wi=function(){function e(A,i,t,o){this.type=1,this.start=A,this.startControl=i,this.endControl=t,this.end=o}return e.prototype.subdivide=function(A,i){var t=We(this.start,this.startControl,A),o=We(this.startControl,this.endControl,A),c=We(this.endControl,this.end,A),d=We(t,o,A),B=We(o,c,A),u=We(d,B,A);return i?new e(this.start,t,d,u):new e(u,B,c,this.end)},e.prototype.add=function(A,i){return new e(this.start.add(A,i),this.startControl.add(A,i),this.endControl.add(A,i),this.end.add(A,i))},e.prototype.reverse=function(){return new e(this.end,this.endControl,this.startControl,this.start)},e}(),NA=function(e){return e.type===1},od=function(){function e(A){var i=A.styles,t=A.bounds,o=Ct(i.borderTopLeftRadius,t.width,t.height),c=o[0],d=o[1],B=Ct(i.borderTopRightRadius,t.width,t.height),u=B[0],C=B[1],v=Ct(i.borderBottomRightRadius,t.width,t.height),F=v[0],E=v[1],K=Ct(i.borderBottomLeftRadius,t.width,t.height),x=K[0],L=K[1],J=[];J.push((c+u)/t.width),J.push((x+F)/t.width),J.push((d+L)/t.height),J.push((C+E)/t.height);var O=Math.max.apply(Math,J);O>1&&(c/=O,d/=O,u/=O,C/=O,F/=O,E/=O,x/=O,L/=O);var k=t.width-u,z=t.height-E,Y=t.width-F,G=t.height-L,D=i.borderTopWidth,q=i.borderRightWidth,j=i.borderBottomWidth,X=i.borderLeftWidth,dA=iA(i.paddingTop,A.bounds.width),SA=iA(i.paddingRight,A.bounds.width),TA=iA(i.paddingBottom,A.bounds.width),tA=iA(i.paddingLeft,A.bounds.width);this.topLeftBorderDoubleOuterBox=c>0||d>0?nA(t.left+X/3,t.top+D/3,c-X/3,d-D/3,AA.TOP_LEFT):new T(t.left+X/3,t.top+D/3),this.topRightBorderDoubleOuterBox=c>0||d>0?nA(t.left+k,t.top+D/3,u-q/3,C-D/3,AA.TOP_RIGHT):new T(t.left+t.width-q/3,t.top+D/3),this.bottomRightBorderDoubleOuterBox=F>0||E>0?nA(t.left+Y,t.top+z,F-q/3,E-j/3,AA.BOTTOM_RIGHT):new T(t.left+t.width-q/3,t.top+t.height-j/3),this.bottomLeftBorderDoubleOuterBox=x>0||L>0?nA(t.left+X/3,t.top+G,x-X/3,L-j/3,AA.BOTTOM_LEFT):new T(t.left+X/3,t.top+t.height-j/3),this.topLeftBorderDoubleInnerBox=c>0||d>0?nA(t.left+X*2/3,t.top+D*2/3,c-X*2/3,d-D*2/3,AA.TOP_LEFT):new T(t.left+X*2/3,t.top+D*2/3),this.topRightBorderDoubleInnerBox=c>0||d>0?nA(t.left+k,t.top+D*2/3,u-q*2/3,C-D*2/3,AA.TOP_RIGHT):new T(t.left+t.width-q*2/3,t.top+D*2/3),this.bottomRightBorderDoubleInnerBox=F>0||E>0?nA(t.left+Y,t.top+z,F-q*2/3,E-j*2/3,AA.BOTTOM_RIGHT):new T(t.left+t.width-q*2/3,t.top+t.height-j*2/3),this.bottomLeftBorderDoubleInnerBox=x>0||L>0?nA(t.left+X*2/3,t.top+G,x-X*2/3,L-j*2/3,AA.BOTTOM_LEFT):new T(t.left+X*2/3,t.top+t.height-j*2/3),this.topLeftBorderStroke=c>0||d>0?nA(t.left+X/2,t.top+D/2,c-X/2,d-D/2,AA.TOP_LEFT):new T(t.left+X/2,t.top+D/2),this.topRightBorderStroke=c>0||d>0?nA(t.left+k,t.top+D/2,u-q/2,C-D/2,AA.TOP_RIGHT):new T(t.left+t.width-q/2,t.top+D/2),this.bottomRightBorderStroke=F>0||E>0?nA(t.left+Y,t.top+z,F-q/2,E-j/2,AA.BOTTOM_RIGHT):new T(t.left+t.width-q/2,t.top+t.height-j/2),this.bottomLeftBorderStroke=x>0||L>0?nA(t.left+X/2,t.top+G,x-X/2,L-j/2,AA.BOTTOM_LEFT):new T(t.left+X/2,t.top+t.height-j/2),this.topLeftBorderBox=c>0||d>0?nA(t.left,t.top,c,d,AA.TOP_LEFT):new T(t.left,t.top),this.topRightBorderBox=u>0||C>0?nA(t.left+k,t.top,u,C,AA.TOP_RIGHT):new T(t.left+t.width,t.top),this.bottomRightBorderBox=F>0||E>0?nA(t.left+Y,t.top+z,F,E,AA.BOTTOM_RIGHT):new T(t.left+t.width,t.top+t.height),this.bottomLeftBorderBox=x>0||L>0?nA(t.left,t.top+G,x,L,AA.BOTTOM_LEFT):new T(t.left,t.top+t.height),this.topLeftPaddingBox=c>0||d>0?nA(t.left+X,t.top+D,Math.max(0,c-X),Math.max(0,d-D),AA.TOP_LEFT):new T(t.left+X,t.top+D),this.topRightPaddingBox=u>0||C>0?nA(t.left+Math.min(k,t.width-q),t.top+D,k>t.width+q?0:Math.max(0,u-q),Math.max(0,C-D),AA.TOP_RIGHT):new T(t.left+t.width-q,t.top+D),this.bottomRightPaddingBox=F>0||E>0?nA(t.left+Math.min(Y,t.width-X),t.top+Math.min(z,t.height-j),Math.max(0,F-q),Math.max(0,E-j),AA.BOTTOM_RIGHT):new T(t.left+t.width-q,t.top+t.height-j),this.bottomLeftPaddingBox=x>0||L>0?nA(t.left+X,t.top+Math.min(G,t.height-j),Math.max(0,x-X),Math.max(0,L-j),AA.BOTTOM_LEFT):new T(t.left+X,t.top+t.height-j),this.topLeftContentBox=c>0||d>0?nA(t.left+X+tA,t.top+D+dA,Math.max(0,c-(X+tA)),Math.max(0,d-(D+dA)),AA.TOP_LEFT):new T(t.left+X+tA,t.top+D+dA),this.topRightContentBox=u>0||C>0?nA(t.left+Math.min(k,t.width+X+tA),t.top+D+dA,k>t.width+X+tA?0:u-X+tA,C-(D+dA),AA.TOP_RIGHT):new T(t.left+t.width-(q+SA),t.top+D+dA),this.bottomRightContentBox=F>0||E>0?nA(t.left+Math.min(Y,t.width-(X+tA)),t.top+Math.min(z,t.height+D+dA),Math.max(0,F-(q+SA)),E-(j+TA),AA.BOTTOM_RIGHT):new T(t.left+t.width-(q+SA),t.top+t.height-(j+TA)),this.bottomLeftContentBox=x>0||L>0?nA(t.left+X+tA,t.top+G,Math.max(0,x-(X+tA)),L-(j+TA),AA.BOTTOM_LEFT):new T(t.left+X+tA,t.top+t.height-(j+TA))}return e}(),AA;(function(e){e[e.TOP_LEFT=0]="TOP_LEFT",e[e.TOP_RIGHT=1]="TOP_RIGHT",e[e.BOTTOM_RIGHT=2]="BOTTOM_RIGHT",e[e.BOTTOM_LEFT=3]="BOTTOM_LEFT"})(AA||(AA={}));var nA=function(e,A,i,t,o){var c=4*((Math.sqrt(2)-1)/3),d=i*c,B=t*c,u=e+i,C=A+t;switch(o){case AA.TOP_LEFT:return new wi(new T(e,C),new T(e,C-B),new T(u-d,A),new T(u,A));case AA.TOP_RIGHT:return new wi(new T(e,A),new T(e+d,A),new T(u,C-B),new T(u,C));case AA.BOTTOM_RIGHT:return new wi(new T(u,A),new T(u,A+B),new T(e+d,C),new T(e,C));case AA.BOTTOM_LEFT:default:return new wi(new T(u,C),new T(u-d,C),new T(e,A+B),new T(e,A))}},Ci=function(e){return[e.topLeftBorderBox,e.topRightBorderBox,e.bottomRightBorderBox,e.bottomLeftBorderBox]},ld=function(e){return[e.topLeftContentBox,e.topRightContentBox,e.bottomRightContentBox,e.bottomLeftContentBox]},vi=function(e){return[e.topLeftPaddingBox,e.topRightPaddingBox,e.bottomRightPaddingBox,e.bottomLeftPaddingBox]},cd=function(){function e(A,i,t){this.offsetX=A,this.offsetY=i,this.matrix=t,this.type=0,this.target=6}return e}(),Qi=function(){function e(A,i){this.path=A,this.target=i,this.type=1}return e}(),hd=function(){function e(A){this.opacity=A,this.type=2,this.target=6}return e}(),dd=function(e){return e.type===0},ea=function(e){return e.type===1},gd=function(e){return e.type===2},ta=function(e,A){return e.length===A.length?e.some(function(i,t){return i===A[t]}):!1},ud=function(e,A,i,t,o){return e.map(function(c,d){switch(d){case 0:return c.add(A,i);case 1:return c.add(A+t,i);case 2:return c.add(A+t,i+o);case 3:return c.add(A,i+o)}return c})},ia=function(){function e(A){this.element=A,this.inlineLevel=[],this.nonInlineLevel=[],this.negativeZIndex=[],this.zeroOrAutoZIndexOrTransformedOrOpacity=[],this.positiveZIndex=[],this.nonPositionedFloats=[],this.nonPositionedInlineLevel=[]}return e}(),ra=function(){function e(A,i){if(this.container=A,this.parent=i,this.effects=[],this.curves=new od(this.container),this.container.styles.opacity<1&&this.effects.push(new hd(this.container.styles.opacity)),this.container.styles.transform!==null){var t=this.container.bounds.left+this.container.styles.transformOrigin[0].number,o=this.container.bounds.top+this.container.styles.transformOrigin[1].number,c=this.container.styles.transform;this.effects.push(new cd(t,o,c))}if(this.container.styles.overflowX!==0){var d=Ci(this.curves),B=vi(this.curves);ta(d,B)?this.effects.push(new Qi(d,6)):(this.effects.push(new Qi(d,2)),this.effects.push(new Qi(B,4)))}}return e.prototype.getEffects=function(A){for(var i=[2,3].indexOf(this.container.styles.position)===-1,t=this.parent,o=this.effects.slice(0);t;){var c=t.effects.filter(function(u){return!ea(u)});if(i||t.container.styles.position!==0||!t.parent){if(o.unshift.apply(o,c),i=[2,3].indexOf(t.container.styles.position)===-1,t.container.styles.overflowX!==0){var d=Ci(t.curves),B=vi(t.curves);ta(d,B)||o.unshift(new Qi(B,6))}}else o.unshift.apply(o,c);t=t.parent}return o.filter(function(u){return BA(u.target,A)})},e}(),zr=function(e,A,i,t){e.container.elements.forEach(function(o){var c=BA(o.flags,4),d=BA(o.flags,2),B=new ra(o,e);BA(o.styles.display,2048)&&t.push(B);var u=BA(o.flags,8)?[]:t;if(c||d){var C=c||o.styles.isPositioned()?i:A,v=new ia(B);if(o.styles.isPositioned()||o.styles.opacity<1||o.styles.isTransformed()){var F=o.styles.zIndex.order;if(F<0){var E=0;C.negativeZIndex.some(function(x,L){return F>x.element.container.styles.zIndex.order?(E=L,!1):E>0}),C.negativeZIndex.splice(E,0,v)}else if(F>0){var K=0;C.positiveZIndex.some(function(x,L){return F>=x.element.container.styles.zIndex.order?(K=L+1,!1):K>0}),C.positiveZIndex.splice(K,0,v)}else C.zeroOrAutoZIndexOrTransformedOrOpacity.push(v)}else o.styles.isFloating()?C.nonPositionedFloats.push(v):C.nonPositionedInlineLevel.push(v);zr(B,v,c?v:i,u)}else o.styles.isInlineLevel()?A.inlineLevel.push(B):A.nonInlineLevel.push(B),zr(B,A,i,u);BA(o.flags,8)&&sa(o,u)})},sa=function(e,A){for(var i=e instanceof Gr?e.start:1,t=e instanceof Gr?e.reversed:!1,o=0;o<A.length;o++){var c=A[o];c.container instanceof bn&&typeof c.container.value=="number"&&c.container.value!==0&&(i=c.container.value),c.listValue=yt(i,c.container.styles.listStyleType,!0),i+=t?-1:1}},Bd=function(e){var A=new ra(e,null),i=new ia(A),t=[];return zr(A,i,i,t),sa(A.container,t),i},na=function(e,A){switch(A){case 0:return GA(e.topLeftBorderBox,e.topLeftPaddingBox,e.topRightBorderBox,e.topRightPaddingBox);case 1:return GA(e.topRightBorderBox,e.topRightPaddingBox,e.bottomRightBorderBox,e.bottomRightPaddingBox);case 2:return GA(e.bottomRightBorderBox,e.bottomRightPaddingBox,e.bottomLeftBorderBox,e.bottomLeftPaddingBox);case 3:default:return GA(e.bottomLeftBorderBox,e.bottomLeftPaddingBox,e.topLeftBorderBox,e.topLeftPaddingBox)}},pd=function(e,A){switch(A){case 0:return GA(e.topLeftBorderBox,e.topLeftBorderDoubleOuterBox,e.topRightBorderBox,e.topRightBorderDoubleOuterBox);case 1:return GA(e.topRightBorderBox,e.topRightBorderDoubleOuterBox,e.bottomRightBorderBox,e.bottomRightBorderDoubleOuterBox);case 2:return GA(e.bottomRightBorderBox,e.bottomRightBorderDoubleOuterBox,e.bottomLeftBorderBox,e.bottomLeftBorderDoubleOuterBox);case 3:default:return GA(e.bottomLeftBorderBox,e.bottomLeftBorderDoubleOuterBox,e.topLeftBorderBox,e.topLeftBorderDoubleOuterBox)}},md=function(e,A){switch(A){case 0:return GA(e.topLeftBorderDoubleInnerBox,e.topLeftPaddingBox,e.topRightBorderDoubleInnerBox,e.topRightPaddingBox);case 1:return GA(e.topRightBorderDoubleInnerBox,e.topRightPaddingBox,e.bottomRightBorderDoubleInnerBox,e.bottomRightPaddingBox);case 2:return GA(e.bottomRightBorderDoubleInnerBox,e.bottomRightPaddingBox,e.bottomLeftBorderDoubleInnerBox,e.bottomLeftPaddingBox);case 3:default:return GA(e.bottomLeftBorderDoubleInnerBox,e.bottomLeftPaddingBox,e.topLeftBorderDoubleInnerBox,e.topLeftPaddingBox)}},fd=function(e,A){switch(A){case 0:return Fi(e.topLeftBorderStroke,e.topRightBorderStroke);case 1:return Fi(e.topRightBorderStroke,e.bottomRightBorderStroke);case 2:return Fi(e.bottomRightBorderStroke,e.bottomLeftBorderStroke);case 3:default:return Fi(e.bottomLeftBorderStroke,e.topLeftBorderStroke)}},Fi=function(e,A){var i=[];return NA(e)?i.push(e.subdivide(.5,!1)):i.push(e),NA(A)?i.push(A.subdivide(.5,!0)):i.push(A),i},GA=function(e,A,i,t){var o=[];return NA(e)?o.push(e.subdivide(.5,!1)):o.push(e),NA(i)?o.push(i.subdivide(.5,!0)):o.push(i),NA(t)?o.push(t.subdivide(.5,!0).reverse()):o.push(t),NA(A)?o.push(A.subdivide(.5,!1).reverse()):o.push(A),o},aa=function(e){var A=e.bounds,i=e.styles;return A.add(i.borderLeftWidth,i.borderTopWidth,-(i.borderRightWidth+i.borderLeftWidth),-(i.borderTopWidth+i.borderBottomWidth))},Ui=function(e){var A=e.styles,i=e.bounds,t=iA(A.paddingLeft,i.width),o=iA(A.paddingRight,i.width),c=iA(A.paddingTop,i.width),d=iA(A.paddingBottom,i.width);return i.add(t+A.borderLeftWidth,c+A.borderTopWidth,-(A.borderRightWidth+A.borderLeftWidth+t+o),-(A.borderTopWidth+A.borderBottomWidth+c+d))},wd=function(e,A){return e===0?A.bounds:e===2?Ui(A):aa(A)},Cd=function(e,A){return e===0?A.bounds:e===2?Ui(A):aa(A)},As=function(e,A,i){var t=wd(Ze(e.styles.backgroundOrigin,A),e),o=Cd(Ze(e.styles.backgroundClip,A),e),c=vd(Ze(e.styles.backgroundSize,A),i,t),d=c[0],B=c[1],u=Ct(Ze(e.styles.backgroundPosition,A),t.width-d,t.height-B),C=Qd(Ze(e.styles.backgroundRepeat,A),u,c,t,o),v=Math.round(t.left+u[0]),F=Math.round(t.top+u[1]);return[C,v,F,d,B]},Ye=function(e){return eA(e)&&e.value===Ve.AUTO},Ei=function(e){return typeof e=="number"},vd=function(e,A,i){var t=A[0],o=A[1],c=A[2],d=e[0],B=e[1];if(!d)return[0,0];if(hA(d)&&B&&hA(B))return[iA(d,i.width),iA(B,i.height)];var u=Ei(c);if(eA(d)&&(d.value===Ve.CONTAIN||d.value===Ve.COVER)){if(Ei(c)){var C=i.width/i.height;return C<c!=(d.value===Ve.COVER)?[i.width,i.width/c]:[i.height*c,i.height]}return[i.width,i.height]}var v=Ei(t),F=Ei(o),E=v||F;if(Ye(d)&&(!B||Ye(B))){if(v&&F)return[t,o];if(!u&&!E)return[i.width,i.height];if(E&&u){var K=v?t:o*c,x=F?o:t/c;return[K,x]}var L=v?t:i.width,J=F?o:i.height;return[L,J]}if(u){var O=0,k=0;return hA(d)?O=iA(d,i.width):hA(B)&&(k=iA(B,i.height)),Ye(d)?O=k*c:(!B||Ye(B))&&(k=O/c),[O,k]}var z=null,Y=null;if(hA(d)?z=iA(d,i.width):B&&hA(B)&&(Y=iA(B,i.height)),z!==null&&(!B||Ye(B))&&(Y=v&&F?z/t*o:i.height),Y!==null&&Ye(d)&&(z=v&&F?Y/o*t:i.width),z!==null&&Y!==null)return[z,Y];throw new Error("Unable to calculate background-size for element")},Ze=function(e,A){var i=e[A];return typeof i=="undefined"?e[0]:i},Qd=function(e,A,i,t,o){var c=A[0],d=A[1],B=i[0],u=i[1];switch(e){case 2:return[new T(Math.round(t.left),Math.round(t.top+d)),new T(Math.round(t.left+t.width),Math.round(t.top+d)),new T(Math.round(t.left+t.width),Math.round(u+t.top+d)),new T(Math.round(t.left),Math.round(u+t.top+d))];case 3:return[new T(Math.round(t.left+c),Math.round(t.top)),new T(Math.round(t.left+c+B),Math.round(t.top)),new T(Math.round(t.left+c+B),Math.round(t.height+t.top)),new T(Math.round(t.left+c),Math.round(t.height+t.top))];case 1:return[new T(Math.round(t.left+c),Math.round(t.top+d)),new T(Math.round(t.left+c+B),Math.round(t.top+d)),new T(Math.round(t.left+c+B),Math.round(t.top+d+u)),new T(Math.round(t.left+c),Math.round(t.top+d+u))];default:return[new T(Math.round(o.left),Math.round(o.top)),new T(Math.round(o.left+o.width),Math.round(o.top)),new T(Math.round(o.left+o.width),Math.round(o.height+o.top)),new T(Math.round(o.left),Math.round(o.height+o.top))]}},Fd="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",oa="Hidden Text",Ud=function(){function e(A){this._data={},this._document=A}return e.prototype.parseMetrics=function(A,i){var t=this._document.createElement("div"),o=this._document.createElement("img"),c=this._document.createElement("span"),d=this._document.body;t.style.visibility="hidden",t.style.fontFamily=A,t.style.fontSize=i,t.style.margin="0",t.style.padding="0",t.style.whiteSpace="nowrap",d.appendChild(t),o.src=Fd,o.width=1,o.height=1,o.style.margin="0",o.style.padding="0",o.style.verticalAlign="baseline",c.style.fontFamily=A,c.style.fontSize=i,c.style.margin="0",c.style.padding="0",c.appendChild(this._document.createTextNode(oa)),t.appendChild(c),t.appendChild(o);var B=o.offsetTop-c.offsetTop+2;t.removeChild(c),t.appendChild(this._document.createTextNode(oa)),t.style.lineHeight="normal",o.style.verticalAlign="super";var u=o.offsetTop-t.offsetTop+2;return d.removeChild(t),{baseline:B,middle:u}},e.prototype.getMetrics=function(A,i){var t=A+" "+i;return typeof this._data[t]=="undefined"&&(this._data[t]=this.parseMetrics(A,i)),this._data[t]},e}(),la=function(){function e(A,i){this.context=A,this.options=i}return e}(),Ed=1e4,yd=function(e){s(A,e);function A(i,t){var o=e.call(this,i,t)||this;return o._activeEffects=[],o.canvas=t.canvas?t.canvas:document.createElement("canvas"),o.ctx=o.canvas.getContext("2d"),t.canvas||(o.canvas.width=Math.floor(t.width*t.scale),o.canvas.height=Math.floor(t.height*t.scale),o.canvas.style.width=t.width+"px",o.canvas.style.height=t.height+"px"),o.fontMetrics=new Ud(document),o.ctx.scale(o.options.scale,o.options.scale),o.ctx.translate(-t.x,-t.y),o.ctx.textBaseline="bottom",o._activeEffects=[],o.context.logger.debug("Canvas renderer initialized ("+t.width+"x"+t.height+") with scale "+t.scale),o}return A.prototype.applyEffects=function(i){for(var t=this;this._activeEffects.length;)this.popEffect();i.forEach(function(o){return t.applyEffect(o)})},A.prototype.applyEffect=function(i){this.ctx.save(),gd(i)&&(this.ctx.globalAlpha=i.opacity),dd(i)&&(this.ctx.translate(i.offsetX,i.offsetY),this.ctx.transform(i.matrix[0],i.matrix[1],i.matrix[2],i.matrix[3],i.matrix[4],i.matrix[5]),this.ctx.translate(-i.offsetX,-i.offsetY)),ea(i)&&(this.path(i.path),this.ctx.clip()),this._activeEffects.push(i)},A.prototype.popEffect=function(){this._activeEffects.pop(),this.ctx.restore()},A.prototype.renderStack=function(i){return n(this,void 0,void 0,function(){var t;return a(this,function(o){switch(o.label){case 0:return t=i.element.container.styles,t.isVisible()?[4,this.renderStackContent(i)]:[3,2];case 1:o.sent(),o.label=2;case 2:return[2]}})})},A.prototype.renderNode=function(i){return n(this,void 0,void 0,function(){return a(this,function(t){switch(t.label){case 0:if(BA(i.container.flags,16))debugger;return i.container.styles.isVisible()?[4,this.renderNodeBackgroundAndBorders(i)]:[3,3];case 1:return t.sent(),[4,this.renderNodeContent(i)];case 2:t.sent(),t.label=3;case 3:return[2]}})})},A.prototype.renderTextWithLetterSpacing=function(i,t,o){var c=this;if(t===0)this.ctx.fillText(i.text,i.bounds.left,i.bounds.top+o);else{var d=Nr(i.text);d.reduce(function(B,u){return c.ctx.fillText(u,B,i.bounds.top+o),B+c.ctx.measureText(u).width},i.bounds.left)}},A.prototype.createFontStyle=function(i){var t=i.fontVariant.filter(function(d){return d==="normal"||d==="small-caps"}).join(""),o=xd(i.fontFamily).join(", "),c=wt(i.fontSize)?""+i.fontSize.number+i.fontSize.unit:i.fontSize.number+"px";return[[i.fontStyle,t,i.fontWeight,c,o].join(" "),o,c]},A.prototype.renderTextNode=function(i,t){return n(this,void 0,void 0,function(){var o,c,d,B,u,C,v,F,E=this;return a(this,function(K){return o=this.createFontStyle(t),c=o[0],d=o[1],B=o[2],this.ctx.font=c,this.ctx.direction=t.direction===1?"rtl":"ltr",this.ctx.textAlign="left",this.ctx.textBaseline="alphabetic",u=this.fontMetrics.getMetrics(d,B),C=u.baseline,v=u.middle,F=t.paintOrder,i.textBounds.forEach(function(x){F.forEach(function(L){switch(L){case 0:E.ctx.fillStyle=pA(t.color),E.renderTextWithLetterSpacing(x,t.letterSpacing,C);var J=t.textShadow;J.length&&x.text.trim().length&&(J.slice(0).reverse().forEach(function(O){E.ctx.shadowColor=pA(O.color),E.ctx.shadowOffsetX=O.offsetX.number*E.options.scale,E.ctx.shadowOffsetY=O.offsetY.number*E.options.scale,E.ctx.shadowBlur=O.blur.number,E.renderTextWithLetterSpacing(x,t.letterSpacing,C)}),E.ctx.shadowColor="",E.ctx.shadowOffsetX=0,E.ctx.shadowOffsetY=0,E.ctx.shadowBlur=0),t.textDecorationLine.length&&(E.ctx.fillStyle=pA(t.textDecorationColor||t.color),t.textDecorationLine.forEach(function(O){switch(O){case 1:E.ctx.fillRect(x.bounds.left,Math.round(x.bounds.top+C),x.bounds.width,1);break;case 2:E.ctx.fillRect(x.bounds.left,Math.round(x.bounds.top),x.bounds.width,1);break;case 3:E.ctx.fillRect(x.bounds.left,Math.ceil(x.bounds.top+v),x.bounds.width,1);break}}));break;case 1:t.webkitTextStrokeWidth&&x.text.trim().length&&(E.ctx.strokeStyle=pA(t.webkitTextStrokeColor),E.ctx.lineWidth=t.webkitTextStrokeWidth,E.ctx.lineJoin=window.chrome?"miter":"round",E.ctx.strokeText(x.text,x.bounds.left,x.bounds.top+C)),E.ctx.strokeStyle="",E.ctx.lineWidth=0,E.ctx.lineJoin="miter";break}})}),[2]})})},A.prototype.renderReplacedElement=function(i,t,o){if(o&&i.intrinsicWidth>0&&i.intrinsicHeight>0){var c=Ui(i),d=vi(t);this.path(d),this.ctx.save(),this.ctx.clip(),this.ctx.drawImage(o,0,0,i.intrinsicWidth,i.intrinsicHeight,c.left,c.top,c.width,c.height),this.ctx.restore()}},A.prototype.renderNodeContent=function(i){return n(this,void 0,void 0,function(){var t,o,c,d,B,u,k,k,C,v,F,E,Y,K,x,G,L,J,O,k,z,Y,G;return a(this,function(D){switch(D.label){case 0:this.applyEffects(i.getEffects(4)),t=i.container,o=i.curves,c=t.styles,d=0,B=t.textNodes,D.label=1;case 1:return d<B.length?(u=B[d],[4,this.renderTextNode(u,c)]):[3,4];case 2:D.sent(),D.label=3;case 3:return d++,[3,1];case 4:if(!(t instanceof In))return[3,8];D.label=5;case 5:return D.trys.push([5,7,,8]),[4,this.context.cache.match(t.src)];case 6:return k=D.sent(),this.renderReplacedElement(t,o,k),[3,8];case 7:return D.sent(),this.context.logger.error("Error loading image "+t.src),[3,8];case 8:if(t instanceof Hn&&this.renderReplacedElement(t,o,t.canvas),!(t instanceof Sn))return[3,12];D.label=9;case 9:return D.trys.push([9,11,,12]),[4,this.context.cache.match(t.svg)];case 10:return k=D.sent(),this.renderReplacedElement(t,o,k),[3,12];case 11:return D.sent(),this.context.logger.error("Error loading svg "+t.svg.substring(0,255)),[3,12];case 12:return t instanceof Tn&&t.tree?(C=new A(this.context,{scale:this.options.scale,backgroundColor:t.backgroundColor,x:0,y:0,width:t.width,height:t.height}),[4,C.render(t.tree)]):[3,14];case 13:v=D.sent(),t.width&&t.height&&this.ctx.drawImage(v,0,0,t.width,t.height,t.bounds.left,t.bounds.top,t.bounds.width,t.bounds.height),D.label=14;case 14:if(t instanceof Vr&&(F=Math.min(t.bounds.width,t.bounds.height),t.type===gi?t.checked&&(this.ctx.save(),this.path([new T(t.bounds.left+F*.39363,t.bounds.top+F*.79),new T(t.bounds.left+F*.16,t.bounds.top+F*.5549),new T(t.bounds.left+F*.27347,t.bounds.top+F*.44071),new T(t.bounds.left+F*.39694,t.bounds.top+F*.5649),new T(t.bounds.left+F*.72983,t.bounds.top+F*.23),new T(t.bounds.left+F*.84,t.bounds.top+F*.34085),new T(t.bounds.left+F*.39363,t.bounds.top+F*.79)]),this.ctx.fillStyle=pA(xn),this.ctx.fill(),this.ctx.restore()):t.type===ui&&t.checked&&(this.ctx.save(),this.ctx.beginPath(),this.ctx.arc(t.bounds.left+F/2,t.bounds.top+F/2,F/4,0,Math.PI*2,!0),this.ctx.fillStyle=pA(xn),this.ctx.fill(),this.ctx.restore())),Id(t)&&t.value.length){switch(E=this.createFontStyle(c),Y=E[0],K=E[1],x=this.fontMetrics.getMetrics(Y,K).baseline,this.ctx.font=Y,this.ctx.fillStyle=pA(c.color),this.ctx.textBaseline="alphabetic",this.ctx.textAlign=Sd(t.styles.textAlign),G=Ui(t),L=0,t.styles.textAlign){case 1:L+=G.width/2;break;case 2:L+=G.width;break}J=G.add(L,0,0,-G.height/2+1),this.ctx.save(),this.path([new T(G.left,G.top),new T(G.left+G.width,G.top),new T(G.left+G.width,G.top+G.height),new T(G.left,G.top+G.height)]),this.ctx.clip(),this.renderTextWithLetterSpacing(new Ut(t.value,J),c.letterSpacing,x),this.ctx.restore(),this.ctx.textBaseline="alphabetic",this.ctx.textAlign="left"}if(!BA(t.styles.display,2048))return[3,20];if(t.styles.listStyleImage===null)return[3,19];if(O=t.styles.listStyleImage,O.type!==0)return[3,18];k=void 0,z=O.url,D.label=15;case 15:return D.trys.push([15,17,,18]),[4,this.context.cache.match(z)];case 16:return k=D.sent(),this.ctx.drawImage(k,t.bounds.left-(k.width+10),t.bounds.top),[3,18];case 17:return D.sent(),this.context.logger.error("Error loading list-style-image "+z),[3,18];case 18:return[3,20];case 19:i.listValue&&t.styles.listStyleType!==-1&&(Y=this.createFontStyle(c)[0],this.ctx.font=Y,this.ctx.fillStyle=pA(c.color),this.ctx.textBaseline="middle",this.ctx.textAlign="right",G=new h(t.bounds.left,t.bounds.top+iA(t.styles.paddingTop,t.bounds.width),t.bounds.width,on(c.lineHeight,c.fontSize.number)/2+1),this.renderTextWithLetterSpacing(new Ut(i.listValue,G),c.letterSpacing,on(c.lineHeight,c.fontSize.number)/2+2),this.ctx.textBaseline="bottom",this.ctx.textAlign="left"),D.label=20;case 20:return[2]}})})},A.prototype.renderStackContent=function(i){return n(this,void 0,void 0,function(){var t,o,O,c,d,O,B,u,O,C,v,O,F,E,O,K,x,O,L,J,O;return a(this,function(k){switch(k.label){case 0:if(BA(i.element.container.flags,16))debugger;return[4,this.renderNodeBackgroundAndBorders(i.element)];case 1:k.sent(),t=0,o=i.negativeZIndex,k.label=2;case 2:return t<o.length?(O=o[t],[4,this.renderStack(O)]):[3,5];case 3:k.sent(),k.label=4;case 4:return t++,[3,2];case 5:return[4,this.renderNodeContent(i.element)];case 6:k.sent(),c=0,d=i.nonInlineLevel,k.label=7;case 7:return c<d.length?(O=d[c],[4,this.renderNode(O)]):[3,10];case 8:k.sent(),k.label=9;case 9:return c++,[3,7];case 10:B=0,u=i.nonPositionedFloats,k.label=11;case 11:return B<u.length?(O=u[B],[4,this.renderStack(O)]):[3,14];case 12:k.sent(),k.label=13;case 13:return B++,[3,11];case 14:C=0,v=i.nonPositionedInlineLevel,k.label=15;case 15:return C<v.length?(O=v[C],[4,this.renderStack(O)]):[3,18];case 16:k.sent(),k.label=17;case 17:return C++,[3,15];case 18:F=0,E=i.inlineLevel,k.label=19;case 19:return F<E.length?(O=E[F],[4,this.renderNode(O)]):[3,22];case 20:k.sent(),k.label=21;case 21:return F++,[3,19];case 22:K=0,x=i.zeroOrAutoZIndexOrTransformedOrOpacity,k.label=23;case 23:return K<x.length?(O=x[K],[4,this.renderStack(O)]):[3,26];case 24:k.sent(),k.label=25;case 25:return K++,[3,23];case 26:L=0,J=i.positiveZIndex,k.label=27;case 27:return L<J.length?(O=J[L],[4,this.renderStack(O)]):[3,30];case 28:k.sent(),k.label=29;case 29:return L++,[3,27];case 30:return[2]}})})},A.prototype.mask=function(i){this.ctx.beginPath(),this.ctx.moveTo(0,0),this.ctx.lineTo(this.canvas.width,0),this.ctx.lineTo(this.canvas.width,this.canvas.height),this.ctx.lineTo(0,this.canvas.height),this.ctx.lineTo(0,0),this.formatPath(i.slice(0).reverse()),this.ctx.closePath()},A.prototype.path=function(i){this.ctx.beginPath(),this.formatPath(i),this.ctx.closePath()},A.prototype.formatPath=function(i){var t=this;i.forEach(function(o,c){var d=NA(o)?o.start:o;c===0?t.ctx.moveTo(d.x,d.y):t.ctx.lineTo(d.x,d.y),NA(o)&&t.ctx.bezierCurveTo(o.startControl.x,o.startControl.y,o.endControl.x,o.endControl.y,o.end.x,o.end.y)})},A.prototype.renderRepeat=function(i,t,o,c){this.path(i),this.ctx.fillStyle=t,this.ctx.translate(o,c),this.ctx.fill(),this.ctx.translate(-o,-c)},A.prototype.resizeImage=function(i,t,o){var c;if(i.width===t&&i.height===o)return i;var d=(c=this.canvas.ownerDocument)!==null&&c!==void 0?c:document,B=d.createElement("canvas");B.width=Math.max(1,t),B.height=Math.max(1,o);var u=B.getContext("2d");return u.drawImage(i,0,0,i.width,i.height,0,0,t,o),B},A.prototype.renderBackgroundImage=function(i){return n(this,void 0,void 0,function(){var t,o,c,d,B,u;return a(this,function(C){switch(C.label){case 0:t=i.styles.backgroundImage.length-1,o=function(v){var F,E,K,dA,bA,xA,tA,CA,j,x,dA,bA,xA,tA,CA,L,J,O,k,z,Y,G,D,q,j,X,dA,SA,TA,tA,CA,me,bA,xA,Se,qA,fe,be,xe,ie,Me,re;return a(this,function($e){switch($e.label){case 0:if(v.type!==0)return[3,5];F=void 0,E=v.url,$e.label=1;case 1:return $e.trys.push([1,3,,4]),[4,c.context.cache.match(E)];case 2:return F=$e.sent(),[3,4];case 3:return $e.sent(),c.context.logger.error("Error loading background-image "+E),[3,4];case 4:return F&&(K=As(i,t,[F.width,F.height,F.width/F.height]),dA=K[0],bA=K[1],xA=K[2],tA=K[3],CA=K[4],j=c.ctx.createPattern(c.resizeImage(F,tA,CA),"repeat"),c.renderRepeat(dA,j,bA,xA)),[3,6];case 5:dl(v)?(x=As(i,t,[null,null,null]),dA=x[0],bA=x[1],xA=x[2],tA=x[3],CA=x[4],L=al(v.angle,tA,CA),J=L[0],O=L[1],k=L[2],z=L[3],Y=L[4],G=document.createElement("canvas"),G.width=tA,G.height=CA,D=G.getContext("2d"),q=D.createLinearGradient(O,z,k,Y),$s(v.stops,J).forEach(function(Ht){return q.addColorStop(Ht.stop,pA(Ht.color))}),D.fillStyle=q,D.fillRect(0,0,tA,CA),tA>0&&CA>0&&(j=c.ctx.createPattern(G,"repeat"),c.renderRepeat(dA,j,bA,xA))):gl(v)&&(X=As(i,t,[null,null,null]),dA=X[0],SA=X[1],TA=X[2],tA=X[3],CA=X[4],me=v.position.length===0?[yr]:v.position,bA=iA(me[0],tA),xA=iA(me[me.length-1],CA),Se=ol(v,bA,xA,tA,CA),qA=Se[0],fe=Se[1],qA>0&&fe>0&&(be=c.ctx.createRadialGradient(SA+bA,TA+xA,0,SA+bA,TA+xA,qA),$s(v.stops,qA*2).forEach(function(Ht){return be.addColorStop(Ht.stop,pA(Ht.color))}),c.path(dA),c.ctx.fillStyle=be,qA!==fe?(xe=i.bounds.left+.5*i.bounds.width,ie=i.bounds.top+.5*i.bounds.height,Me=fe/qA,re=1/Me,c.ctx.save(),c.ctx.translate(xe,ie),c.ctx.transform(1,0,0,Me,0,0),c.ctx.translate(-xe,-ie),c.ctx.fillRect(SA,re*(TA-ie)+ie,tA,CA*re),c.ctx.restore()):c.ctx.fill())),$e.label=6;case 6:return t--,[2]}})},c=this,d=0,B=i.styles.backgroundImage.slice(0).reverse(),C.label=1;case 1:return d<B.length?(u=B[d],[5,o(u)]):[3,4];case 2:C.sent(),C.label=3;case 3:return d++,[3,1];case 4:return[2]}})})},A.prototype.renderSolidBorder=function(i,t,o){return n(this,void 0,void 0,function(){return a(this,function(c){return this.path(na(o,t)),this.ctx.fillStyle=pA(i),this.ctx.fill(),[2]})})},A.prototype.renderDoubleBorder=function(i,t,o,c){return n(this,void 0,void 0,function(){var d,B;return a(this,function(u){switch(u.label){case 0:return t<3?[4,this.renderSolidBorder(i,o,c)]:[3,2];case 1:return u.sent(),[2];case 2:return d=pd(c,o),this.path(d),this.ctx.fillStyle=pA(i),this.ctx.fill(),B=md(c,o),this.path(B),this.ctx.fill(),[2]}})})},A.prototype.renderNodeBackgroundAndBorders=function(i){return n(this,void 0,void 0,function(){var t,o,c,d,B,u,C,v,F=this;return a(this,function(E){switch(E.label){case 0:return this.applyEffects(i.getEffects(2)),t=i.container.styles,o=!ge(t.backgroundColor)||t.backgroundImage.length,c=[{style:t.borderTopStyle,color:t.borderTopColor,width:t.borderTopWidth},{style:t.borderRightStyle,color:t.borderRightColor,width:t.borderRightWidth},{style:t.borderBottomStyle,color:t.borderBottomColor,width:t.borderBottomWidth},{style:t.borderLeftStyle,color:t.borderLeftColor,width:t.borderLeftWidth}],d=Hd(Ze(t.backgroundClip,0),i.curves),o||t.boxShadow.length?(this.ctx.save(),this.path(d),this.ctx.clip(),ge(t.backgroundColor)||(this.ctx.fillStyle=pA(t.backgroundColor),this.ctx.fill()),[4,this.renderBackgroundImage(i.container)]):[3,2];case 1:E.sent(),this.ctx.restore(),t.boxShadow.slice(0).reverse().forEach(function(K){F.ctx.save();var x=Ci(i.curves),L=K.inset?0:Ed,J=ud(x,-L+(K.inset?1:-1)*K.spread.number,(K.inset?1:-1)*K.spread.number,K.spread.number*(K.inset?-2:2),K.spread.number*(K.inset?-2:2));K.inset?(F.path(x),F.ctx.clip(),F.mask(J)):(F.mask(x),F.ctx.clip(),F.path(J)),F.ctx.shadowOffsetX=K.offsetX.number+L,F.ctx.shadowOffsetY=K.offsetY.number,F.ctx.shadowColor=pA(K.color),F.ctx.shadowBlur=K.blur.number,F.ctx.fillStyle=K.inset?pA(K.color):"rgba(0,0,0,1)",F.ctx.fill(),F.ctx.restore()}),E.label=2;case 2:B=0,u=0,C=c,E.label=3;case 3:return u<C.length?(v=C[u],v.style!==0&&!ge(v.color)&&v.width>0?v.style!==2?[3,5]:[4,this.renderDashedDottedBorder(v.color,v.width,B,i.curves,2)]:[3,11]):[3,13];case 4:return E.sent(),[3,11];case 5:return v.style!==3?[3,7]:[4,this.renderDashedDottedBorder(v.color,v.width,B,i.curves,3)];case 6:return E.sent(),[3,11];case 7:return v.style!==4?[3,9]:[4,this.renderDoubleBorder(v.color,v.width,B,i.curves)];case 8:return E.sent(),[3,11];case 9:return[4,this.renderSolidBorder(v.color,B,i.curves)];case 10:E.sent(),E.label=11;case 11:B++,E.label=12;case 12:return u++,[3,3];case 13:return[2]}})})},A.prototype.renderDashedDottedBorder=function(i,t,o,c,d){return n(this,void 0,void 0,function(){var B,u,C,v,F,E,K,x,L,J,O,k,z,Y,G,D,G,D;return a(this,function(q){return this.ctx.save(),B=fd(c,o),u=na(c,o),d===2&&(this.path(u),this.ctx.clip()),NA(u[0])?(C=u[0].start.x,v=u[0].start.y):(C=u[0].x,v=u[0].y),NA(u[1])?(F=u[1].end.x,E=u[1].end.y):(F=u[1].x,E=u[1].y),o===0||o===2?K=Math.abs(C-F):K=Math.abs(v-E),this.ctx.beginPath(),d===3?this.formatPath(B):this.formatPath(u.slice(0,2)),x=t<3?t*3:t*2,L=t<3?t*2:t,d===3&&(x=t,L=t),J=!0,K<=x*2?J=!1:K<=x*2+L?(O=K/(2*x+L),x*=O,L*=O):(k=Math.floor((K+L)/(x+L)),z=(K-k*x)/(k-1),Y=(K-(k+1)*x)/k,L=Y<=0||Math.abs(L-z)<Math.abs(L-Y)?z:Y),J&&(d===3?this.ctx.setLineDash([0,x+L]):this.ctx.setLineDash([x,L])),d===3?(this.ctx.lineCap="round",this.ctx.lineWidth=t):this.ctx.lineWidth=t*2+1.1,this.ctx.strokeStyle=pA(i),this.ctx.stroke(),this.ctx.setLineDash([]),d===2&&(NA(u[0])&&(G=u[3],D=u[0],this.ctx.beginPath(),this.formatPath([new T(G.end.x,G.end.y),new T(D.start.x,D.start.y)]),this.ctx.stroke()),NA(u[1])&&(G=u[1],D=u[2],this.ctx.beginPath(),this.formatPath([new T(G.end.x,G.end.y),new T(D.start.x,D.start.y)]),this.ctx.stroke())),this.ctx.restore(),[2]})})},A.prototype.render=function(i){return n(this,void 0,void 0,function(){var t;return a(this,function(o){switch(o.label){case 0:return this.options.backgroundColor&&(this.ctx.fillStyle=pA(this.options.backgroundColor),this.ctx.fillRect(this.options.x,this.options.y,this.options.width,this.options.height)),t=Bd(i),[4,this.renderStack(t)];case 1:return o.sent(),this.applyEffects([]),[2,this.canvas]}})})},A}(la),Id=function(e){return e instanceof Ln||e instanceof Mn?!0:e instanceof Vr&&e.type!==ui&&e.type!==gi},Hd=function(e,A){switch(e){case 0:return Ci(A);case 2:return ld(A);case 1:default:return vi(A)}},Sd=function(e){switch(e){case 1:return"center";case 2:return"right";case 0:default:return"left"}},bd=["-apple-system","system-ui"],xd=function(e){return/iPhone OS 15_(0|1)/.test(window.navigator.userAgent)?e.filter(function(A){return bd.indexOf(A)===-1}):e},Md=function(e){s(A,e);function A(i,t){var o=e.call(this,i,t)||this;return o.canvas=t.canvas?t.canvas:document.createElement("canvas"),o.ctx=o.canvas.getContext("2d"),o.options=t,o.canvas.width=Math.floor(t.width*t.scale),o.canvas.height=Math.floor(t.height*t.scale),o.canvas.style.width=t.width+"px",o.canvas.style.height=t.height+"px",o.ctx.scale(o.options.scale,o.options.scale),o.ctx.translate(-t.x,-t.y),o.context.logger.debug("EXPERIMENTAL ForeignObject renderer initialized ("+t.width+"x"+t.height+" at "+t.x+","+t.y+") with scale "+t.scale),o}return A.prototype.render=function(i){return n(this,void 0,void 0,function(){var t,o;return a(this,function(c){switch(c.label){case 0:return t=Rr(this.options.width*this.options.scale,this.options.height*this.options.scale,this.options.scale,this.options.scale,i),[4,Ld(t)];case 1:return o=c.sent(),this.options.backgroundColor&&(this.ctx.fillStyle=pA(this.options.backgroundColor),this.ctx.fillRect(0,0,this.options.width*this.options.scale,this.options.height*this.options.scale)),this.ctx.drawImage(o,-this.options.x*this.options.scale,-this.options.y*this.options.scale),[2,this.canvas]}})})},A}(la),Ld=function(e){return new Promise(function(A,i){var t=new Image;t.onload=function(){A(t)},t.onerror=i,t.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent(new XMLSerializer().serializeToString(e))})},Td=function(){function e(A){var i=A.id,t=A.enabled;this.id=i,this.enabled=t,this.start=Date.now()}return e.prototype.debug=function(){for(var A=[],i=0;i<arguments.length;i++)A[i]=arguments[i];this.enabled&&(typeof window!="undefined"&&window.console&&typeof console.debug=="function"?console.debug.apply(console,l([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},e.prototype.getTime=function(){return Date.now()-this.start},e.prototype.info=function(){for(var A=[],i=0;i<arguments.length;i++)A[i]=arguments[i];this.enabled&&typeof window!="undefined"&&window.console&&typeof console.info=="function"&&console.info.apply(console,l([this.id,this.getTime()+"ms"],A))},e.prototype.warn=function(){for(var A=[],i=0;i<arguments.length;i++)A[i]=arguments[i];this.enabled&&(typeof window!="undefined"&&window.console&&typeof console.warn=="function"?console.warn.apply(console,l([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},e.prototype.error=function(){for(var A=[],i=0;i<arguments.length;i++)A[i]=arguments[i];this.enabled&&(typeof window!="undefined"&&window.console&&typeof console.error=="function"?console.error.apply(console,l([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},e.instances={},e}(),Dd=function(){function e(A,i){var t;this.windowBounds=i,this.instanceName="#"+e.instanceCount++,this.logger=new Td({id:this.instanceName,enabled:A.logging}),this.cache=(t=A.cache)!==null&&t!==void 0?t:new ed(this,A)}return e.instanceCount=1,e}(),Kd=function(e,A){return A===void 0&&(A={}),Pd(e,A)};typeof window!="undefined"&&Aa.setContext(window);var Pd=function(e,A){return n(void 0,void 0,void 0,function(){var i,t,o,c,d,B,u,C,v,F,E,K,x,L,J,O,k,z,Y,G,q,D,q,j,X,dA,SA,TA,tA,CA,me,bA,xA,Se,qA,fe,be,xe,ie,Me;return a(this,function(re){switch(re.label){case 0:if(!e||typeof e!="object")return[2,Promise.reject("Invalid element provided as first argument")];if(i=e.ownerDocument,!i)throw new Error("Element is not attached to a Document");if(t=i.defaultView,!t)throw new Error("Document is not attached to a Window");return o={allowTaint:(j=A.allowTaint)!==null&&j!==void 0?j:!1,imageTimeout:(X=A.imageTimeout)!==null&&X!==void 0?X:15e3,proxy:A.proxy,useCORS:(dA=A.useCORS)!==null&&dA!==void 0?dA:!1},c=r({logging:(SA=A.logging)!==null&&SA!==void 0?SA:!0,cache:A.cache},o),d={windowWidth:(TA=A.windowWidth)!==null&&TA!==void 0?TA:t.innerWidth,windowHeight:(tA=A.windowHeight)!==null&&tA!==void 0?tA:t.innerHeight,scrollX:(CA=A.scrollX)!==null&&CA!==void 0?CA:t.pageXOffset,scrollY:(me=A.scrollY)!==null&&me!==void 0?me:t.pageYOffset},B=new h(d.scrollX,d.scrollY,d.windowWidth,d.windowHeight),u=new Dd(c,B),C=(bA=A.foreignObjectRendering)!==null&&bA!==void 0?bA:!1,v={allowTaint:(xA=A.allowTaint)!==null&&xA!==void 0?xA:!1,onclone:A.onclone,ignoreElements:A.ignoreElements,inlineImages:C,copyStyles:C},u.logger.debug("Starting document clone with size "+B.width+"x"+B.height+" scrolled to "+-B.left+","+-B.top),F=new jn(u,e,v),E=F.clonedReferenceElement,E?[4,F.toIFrame(i,B)]:[2,Promise.reject("Unable to find element in cloned iframe")];case 1:return K=re.sent(),x=Xr(E)||kh(E)?m(E.ownerDocument):g(u,E),L=x.width,J=x.height,O=x.left,k=x.top,z=kd(u,E,A.backgroundColor),Y={canvas:A.canvas,backgroundColor:z,scale:(qA=(Se=A.scale)!==null&&Se!==void 0?Se:t.devicePixelRatio)!==null&&qA!==void 0?qA:1,x:((fe=A.x)!==null&&fe!==void 0?fe:0)+O,y:((be=A.y)!==null&&be!==void 0?be:0)+k,width:(xe=A.width)!==null&&xe!==void 0?xe:Math.ceil(L),height:(ie=A.height)!==null&&ie!==void 0?ie:Math.ceil(J)},C?(u.logger.debug("Document cloned, using foreign object rendering"),q=new Md(u,Y),[4,q.render(E)]):[3,3];case 2:return G=re.sent(),[3,5];case 3:return u.logger.debug("Document cloned, element located at "+O+","+k+" with size "+L+"x"+J+" using computed rendering"),u.logger.debug("Starting DOM parsing"),D=Kn(u,E),z===D.styles.backgroundColor&&(D.styles.backgroundColor=ee.TRANSPARENT),u.logger.debug("Starting renderer for element at "+Y.x+","+Y.y+" with size "+Y.width+"x"+Y.height),q=new yd(u,Y),[4,q.render(D)];case 4:G=re.sent(),re.label=5;case 5:return(!((Me=A.removeContainer)!==null&&Me!==void 0)||Me)&&(jn.destroy(K)||u.logger.error("Cannot detach cloned iframe as it is not in the DOM anymore")),u.logger.debug("Finished rendering"),[2,G]}})})},kd=function(e,A,i){var t=A.ownerDocument,o=t.documentElement?vt(e,getComputedStyle(t.documentElement).backgroundColor):ee.TRANSPARENT,c=t.body?vt(e,getComputedStyle(t.body).backgroundColor):ee.TRANSPARENT,d=typeof i=="string"?vt(e,i):i===null?ee.TRANSPARENT:**********;return A===t.documentElement?ge(o)?ge(c)?d:c:o:d};return Kd})});var qd={};Xd(qd,{default:()=>ar});module.exports=Jd(qd);var ba=require("obsidian");var gA=require("obsidian");var sA=require("obsidian");var FA={AGAIN:1,HARD:2,GOOD:3,EASY:4},es={request_retention:.9,maximum_interval:36500,newCardsPerDay:20,reviewsPerDay:100,w:[.4872,1.4003,3.7145,13.8206,5.1618,1.2298,.8975,.031,1.6474,.1367,1.0461,2.1072,.0793,.3246,1.587,.2272,2.8755]};var Ba=require("obsidian");var da={"Select the AI service provider":"Select the AI service provider.","Ollama (Local)":"Ollama (local)",Model:"Model","Failed to fetch models":"Failed to fetch models.","API Key":"API key","Custom API Address":"Custom API address","If using a custom API proxy, please enter the full API address":"If using a custom API proxy, please enter the full API address.","Please enter your API Key":"Please enter your API key.","Validating API Key...":"Validating API key...","API Key verification successful!":"API key verification successful!","API Key verification failed. Please check your API Key.":"API key verification failed. Please check your API key.",Save:"Save",Cancel:"Cancel",Edit:"Edit",Delete:"Delete","Custom Model":"Custom Model","API Key is valid!":"API Key is valid!","Failed to validate API Key. Please check your key and try again.":"Failed to validate API Key. Please check your key and try again.","Please enter an API Key first":"Please enter an API Key first.","Checking...":"Checking...","API Key and the current model are both available!":"API Key and the current model are both available!","API Key is invalid or there is a server error. Please check if your API Key is correct.":"API Key is invalid or there is a server error. Please check if your API Key is correct.","Select the OpenAI model to use":"Select the OpenAI model to use.","OpenAI Settings":"OpenAI service","Enter your OpenAI API Key.":"Enter your OpenAI API key.","No available models found.":"No available models found.","API Key validated successfully!":"API Key validated successfully!","No models available. Please check your API Key.":"No models available. Please check your API Key.","Anthropic Settings":"Anthropic service","Enter your Anthropic API Key.":"Enter your Anthropic API key.","Select the Anthropic model to use":"Select the Anthropic model to use.","Select a model or use a custom one":"Select a model or use a custom one.","Model ID can only contain letters, numbers, underscores, dots and hyphens":"Model ID can only contain letters, numbers, underscores, dots and hyphens.","Select the Gemini model to use":"Select the Gemini model to use.","Unable to create model selection dropdown menu.":"Unable to create model selection dropdown menu.","Gemini Settings":"Gemini service","Enter your Gemini API Key":"Enter your Gemini API key.","Deepseek Settings":"Deepseek service","Enter your Deepseek API Key":"Enter your Deepseek API key.","Ollama Settings":"Ollama service","Server Address":"Server address","Ollama server address (default: http://localhost:11434)":"Ollama server address (default: http://localhost:11434)",Check:"Check","Successfully connected to Ollama service":"Successfully connected to Ollama service.","No models found. Please download models using ollama":"No models found. Please download models using ollama.","Could not connect to Ollama service":"Could not connect to Ollama service","Failed to connect to Ollama service. Please check the server address.":"Failed to connect to Ollama service. Please check the server address.","Currently selected model (Test connection to see all available models)":"Currently selected model (Test connection to see all available models)","Select a model to use":"Select a model to use","No models available. Please load an available model first.":"No models available. Please load an available model first.","No models available":"No models available","Prompt settings":"Custom prompt","Add Prompt":"Add prompt","Input Prompt Name":"Input prompt name","Input Prompt Content\nAvailable parameters:\n{{highlight}} - Current highlighted text\n{{comment}} - Existing comment":`Input prompt content
Available parameters:
{{highlight}} - Current highlighted text
{{comment}} - Existing comment`,"Prompt added":"Prompt added","Prompt updated":"Prompt updated","Shift + Enter Wrap, Enter Save":"Shift + Enter Wrap, Enter Save","Delete comment":"Delete","Add Comment":"Add comment","Export as Image":"Export as image","Select Prompt":"Select prompt","Please add Prompt in the settings first":"Please add prompt in the settings first","AI comments have been added":"AI comments have been added","AI comments failed:":"AI comments failed:",Chat:"Chat","Failed to process dropped highlight:":"Failed to process dropped highlight:","highlighted notes":" highlighted notes","Input message...":"Input message...","Unable to access the Ollama model, please check the service.":"Unable to access the Ollama model, please check the service.","Unable to get Gemini model list, please check API Key and network connection.":"Unable to get Gemini model list, please check API key and network connection.",Download:"Download","Export successful!":"Export successful!","Export failed, please try again.":"Export failed, please try again.","Loading...":"Loading...","Search...":"Search...","No matching content found.":"No matching content found.","The current document has no highlighted content.":"The current document has no highlighted content.","No corresponding file found.":"No corresponding file found.","Export failed: Failed to load necessary components.":"Export failed: Failed to load necessary components.","All Highlight":"All highlight","Export as notes":"Export as notes","Add File Comment":"Add file comment","File Comment":"File comment","Successfully exported highlights to: ":"Successfully exported highlights to: ","Failed to export highlights: ":"Failed to export highlights: ","Default Template":"Default template","Modern minimalist knowledge card style":"Modern minimalist knowledge card style","Academic Template":"Academic template","Formal style suitable for academic citations":"Formal style suitable for academic citations","Social Template":"Social template","Modern style suitable for social media sharing":"Modern style suitable for social media sharing","Open AI chat window":"Open AI chat window","Open HiNote window":"Open HiNote window","General Settings":"General settings","Export Path":"Export path","Set the path for exported highlight notes. Leave empty to use vault root. The path should be relative to your vault root.":"Set the path for exported highlight notes. Leave empty to use vault root. The path should be relative to your vault root.",Exclusions:"Exclusions","Comma separated list of paths, tags, note titles or file extensions that will be excluded from highlighting. e.g. folder1, folder1/folder2, [[note1]], [[note2]], *.excalidraw.md":"Comma separated list of paths, tags, note titles or file extensions that will be excluded from highlighting. e.g. folder1, folder1/folder2, [[note1]], [[note2]], *.excalidraw.md","Custom text extraction":"Custom text extraction","Use Custom Pattern":"Use custom pattern","Enable to use a custom regular expression for extracting text.":"Enable to use a custom regular expression for extracting text.","Custom Pattern":"Custom pattern","Enter a custom regular expression for extracting text. Use capture groups () to specify the text to extract. The first non-empty capture group will be used as the extracted text.":"Enter a custom regular expression for extracting text. Use capture groups () to specify the text to extract. The first non-empty capture group will be used as the extracted text.","Default Color":"Default color","Set the default color for decorators when no color is specified. Leave empty to use system default.":"Set the default color for decorators when no color is specified. Leave empty to use system default.","Flashcard learning":"Flashcard learning","New cards per day":"New cards per day","Maximum number of new cards to learn each day":"Maximum number of new cards to learn each day.","Reviews per day":"Reviews per day","Maximum number of cards to review each day":"Maximum number of cards to review each day.","Target retention":"Target retention","Target memory retention rate (0.8 = 80%)":"Target memory retention rate (0.8 = 80%).","Maximum interval":"Maximum interval","Maximum interval in days between reviews":"Maximum interval in days between reviews.","Reset daily stats":"Reset daily stats","Reset today's learning statistics":"Reset today's learning statistics.",Reset:"Reset","Daily statistics have been reset":"Daily statistics have been reset","No statistics to reset for today":"No statistics to reset for today",Advanced:"Advanced","These settings control the FSRS algorithm parameters. Only change them if you understand the algorithm.":"These settings control the FSRS algorithm parameters. Only change them if you understand the algorithm.","Reset algorithm parameters":"Reset algorithm parameters","Reset the FSRS algorithm parameters to default values":"Reset the FSRS algorithm parameters to default values.","Reset to Default":"Reset to default","FSRS parameters have been reset to default values":"FSRS parameters have been reset to default values.",days:"days","Activate HiCard":"Activate HiCard","Enter your license key to activate HiCard feature.":"Enter your license key to activate HiCard feature.","Enter license key":"Enter license key",Activate:"Activate","Please enter a license key":"Please enter a license key","HiCard activated successfully!":"HiCard activated successfully!","Invalid license key":"Invalid license key","Use global settings":"Use global settings","New cards per day:":"New cards per day:","Reviews per day:":"Reviews per day:","All Cards":"All Cards","Due Today":"Due Today","New Cards":"New Cards",Learned:"Learned","Create Group":"Create Group","Group name":"Group name",Create:"Create",Again:"Again",Hard:"Hard",Good:"Good",Easy:"Easy","Show Answer":"Show Answer",Card:"Card",of:"of",Settings:"Settings","Are you sure you want to delete this group?":"Are you sure you want to delete this group?",Yes:"Yes",No:"No","You've completed all cards for today!":"You've completed all cards for today!","No cards available.":"No cards available.","Return to First Card":"Return to First Card","Edit Group":"Edit Group","Create New Group":"Create New Group","Group Name":"Group Name","Please fill in all fields":"Please fill in all fields","Saving...":"Saving...","Creating...":"Creating...","Group updated successfully":"Group updated successfully","Failed to update group":"Failed to update group","Group created successfully":"Group created successfully","Failed to create or update group":"Failed to create or update group",Due:"Due",New:"New",Review:"Review",Retention:"Retention","Limits:":"Limits:","\u6BCF\u65E5\u5B66\u4E60\u9650\u5236\n":`Daily learning limits
`,"\u65B0\u5361\u7247:":"New cards:","\u590D\u4E60\u5361\u7247:":"Review cards:","\u5B66\u4E60\u5B8C\u6210\uFF01":"Learning completed!",\u8FD4\u56DE\u7B2C\u4E00\u5F20\u5361\u7247:"Return to first card","\u60A8\u4ECA\u5929\u7684\u65B0\u5361\u7247\u5B66\u4E60\u914D\u989D\u5DF2\u7528\u5B8C\uFF01\u660E\u5929\u518D\u6765\u5B66\u4E60\u5427\u3002":"You've reached your daily quota for new cards! Come back tomorrow.","\u60A8\u4ECA\u5929\u7684\u590D\u4E60\u914D\u989D\u5DF2\u7528\u5B8C\uFF01\u660E\u5929\u518D\u6765\u590D\u4E60\u5427\u3002":"You've reached your daily quota for reviews! Come back tomorrow.",'\u60A8\u4ECA\u5929\u5728 "':`You've reached your daily quota in "`,'" \u5206\u7EC4\u7684\u65B0\u5361\u7247\u5B66\u4E60\u914D\u989D\u5DF2\u7528\u5B8C\uFF01\u660E\u5929\u518D\u6765\u5B66\u4E60\u5427\u3002':'" group for new cards! Come back tomorrow.','" \u5206\u7EC4\u7684\u590D\u4E60\u914D\u989D\u5DF2\u7528\u5B8C\uFF01\u660E\u5929\u518D\u6765\u590D\u4E60\u5427\u3002':'" group for reviews! Come back tomorrow.','\u606D\u559C\uFF01\u60A8\u5DF2\u5B8C\u6210 "':`Congratulations! You've completed all cards in "`,'" \u4E2D\u7684\u6240\u6709\u5361\u7247\u5B66\u4E60\u3002':'" group.','\u786E\u5B9A\u8981\u5220\u9664\u5206\u7EC4 "':'Are you sure you want to delete group "','" \u5417\uFF1F':'"?',\u5206\u7EC4\u5220\u9664\u6210\u529F:"Group deleted successfully",\u5220\u9664\u5206\u7EC4\u5931\u8D25:"Failed to delete group","\u652F\u6301\u4EE5\u4E0B\u683C\u5F0F\uFF1A\n- \u6587\u4EF6\u5939\uFF1Afolder1, folder1/folder2\n- \u7B14\u8BB0\uFF1A[[note1]], [[note2]]\n- \u6807\u7B7E\uFF1A#tag1, #tag2\n- \u901A\u914D\u7B26\uFF1A*.excalidraw.md\n- \u5185\u5BB9\uFF1A\u76F4\u63A5\u8F93\u5165\u8981\u641C\u7D22\u7684\u6587\u672C":`Supports the following formats:
- Folders: folder1, folder1/folder2
- Notes: [[note1]], [[note2]]
- Tags: #tag1, #tag2
- Wildcards: *.excalidraw.md
- Content: directly enter text to search for`,"\u53CD\u8F6C\u5361\u7247\uFF08\u4F7F\u7528\u8BC4\u8BBA\u4F5C\u4E3A\u95EE\u9898\uFF09":"Reverse cards (use comments as questions)","Learning settings":"Learning settings","\u8BB0\u5FC6\u4FDD\u6301\u7387 = (\u603B\u590D\u4E60\u6B21\u6570 - \u9057\u5FD8\u6B21\u6570) / \u603B\u590D\u4E60\u6B21\u6570\n\u8BE5\u6307\u6807\u53CD\u6620\u4E86\u4F60\u7684\u5B66\u4E60\u6548\u679C\uFF0C\u8D8A\u9AD8\u8BF4\u660E\u8BB0\u5FC6\u6548\u679C\u8D8A\u597D":`Memory retention = (total reviews - forgotten reviews) / total reviews
This metric reflects your learning effectiveness. Higher means better memory retention`,"Open (DoubleClick)":"Open (double-click)"};var ga={"Select the AI service provider":"\u9009\u62E9 AI \u670D\u52A1\u63D0\u4F9B\u5546\u3002","Ollama (Local)":"Ollama (\u672C\u5730)",Model:"\u6A21\u578B","Failed to fetch models":"\u83B7\u53D6\u6A21\u578B\u5931\u8D25","API Key":"API \u5BC6\u94A5","Custom API Address":"\u81EA\u5B9A\u4E49 API \u5730\u5740","If using a custom API proxy, please enter the full API address":"\u5982\u679C\u4F7F\u7528\u81EA\u5B9A\u4E49 API \u4EE3\u7406\uFF0C\u8BF7\u8F93\u5165\u5B8C\u6574\u7684 API \u5730\u5740","Please enter your API Key":"\u8BF7\u8F93\u5165\u60A8\u7684 API \u5BC6\u94A5","Validating API Key...":"\u6B63\u5728\u9A8C\u8BC1 API \u5BC6\u94A5...","API Key verification successful!":"API \u5BC6\u94A5\u9A8C\u8BC1\u6210\u529F\uFF01","API Key verification failed. Please check your API Key.":"API \u5BC6\u94A5\u9A8C\u8BC1\u5931\u8D25\u3002\u8BF7\u68C0\u67E5\u60A8\u7684 API \u5BC6\u94A5\u3002",Save:"\u4FDD\u5B58",Cancel:"\u53D6\u6D88",Edit:"\u7F16\u8F91",Delete:"\u5220\u9664","Custom Model":"\u81EA\u5B9A\u4E49\u6A21\u578B","API Key is valid!":"API \u5BC6\u94A5\u6709\u6548\uFF01","Failed to validate API Key. Please check your key and try again.":"API \u5BC6\u94A5\u9A8C\u8BC1\u5931\u8D25\uFF0C\u8BF7\u68C0\u67E5\u60A8\u7684\u5BC6\u94A5\u5E76\u91CD\u8BD5","Please enter an API Key first":"\u8BF7\u5148\u8F93\u5165 API \u5BC6\u94A5","Checking...":"\u68C0\u67E5\u4E2D...","API Key and the current model are both available!":"API \u5BC6\u94A5\u548C\u5F53\u524D\u6A21\u578B\u90FD\u53EF\u7528\uFF01","API Key is invalid or there is a server error. Please check if your API Key is correct.":"API \u5BC6\u94A5\u65E0\u6548\u6216\u670D\u52A1\u5668\u9519\u8BEF\u3002\u8BF7\u68C0\u67E5\u60A8\u7684 API \u5BC6\u94A5\u662F\u5426\u6B63\u786E\u3002","Select the OpenAI model to use":"\u9009\u62E9 OpenAI \u6A21\u578B","OpenAI Settings":"OpenAI \u670D\u52A1","Enter your OpenAI API Key.":"\u8F93\u5165\u60A8\u7684 OpenAI API \u5BC6\u94A5\u3002","No available models found.":"\u672A\u627E\u5230\u53EF\u7528\u6A21\u578B\u3002","API Key validated successfully!":"API \u5BC6\u94A5\u9A8C\u8BC1\u6210\u529F\uFF01","No models available. Please check your API Key.":"\u6CA1\u6709\u53EF\u7528\u7684\u6A21\u578B\u3002\u8BF7\u68C0\u67E5\u60A8\u7684 API \u5BC6\u94A5\u3002","Anthropic Settings":"Anthropic \u670D\u52A1","Enter your Anthropic API Key.":"\u8F93\u5165\u60A8\u7684 Anthropic API \u5BC6\u94A5\u3002","Select the Anthropic model to use":"\u9009\u62E9 Anthropic \u6A21\u578B","Select a model or use a custom one":"\u9009\u62E9\u6A21\u578B\u6216\u4F7F\u7528\u81EA\u5B9A\u4E49\u6A21\u578B","Model ID can only contain letters, numbers, underscores, dots and hyphens":"\u6A21\u578B ID \u53EA\u80FD\u5305\u542B\u5B57\u6BCD\u3001\u6570\u5B57\u3001\u4E0B\u5212\u7EBF\u3001\u70B9\u548C\u8FDE\u5B57\u7B26","Select the Gemini model to use":"\u9009\u62E9 Gemini \u6A21\u578B","Unable to create model selection dropdown menu.":"\u65E0\u6CD5\u521B\u5EFA\u6A21\u578B\u9009\u62E9\u4E0B\u62C9\u83DC\u5355\u3002","Gemini Settings":"Gemini \u670D\u52A1","Enter your Gemini API Key":"\u8F93\u5165\u60A8\u7684 Gemini API \u5BC6\u94A5","Deepseek Settings":"Deepseek \u670D\u52A1","Enter your Deepseek API Key":"\u8F93\u5165\u60A8\u7684 Deepseek API \u5BC6\u94A5","Ollama Settings":"Ollama \u670D\u52A1","Server Address":"\u670D\u52A1\u5668\u5730\u5740","Ollama server address (default: http://localhost:11434)":"Ollama \u670D\u52A1\u5668\u5730\u5740\uFF08\u9ED8\u8BA4\uFF1Ahttp://localhost:11434\uFF09",Check:"\u9A8C\u8BC1","Successfully connected to Ollama service":"\u6210\u529F\u8FDE\u63A5\u5230 Ollama \u670D\u52A1","No models found. Please download models using ollama":"\u672A\u627E\u5230\u6A21\u578B\u3002\u8BF7\u4F7F\u7528 ollama \u4E0B\u8F7D\u6A21\u578B","Could not connect to Ollama service":"\u65E0\u6CD5\u8FDE\u63A5\u5230 Ollama \u670D\u52A1","Failed to connect to Ollama service. Please check the server address.":"\u65E0\u6CD5\u8FDE\u63A5\u5230 Ollama \u670D\u52A1\uFF0C\u8BF7\u68C0\u67E5\u670D\u52A1\u5668\u5730\u5740\u3002","Currently selected model (Test connection to see all available models)":"\u5F53\u524D\u9009\u62E9\u7684\u6A21\u578B\uFF08\u6D4B\u8BD5\u8FDE\u63A5\u4EE5\u67E5\u770B\u53EF\u7528\u6A21\u578B\uFF09","Select a model to use":"\u9009\u62E9\u4E00\u4E2A\u6A21\u578B","No models available. Please load an available model first.":"\u65E0\u53EF\u7528\u6A21\u578B\uFF0C\u8BF7\u5148\u52A0\u8F7D\u53EF\u7528\u6A21\u578B\u3002","No models available":"\u65E0\u53EF\u7528\u6A21\u578B","Prompt settings":"\u81EA\u5B9A\u4E49 Prompt","Add Prompt":"\u6DFB\u52A0 Prompt","Input Prompt Name":"\u8F93\u5165 Prompt \u540D\u79F0","Input Prompt Content\nAvailable parameters:\n{{highlight}} - Current highlighted text\n{{comment}} - Existing comment":`\u8F93\u5165 Prompt \u5185\u5BB9
\u53EF\u7528\u53C2\u6570:
{{highlight}} - \u5F53\u524D\u9AD8\u4EAE\u6587\u672C
{{comment}} - \u5DF2\u5B58\u5728\u7684\u6CE8\u91CA`,"Prompt added":"Prompt \u5DF2\u6DFB\u52A0","Prompt updated":"Prompt \u5DF2\u66F4\u65B0","Shift + Enter Wrap, Enter Save":"Shift + Enter \u6362\u884C\uFF0CEnter \u4FDD\u5B58","Delete comment":"\u5220\u9664\u8BC4\u8BBA","Add Comment":"\u6DFB\u52A0\u8BC4\u8BBA","Export as Image":"\u5BFC\u51FA\u4E3A\u56FE\u7247","Select Prompt":"\u9009\u62E9 Prompt","Please add Prompt in the settings first":"\u8BF7\u5148\u5728\u8BBE\u7F6E\u4E2D\u6DFB\u52A0 Prompt","AI comments have been added":"AI \u8BC4\u8BBA\u5DF2\u6DFB\u52A0","AI comments failed:":"AI \u8BC4\u8BBA\u5931\u8D25\uFF1A",Chat:"\u5BF9\u8BDD","Failed to process dropped highlight:":"\u65E0\u6CD5\u5904\u7406\u5220\u9664\u7684\u9AD8\u4EAE\uFF1A","highlighted notes":" \u6761\u9AD8\u4EAE\u7B14\u8BB0","Input message...":"\u8F93\u5165\u6D88\u606F...","Unable to access the Ollama model, please check the service.":"\u65E0\u6CD5\u8BBF\u95EE Ollama \u6A21\u578B\uFF0C\u8BF7\u68C0\u67E5\u670D\u52A1\u662F\u5426\u6B63\u5E38\u8FD0\u884C","Unable to get Gemini model list, please check API Key and network connection.":"\u65E0\u6CD5\u83B7\u53D6 Gemini \u6A21\u578B\u5217\u8868\uFF0C\u8BF7\u68C0\u67E5 API Key \u548C\u7F51\u7EDC\u8FDE\u63A5",Download:"\u4E0B\u8F7D","Export successful!":"\u5BFC\u51FA\u6210\u529F\uFF01","Export failed, please try again.":"\u5BFC\u51FA\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5","Loading...":"\u52A0\u8F7D\u4E2D...","Search...":"\u641C\u7D22\u9AD8\u4EAE\u6216\u8BC4\u8BBA...","No matching content found.":"\u672A\u627E\u5230\u5339\u914D\u7684\u5185\u5BB9\u3002","The current document has no highlighted content.":"\u5F53\u524D\u6587\u6863\u6CA1\u6709\u9AD8\u4EAE\u5185\u5BB9\u3002","No corresponding file found.":"\u672A\u627E\u5230\u76F8\u5E94\u7684\u6587\u4EF6\u3002","Export failed: Failed to load necessary components.":"\u5BFC\u51FA\u5931\u8D25\uFF1A\u52A0\u8F7D\u5FC5\u8981\u7EC4\u4EF6\u5931\u8D25\u3002","All Highlight":"\u5168\u90E8\u9AD8\u4EAE","Export as notes":"\u5BFC\u51FA\u4E3A\u7B14\u8BB0","Add File Comment":"\u6DFB\u52A0\u6587\u4EF6\u8BC4\u8BBA","File Comment":"\u6587\u4EF6\u8BC4\u8BBA","Successfully exported highlights to: ":"\u6210\u529F\u5BFC\u51FA\u9AD8\u4EAE\u5230\uFF1A","Failed to export highlights: ":"\u65E0\u6CD5\u5BFC\u51FA\u9AD8\u4EAE\uFF1A","Default Template":"\u9ED8\u8BA4\u6A21\u677F","Modern minimalist knowledge card style":"\u73B0\u4EE3\u6781\u7B80\u77E5\u8BC6\u5361\u7247\u98CE\u683C","Academic Template":"\u5B66\u672F\u6A21\u677F","Formal style suitable for academic citations":"\u9002\u7528\u4E8E\u5B66\u672F\u5F15\u7528\u7684\u6B63\u5F0F\u98CE\u683C","Social Template":"\u793E\u4EA4\u6A21\u677F","Modern style suitable for social media sharing":"\u9002\u7528\u4E8E\u793E\u4EA4\u5A92\u4F53\u5206\u4EAB\u7684\u73B0\u4EE3\u98CE\u683C","Open AI chat window":"\u6253\u5F00 AI \u5BF9\u8BDD\u7A97\u53E3","Open HiNote window":"\u6253\u5F00 HiNote \u7A97\u53E3","General Settings":"\u901A\u7528\u8BBE\u7F6E","Export Path":"\u5BFC\u51FA\u8DEF\u5F84","Set the path for exported highlight notes. Leave empty to use vault root. The path should be relative to your vault root.":"\u8BBE\u7F6E\u9AD8\u4EAE\u7B14\u8BB0\u7684\u5BFC\u51FA\u8DEF\u5F84\u3002\u7559\u7A7A\u5219\u4F7F\u7528 vault \u6839\u76EE\u5F55\u3002\u8DEF\u5F84\u5E94\u76F8\u5BF9\u4E8E vault \u6839\u76EE\u5F55\u3002",Exclusions:"\u6392\u9664","Comma separated list of paths, tags, note titles or file extensions that will be excluded from highlighting. e.g. folder1, folder1/folder2, [[note1]], [[note2]], *.excalidraw.md":"\u9017\u53F7\u5206\u9694\u7684\u8DEF\u5F84\uFF0C\u6807\u7B7E\uFF0C\u7B14\u8BB0\u6807\u9898\u6216\u6587\u4EF6\u6269\u5C55\u540D\uFF0C\u5C06\u4ECE\u9AD8\u4EAE\u4E2D\u6392\u9664\u3002\u4F8B\u5982\uFF1Afolder1, folder1/folder2, [[note1]], [[note2]], *.excalidraw.md","Custom text extraction":"\u81EA\u5B9A\u4E49\u6587\u672C\u63D0\u53D6","Use Custom Pattern":"\u4F7F\u7528\u81EA\u5B9A\u4E49\u6A21\u5F0F","Enable to use a custom regular expression for extracting text.":"\u542F\u7528\u81EA\u5B9A\u4E49\u6B63\u5219\u8868\u8FBE\u5F0F\u4EE5\u63D0\u53D6\u6587\u672C\u3002","Custom Pattern":"\u81EA\u5B9A\u4E49\u6A21\u5F0F","Enter a custom regular expression for extracting text. Use capture groups () to specify the text to extract. The first non-empty capture group will be used as the extracted text.":"\u8F93\u5165\u81EA\u5B9A\u4E49\u6B63\u5219\u8868\u8FBE\u5F0F\u4EE5\u63D0\u53D6\u6587\u672C\u3002\u4F7F\u7528\u6355\u83B7\u7EC4 () \u6307\u5B9A\u8981\u63D0\u53D6\u7684\u6587\u672C\u3002\u7B2C\u4E00\u4E2A\u975E\u7A7A\u6355\u83B7\u7EC4\u5C06\u7528\u4F5C\u63D0\u53D6\u7684\u6587\u672C\u3002","Default Color":"\u9ED8\u8BA4\u989C\u8272","Set the default color for decorators when no color is specified. Leave empty to use system default.":"\u8BBE\u7F6E\u5F53\u672A\u6307\u5B9A\u989C\u8272\u65F6\u7684\u88C5\u9970\u5668\u7684\u9ED8\u8BA4\u989C\u8272\u3002\u7559\u7A7A\u4EE5\u4F7F\u7528\u7CFB\u7EDF\u9ED8\u8BA4\u989C\u8272\u3002","Flashcard learning":"\u95EA\u5361\u5B66\u4E60\u8BBE\u7F6E","New cards per day":"\u6BCF\u65E5\u65B0\u5361\u7247\u6570\u91CF","Maximum number of new cards to learn each day":"\u6BCF\u5929\u5B66\u4E60\u7684\u65B0\u5361\u7247\u6700\u5927\u6570\u91CF","Reviews per day":"\u6BCF\u65E5\u590D\u4E60\u6570\u91CF","Maximum number of cards to review each day":"\u6BCF\u5929\u590D\u4E60\u7684\u5361\u7247\u6700\u5927\u6570\u91CF","Target retention":"\u76EE\u6807\u8BB0\u5FC6\u4FDD\u6301\u7387","Target memory retention rate (0.8 = 80%)":"\u76EE\u6807\u8BB0\u5FC6\u4FDD\u6301\u7387\uFF080.8 = 80%\uFF09","Maximum interval":"\u6700\u5927\u95F4\u9694","Maximum interval in days between reviews":"\u590D\u4E60\u4E4B\u95F4\u7684\u6700\u5927\u95F4\u9694\u5929\u6570","Reset daily stats":"\u91CD\u7F6E\u4ECA\u65E5\u7EDF\u8BA1","Reset today's learning statistics":"\u91CD\u7F6E\u4ECA\u5929\u7684\u5B66\u4E60\u7EDF\u8BA1\u6570\u636E",Reset:"\u91CD\u7F6E","Daily statistics have been reset":"\u4ECA\u65E5\u7EDF\u8BA1\u5DF2\u91CD\u7F6E","No statistics to reset for today":"\u4ECA\u5929\u6CA1\u6709\u53EF\u91CD\u7F6E\u7684\u7EDF\u8BA1\u6570\u636E",Advanced:"\u9AD8\u7EA7\u8BBE\u7F6E","These settings control the FSRS algorithm parameters. Only change them if you understand the algorithm.":"\u8FD9\u4E9B\u8BBE\u7F6E\u63A7\u5236FSRS\u7B97\u6CD5\u53C2\u6570\u3002\u4EC5\u5728\u7406\u89E3\u7B97\u6CD5\u7684\u60C5\u51B5\u4E0B\u66F4\u6539\u5B83\u4EEC\u3002","Reset algorithm parameters":"\u91CD\u7F6E\u7B97\u6CD5\u53C2\u6570","Reset the FSRS algorithm parameters to default values":"\u5C06FSRS\u7B97\u6CD5\u53C2\u6570\u91CD\u7F6E\u4E3A\u9ED8\u8BA4\u503C","Reset to Default":"\u91CD\u7F6E\u4E3A\u9ED8\u8BA4\u503C","FSRS parameters have been reset to default values":"FSRS\u53C2\u6570\u5DF2\u91CD\u7F6E\u4E3A\u9ED8\u8BA4\u503C",days:"\u5929","Activate HiCard":"\u6FC0\u6D3B HiCard","Enter your license key to activate HiCard feature.":"\u8F93\u5165\u60A8\u7684\u8BB8\u53EF\u8BC1\u5BC6\u94A5\u4EE5\u6FC0\u6D3B HiCard \u529F\u80FD\u3002","Enter license key":"\u8F93\u5165\u8BB8\u53EF\u8BC1\u5BC6\u94A5",Activate:"\u6FC0\u6D3B","Please enter a license key":"\u8BF7\u8F93\u5165\u8BB8\u53EF\u8BC1\u5BC6\u94A5","HiCard activated successfully!":"HiCard \u6FC0\u6D3B\u6210\u529F\uFF01","Invalid license key":"\u65E0\u6548\u7684\u8BB8\u53EF\u8BC1\u5BC6\u94A5","Use global settings":"\u4F7F\u7528\u5168\u5C40\u8BBE\u7F6E","New cards per day:":"\u6BCF\u65E5\u65B0\u5361\u6570\u91CF:","Reviews per day:":"\u6BCF\u65E5\u590D\u4E60\u6570\u91CF:","All Cards":"\u5168\u90E8\u5361\u7247","Due Today":"\u4ECA\u65E5\u5230\u671F","New Cards":"\u65B0\u5361\u7247",Learned:"\u5DF2\u5B66\u4E60","Create Group":"\u521B\u5EFA\u5206\u7EC4","Group name":"\u5206\u7EC4\u540D\u79F0",Create:"\u521B\u5EFA",Again:"\u91CD\u6765",Hard:"\u56F0\u96BE",Good:"\u826F\u597D",Easy:"\u7B80\u5355","Show Answer":"\u663E\u793A\u7B54\u6848",Card:"\u5361\u7247",of:"\u5171",Settings:"\u8BBE\u7F6E","Are you sure you want to delete this group?":"\u786E\u5B9A\u8981\u5220\u9664\u6B64\u5206\u7EC4\u5417\uFF1F",Yes:"\u662F",No:"\u5426","You've completed all cards for today!":"\u60A8\u4ECA\u5929\u5DF2\u5B8C\u6210\u6240\u6709\u5361\u7247\uFF01","No cards available.":"\u6CA1\u6709\u53EF\u7528\u7684\u5361\u7247\u3002","Return to First Card":"\u8FD4\u56DE\u7B2C\u4E00\u5F20\u5361\u7247","Edit Group":"\u7F16\u8F91\u5206\u7EC4","Create New Group":"\u521B\u5EFA\u65B0\u5206\u7EC4","Group Name":"\u5206\u7EC4\u540D\u79F0","Please fill in all fields":"\u8BF7\u586B\u5199\u6240\u6709\u5B57\u6BB5","Saving...":"\u4FDD\u5B58\u4E2D...","Creating...":"\u521B\u5EFA\u4E2D...","Group updated successfully":"\u5206\u7EC4\u66F4\u65B0\u6210\u529F","Failed to update group":"\u5206\u7EC4\u66F4\u65B0\u5931\u8D25","Group created successfully":"\u5206\u7EC4\u521B\u5EFA\u6210\u529F","Failed to create or update group":"\u521B\u5EFA\u6216\u66F4\u65B0\u5206\u7EC4\u5931\u8D25",Due:"\u5230\u671F",New:"\u65B0\u7684",Review:"\u590D\u4E60",Retention:"\u4FDD\u7559\u7387","Limits:":"\u9650\u5236\uFF1A","\u6BCF\u65E5\u5B66\u4E60\u9650\u5236\n":`\u6BCF\u65E5\u5B66\u4E60\u9650\u5236
`,"\u65B0\u5361\u7247:":"\u65B0\u5361\u7247:","\u590D\u4E60\u5361\u7247:":"\u590D\u4E60\u5361\u7247:","\u5B66\u4E60\u5B8C\u6210\uFF01":"\u5B66\u4E60\u5B8C\u6210\uFF01",\u8FD4\u56DE\u7B2C\u4E00\u5F20\u5361\u7247:"\u8FD4\u56DE\u7B2C\u4E00\u5F20\u5361\u7247","\u60A8\u4ECA\u5929\u7684\u65B0\u5361\u7247\u5B66\u4E60\u914D\u989D\u5DF2\u7528\u5B8C\uFF01\u660E\u5929\u518D\u6765\u5B66\u4E60\u5427\u3002":"\u60A8\u4ECA\u5929\u7684\u65B0\u5361\u7247\u5B66\u4E60\u914D\u989D\u5DF2\u7528\u5B8C\uFF01\u660E\u5929\u518D\u6765\u5B66\u4E60\u5427\u3002","\u60A8\u4ECA\u5929\u7684\u590D\u4E60\u914D\u989D\u5DF2\u7528\u5B8C\uFF01\u660E\u5929\u518D\u6765\u590D\u4E60\u5427\u3002":"\u60A8\u4ECA\u5929\u7684\u590D\u4E60\u914D\u989D\u5DF2\u7528\u5B8C\uFF01\u660E\u5929\u518D\u6765\u590D\u4E60\u5427\u3002",'\u60A8\u4ECA\u5929\u5728 "':'\u60A8\u4ECA\u5929\u5728 "','" \u5206\u7EC4\u7684\u65B0\u5361\u7247\u5B66\u4E60\u914D\u989D\u5DF2\u7528\u5B8C\uFF01\u660E\u5929\u518D\u6765\u5B66\u4E60\u5427\u3002':'" \u5206\u7EC4\u7684\u65B0\u5361\u7247\u5B66\u4E60\u914D\u989D\u5DF2\u7528\u5B8C\uFF01\u660E\u5929\u518D\u6765\u5B66\u4E60\u5427\u3002','" \u5206\u7EC4\u7684\u590D\u4E60\u914D\u989D\u5DF2\u7528\u5B8C\uFF01\u660E\u5929\u518D\u6765\u590D\u4E60\u5427\u3002':'" \u5206\u7EC4\u7684\u590D\u4E60\u914D\u989D\u5DF2\u7528\u5B8C\uFF01\u660E\u5929\u518D\u6765\u590D\u4E60\u5427\u3002','\u606D\u559C\uFF01\u60A8\u5DF2\u5B8C\u6210 "':'\u606D\u559C\uFF01\u60A8\u5DF2\u5B8C\u6210 "','" \u4E2D\u7684\u6240\u6709\u5361\u7247\u5B66\u4E60\u3002':'" \u4E2D\u7684\u6240\u6709\u5361\u7247\u5B66\u4E60\u3002','\u786E\u5B9A\u8981\u5220\u9664\u5206\u7EC4 "':'\u786E\u5B9A\u8981\u5220\u9664\u5206\u7EC4 "','" \u5417\uFF1F':'" \u5417\uFF1F',\u5206\u7EC4\u5220\u9664\u6210\u529F:"\u5206\u7EC4\u5220\u9664\u6210\u529F",\u5220\u9664\u5206\u7EC4\u5931\u8D25:"\u5220\u9664\u5206\u7EC4\u5931\u8D25","\u652F\u6301\u4EE5\u4E0B\u683C\u5F0F\uFF1A\n- \u6587\u4EF6\u5939\uFF1Afolder1, folder1/folder2\n- \u7B14\u8BB0\uFF1A[[note1]], [[note2]]\n- \u6807\u7B7E\uFF1A#tag1, #tag2\n- \u901A\u914D\u7B26\uFF1A*.excalidraw.md\n- \u5185\u5BB9\uFF1A\u76F4\u63A5\u8F93\u5165\u8981\u641C\u7D22\u7684\u6587\u672C":`\u652F\u6301\u4EE5\u4E0B\u683C\u5F0F\uFF1A
- \u6587\u4EF6\u5939\uFF1Afolder1, folder1/folder2
- \u7B14\u8BB0\uFF1A[[note1]], [[note2]]
- \u6807\u7B7E\uFF1A#tag1, #tag2
- \u901A\u914D\u7B26\uFF1A*.excalidraw.md
- \u5185\u5BB9\uFF1A\u76F4\u63A5\u8F93\u5165\u8981\u641C\u7D22\u7684\u6587\u672C`,"\u53CD\u8F6C\u5361\u7247\uFF08\u4F7F\u7528\u8BC4\u8BBA\u4F5C\u4E3A\u95EE\u9898\uFF09":"\u53CD\u8F6C\u5361\u7247\uFF08\u4F7F\u7528\u8BC4\u8BBA\u4F5C\u4E3A\u95EE\u9898\uFF09","Learning settings":"\u5B66\u4E60\u8BBE\u7F6E","\u8BB0\u5FC6\u4FDD\u6301\u7387 = (\u603B\u590D\u4E60\u6B21\u6570 - \u9057\u5FD8\u6B21\u6570) / \u603B\u590D\u4E60\u6B21\u6570\n\u8BE5\u6307\u6807\u53CD\u6620\u4E86\u4F60\u7684\u5B66\u4E60\u6548\u679C\uFF0C\u8D8A\u9AD8\u8BF4\u660E\u8BB0\u5FC6\u6548\u679C\u8D8A\u597D":`\u8BB0\u5FC6\u4FDD\u6301\u7387 = (\u603B\u590D\u4E60\u6B21\u6570 - \u9057\u5FD8\u6B21\u6570) / \u603B\u590D\u4E60\u6B21\u6570
\u8BE5\u6307\u6807\u53CD\u6620\u4E86\u4F60\u7684\u5B66\u4E60\u6548\u679C\uFF0C\u8D8A\u9AD8\u8BF4\u660E\u8BB0\u5FC6\u6548\u679C\u8D8A\u597D`,"Open (double-click)":"\u53CC\u51FB\u6253\u5F00"};var ua={en:da,zh:ga};function p(M){let n=(Ba.moment.locale().startsWith("zh")?ua.zh:ua.en)[M];return n||M}var St=class{constructor(s,r){this.progressContainer=null;this.currentIndex=0;this.isFlipped=!1;this.cards=[];this.isActive=!1;this.currentCard=null;this.currentGroupName="All Cards";this.completionMessage=null;this.groupCompletionMessages={};this.groupProgress={};this.ratingButtons=[{label:p("Again"),rating:FA.AGAIN,key:"1",ratingText:"again",stability:.1},{label:p("Hard"),rating:FA.HARD,key:"2",ratingText:"hard",stability:.5},{label:p("Good"),rating:FA.GOOD,key:"3",ratingText:"good",stability:2},{label:p("Easy"),rating:FA.EASY,key:"4",ratingText:"easy",stability:4}];this.container=s,this.fsrsManager=r.fsrsManager,this.app=r.app,this.setupKeyboardShortcuts();let n=this.fsrsManager.getUIState();this.currentGroupName=n.currentGroupName,this.currentIndex=n.currentIndex,this.isFlipped=n.isFlipped,this.completionMessage=n.completionMessage||null,this.groupCompletionMessages=n.groupCompletionMessages||{},this.groupProgress=n.groupProgress||{}}formatInterval(s){return s<1?`${Math.round(s*24)}h`:s<30?`${Math.round(s)}d`:s<365?`${Math.round(s/30)}mo`:`${Math.round(s/365)}y`}setLicenseManager(s){this.licenseManager=s}setCards(s){var r;for(let n of s){if(!((r=n.comments)!=null&&r.length))continue;let a=n.text&&n.text.includes("#"),l=n.comments.map(h=>h.content).join("<hr>");n.filePath&&(this.fsrsManager.getCardsByFile(n.filePath).filter(g=>g.text===n.text).length===0?this.fsrsManager.addCard(n.text,l,n.filePath):this.fsrsManager.updateCardContent(n.text,l,n.filePath))}if(this.currentGroupName==="All Cards")this.cards=this.fsrsManager.getLatestCards();else if(this.currentGroupName==="Due Today")this.cards=this.fsrsManager.getDueCards();else if(this.currentGroupName==="New Cards")this.cards=this.fsrsManager.getLatestCards().filter(n=>n.lastReview===0);else if(this.currentGroupName==="Learned")this.cards=this.fsrsManager.getLatestCards().filter(n=>n.lastReview>0);else{let n=this.fsrsManager.getCardGroups().find(a=>a.name===this.currentGroupName);n?this.cards=this.fsrsManager.getCardsInGroup(n):this.cards=this.fsrsManager.getLatestCards()}this.currentIndex=0,this.isFlipped=!1,this.currentCard=this.cards[0]||null,this.isActive&&this.activate()}cleanup(){document.removeEventListener("keydown",this.boundHandleKeyDown),this.deactivate()}async activate(){if(!this.licenseManager)return;let s=await this.licenseManager.isActivated();this.isActive=!0,s?this.render():this.renderActivation()}renderActivation(){this.container.empty(),this.container.addClass("flashcard-mode");let s=this.container.createEl("div",{cls:"flashcard-activation-container"}),r=s.createEl("div",{cls:"flashcard-activation-header",text:p("Activate HiCard")}),n=s.createEl("div",{cls:"flashcard-activation-description",text:p("Enter your license key to activate HiCard feature.")}),a=s.createEl("div",{cls:"flashcard-activation-input-container"}),l=a.createEl("input",{cls:"flashcard-activation-input",type:"text",placeholder:p("Enter license key")});a.createEl("button",{cls:"flashcard-activation-button",text:p("Activate")}).addEventListener("click",async()=>{let g=l.value.trim();if(!g){new sA.Notice(p("Please enter a license key"));return}await this.licenseManager.activateLicense(g)?(new sA.Notice(p("HiCard activated successfully!")),this.render()):new sA.Notice(p("Invalid license key"))})}deactivate(){this.saveState(),this.isActive=!1,this.container.empty(),this.container.removeClass("flashcard-mode"),this.container.removeClass("flashcard-container"),this.container.removeClass("is-flipped");let s=this.container.querySelector(".flashcard-container");s&&s.remove()}showGroupModal(s){var JA,kA,Ue,nt,Pt;let r=document.createElement("div");r.className="flashcard-modal",r.addEventListener("keydown",IA=>{IA.stopPropagation()});let n=document.createElement("div");n.className="flashcard-modal-content";let a=document.createElement("h3");a.textContent=s?p("Edit Group"):p("Create New Group"),n.appendChild(a);let l=document.createElement("input");l.type="text",l.placeholder=p("Group Name"),l.className="flashcard-modal-input",s&&(l.value=s.name),n.appendChild(l);let h=document.createElement("textarea");h.placeholder=p(`\u652F\u6301\u4EE5\u4E0B\u683C\u5F0F\uFF1A
- \u6587\u4EF6\u5939\uFF1Afolder1, folder1/folder2
- \u7B14\u8BB0\uFF1A[[note1]], [[note2]]
- \u6807\u7B7E\uFF1A#tag1, #tag2
- \u901A\u914D\u7B26\uFF1A*.excalidraw.md
- \u5185\u5BB9\uFF1A\u76F4\u63A5\u8F93\u5165\u8981\u641C\u7D22\u7684\u6587\u672C`),h.className="flashcard-modal-input",h.rows=3,s&&(h.value=s.filter),n.appendChild(h);let g=document.createElement("div");g.className="flashcard-modal-option";let m=document.createElement("input");m.type="checkbox",m.className="flashcard-modal-checkbox",m.checked=(s==null?void 0:s.isReversed)||!1,g.appendChild(m);let w=document.createElement("label");w.textContent=p("\u53CD\u8F6C\u5361\u7247\uFF08\u4F7F\u7528\u8BC4\u8BBA\u4F5C\u4E3A\u95EE\u9898\uFF09"),w.className="flashcard-modal-label",g.appendChild(w),n.appendChild(g);let f=document.createElement("div");f.className="flashcard-modal-settings";let U=document.createElement("div");U.className="flashcard-modal-settings-header";let H=document.createElement("h4");H.textContent=p("Learning settings"),H.className="settings-title",U.appendChild(H);let I=document.createElement("div");I.className="flashcard-modal-option use-global-option";let Q=document.createElement("input");Q.type="checkbox",Q.className="flashcard-modal-checkbox",Q.id="use-global-settings",Q.checked=((JA=s==null?void 0:s.settings)==null?void 0:JA.useGlobalSettings)!==!1,I.appendChild(Q);let S=document.createElement("label");S.textContent=p("Use global settings"),S.className="flashcard-modal-label",S.htmlFor="use-global-settings",I.appendChild(S),U.appendChild(I),f.appendChild(U);let b=document.createElement("div");b.className="flashcard-modal-option slider-option";let y=document.createElement("label");y.textContent=p("New cards per day:"),y.className="flashcard-modal-label",b.appendChild(y);let V=document.createElement("div");V.className="slider-with-value";let R=document.createElement("input");R.type="range",R.min="5",R.max="100",R.step="5",R.className="flashcard-modal-slider",R.value=((Ue=(kA=s==null?void 0:s.settings)==null?void 0:kA.newCardsPerDay)==null?void 0:Ue.toString())||"20";let N=parseInt(R.value);N<5?R.value="5":N%5!==0&&(R.value=(Math.round(N/5)*5).toString()),V.appendChild(R);let _=document.createElement("span");_.className="slider-value",_.textContent=R.value,V.appendChild(_),R.addEventListener("input",()=>{_.textContent=R.value}),b.appendChild(V),f.appendChild(b);let Z=document.createElement("div");Z.className="flashcard-modal-option slider-option";let cA=document.createElement("label");cA.textContent=p("Reviews per day:"),cA.className="flashcard-modal-label",Z.appendChild(cA);let mA=document.createElement("div");mA.className="slider-with-value";let rA=document.createElement("input");rA.type="range",rA.min="10",rA.max="300",rA.step="10",rA.className="flashcard-modal-slider",rA.value=((Pt=(nt=s==null?void 0:s.settings)==null?void 0:nt.reviewsPerDay)==null?void 0:Pt.toString())||"100";let uA=parseInt(rA.value);uA<10?rA.value="10":uA%10!==0&&(rA.value=(Math.round(uA/10)*10).toString()),mA.appendChild(rA);let yA=document.createElement("span");yA.className="slider-value",yA.textContent=rA.value,mA.appendChild(yA),rA.addEventListener("input",()=>{yA.textContent=rA.value}),Z.appendChild(mA),f.appendChild(Z);let _A=()=>{let IA=Q.checked;R.disabled=IA,rA.disabled=IA,b.classList.toggle("disabled",IA),Z.classList.toggle("disabled",IA)};Q.addEventListener("change",_A),_A(),n.appendChild(f);let XA=document.createElement("div");XA.className="flashcard-modal-buttons";let aA=document.createElement("button");aA.textContent=p("Cancel"),aA.className="flashcard-modal-button",aA.onclick=()=>{document.body.removeChild(r)};let oA=document.createElement("button");oA.textContent=s?p("Save"):p("Create"),oA.className="flashcard-modal-button primary",oA.onclick=async()=>{let IA=l.value.trim(),at=h.value.trim();if(!IA||!at){new sA.Notice(p("Please fill in all fields"));return}try{oA.disabled=!0,oA.textContent=s?p("Saving..."):p("Creating...");let Pe={useGlobalSettings:Q.checked,newCardsPerDay:parseInt(R.value),reviewsPerDay:parseInt(rA.value)};if(s)await this.fsrsManager.updateCardGroup(s.id,{name:IA,filter:at,isReversed:m.checked,settings:Pe})?(this.currentGroupName===s.name&&(this.currentGroupName=IA),document.body.removeChild(r),this.render(),new sA.Notice(p("Group updated successfully"))):new sA.Notice(p("Failed to update group"));else{let or=await this.fsrsManager.createCardGroup({name:IA,filter:at,sortOrder:this.fsrsManager.getCardGroups().length,createdTime:Date.now(),isReversed:m.checked,settings:Pe});this.currentGroupName=IA,document.body.removeChild(r),this.render(),new sA.Notice(p("Group created successfully"))}}catch(Pe){new sA.Notice(p("Failed to create or update group"))}finally{oA.disabled=!1,oA.textContent=s?p("Save"):p("Create")}},XA.appendChild(aA),XA.appendChild(oA),n.appendChild(XA),r.appendChild(n),document.body.appendChild(r),l.focus()}showCreateGroupModal(){this.showGroupModal()}showEditGroupModal(s){this.showGroupModal(s)}renderGroupStats(s,r){let n=r?this.fsrsManager.getGroupProgress(r):this.fsrsManager.getProgress();if(!n)return;let a=s.createEl("div",{cls:"flashcard-stats-container"}),l=a.createEl("div",{cls:"flashcard-stat-item"});l.createEl("span",{cls:"flashcard-stat-label",text:p("Due Today")}),l.createEl("span",{cls:"flashcard-stat-value",text:n.due.toString()});let h=a.createEl("div",{cls:"flashcard-stat-item"});h.createEl("span",{cls:"flashcard-stat-label",text:p("New Cards")}),h.createEl("span",{cls:"flashcard-stat-value",text:n.newCards.toString()});let g=a.createEl("div",{cls:"flashcard-stat-item"});g.createEl("span",{cls:"flashcard-stat-label",text:p("Learned")}),g.createEl("span",{cls:"flashcard-stat-value",text:n.learned.toString()})}render(){if(!this.isActive){this.deactivate();return}this.container.empty(),this.container.addClass("flashcard-mode"),this.progressContainer=this.container.createEl("div",{cls:"flashcard-progress-container"});let s=this.progressContainer.createEl("div",{cls:"flashcard-progress-text"});s.createSpan({text:this.currentGroupName,cls:"group-name"}),s.createSpan({text:"|",cls:"separator"});let r=this.getGroupProgress();[{label:p("Due"),value:r.due},{label:p("New"),value:r.newCards},{label:p("Learned"),value:r.learned},{label:p("Retention"),value:`${(r.retention*100).toFixed(1)}%`}].forEach((y,V)=>{V>0&&s.createSpan({text:"|",cls:"separator"});let R=s.createEl("div",{cls:"stat"});if(R.createSpan({text:y.label+": "}),R.createSpan({text:y.value.toString(),cls:"stat-value"}),y.label===p("Retention")){let N=R.createSpan({cls:"help-icon"});(0,sA.setIcon)(N,"help-circle"),N.setAttribute("aria-label",p(`\u8BB0\u5FC6\u4FDD\u6301\u7387 = (\u603B\u590D\u4E60\u6B21\u6570 - \u9057\u5FD8\u6B21\u6570) / \u603B\u590D\u4E60\u6B21\u6570
\u8BE5\u6307\u6807\u53CD\u6620\u4E86\u4F60\u7684\u5B66\u4E60\u6548\u679C\uFF0C\u8D8A\u9AD8\u8BF4\u660E\u8BB0\u5FC6\u6548\u679C\u8D8A\u597D`))}}),this.updateProgress();let a=this.container.createEl("div",{cls:"flashcard-main-container"}),l=a.createEl("div",{cls:"flashcard-sidebar"}),g=l.createEl("div",{cls:"flashcard-default-groups"}).createEl("div",{cls:"flashcard-group-list"}),m=this.fsrsManager.getLatestCards(),w=Date.now();[{name:p("All Cards"),icon:"gallery-thumbnails",getCards:()=>m},{name:p("Due Today"),icon:"calendar-clock",getCards:()=>m.filter(y=>y.nextReview<=w)},{name:p("New Cards"),icon:"sparkle",getCards:()=>m.filter(y=>y.lastReview===0)},{name:p("Learned"),icon:"check-small",getCards:()=>m.filter(y=>y.lastReview>0)}].forEach((y,V)=>{let R=y.getCards(),N=g.createEl("div",{cls:`flashcard-group-item ${y.name===this.currentGroupName?"active":""}`}),_=N.createEl("div",{cls:"flashcard-group-item-left"}),Z=_.createEl("div",{cls:"flashcard-group-icon"});(0,sA.setIcon)(Z,y.icon),_.createEl("span",{cls:"flashcard-group-name",text:y.name}),N.createEl("span",{cls:"flashcard-group-count",text:R.length.toString()}),N.addEventListener("click",()=>{this.groupCompletionMessages[this.currentGroupName]=this.completionMessage,this.groupProgress[this.currentGroupName]={currentIndex:this.currentIndex,isFlipped:this.isFlipped},this.currentGroupName=y.name,this.container.querySelectorAll(".flashcard-group-item").forEach(mA=>mA.classList.remove("active")),N.classList.add("active"),this.completionMessage=this.groupCompletionMessages[y.name]||null,this.cards=R,this.groupProgress[y.name]&&!this.completionMessage?(this.currentIndex=this.groupProgress[y.name].currentIndex,this.isFlipped=this.groupProgress[y.name].isFlipped):(this.currentIndex=0,this.isFlipped=!1),this.currentCard=this.cards[this.currentIndex]||null,this.saveState(),this.render()})});let U=l.createEl("div",{cls:"flashcard-custom-groups"}),H=U.createEl("div",{cls:"flashcard-add-group"});(0,sA.setIcon)(H,"plus"),H.addEventListener("click",()=>this.showCreateGroupModal());let I=U.createEl("div",{cls:"flashcard-group-list"});(this.fsrsManager.getCardGroups()||[]).forEach(y=>{let V=I.createEl("div",{cls:`flashcard-group-item ${y.name===this.currentGroupName?"active":""}`}),R=V.createEl("div",{cls:"flashcard-group-item-header"}),N=R.createEl("div",{cls:"flashcard-group-title"}),_=N.createEl("span",{cls:"flashcard-group-icon"});(0,sA.setIcon)(_,y.filter.startsWith("#")?"hash":"gallery-horizontal-end"),N.createEl("span",{cls:"flashcard-group-name",text:y.name});let Z=R.createEl("div",{cls:"flashcard-group-actions"}),cA=Z.createEl("div",{cls:"flashcard-group-action"});(0,sA.setIcon)(cA,"edit"),cA.addEventListener("click",uA=>{uA.stopPropagation(),this.showEditGroupModal(y)});let mA=Z.createEl("div",{cls:"flashcard-group-action"});(0,sA.setIcon)(mA,"trash"),mA.addEventListener("click",async uA=>{if(uA.stopPropagation(),confirm(p('\u786E\u5B9A\u8981\u5220\u9664\u5206\u7EC4 "')+y.name+p('" \u5417\uFF1F')))try{await this.fsrsManager.deleteCardGroup(y.id)?(this.currentGroupName===y.name&&(this.currentGroupName="All Cards"),new sA.Notice(p("\u5206\u7EC4\u5220\u9664\u6210\u529F")),this.render()):new sA.Notice(p("\u5220\u9664\u5206\u7EC4\u5931\u8D25"))}catch(yA){new sA.Notice(p("\u5220\u9664\u5206\u7EC4\u5931\u8D25"))}});let rA=this.fsrsManager.getGroupProgress(y.id);if(rA){let uA=V.createEl("div",{cls:"flashcard-group-stats"}),yA=uA.createEl("div",{cls:"flashcard-group-stat",attr:{"data-tooltip":p("Due Today")}}),_A=yA.createEl("span",{cls:"flashcard-stat-icon"});(0,sA.setIcon)(_A,"calendar-clock"),yA.createEl("span",{text:rA.due.toString()});let XA=uA.createEl("div",{cls:"flashcard-group-stat",attr:{"data-tooltip":p("New Cards")}}),aA=XA.createEl("span",{cls:"flashcard-stat-icon"});(0,sA.setIcon)(aA,"sparkle"),XA.createEl("span",{text:rA.newCards.toString()});let oA=uA.createEl("div",{cls:"flashcard-group-stat",attr:{"data-tooltip":p("Learned")}}),JA=oA.createEl("span",{cls:"flashcard-stat-icon"});(0,sA.setIcon)(JA,"check-small"),oA.createEl("span",{text:rA.learned.toString()})}V.addEventListener("click",()=>{this.groupCompletionMessages[this.currentGroupName]=this.completionMessage,this.groupProgress[this.currentGroupName]={currentIndex:this.currentIndex,isFlipped:this.isFlipped},this.currentGroupName=y.name,this.container.querySelectorAll(".flashcard-group-item").forEach(_A=>_A.classList.remove("active")),V.classList.add("active"),this.completionMessage=this.groupCompletionMessages[y.name]||null;let yA=this.fsrsManager.getCardsInGroup(y);this.cards=yA,this.groupProgress[y.name]&&!this.completionMessage?(this.currentIndex=this.groupProgress[y.name].currentIndex,this.isFlipped=this.groupProgress[y.name].isFlipped):(this.currentIndex=0,this.isFlipped=!1),this.currentCard=this.cards[this.currentIndex]||null,this.saveState(),this.render()})});let b=a.createEl("div",{cls:"flashcard-content-area"}).createEl("div",{cls:"flashcard-container"});if(this.cards.length===0){b.createEl("div",{cls:"flashcard-empty",text:p("No cards due for review")});return}if(this.completionMessage){let y=b.createEl("div",{cls:"flashcard-completion-message"}),V=y.createEl("div",{cls:"completion-icon"});(0,sA.setIcon)(V,"check-circle"),y.createEl("h3",{text:p("\u5B66\u4E60\u5B8C\u6210\uFF01")}),y.createEl("p",{text:this.completionMessage}),y.createEl("button",{cls:"flashcard-return-button",text:p("\u8FD4\u56DE\u7B2C\u4E00\u5F20\u5361\u7247")}).addEventListener("click",()=>{this.completionMessage=null,this.groupCompletionMessages[this.currentGroupName]=null,this.groupProgress[this.currentGroupName]={currentIndex:0,isFlipped:!1},this.currentIndex=0,this.currentCard=this.cards[0]||null,this.isFlipped=!1,this.saveState(),this.render()});return}if(this.currentCard){let y=b.createEl("div",{cls:"flashcard"}),N=this.fsrsManager.getCardGroups().filter(aA=>this.fsrsManager.getCardsInGroup(aA).some(JA=>{var kA;return JA.id===((kA=this.currentCard)==null?void 0:kA.id)})).some(aA=>aA.isReversed),_=N?this.currentCard.answer:this.currentCard.text,Z=N?this.currentCard.text:this.currentCard.answer,cA=N,mA=aA=>aA.split("<hr>").filter(kA=>!/^(#[^\s#]+\s*)+$/.test(kA.trim())).join("<hr>");N?_=mA(_):Z=mA(Z);let uA=y.createEl("div",{cls:"flashcard-side flashcard-front"}).createEl("div",{cls:"flashcard-content"});cA?this.renderHTMLContent(uA,_):uA.textContent=_;let _A=y.createEl("div",{cls:"flashcard-side flashcard-back"}).createEl("div",{cls:"flashcard-content"});cA?_A.textContent=Z:this.renderHTMLContent(_A,Z),y.addEventListener("click",()=>this.flipCard());let XA=b.createEl("div",{cls:"flashcard-rating"});if(this.ratingButtons.forEach(aA=>{var kA;let oA=XA.createEl("button",{cls:"flashcard-rating-button",attr:{"data-rating":aA.ratingText,title:`${aA.label} (${aA.key})`}});oA.createSpan({text:aA.label});let JA=((kA=this.currentCard)==null?void 0:kA.lastReview)===0?aA.stability:this.fsrsManager.fsrsService.calculateNextInterval(.9,aA.stability);oA.createSpan({text:this.formatInterval(JA),cls:"days"}),oA.addEventListener("click",Ue=>{Ue.stopPropagation(),this.rateCard(aA.rating)})}),this.isFlipped&&y.classList.add("is-flipped"),b.createEl("div",{cls:"flashcard-counter",text:`${this.currentIndex+1}/${this.cards.length}`}),this.currentCard.filePath){let oA=b.createEl("div",{cls:"flashcard-source"}).createEl("span",{text:this.currentCard.filePath.split("/").pop()||""});this.addPagePreview(oA,this.currentCard.filePath)}}else b.createEl("div",{cls:"flashcard-empty-state",text:p("No cards available")})}setupKeyboardShortcuts(){this.boundHandleKeyDown=this.handleKeyDown.bind(this),document.addEventListener("keydown",this.boundHandleKeyDown)}handleKeyDown(s){if(!(!this.isActive||s.target instanceof HTMLInputElement||s.target instanceof HTMLTextAreaElement||!(this.container.contains(s.target)||this.container===s.target))){if(s.key==="Enter"||s.key===" ")s.preventDefault(),this.flipCard();else if(s.key==="ArrowLeft")s.preventDefault(),this.isFlipped||this.previousCard();else if(s.key==="ArrowRight")s.preventDefault(),this.isFlipped||this.nextCard();else if(this.isFlipped){let n=this.ratingButtons.find(a=>a.key===s.key);n&&(s.preventDefault(),this.rateCard(n.rating))}}}rateCard(s){if(!this.currentCard)return;let r=this.fsrsManager.reviewCard(this.currentCard.id,s);if(r){this.cards[this.currentIndex]=r;let n=!1,a=this.fsrsManager.getCardGroups().find(h=>h.name===this.currentGroupName),l=a==null?void 0:a.id;if(this.currentGroupName==="New Cards"&&!this.fsrsManager.canLearnNewCardsToday())this.completionMessage=p("\u60A8\u4ECA\u5929\u7684\u65B0\u5361\u7247\u5B66\u4E60\u914D\u989D\u5DF2\u7528\u5B8C\uFF01\u660E\u5929\u518D\u6765\u5B66\u4E60\u5427\u3002");else if(this.currentGroupName==="Due Today"&&!this.fsrsManager.canReviewCardsToday())this.completionMessage=p("\u60A8\u4ECA\u5929\u7684\u590D\u4E60\u914D\u989D\u5DF2\u7528\u5B8C\uFF01\u660E\u5929\u518D\u6765\u590D\u4E60\u5427\u3002");else if(l){let h=r.reviews===1;h&&!this.fsrsManager.canLearnNewCardsToday(l)?this.completionMessage=p('\u60A8\u4ECA\u5929\u5728 "')+this.currentGroupName+p('" \u5206\u7EC4\u7684\u65B0\u5361\u7247\u5B66\u4E60\u914D\u989D\u5DF2\u7528\u5B8C\uFF01\u660E\u5929\u518D\u6765\u5B66\u4E60\u5427\u3002'):!h&&!this.fsrsManager.canReviewCardsToday(l)?this.completionMessage=p('\u60A8\u4ECA\u5929\u5728 "')+this.currentGroupName+p('" \u5206\u7EC4\u7684\u590D\u4E60\u914D\u989D\u5DF2\u7528\u5B8C\uFF01\u660E\u5929\u518D\u6765\u590D\u4E60\u5427\u3002'):this.currentIndex<this.cards.length-1?(this.currentIndex++,n=!0):(this.completionMessage=p('\u606D\u559C\uFF01\u60A8\u5DF2\u5B8C\u6210 "')+this.currentGroupName+p('" \u4E2D\u7684\u6240\u6709\u5361\u7247\u5B66\u4E60\u3002'),this.currentIndex=0,n=this.cards.length>0)}else this.currentIndex<this.cards.length-1?(this.currentIndex++,n=!0):(this.completionMessage=p('\u606D\u559C\uFF01\u60A8\u5DF2\u5B8C\u6210 "')+this.currentGroupName+p('" \u4E2D\u7684\u6240\u6709\u5361\u7247\u5B66\u4E60\u3002'),this.currentIndex=0,n=this.cards.length>0);this.currentCard=n?this.cards[this.currentIndex]:null,this.isFlipped=!1,this.saveState(),this.render(),this.updateProgress(),(this.currentGroupName==="New Cards"||this.currentGroupName==="Due Today")&&this.refreshCardList()}}refreshCardList(){if(this.currentGroupName==="New Cards"){let s=this.fsrsManager.getLatestCards();this.cards=s.filter(r=>r.lastReview===0)}else if(this.currentGroupName==="Due Today"){let s=this.fsrsManager.getLatestCards(),r=Date.now();this.cards=s.filter(n=>n.nextReview<=r)}else if(this.currentGroupName==="All Cards")this.cards=this.fsrsManager.getLatestCards();else if(this.currentGroupName==="Learned"){let s=this.fsrsManager.getLatestCards();this.cards=s.filter(r=>r.lastReview>0)}else{let s=this.fsrsManager.getCardGroups().find(r=>r.name===this.currentGroupName);if(s){let r=this.fsrsManager.getCardsInGroup(s);if(s.settings&&!s.settings.useGlobalSettings&&(this.fsrsManager.getRemainingNewCardsToday(s.id)<=0&&(r=r.filter(l=>l.lastReview>0)),this.fsrsManager.getRemainingReviewsToday(s.id)<=0)){let l=Date.now();r=r.filter(h=>h.lastReview===0||h.nextReview>l)}this.cards=r}}this.completionMessage||(this.currentIndex>=this.cards.length&&(this.currentIndex=0),this.currentCard=this.cards[this.currentIndex]||null,this.isFlipped=!1),this.saveState(),this.render()}flipCard(){if(!this.currentCard)return;this.isFlipped=!this.isFlipped;let s=this.container.querySelector(".flashcard");s&&(s.classList.toggle("is-flipped",this.isFlipped),this.saveState(),this.updateProgress())}nextCard(){this.currentIndex<this.cards.length-1&&(this.currentIndex++,this.currentCard=this.cards[this.currentIndex],this.isFlipped=!1,this.saveState(),this.render(),this.updateProgress())}saveState(){this.fsrsManager.updateUIState({currentGroupName:this.currentGroupName,currentIndex:this.currentIndex,isFlipped:this.isFlipped,completionMessage:this.completionMessage,groupCompletionMessages:this.groupCompletionMessages,groupProgress:this.groupProgress})}getGroupProgress(){let s={due:0,newCards:0,learned:0,retention:0};if(this.currentGroupName==="All Cards")return this.fsrsManager.getProgress()||s;let r=this.fsrsManager.getCardGroups().find(l=>l.name===this.currentGroupName);if(r)return this.fsrsManager.getGroupProgress(r.id)||s;let n=this.fsrsManager.getLatestCards(),a=Date.now();if(this.currentGroupName==="Due Today"){let l=n.filter(h=>h.nextReview<=a);return{due:l.length,newCards:l.filter(h=>h.lastReview===0).length,learned:l.filter(h=>h.lastReview>0).length,retention:this.calculateRetention(l)}}else if(this.currentGroupName==="New Cards"){let l=n.filter(h=>h.lastReview===0);return{due:l.filter(h=>h.nextReview<=a).length,newCards:l.length,learned:0,retention:0}}else if(this.currentGroupName==="Learned"){let l=n.filter(h=>h.lastReview>0);return{due:l.filter(h=>h.nextReview<=a).length,newCards:0,learned:l.length,retention:this.calculateRetention(l)}}return this.fsrsManager.getProgress()||s}calculateRetention(s){if(s.length===0)return 0;let r=s.filter(l=>l.lastReview>0);if(r.length===0)return 0;let n=r.reduce((l,h)=>l+h.reviews,0),a=r.reduce((l,h)=>l+h.lapses,0);return n>0?(n-a)/n:0}updateProgress(){if(!this.progressContainer||this.cards.length===0)return;let r=this.currentIndex/this.cards.length*100;this.progressContainer.style.setProperty("--progress-width",`${r}%`);let n=this.progressContainer.querySelector(".progress-text"),a=`${this.currentIndex+1}/${this.cards.length} | ${Math.round(r)}%`;if(n)n.textContent=a;else{let m=this.progressContainer.createSpan({cls:"progress-text"});m.textContent=a}let l=this.progressContainer.querySelector(".group-name");l&&(l.textContent=this.currentGroupName);let h=this.getGroupProgress(),g=this.progressContainer.querySelectorAll(".stat-value");if(g.length>=4&&(g[0].textContent=h.due.toString(),g[1].textContent=h.newCards.toString(),g[2].textContent=h.learned.toString(),g[3].textContent=`${(h.retention*100).toFixed(1)}%`,g.length>=5)){let m=this.fsrsManager.getCardGroups().find(H=>H.name===this.currentGroupName),w=m==null?void 0:m.id,f=this.fsrsManager.getRemainingNewCardsToday(w),U=this.fsrsManager.getRemainingReviewsToday(w);g[4].textContent=`${f} ${p("New")}, ${U} ${p("Review")}`}}addPagePreview(s,r){if(!r)return;let n=this.app.vault.getAbstractFileByPath(r);if(!(n instanceof sA.TFile))return;let a;s.addEventListener("mouseenter",l=>{a=setTimeout(async()=>{let h=l.target;this.app.workspace.trigger("hover-link",{event:l,source:"hi-note",hoverParent:h,targetEl:h,linktext:n.path})},300)}),s.addEventListener("mouseleave",()=>{a&&clearTimeout(a)})}previousCard(){this.currentIndex>0&&(this.currentIndex--,this.currentCard=this.cards[this.currentIndex],this.isFlipped=!1,this.saveState(),this.render(),this.updateProgress())}renderHTMLContent(s,r){for(;s.firstChild;)s.removeChild(s.firstChild);let n=r.split("<hr>");n.forEach((a,l)=>{if(a.trim()){let h=s.createEl("div",{cls:"flashcard-paragraph"}),g=a.trim().replace(/<\/?b>/g,"**").replace(/<\/?i>/g,"_").replace(/<\/?u>/g,"").replace(/<\/?strong>/g,"**").replace(/<\/?em>/g,"_").replace(/<br\s*\/?>/g,`
`).replace(/<\/?p>/g,`
`).replace(/<\/?div>/g,`
`).replace(/<span class="highlight-tag">(.*?)<\/span>/g,"$1").replace(/<[^>]*>/g,"");h.setText(g)}l<n.length-1&&s.createEl("hr")})}destroy(){document.removeEventListener("keydown",this.boundHandleKeyDown),this.container.removeClass("flashcard-mode"),this.container.empty()}};var bt=require("obsidian");var pa={id:"default",name:p("Default Template"),description:p("Modern minimalist knowledge card style"),render:M=>{var I;let s=document.createElement("div");s.className="highlight-export-card highlight-export-card-modern";let r=document.createElement("div");r.className="highlight-export-quote-section";let n=document.createElement("div");n.className="highlight-export-quote-decoration";let a=document.createElementNS("http://www.w3.org/2000/svg","svg");a.setAttribute("viewBox","0 0 24 24"),a.setAttribute("width","48"),a.setAttribute("height","48"),a.setAttribute("fill","none"),a.setAttribute("stroke","currentColor"),a.setAttribute("stroke-width","1");let l=document.createElementNS("http://www.w3.org/2000/svg","path");l.setAttribute("d","M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z");let h=document.createElementNS("http://www.w3.org/2000/svg","path");h.setAttribute("d","M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z"),a.appendChild(l),a.appendChild(h),n.appendChild(a),r.appendChild(n);let g=document.createElement("div");g.className="highlight-export-quote",g.textContent=M.text,r.appendChild(g),s.appendChild(r);let m=document.createElement("div");m.className="highlight-export-footer";let w=document.createElement("div");w.className="highlight-export-source",w.textContent=M.fileName||((I=M.filePath)==null?void 0:I.split("/").pop())||"Untitled",m.appendChild(w);let f=document.createElement("div");f.className="highlight-export-date";let U=new Date,H={year:"numeric",month:"long",day:"numeric"};return f.textContent=U.toLocaleDateString(void 0,H),m.appendChild(f),s.appendChild(m),s}},Wd={id:"academic",name:p("Academic Template"),description:p("Formal style suitable for academic citations"),render:M=>{var h;let s=document.createElement("div");s.className="highlight-export-card highlight-export-card-academic";let r=document.createElement("div");r.className="highlight-export-quote",r.textContent=`"${M.text}"`,s.appendChild(r);let n=document.createElement("div");n.className="highlight-export-footer";let a=document.createElement("div");a.className="highlight-export-source",a.textContent=M.fileName||((h=M.filePath)==null?void 0:h.split("/").pop())||"Untitled",n.appendChild(a);let l=document.createElement("div");return l.className="highlight-export-date",l.textContent=`Retrieved: ${new Date().toLocaleDateString()}`,n.appendChild(l),s.appendChild(n),s}},Yd={id:"social",name:p("Social Template"),description:p("Modern style suitable for social media sharing"),render:M=>{var U;let s=document.createElement("div");s.className="highlight-export-card highlight-export-card-social";let r=document.createElement("div");r.className="highlight-export-header";let n=document.createElement("div");n.className="highlight-export-logo";let a=document.createElementNS("http://www.w3.org/2000/svg","svg");a.setAttribute("xmlns","http://www.w3.org/2000/svg"),a.setAttribute("width","24"),a.setAttribute("height","24"),a.setAttribute("viewBox","0 0 24 24"),a.setAttribute("fill","none"),a.setAttribute("stroke","currentColor"),a.setAttribute("stroke-width","2"),a.setAttribute("stroke-linecap","round"),a.setAttribute("stroke-linejoin","round");let l=document.createElementNS("http://www.w3.org/2000/svg","circle");l.setAttribute("cx","12"),l.setAttribute("cy","12"),l.setAttribute("r","10");let h=[["14.31","8","20.05","17.94"],["9.69","8","21.17","8"],["7.38","12","13.12","2.06"],["9.69","16","3.95","6.06"],["14.31","16","2.83","16"],["16.62","12","10.88","21.94"]].map(([H,I,Q,S])=>{let b=document.createElementNS("http://www.w3.org/2000/svg","line");return b.setAttribute("x1",H),b.setAttribute("y1",I),b.setAttribute("x2",Q),b.setAttribute("y2",S),b});a.appendChild(l),h.forEach(H=>a.appendChild(H)),n.appendChild(a),r.appendChild(n);let g=document.createElement("div");g.className="highlight-export-app-name",g.textContent="Obsidian",r.appendChild(g),s.appendChild(r);let m=document.createElement("div");m.className="highlight-export-quote",m.textContent=M.text,s.appendChild(m);let w=document.createElement("div");w.className="highlight-export-footer";let f=document.createElement("div");return f.className="highlight-export-source",f.textContent=M.fileName||((U=M.filePath)==null?void 0:U.split("/").pop())||"Untitled",w.appendChild(f),s.appendChild(w),s}},ts=[pa,Wd,Yd];function is(M){return ts.find(s=>s.id===M)||pa}var ma=`
    /* \u5BFC\u51FA\u5361\u7247\u57FA\u7840\u6837\u5F0F */
    .highlight-export-card {
        transition: all 0.3s ease;
        padding: 20px;
        background-color: #ffffff;
        border-radius: 12px;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        
        /* \u6DFB\u52A0\u66F4\u4E25\u683C\u7684\u6587\u672C\u63A7\u5236 */
        font-size: 16px;
        line-height: 1.6;
        letter-spacing: normal;
        word-spacing: normal;
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    }

    /* \u73B0\u4EE3\u98CE\u683C\u6A21\u677F */
    .highlight-export-card-modern {
        padding: 24px;
        position: relative;
        overflow: hidden;
    }

    .highlight-export-card-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background: linear-gradient(to bottom right, #5871ef 0%, #4c63e6 100%);
    }

    .highlight-export-card-modern .highlight-export-quote-decoration {
        position: absolute;
        top: 24px;
        right: 24px;
        opacity: 0.06;
        transform: scale(2);
    }

    .highlight-export-card-modern .highlight-export-quote-section {
        position: relative;
        padding: 24px 0;
        /* \u786E\u4FDD\u5F15\u7528\u90E8\u5206\u7684\u6587\u672C\u6837\u5F0F\u4E00\u81F4\u6027 */
        font-size: inherit;
        line-height: inherit;
        letter-spacing: inherit;
        word-spacing: inherit;
    }

    .highlight-export-card-modern .highlight-export-quote {
        font-size: 1em;
        line-height: 1.7;
        color: #333333;
        font-weight: 400;
        margin: 0;
        position: relative;
        z-index: 1;
        /* \u786E\u4FDD\u5F15\u7528\u6587\u672C\u7684\u6837\u5F0F\u4E00\u81F4\u6027 */
        letter-spacing: normal;
        word-spacing: normal;
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
    }

    /* \u786E\u4FDD\u6240\u6709\u6587\u672C\u5143\u7D20\u7EE7\u627F\u57FA\u7840\u6837\u5F0F */
    .highlight-export-card * {
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
    }

    /* \u5BFC\u51FA\u9884\u89C8\u5BB9\u5668 */
    .highlight-export-preview {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        padding: 20px;
    }

    /* \u5BFC\u51FA\u5BB9\u5668 */
    .highlight-export-container {
        padding: 20px;
        margin: 0;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        width: 480px;
    }
`;var Ii=class extends bt.Modal{constructor(r,n,a){super(r);this.selectedTemplateId="default";this.highlight=n,this.html2canvasInstance=a}async onOpen(){let{contentEl:r}=this;r.empty(),r.addClass("highlight-export-modal");let n=r.createEl("div",{cls:"highlight-export-main-container"}),l=n.createEl("div",{cls:"highlight-template-selector"}).createEl("select",{cls:"highlight-template-select"});ts.forEach(g=>{let m=l.createEl("option",{text:g.name,value:g.id});this.selectedTemplateId===g.id&&(m.selected=!0)}),l.addEventListener("change",g=>{let m=g.target;this.selectedTemplateId=m.value,this.updatePreview()}),this.previewContainer=n.createEl("div",{cls:"highlight-export-preview-container"}),this.updatePreview();let h=r.createEl("div",{cls:"highlight-export-modal-buttons"});h.createEl("button",{cls:"highlight-btn",text:p("Cancel")}).addEventListener("click",()=>this.close()),h.createEl("button",{cls:"highlight-btn highlight-btn-primary",text:p("Download")}).addEventListener("click",async()=>{try{let g=document.createElement("div");g.className="highlight-export-container";let w=is(this.selectedTemplateId).render(this.highlight);g.appendChild(w),document.body.appendChild(g);let U=(await this.html2canvasInstance(g,{backgroundColor:null,scale:window.devicePixelRatio*2,useCORS:!0,allowTaint:!0,logging:!1,imageTimeout:0,removeContainer:!0,onclone:async I=>{let Q=I.createElement("style");Q.textContent=this.getExportStyles(),I.head.appendChild(Q),await new Promise(S=>setTimeout(S,100))}})).toDataURL("image/png",1),H=document.createElement("a");H.download=`highlight-${this.selectedTemplateId}-${Date.now()}.png`,H.href=U,H.click(),this.close(),new bt.Notice(p("Export successful!"))}catch(g){new bt.Notice(p("Export failed, please try again"))}})}updatePreview(){this.previewContainer.empty(),this.previewContainer.className="highlight-export-preview";let n=is(this.selectedTemplateId).render(this.highlight);this.previewContainer.appendChild(n)}getExportStyles(){return`
            body {
                margin: 0;
                background: none;
            }
            ${ma}
        `}onClose(){let{contentEl:r}=this;r.empty()}};var xt=class{constructor(s,r,n){this.highlight=r;this.onHighlightClick=n;this.render(s)}render(s){this.container=s.createEl("div",{cls:"highlight-content"}),this.renderText()}renderText(){this.textContainer=this.container.createEl("div",{cls:"highlight-text-container"});let s=this.textContainer.createEl("div",{cls:"highlight-text-decorator"});this.highlight.backgroundColor&&(s.style.backgroundColor=this.highlight.backgroundColor);let n=this.textContainer.createEl("div",{cls:"highlight-text"}).createEl("div",{cls:`highlight-text-content ${this.highlight.isVirtual?"virtual-highlight":""}`}),l=((this.highlight.isVirtual?this.highlight.displayText:this.highlight.text)||"").split(`
`);l.forEach((h,g)=>{let m=n.createEl("p",{text:h,cls:"highlight-text-line"});g<l.length-1&&m.addClass("highlight-text-line-spacing")}),n.addEventListener("mousedown",async h=>{h.preventDefault(),h.stopPropagation(),await this.onHighlightClick(this.highlight)})}getContainer(){return this.container}};xt.dragPreview=null;var as=require("obsidian");var Le=require("obsidian");var qe=[{id:"deepseek-chat",name:"Deepseek Chat"},{id:"deepseek-reasoner",name:"Deepseek Reasoner"}],we=[{id:"gemini-1.5-flash",name:"Gemini 1.5 Flash"},{id:"gemini-1.5-pro",name:"Gemini 1.5 Pro"},{id:"gemini-2.0-flash",name:"Gemini 2.0 Flash"},{id:"gemini-2.0-flash-lite-preview-02-05",name:"Gemini 2.0 Flash Lite"}],jA=[{id:"deepseek-ai/DeepSeek-V3",name:"DeepSeek V3",isCustom:!1},{id:"Qwen/Qwen2.5-7B-Instruct",name:"Qwen2.5 7B",isCustom:!1},{id:"Qwen/Qwen2.5-14B-Instruct",name:"Qwen2.5 14B",isCustom:!1},{id:"Pro/Qwen/Qwen2-7B-Instruct",name:"Qwen2 7B",isCustom:!1},{id:"Pro/THUDM/glm-4-9b-chat",name:"GLM-4 9B",isCustom:!1},{id:"google/gemma-2-9b-it",name:"Gemma2 9B",isCustom:!1}],W={excludePatterns:"",useCustomPattern:!1,highlightPattern:"==\\s*(.*?)\\s*==|<mark[^>]*>(.*?)</mark>|<span[^>]*>(.*?)</span>",defaultHighlightColor:"#ffeb3b",ai:{provider:"ollama",ollama:{host:"http://localhost:11434",model:""},gemini:{apiKey:"",model:"gemini-pro",baseUrl:"",isCustomModel:!1},openai:{apiKey:"",model:"gpt-4o",baseUrl:""},anthropic:{apiKey:"",model:"claude-2",apiAddress:"",isCustomModel:!1,lastCustomModel:""},deepseek:{apiKey:"",model:"deepseek-chat",baseUrl:""},prompts:{"\u{1F914} Key Insight":"{{highlight}}.Please reinterpret the above content from a fresh perspective and summarize its core idea within 200 characters."}},export:{exportPath:""}};var je=require("obsidian"),ze=class{constructor(s="http://localhost:11434"){this.retryAttempts=3;this.retryDelay=1e3;!s.startsWith("http://")&&!s.startsWith("https://")&&(s="http://"+s),this.baseUrl=s.replace(/\/$/,"")}async listModels(){try{await this.ensureConnection();let s=await this.makeRequest({endpoint:"/api/tags",method:"GET"});if(!s||!s.models)throw new Error("Invalid API response format");return s.models.map(r=>r.name)}catch(s){throw this.handleError(s)}}async generateCompletion(s,r){try{await this.ensureConnection();let n=await this.makeRequest({endpoint:"/api/generate",method:"POST",body:JSON.stringify({model:s,prompt:r,stream:!1})});if(!n||!n.response)throw new Error("Invalid API response format");return n.response}catch(n){throw this.handleError(n)}}async pullModel(s){try{new je.Notice(`Downloading model ${s}...`);let r=await this.makeRequest({endpoint:"/api/pull",method:"POST",body:JSON.stringify({name:s})});if(!r.ok)throw new Error(`Failed to download model: ${r.status}`);new je.Notice(`Model ${s} downloaded successfully`)}catch(r){throw new Error(`Failed to download model: ${r.message}`)}}async chat(s,r){var n;try{await this.ensureConnection();let a=await this.makeRequest({endpoint:"/api/chat",method:"POST",body:JSON.stringify({model:s,messages:r,stream:!1})});if(!a||!((n=a.message)!=null&&n.content))throw new Error("Invalid API response format");return a.message.content}catch(a){throw this.handleError(a)}}async ensureConnection(){if(!this.baseUrl)throw new Error("Ollama service not configured. Please set the host in settings.");if(!await this.testConnection())throw new Error("Unable to connect to Ollama service. Please ensure the service is running.")}async testConnection(){if(!this.baseUrl)return!1;try{let s=await this.makeRequest({endpoint:"/api/version",method:"GET"});return!!(s!=null&&s.version)}catch(s){return!1}}async makeRequest(s){let r=null;for(let n=1;n<=this.retryAttempts;n++)try{let a=new URL(s.endpoint,this.baseUrl).toString(),l=await(0,je.requestUrl)({url:a,method:s.method,headers:{"Content-Type":"application/json",Accept:"application/json"},body:s.body,throw:!1});if(l.status===200)try{return l.text?JSON.parse(l.text):{}}catch(g){throw new Error("Invalid JSON response from server")}let h=`HTTP error! status: ${l.status}`;try{let g=JSON.parse(l.text);g.error&&(h=g.error)}catch(g){l.text&&(h=l.text)}throw new Error(h)}catch(a){if(r=a,n<this.retryAttempts){await this.delay(this.retryDelay*n);continue}break}throw r}handleError(s){return s.message.includes("ECONNREFUSED")?(new je.Notice("Ollama service is not running. Please start the service."),new Error("Unable to connect to Ollama service. Please ensure the service is running.")):s instanceof TypeError&&s.message.includes("Invalid URL")?new Error(`Invalid Ollama service URL: ${this.baseUrl}`):s}delay(s){return new Promise(r=>setTimeout(r,s))}};var rs=require("obsidian"),At=class{constructor(s,r,n){this.apiKey=s,this.apiAddress=r||"https://api.anthropic.com",this.model=n||"claude-3-opus-20240229"}async generateResponse(s){try{let r=await(0,rs.requestUrl)({url:`${this.apiAddress}/v1/messages`,method:"POST",headers:{"x-api-key":this.apiKey,"anthropic-version":"2023-06-01","content-type":"application/json"},body:JSON.stringify({model:this.model,max_tokens:4096,messages:[{role:"user",content:s}]})});if(r.status!==200)throw new Error(`Anthropic API error: ${r.text}`);return r.json.content[0].text}catch(r){throw new Error("Failed to generate response from Anthropic API")}}async testConnection(){try{return(await(0,rs.requestUrl)({url:`${this.apiAddress}/v1/messages`,method:"POST",headers:{"x-api-key":this.apiKey,"anthropic-version":"2023-06-01","content-type":"application/json"},body:JSON.stringify({model:this.model,max_tokens:1,messages:[{role:"user",content:"Hi"}]})})).status===200}catch(s){return!1}}};var Hi=require("obsidian"),Si=class{constructor(s,r="gemini-pro",n){this.apiKey=s,this.model=r,this.baseUrl=n||"https://generativelanguage.googleapis.com"}updateModel(s){this.model=s}async generateResponse(s){var r,n,a,l,h;try{let g=`${this.baseUrl}/v1beta/models/${this.model}:generateContent?key=${this.apiKey}`,w=await(0,Hi.requestUrl)({url:g,method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:s}]}],generationConfig:{maxOutputTokens:2048,temperature:.7}})});if(w.status!==200)throw new Error(`Gemini API error (${w.status}): ${w.text}`);let f=w.json;if(!((h=(l=(a=(n=(r=f.candidates)==null?void 0:r[0])==null?void 0:n.content)==null?void 0:a.parts)==null?void 0:l[0])!=null&&h.text))throw new Error("Invalid response format from Gemini API");return f.candidates[0].content.parts[0].text}catch(g){throw g instanceof Error?g:new Error("Failed to generate response from Gemini API")}}async chat(s){var r,n,a,l,h;try{let g=`${this.baseUrl}/v1beta/models/${this.model}:generateContent?key=${this.apiKey}`,w={contents:s.map(H=>({role:H.role==="assistant"?"model":"user",parts:[{text:H.content}]})),generationConfig:{maxOutputTokens:2048,temperature:.7}},f=await(0,Hi.requestUrl)({url:g,method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(w)});if(f.status!==200)throw new Error(`Gemini Chat API error (${f.status}): ${f.text}`);let U=f.json;if(!((h=(l=(a=(n=(r=U.candidates)==null?void 0:r[0])==null?void 0:n.content)==null?void 0:a.parts)==null?void 0:l[0])!=null&&h.text))throw new Error("Invalid response format from Gemini Chat API");return U.candidates[0].content.parts[0].text}catch(g){throw g instanceof Error?g:new Error("Failed to chat with Gemini API")}}async testConnection(){try{let s=`${this.baseUrl}/v1beta/models/${this.model}?key=${this.apiKey}`,r=await(0,Hi.requestUrl)({url:s,method:"GET"});return r.status,r.status===200}catch(s){return!1}}};var ss=require("obsidian"),bi=class{constructor(s){var r;if(!((r=s.siliconflow)!=null&&r.apiKey))throw new Error("SiliconFlow API key is required");this.settings=s,this.baseUrl=s.siliconflow.baseUrl||"https://api.siliconflow.cn/v1"}async chat(s,r){var n,a;try{let l=((n=this.settings.siliconflow)==null?void 0:n.model)||"deepseek-ai/DeepSeek-V3",h=await(0,ss.requestUrl)({url:`${this.baseUrl}/chat/completions`,method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${(a=this.settings.siliconflow)==null?void 0:a.apiKey}`},body:JSON.stringify({model:l,messages:s.map(m=>({role:m.role,content:m.content})),stream:!1})});if(h.status!==200)throw new Error(`SiliconFlow API request failed: ${h.text}`);let g=h.json;if(!g.choices||!g.choices[0]||!g.choices[0].message)throw new Error("Unexpected API response format");return g.choices[0].message.content}catch(l){throw l}}async listModels(){var s;try{let r=await(0,ss.requestUrl)({url:`${this.baseUrl}/models`,method:"GET",headers:{Authorization:`Bearer ${(s=this.settings.siliconflow)==null?void 0:s.apiKey}`}});if(r.status!==200)throw new Error(`Failed to list models: ${r.text}`);return r.json.data.map(n=>({id:n.id,name:n.id.split("/").pop()||n.id,isCustom:!1}))}catch(r){throw r}}};var ns=require("obsidian"),xi=class{constructor(s,r="deepseek-chat",n){this.apiKey=s;this.model=r,this.baseUrl=n||"https://api.deepseek.com/v1"}updateModel(s){this.model=s}async generateResponse(s){let r=[{role:"user",content:s}];return await this.chat(r)}async chat(s){var r,n,a;try{let l=await(0,ns.requestUrl)({url:`${this.baseUrl}/chat/completions`,method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.apiKey}`},body:JSON.stringify({model:this.model,messages:s,temperature:.7,max_tokens:4096,frequency_penalty:0,presence_penalty:0})});if(l.status!==200)throw new Error(`Deepseek API error (${l.status}): ${l.text}`);let h=l.json;if(!((a=(n=(r=h.choices)==null?void 0:r[0])==null?void 0:n.message)!=null&&a.content))throw new Error("Invalid response format from Deepseek API");return h.choices[0].message.content}catch(l){throw l instanceof Error?l:new Error("Failed to generate response from Deepseek API")}}async testConnection(){try{return(await(0,ns.requestUrl)({url:`${this.baseUrl}/chat/completions`,method:"POST",headers:{Authorization:`Bearer ${this.apiKey}`,"Content-Type":"application/json"},body:JSON.stringify({model:this.model,messages:[{role:"user",content:"test"}],max_tokens:10})})).status===200}catch(s){return!1}}};var fa=require("obsidian"),et=class{constructor(s){this.settings=s;this.anthropicService=null;this.geminiService=null;this.deepseekService=null;this.siliconflowService=null;this.currentState={provider:"",model:""};var r,n,a,l,h,g,m,w;switch((r=s.ollama)!=null&&r.host&&(this.ollamaService=new ze(s.ollama.host)),(n=s.anthropic)!=null&&n.apiKey&&(this.anthropicService=new At(s.anthropic.apiKey,s.anthropic.apiAddress,s.anthropic.model)),(a=s.gemini)!=null&&a.apiKey&&(this.geminiService=new Si(s.gemini.apiKey,s.gemini.model,s.gemini.baseUrl)),(l=s.deepseek)!=null&&l.apiKey&&(this.deepseekService=new xi(s.deepseek.apiKey,s.deepseek.model,s.deepseek.baseUrl)),(h=s.siliconflow)!=null&&h.apiKey&&(this.siliconflowService=new bi(this.settings)),this.currentState.provider=s.provider,s.provider){case"gemini":this.currentState.model=((g=s.gemini)==null?void 0:g.model)||"";break;case"deepseek":this.currentState.model=((m=s.deepseek)==null?void 0:m.model)||"deepseek-chat";break;case"siliconflow":this.currentState.model=((w=s.siliconflow)==null?void 0:w.model)||"internlm/internlm2_5-7b-chat";break}}updateModel(s,r){switch(this.currentState.provider=s,this.currentState.model=r,s){case"gemini":this.geminiService&&this.geminiService.updateModel(r);break;case"deepseek":this.deepseekService&&this.deepseekService.updateModel(r);break}}async generateResponse(s,r,n){let a=s.replace("{{highlight}}",r);switch(n&&(a=a.replace("{{comment}}",n)),this.settings.provider){case"openai":return await this.callOpenAI(a);case"anthropic":return await this.callAnthropic(a);case"ollama":return await this.callOllama(a);case"gemini":return await this.callGemini(a);case"deepseek":return await this.callDeepseek(a);case"siliconflow":return await this.callSiliconFlow(a);default:throw new Error("AI service not configured")}}async chat(s){switch(this.settings.provider){case"openai":return await this.chatWithOpenAI(s);case"anthropic":return await this.chatWithAnthropic(s);case"ollama":return await this.chatWithOllama(s);case"gemini":return await this.chatWithGemini(s);case"deepseek":return await this.chatWithDeepseek(s);case"siliconflow":return await this.chatWithSiliconFlow(s);default:throw new Error("AI service not configured")}}async chatWithOpenAI(s){var a;if(!((a=this.settings.openai)!=null&&a.apiKey))throw new Error("OpenAI API Key not configured");let r=await(0,fa.requestUrl)({url:this.settings.openai.baseUrl||"https://api.openai.com/v1/chat/completions",method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.settings.openai.apiKey}`},body:JSON.stringify({model:this.settings.openai.model,messages:s,temperature:.7})});if(r.status!==200)throw new Error(`OpenAI API request failed: ${r.text}`);return r.json.choices[0].message.content}async chatWithAnthropic(s){if(!this.anthropicService)throw new Error("Anthropic service not configured");let r=s[s.length-1];return await this.anthropicService.generateResponse(r.content)}async chatWithOllama(s){var r;if(!this.ollamaService)throw new Error("Ollama service not configured");if(!((r=this.settings.ollama)!=null&&r.model))throw new Error("Ollama model not configured");return await this.ollamaService.chat(this.settings.ollama.model,s)}async chatWithGemini(s){if(!this.geminiService)throw new Error("Gemini service not configured");return await this.geminiService.chat(s)}async chatWithSiliconFlow(s){if(!this.siliconflowService)throw new Error("SiliconFlow service not configured");try{return await this.siliconflowService.chat(s)}catch(r){throw r}}async chatWithDeepseek(s){if(!this.deepseekService)throw new Error("Deepseek service not configured");return await this.deepseekService.chat(s)}async callOpenAI(s){return await this.chatWithOpenAI([{role:"user",content:s}])}async callAnthropic(s){if(!this.anthropicService)throw new Error("Anthropic service not configured");return await this.anthropicService.generateResponse(s)}async callOllama(s){var r;if(!this.ollamaService)throw new Error("Ollama service not configured");if(!((r=this.settings.ollama)!=null&&r.model))throw new Error("Ollama model not configured");return await this.ollamaService.generateCompletion(this.settings.ollama.model,s)}async callGemini(s){if(!this.geminiService)throw new Error("Gemini service not configured");return await this.geminiService.generateResponse(s)}async callSiliconFlow(s){if(!this.siliconflowService)throw new Error("SiliconFlow service not configured");try{return await this.siliconflowService.chat([{role:"user",content:s}])}catch(r){throw r}}async callDeepseek(s){if(!this.deepseekService)throw new Error("Deepseek service not configured");return await this.deepseekService.generateResponse(s)}async testConnection(){switch(this.settings.provider){case"openai":try{return await this.chatWithOpenAI([{role:"user",content:"test"}]),!0}catch(s){return!1}case"anthropic":return this.anthropicService?await this.anthropicService.testConnection():!1;case"ollama":return this.ollamaService?await this.ollamaService.testConnection():!1;case"gemini":return this.geminiService?await this.geminiService.testConnection():!1;case"deepseek":return this.deepseekService?await this.deepseekService.testConnection():!1;default:return!1}}async listOllamaModels(){if(!this.ollamaService)throw new Error("Ollama service not configured");return await this.ollamaService.listModels()}async listGeminiModels(){if(!this.geminiService)throw new Error("Gemini service not configured");return Promise.resolve(we)}async listDeepseekModels(){if(!this.deepseekService)throw new Error("Deepseek service not configured");return Promise.resolve([{id:"deepseek-chat",name:"Deepseek Chat"},{id:"deepseek-coder",name:"Deepseek Coder"}])}async listSiliconFlowModels(){return jA}async listOpenAIModels(){var r,n;let s=[{id:"gpt-4",name:"GPT-4",isCustom:!1},{id:"gpt-4-turbo",name:"GPT-4 Turbo",isCustom:!1},{id:"gpt-3.5-turbo",name:"GPT-3.5 Turbo",isCustom:!1}];return(r=this.settings.openai)!=null&&r.isCustomModel&&((n=this.settings.openai)!=null&&n.model)&&s.push({id:this.settings.openai.model,name:this.settings.openai.model,isCustom:!0}),s}};var Mi=class{constructor(s,r,n,a){this.highlight=r;this.onCommentAdd=a;var g;this.plugin=n,this.container=s,this.initButton(),this.boundClickHandler=m=>{this.container.contains(m.target)||this.closeDropdown()},document.addEventListener("click",this.boundClickHandler);let l=(g=this.plugin.app.workspace.getLeavesOfType("comment-view")[0])==null?void 0:g.view,h=l instanceof se?l:null;h!=null&&h.registerAIButton&&h.registerAIButton(this)}destroy(){var n;document.removeEventListener("click",this.boundClickHandler);let s=(n=this.plugin.app.workspace.getLeavesOfType("comment-view")[0])==null?void 0:n.view,r=s instanceof se?s:null;r!=null&&r.unregisterAIButton&&r.unregisterAIButton(this)}initButton(){let s=this.container.createEl("div",{cls:"highlight-ai-container"}),r=s.createEl("button",{cls:"highlight-action-btn highlight-ai-btn",attr:{"aria-label":p("Select Prompt")}});(0,Le.setIcon)(r,"bot-message-square"),this.dropdown=s.createEl("div",{cls:"highlight-ai-dropdown hi-note-hidden"}),this.dropdown.addEventListener("click",n=>{n.stopPropagation()}),this.updateDropdownContent(),r.addEventListener("click",n=>{n.stopPropagation(),this.toggleDropdown()}),this.aiButton=r}toggleDropdown(){this.dropdown.hasClass("hi-note-hidden")?(document.querySelectorAll(".highlight-ai-dropdown").forEach(s=>{s!==this.dropdown&&s.addClass("hi-note-hidden")}),this.dropdown.removeClass("hi-note-hidden")):this.dropdown.addClass("hi-note-hidden")}updateDropdownContent(){this.dropdown.empty();let s=Object.entries(this.plugin.settings.ai.prompts||{});s.length>0?s.forEach(([r,n])=>{this.dropdown.createEl("div",{cls:"highlight-ai-dropdown-item",text:r}).addEventListener("click",async()=>{this.dropdown.addClass("hi-note-hidden"),await this.handleAIAnalysis(r)})}):this.dropdown.createEl("div",{cls:"highlight-ai-dropdown-item",text:p("Please add Prompt in the settings")})}async handleAIAnalysis(s){try{this.setLoading(!0);let r=new et(this.plugin.settings.ai),n=this.plugin.settings.ai.prompts[s];if(!n)throw new Error(p(`\u672A\u627E\u5230\u540D\u4E3A "${s}" \u7684 Prompt`));let l=(this.highlight.comments||[]).map(g=>g.content).join(`
`),h=await r.generateResponse(n,this.highlight.text,l);await this.onCommentAdd(h),new Le.Notice(p("AI comments have been added"))}catch(r){new Le.Notice(p(`AI comments failed:) ${r.message}`))}finally{this.setLoading(!1)}}setLoading(s){s?(this.aiButton.addClass("loading"),(0,Le.setIcon)(this.aiButton,"loader")):(this.aiButton.removeClass("loading"),(0,Le.setIcon)(this.aiButton,"bot-message-square"))}closeDropdown(){this.dropdown&&(this.dropdown.addClass("hi-note-hidden"),requestAnimationFrame(()=>{this.dropdown.addClass("highlight-dropdown-hidden"),requestAnimationFrame(()=>{this.dropdown.removeClass("highlight-dropdown-hidden")})}))}};var Li=class{constructor(s,r,n,a){this.highlight=r;this.plugin=n;this.options=a;this.render(s)}render(s){this.container=s.createEl("div",{cls:"highlight-action-buttons"}),this.renderLeftButtons(),this.renderRightButtons()}renderLeftButtons(){let s=this.container.createEl("div",{cls:"highlight-action-buttons-left"});new Mi(s,this.highlight,this.plugin,this.options.onAIResponse)}renderRightButtons(){let s=this.container.createEl("div",{cls:"highlight-action-buttons-right"}),r=s.createEl("button",{cls:"highlight-action-btn highlight-add-comment-btn",attr:{"aria-label":p("Add Comment")}});(0,as.setIcon)(r,"square-plus"),r.addEventListener("click",a=>{a.stopPropagation(),this.options.onCommentAdd()});let n=s.createEl("button",{cls:"highlight-action-btn highlight-share-btn",attr:{"aria-label":p("Export as Image")}});(0,as.setIcon)(n,"image-down"),n.addEventListener("click",a=>{a.stopPropagation(),this.options.onExport(this.highlight)})}};var wa=/#[\w\u4e00-\u9fa5]+/g,Ti=/^\s*(#[\w\u4e00-\u9fa5]+(\s+#[\w\u4e00-\u9fa5]+)*\s*)$/,Di=class{constructor(s,r,n){this.highlight=r;this.onCommentEdit=n;this.render(s)}render(s){if((this.highlight.comments||[]).length===0)return;let n=s.createEl("div",{cls:"hi-notes-section"});this.container=n.createEl("div",{cls:"hi-notes-list"}),this.renderComments()}renderComments(){let s=this.highlight.comments||[];for(s.sort((r,n)=>{let a=Ti.test(r.content),l=Ti.test(n.content);return a&&!l?-1:!a&&l?1:n.updatedAt-r.updatedAt});this.container.firstChild;)this.container.removeChild(this.container.firstChild);s.forEach(r=>{let n=Ti.test(r.content),a=this.container.createEl("div",{cls:`hi-note${n?" pure-tags-comment":""}`,attr:{"data-comment-id":r.id}}),l=a.createEl("div",{cls:"hi-note-content"}),h=r.content;if(Ti.test(h)){let m=h,w=h.match(wa)||[];for(;l.firstChild;)l.removeChild(l.firstChild);if(w.length>0){let f=m.split(wa),U=0;f.forEach((H,I)=>{if(H&&l.appendChild(document.createTextNode(H)),I<w.length){let Q=document.createElement("span");Q.className="highlight-tag",Q.textContent=w[I],l.appendChild(Q)}})}else l.textContent=h}else l.textContent=h;l.addEventListener("dblclick",m=>{m.stopPropagation(),m.preventDefault(),this.onCommentEdit(r)}),l.addEventListener("click",m=>{m.stopPropagation()});let g=a.createEl("div",{cls:"hi-note-footer"});g.createEl("div",{text:new Date(r.updatedAt).toLocaleString(),cls:"hi-note-time"}),g.createEl("span",{text:"\u53CC\u51FB\u7F16\u8F91",cls:"hi-note-edit-hint"}),g.createEl("div",{cls:"hi-note-actions"})})}};var tt=require("obsidian");var Te=class Te{static start(s,r,n={}){var m;let{showIcon:a=!0,maxLength:l=30}=n;this.clear(),this.instance=document.createElement("div"),this.instance.className="highlight-dragging";let h=document.createElement("div");h.className="highlight-dragging-content";let g=r.length>l?r.slice(0,l)+"...":r;h.textContent=g,this.instance.appendChild(h),document.body.appendChild(this.instance),this.updatePosition(s.clientX,s.clientY),(m=s.dataTransfer)==null||m.setDragImage(this.dragImage,0,0),document.addEventListener("dragover",this.handleDragOver)}static updatePosition(s,r){this.instance&&this.instance.setAttribute("style",`left: ${s+10}px; top: ${r+10}px;`)}static clear(){this.instance&&(this.instance.remove(),this.instance=null),document.removeEventListener("dragover",this.handleDragOver)}};Te.instance=null,Te.dragImage=new Image,Te.dragImage.src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",Te.handleDragOver=s=>{s.preventDefault(),Te.updatePosition(s.clientX,s.clientY)};var Mt=Te;var Ca=require("obsidian"),Lt=class{constructor(s,r){this.highlight=s;this.plugin=r}generateSync(){var r,n,a,l;let s=[];if(this.highlight.isVirtual){let h=((n=(r=this.highlight.filePath)==null?void 0:r.split("/").pop())==null?void 0:n.replace(".md",""))||"File";s.push(`> [!note] [[${h}]]`),s.push("> ")}else{s.push("> [!quote] Highlight");let h=!1;if(this.highlight.blockId){let g=this.highlight.filePath;if(!g){let m=this.plugin.app.workspace.getActiveFile();m&&(g=m.path)}if(g){let m=(a=g.split("/").pop())==null?void 0:a.replace(".md","");if(m){let w=`> ![[${m}#^${this.highlight.blockId}]]`;s.push(w),s.push("> "),h=!0}}}else if(this.highlight.filePath&&typeof this.highlight.position=="number"){let g=this.plugin.app.vault.getAbstractFileByPath(this.highlight.filePath);if(g instanceof Ca.TFile){let m=this.plugin.app.metadataCache.getFileCache(g);if(m!=null&&m.sections){let w=this.highlight.position,f=m.sections.find(U=>U.position.start.offset<=w&&U.position.end.offset>=w);if(f!=null&&f.id){let U=(l=this.highlight.filePath.split("/").pop())==null?void 0:l.replace(".md","");if(U){let H=`> ![[${U}#^${f.id}]]`;s.push(H),s.push("> "),h=!0}}}}}!h&&this.highlight.text&&(s.push(`> ${this.highlight.text}`),s.push("> "))}if(this.highlight.comments&&this.highlight.comments.length>0)for(let h of this.highlight.comments)s.push(...this.formatComment(h,!1));return s.join(`
`)}async generate(){return this.generateSync()}formatComment(s,r){let n=[],a=r?">":">>";r||n.push(">> [!note] Comment");let l=s.content.split(`
`).map(h=>(h=h.trim(),h?`${a} ${h}`:a)).join(`
`);if(n.push(l),s.updatedAt){let h=window.moment(s.updatedAt);n.push(`${a} *${h.format("YYYY-MM-DD HH:mm:ss")}*`)}return n.push(">"),n}};var De=class De{constructor(s,r,n,a,l=!1,h){this.container=s;this.highlight=r;this.plugin=n;this.options=a;this.isInMainView=l;this.fileName=h;this.isEditing=!1;this.render()}render(){if(this.card=this.container.createEl("div",{cls:`highlight-card ${this.highlight.isVirtual?"virtual-highlight-card":""}`,attr:{"data-highlight":JSON.stringify(this.highlight)}}),this.card.addEventListener("click",r=>{this.isEditing||this.selectCard()}),this.isInMainView&&this.fileName){let r=this.card.createEl("div",{cls:"highlight-card-filename"});r.setAttribute("draggable","true"),this.highlight&&this.highlight.filePath&&typeof this.highlight.position=="number"&&this.plugin.app.vault.getAbstractFileByPath(this.highlight.filePath)instanceof tt.TFile&&this.generateDragContent().catch(h=>{console.error("[HighlightCard] Error pre-generating block ID:",h)}),r.addEventListener("dragstart",async l=>{var h,g;try{if(!this.highlight||!this.highlight.text)throw new Error("Invalid highlight data");let m;try{let f=new Promise((U,H)=>{setTimeout(()=>H(new Error("Block ID generation timeout")),300)});m=await Promise.race([this.generateDragContent(),f])}catch(f){console.debug("[HighlightCard] Using sync fallback for drag content:",f),m=this.generateDragContentSync()}(h=l.dataTransfer)==null||h.setData("text/plain",m);let w=JSON.stringify(this.highlight);(g=l.dataTransfer)==null||g.setData("application/highlight",w),r.addClass("dragging"),Mt.start(l,this.highlight.text)}catch(m){console.error("[HighlightCard] Error during drag start:",m),l.preventDefault(),l.stopPropagation()}}),r.addEventListener("dragend",()=>{r.removeClass("dragging"),Mt.clear()});let n=r.createEl("span",{cls:"highlight-card-filename-icon",attr:{"aria-label":p("Open (DoubleClick)")}});(0,tt.setIcon)(n,"file-text"),n.addEventListener("dblclick",async l=>{l.stopPropagation();let h=this.highlight.filePath||this.fileName;if(!h)return;let g=this.plugin.app.vault.getAbstractFileByPath(h);if(!g||!(g instanceof tt.TFile))return;let m=this.plugin.app.workspace.getLeavesOfType("markdown"),w=this.plugin.app.workspace.activeLeaf,f=m.find(U=>U!==w);if(f||(f=this.plugin.app.workspace.getLeaf("split","vertical")),await f.openFile(g),this.highlight.position!==void 0){let U=f.view;if(U.getViewType()==="markdown"){let H=U.getViewType()==="markdown"?U.editor:null;if(H){let I=H.offsetToPos(this.highlight.position);H.setCursor(I),H.scrollIntoView({from:I,to:I},!0)}}}});let a=r.createEl("span",{text:this.fileName,cls:"highlight-card-filename-text"});this.addPagePreview(a,this.highlight.filePath||this.fileName)}let s=this.card.createEl("div",{cls:"highlight-content"});new xt(s,this.highlight,this.options.onHighlightClick),new Li(s,this.highlight,this.plugin,{onCommentAdd:()=>this.options.onCommentAdd(this.highlight),onExport:()=>{let r={...this.highlight};this.fileName&&(r.fileName=this.fileName),this.options.onExport(r)},onAIResponse:this.options.onAIResponse}),new Di(this.card,this.highlight,r=>{this.isEditing=!0,this.selectCard(),this.options.onCommentEdit(this.highlight,r)})}selectCard(){De.selectedCard&&De.selectedCard!==this.card&&De.selectedCard.removeClass("selected"),this.card.addClass("selected"),De.selectedCard=this.card}getElement(){return this.card}addPagePreview(s,r){if(!r)return;let n=this.plugin.app.vault.getAbstractFileByPath(r);if(!(n instanceof tt.TFile))return;let a;s.addEventListener("mouseenter",l=>{a=setTimeout(async()=>{let h=l.target;this.plugin.app.workspace.trigger("hover-link",{event:l,source:"hi-note",hoverParent:h,targetEl:h,linktext:n.path})},300)}),s.addEventListener("mouseleave",()=>{a&&clearTimeout(a)})}update(s){this.highlight=s,this.isEditing=!1,this.card.empty(),this.render()}generateDragContentSync(){return new Lt(this.highlight,this.plugin).generateSync()}async generateDragContent(){return new Lt(this.highlight,this.plugin).generate()}};De.selectedCard=null;var Ki=De;var Pi=class{static shouldExclude(s,r){if(!r||r.trim().length===0)return!1;let n=r.split(",").map(h=>h.trim()).filter(h=>h.length>0),a=s.path,l=s.basename;return n.some(h=>{if(h=h.trim(),!h)return!1;if(h.startsWith("[[")&&h.endsWith("]]")){let w=h.slice(2,-2);return l===w}if(h.startsWith("*.")){let w=h.slice(2);return s.extension===w||a.endsWith(w)}let g=h.replace(/^\/+/,""),m=a.replace(/^\/+/,"");return m.startsWith(g+"/")||m===g})}};var Tt=class{extractColorFromStyle(s){if(!s)return null;let r=s.match(/background(?:-color)?:\s*((?:rgba?\(.*?\)|#[0-9a-fA-F]{3,8}|var\(--[^)]+\)))/);return r?r[1]:null}extractColorFromElement(s){let r=s.match(/style=["']([^"']*)["']/);return r?this.extractColorFromStyle(r[1]):null}};Tt.COLOR_PATTERNS=[/rgba?\(\d+,\s*\d+,\s*\d+(?:,\s*[0-9.]+)?\)/,/#[0-9a-fA-F]{3,8}/,/var\(--[^)]+\)/];var va=require("obsidian"),Ce=class{constructor(s){this.app=s;this.events=new va.Events}emitHighlightUpdate(s,r,n){this.events.trigger("highlight:update",s,r,n)}emitHighlightDelete(s,r){this.events.trigger("highlight:delete",s,r)}emitCommentUpdate(s,r,n){this.events.trigger("comment:update",s,r,n)}emitCommentDelete(s,r){this.events.trigger("comment:delete",s,r)}emitFlashcardChanged(){this.events.trigger("flashcard:changed")}on(s,r){return this.events.on(s,r)}off(s,r){this.events.off(s,r)}};var it=class{constructor(s){this.app=s}getOrCreateBlockId(s,r){let n=s.getLine(r),a=n.match(/\^([a-zA-Z0-9-]+)$/);if(a)return a[1];let l=Date.now().toString(36),h=Math.random().toString(36).substr(2,5),g=`${l}-${h}`;return s.setLine(r,`${n} ^${g}`),g}extractBlockId(s){let r=s.match(/\^([a-zA-Z0-9-]+)$/);return r?r[1]:void 0}getParagraphBlockId(s,r){let n=this.app.metadataCache.getFileCache(s);if(!(n!=null&&n.sections))return;let a=n.sections.find(l=>l.position.start.offset<=r&&l.position.end.offset>=r);return a!=null&&a.id?`${s.path}#^${a.id}`:void 0}async createParagraphBlockId(s,r){let n=this.getParagraphBlockId(s,r);if(n)return n;let a=this.app.workspace.getLeaf();await a.openFile(s,{active:!1});let h=a.view.editor,g=h.offsetToPos(r),m=this.getOrCreateBlockId(h,g.line);return await this.app.vault.modify(s,h.getValue()),`${s.path}#^${m}`}hasValidBlockId(s){return/\^[a-zA-Z0-9-]+$/.test(s)}};var ki=class ki{constructor(s){this.app=s;var a;let r=s.plugins,n=r&&r.plugins?r.plugins["hi-note"]:void 0;this.settings=n==null?void 0:n.settings,this.colorExtractor=new Tt,this.eventManager=new Ce(s),this.blockIdService=new it(s),console.debug("[HighlightService] Current settings:",this.settings),console.debug("[HighlightService] Exclude patterns:",(a=this.settings)==null?void 0:a.excludePatterns)}shouldProcessFile(s){var r;return!Pi.shouldExclude(s,((r=this.settings)==null?void 0:r.excludePatterns)||"")}extractHighlights(s){let r=[],n=this.settings.useCustomPattern?new RegExp(this.settings.highlightPattern,"g"):ki.DEFAULT_HIGHLIGHT_PATTERN,a;for(;(a=n.exec(s))!==null;){let l=a,h=l[0],g=l.slice(1).find(f=>f!==void 0),m=null;if(h.includes("style=")&&(m=this.colorExtractor.extractColorFromElement(h)),!r.some(f=>typeof f.position=="number"&&Math.abs(f.position-l.index)<10&&f.text===g)&&g){let f=this.app.workspace.getActiveFile();if(f){let U=this.app.metadataCache.getFileCache(f);if(U!=null&&U.sections){let H=U.sections.find(Q=>Q.position.start.offset<=l.index&&Q.position.end.offset>=l.index),I=H==null?void 0:H.id;if(!I){let Q=this.blockIdService.getParagraphBlockId(f,l.index);if(Q){let S=Q.match(/#\^([a-zA-Z0-9-]+)/);S&&S[1]&&(I=S[1])}}r.push({text:g,position:l.index,paragraphOffset:this.getParagraphOffset(s,l.index),backgroundColor:m||this.settings.defaultHighlightColor,id:`highlight-${Date.now()}-${l.index}`,comments:[],createdAt:Date.now(),updatedAt:Date.now(),originalLength:h.length,blockId:I})}}}}return r.sort((l,h)=>{var g,m;return((g=l.position)!=null?g:0)-((m=h.position)!=null?m:0)})}getParagraphOffset(s,r){let n=s.substring(0,r),a=n.split(/\n\s*\n/);return n.length-a[a.length-1].length}async getFilesWithHighlights(){let s=this.app.vault.getMarkdownFiles(),r=[],n=0;for(let a of s){if(!this.shouldProcessFile(a)){console.debug(`[HighlightService] Skipping excluded file: ${a.path}`);continue}let l=await this.app.vault.read(a),h=this.extractHighlights(l);h.length>0&&(r.push(a),n+=h.length,console.debug(`[HighlightService] Found ${h.length} highlights in ${a.path}`))}return console.info(`[HighlightService] Found ${n} highlights in ${r.length} files`),r}createBlockIdForPosition(s,r,n=!1){try{let a=this.blockIdService.getParagraphBlockId(s,r);if(a){let l=a.match(/#\^([a-zA-Z0-9-]+)/);return l&&l[1]?l[1]:void 0}n&&this.blockIdService.createParagraphBlockId(s,r).then(l=>{let h,g=l.match(/#\^([a-zA-Z0-9-]+)/);return g&&g[1]&&(h=g[1]),console.debug(`[HighlightService] Created block ID: ${h}`),h}).catch(l=>{console.error("[HighlightService] Error creating block ID:",l)});return}catch(a){console.error("[HighlightService] Error in createBlockIdForPosition:",a);return}}async createBlockIdForHighlight(s,r){try{let n=this.blockIdService.getParagraphBlockId(s,r);return n||await this.blockIdService.createParagraphBlockId(s,r)}catch(n){throw console.error("[HighlightService] Error creating block ID for highlight:",n),n}}};ki.DEFAULT_HIGHLIGHT_PATTERN=/==\s*(.*?)\s*==|<mark[^>]*>(.*?)<\/mark>|<span[^>]*>(.*?)<\/span>/g;var ne=ki;var Oi=require("obsidian"),Ri=class{constructor(s){this.app=s}async jumpToHighlight(s,r){let n=await this.openOrActivateFile(r);n&&await this.locateAndHighlightText(n,s.text)}async openOrActivateFile(s){let n=this.app.workspace.getLeavesOfType("markdown").find(a=>{var h;return((h=a.view.file)==null?void 0:h.path)===s});if(!n)try{let a=this.app.vault.getAbstractFileByPath(s);if(!a)return new Oi.Notice("\u672A\u627E\u5230\u6587\u4EF6"),null;n=await this.app.workspace.getLeaf("tab"),await n.openFile(a)}catch(a){return new Oi.Notice("\u6253\u5F00\u6587\u4EF6\u5931\u8D25"),null}return await this.app.workspace.setActiveLeaf(n,{focus:!1}),n}async locateAndHighlightText(s,r){await new Promise(w=>setTimeout(w,100));let a=s.view.editor,h=a.getValue().indexOf(r);if(h===-1){new Oi.Notice("\u672A\u627E\u5230\u9AD8\u4EAE\u5185\u5BB9");return}let g=a.offsetToPos(h),m=a.offsetToPos(h+r.length);a.setSelection(g,m),a.scrollIntoView({from:g,to:m},!0),this.app.workspace.setActiveLeaf(s,{focus:!0})}};var Ni=class{constructor(s,r){this.app=s;this.commentStore=r;this.highlightService=new ne(s)}async exportHighlightsToNote(s){var f,U;let r=await this.getFileHighlights(s);if(!r||r.length===0)throw new Error(p("No highlights found in the current file."));let n=await this.generateExportContent(s,r),a=this.app.plugins,l=a&&a.plugins?a.plugins["hi-note"]:void 0,h=((U=(f=l==null?void 0:l.settings)==null?void 0:f.export)==null?void 0:U.exportPath)||"",g=`${s.basename} - Highlights ${window.moment().format("YYYYMMDDHHmm")}`,m=g;return h&&(this.app.vault.getAbstractFileByPath(h)||await this.app.vault.createFolder(h),m=`${h}/${g}`),await this.app.vault.create(`${m}.md`,n)}async getFileHighlights(s){let r=await this.app.vault.read(s),n=this.highlightService.extractHighlights(r),a=this.commentStore.getFileComments(s),l=a.filter(m=>m.isVirtual&&m.comments&&m.comments.length>0),h=a.filter(m=>!m.isVirtual),g=n.map(m=>{var f,U,H,I;let w=h.find(Q=>{let S=Q.text===m.text;return S&&typeof Q.position=="number"&&typeof m.position=="number"?Math.abs(Q.position-m.position)<1e3:S});return w?{...w,position:(f=m.position)!=null?f:0,paragraphOffset:(U=m.paragraphOffset)!=null?U:0}:{id:this.generateHighlightId(m),...m,position:(H=m.position)!=null?H:0,paragraphOffset:(I=m.paragraphOffset)!=null?I:0,comments:[],createdAt:Date.now(),updatedAt:Date.now()}});return[...l,...g]}async generateExportContent(s,r){let n=[];n.push(`[[${s.basename}]] - HighlightsNotes`),n.push("");for(let a of r){if(a.isVirtual)n.push("> [!note] File Comment"),n.push("> ");else{if(n.push("> [!quote] Highlight"),typeof a.position=="number")try{let l=await this.highlightService.createBlockIdForHighlight(s,a.position);l?n.push(`> ![[${l}]]`):n.push(`> ${a.text}`)}catch(l){console.error("[ExportService] Error creating block ID:",l),n.push(`> ${a.text}`)}else n.push(`> ${a.text}`);n.push("> ")}if(a.comments&&a.comments.length>0){a.isVirtual||(n.push("> ---"),n.push("> "));for(let l of a.comments){if(a.isVirtual){let h=l.content.split(`
`).map(g=>(g=g.trim(),g?`> ${g}`:">")).join(`
`);n.push(h)}else{n.push(">> [!note] Comment");let h=l.content.split(`
`).map(g=>(g=g.trim(),g?`>> ${g}`:">>")).join(`
`);n.push(h)}if(l.updatedAt){let h=window.moment(l.updatedAt);a.isVirtual?n.push(`> *${h.format("YYYY-MM-DD HH:mm:ss")}*`):n.push(`>> *${h.format("YYYY-MM-DD HH:mm:ss")}*`)}n.push((a.isVirtual,">"))}}n.push("")}return n.join(`
`)}getParagraphOffset(s,r){let a=s.substring(0,r).lastIndexOf(`
`);return a===-1?r:r-a}generateHighlightId(s){return`highlight-${Date.now()}-${Math.random().toString(36).substr(2,9)}`}};var Zd=/#[\w\u4e00-\u9fa5]+/g,$d=/^\s*(#[\w\u4e00-\u9fa5]+(\s+#[\w\u4e00-\u9fa5]+)*\s*)$/,Gi=class{constructor(s,r,n,a){this.card=s;this.highlight=r;this.existingComment=n;this.options=a;this.cancelEdit=()=>{};this.isProcessing=!1;this.boundHandleOutsideClick=this.handleOutsideClick.bind(this),document.addEventListener("click",this.boundHandleOutsideClick)}show(){this.existingComment?this.showEditMode():this.showCreateMode()}showEditMode(){let s=this.card.querySelector(`[data-comment-id="${this.existingComment.id}"]`);if(!s)return;let r=s.querySelector(".hi-note-content");if(!r)return;let n=r.textContent||"";this.textarea=document.createElement("textarea"),this.textarea.value=n,this.textarea.className="hi-note-input",this.textarea.style.minHeight=`${r.offsetHeight}px`,this.textarea.addEventListener("input",()=>{this.processTagsInInput(),this.autoResizeTextarea()}),r.replaceWith(this.textarea);let a=s.querySelector(".hi-note-footer");a&&a.addClass("hi-note-hidden"),this.actionHint=s.createEl("div",{cls:"hi-note-actions-hint"}),this.actionHint.createEl("span",{cls:"hi-note-hint",text:p("Shift + Enter Wrap, Enter Save")}),this.options.onDelete&&this.actionHint.createEl("div",{cls:"hi-note-delete-link",text:p("Delete comment")}).addEventListener("click",async h=>{var g,m;h.stopPropagation(),await((m=(g=this.options).onDelete)==null?void 0:m.call(g))}),this.setupKeyboardEvents(r,a||void 0),setTimeout(()=>{this.textarea.focus(),this.textarea.setSelectionRange(this.textarea.value.length,this.textarea.value.length)},50)}showCreateMode(){let s=document.createElement("div");s.className="hi-note-input",this.textarea=s.createEl("textarea"),this.textarea.addEventListener("input",()=>{this.processTagsInInput(),this.autoResizeTextarea()}),s.createEl("div",{cls:"hi-note-hint",text:p("Shift + Enter Wrap, Enter Save")});let r=this.card.querySelector(".hi-notes-section");r||(r=this.card.createEl("div",{cls:"hi-notes-section"}),r.createEl("div",{cls:"hi-notes-list"}));let n=r.querySelector(".hi-notes-list");n&&n.insertBefore(s,n.firstChild),this.setupKeyboardEvents(),setTimeout(()=>{this.textarea.focus()},50)}setupKeyboardEvents(s,r){this.cancelEdit=()=>{this.existingComment?s&&r&&requestAnimationFrame(()=>{this.textarea.replaceWith(s),this.actionHint.remove(),r.removeClass("hi-note-hidden"),document.removeEventListener("click",this.boundHandleOutsideClick)}):requestAnimationFrame(()=>{var n,a;(n=this.textarea.closest(".hi-note-input"))==null||n.remove(),this.card.querySelector(".hi-note")||(a=this.card.querySelector(".hi-notes-section"))==null||a.remove(),document.removeEventListener("click",this.boundHandleOutsideClick)}),this.options.onCancel()},this.textarea.onkeydown=async n=>{if(n.key==="Enter"){if(n.shiftKey||(n.preventDefault(),this.isProcessing))return;let a=this.textarea.value.trim();if(!a)return;this.isProcessing=!0,this.textarea.disabled=!0;try{await this.options.onSave(a),requestAnimationFrame(()=>{document.removeEventListener("click",this.boundHandleOutsideClick),this.isProcessing=!1,this.textarea.disabled=!1})}catch(l){this.textarea.disabled=!1,this.isProcessing=!1}}}}autoResizeTextarea(){let s=window.scrollY;this.textarea.style.height="auto";let r=this.textarea.scrollHeight;this.textarea.style.height=`${r}px`,window.scrollTo(0,s)}processTagsInInput(){var r,n,a,l;let s=this.textarea.value;if($d.test(s)){let h=s.match(Zd)||[];if(h.length>0){let g=(r=this.textarea.parentElement)==null?void 0:r.querySelector(".highlight-tags-preview");if(g||(g=document.createElement("div"),g.className="highlight-tags-preview",(n=this.textarea.parentElement)==null||n.insertBefore(g,this.textarea)),g){for(;g.firstChild;)g.removeChild(g.firstChild);let m=g;h.forEach(w=>{let f=document.createElement("span");f.className="highlight-tag",f.textContent=w,m.appendChild(f)})}}}else(l=(a=this.textarea.parentElement)==null?void 0:a.querySelector(".highlight-tags-preview"))==null||l.remove()}handleOutsideClick(s){if(!this.textarea||this.isProcessing)return;let r=s.target;if(!this.textarea.contains(r)&&!r.closest(".hi-note-actions-hint")){s.preventDefault(),s.stopPropagation(),this.isProcessing=!0;let a=this.textarea.value.trim();if(a){let l=a;this.textarea.disabled=!0,this.options.onSave(l).then(()=>{requestAnimationFrame(()=>{document.removeEventListener("click",this.boundHandleOutsideClick),this.isProcessing=!1,this.textarea.disabled=!1})}).catch(()=>{this.textarea.disabled=!1,this.isProcessing=!1})}else requestAnimationFrame(()=>{this.cancelEdit(),this.isProcessing=!1})}}destroy(){document.removeEventListener("click",this.boundHandleOutsideClick),this.isProcessing=!1}};var DA=require("obsidian");var Qa=require("obsidian");var Vi=class{constructor(s){this.plugin=s;this.aiService=new et(this.plugin.settings.ai)}updateModel(s,r){this.aiService&&this.aiService.updateModel(s,r)}async sendMessage(s,r=[]){try{let n=r.map(l=>({role:l.role,content:l.content}));return n.push({role:"user",content:s}),{content:await this.aiService.chat(n),type:"assistant",timestamp:Date.now()}}catch(n){throw new Qa.Notice("\u83B7\u53D6 AI \u54CD\u5E94\u5931\u8D25\uFF0C\u8BF7\u68C0\u67E5\u670D\u52A1\u914D\u7F6E\u548C\u7F51\u7EDC\u8FDE\u63A5"),n}}async testConnection(){return await this.aiService.testConnection()}};var UA=class UA{constructor(s,r){this.plugin=r;this.isProcessing=!1;this.draggedContents=[];this.chatHistory=[];this.currentPreviewContainer=null;this.textarea=null;this.chatModelState={provider:"",model:""};if(UA.instance)return UA.instance;this.app=s,this.chatService=new Vi(this.plugin),this.floatingButton=document.querySelector(".highlight-floating-button"),this.containerEl=document.createElement("div"),this.containerEl.addClass("highlight-chat-window");let n=this.containerEl.createEl("div",{cls:"highlight-chat-header"}),a=n.createEl("div",{cls:"highlight-chat-title"});a.createEl("span",{text:p("Chat")});let l=a.createEl("div",{cls:"highlight-chat-model",text:this.getCurrentModelName()});l.addEventListener("click",y=>{y.stopPropagation(),this.showModelSelector(l,y)});let h=n.createEl("div",{cls:"highlight-chat-buttons"}),g=h.createEl("div",{cls:"highlight-chat-clear"});(0,DA.setIcon)(g,"eraser"),g.addEventListener("click",()=>this.clearChat());let m=h.createEl("div",{cls:"highlight-chat-close"});(0,DA.setIcon)(m,"x"),m.addEventListener("click",()=>this.close());let w=this.containerEl.createEl("div",{cls:"highlight-chat-history"}),f=this.containerEl.createEl("div",{cls:"highlight-chat-input-container"});this.setupChatInput(f);let U={text:"",position:0,paragraphOffset:0,paragraphId:"chat",createdAt:Date.now(),updatedAt:Date.now()};w.addEventListener("dragenter",y=>{y.preventDefault(),w.addClass("drag-over")}),w.addEventListener("dragover",y=>{y.preventDefault(),y.stopPropagation(),w.addClass("drag-over");let V=w.getBoundingClientRect(),R=w.scrollTop,N=V.height;w.addClass("highlight-chat-history-drag-guide"),w.style.setProperty("--drag-guide-top",`${R+12}px`),w.style.setProperty("--drag-guide-height",`${N-24}px`);let _=document.querySelector(".highlight-dragging");_&&(_.addClass("highlight-chat-preview"),_.style.left=`${y.clientX+10}px`,_.style.top=`${y.clientY+10}px`)}),w.addEventListener("dragleave",y=>{w.contains(y.relatedTarget)||w.removeClass("drag-over")}),w.addEventListener("drop",async y=>{var R;y.preventDefault(),w.removeClass("drag-over");let V=(R=y.dataTransfer)==null?void 0:R.getData("application/highlight");if(V)try{let N=JSON.parse(V);if(!N.text)return;this.draggedContents.some(Z=>Z.text===N.text)||(this.draggedContents.push(N),this.showDraggedPreviewsInChat(w))}catch(N){}});let H=!1,I,Q,S,b;if(n.addEventListener("mousedown",y=>{y.target===m||y.target===g||(H=!0,S=y.clientX-this.containerEl.offsetLeft,b=y.clientY-this.containerEl.offsetTop,n.addClass("dragging"))}),document.addEventListener("mousemove",y=>{if(!H)return;y.preventDefault(),I=y.clientX-S,Q=y.clientY-b;let V=window.innerWidth-this.containerEl.offsetWidth,R=window.innerHeight-this.containerEl.offsetHeight;I=Math.max(0,Math.min(I,V)),Q=Math.max(0,Math.min(Q,R)),this.containerEl.addClass("highlight-chat-window"),this.containerEl.style.left=`${I}px`,this.containerEl.style.top=`${Q}px`}),document.addEventListener("mouseup",()=>{H=!1,n.removeClass("dragging")}),UA.savedState){this.chatHistory=UA.savedState.chatHistory,this.draggedContents=UA.savedState.draggedContents;let y=this.containerEl.querySelector(".highlight-chat-history");if(y){for(;y.firstChild;)y.removeChild(y.firstChild);this.chatHistory.forEach(V=>{this.addMessage(y,V.content,V.role,!1)}),this.draggedContents.length>0&&this.showDraggedPreviewsInChat(y),UA.savedState.currentPreviewContainer&&(this.currentPreviewContainer=y.querySelector(".highlight-chat-preview-cards"))}}UA.instance=this}showDraggedPreviewsInChat(s){var h;if(!this.currentPreviewContainer){let m=s.createEl("div",{cls:"highlight-chat-message highlight-chat-message-preview"}).createEl("div",{cls:"highlight-chat-previews"}),w=m.createEl("div",{cls:"highlight-chat-preview-header"});w.createEl("span",{cls:"highlight-chat-preview-count",text:String(this.draggedContents.length)}),w.createSpan({text:p("highlighted notes")});let f=m.createEl("div",{cls:"highlight-chat-preview-cards"});this.currentPreviewContainer=f}let r=this.currentPreviewContainer.createEl("div",{cls:"highlight-chat-preview-card"}),n=this.draggedContents[this.draggedContents.length-1];r.createEl("div",{cls:"highlight-chat-preview-content",text:n.text});let a=r.createEl("div",{cls:"highlight-chat-preview-delete"});(0,DA.setIcon)(a,"x"),a.addEventListener("click",()=>{var m;let g=this.draggedContents.indexOf(n);if(g>-1)if(this.draggedContents.splice(g,1),r.remove(),this.draggedContents.length===0){let w=(m=this.currentPreviewContainer)==null?void 0:m.closest(".highlight-chat-message-preview");w&&(w.remove(),this.currentPreviewContainer=null)}else this.updatePreviewCount()});let l=(h=this.currentPreviewContainer.closest(".highlight-chat-message-preview"))==null?void 0:h.querySelector(".highlight-chat-preview-count");l&&(l.textContent=String(this.draggedContents.length)),s.scrollTop=s.scrollHeight}show(){document.body.contains(this.containerEl)?this.containerEl.removeClass("highlight-chat-hidden"):(this.containerEl.addClass("highlight-chat-window"),this.containerEl.addClass("highlight-chat-window-position"),document.body.appendChild(this.containerEl)),this.floatingButton&&this.floatingButton.addClass("hi-note-hidden"),requestAnimationFrame(()=>{var s;(s=this.textarea)==null||s.focus()})}close(){this.containerEl.addClass("highlight-chat-hidden"),this.floatingButton&&this.floatingButton.removeClass("hi-note-hidden")}addMessage(s,r,n,a=!0){let l=s.createEl("div",{cls:"highlight-chat-message"}),h=l.createEl("div",{cls:"highlight-chat-message-content"});l.addClass(`highlight-chat-message-${n}`),h.addClass(`highlight-chat-message-content-${n}`),n==="assistant"&&a?this.typeWriter(h,r):h.textContent=r,s.scrollTop=s.scrollHeight}async typeWriter(s,r,n=30){let a=0;s.textContent="";let l=s.createEl("span",{cls:"highlight-chat-cursor"}),h=()=>{a<r.length?(s.insertBefore(document.createTextNode(r.charAt(a)),l),a++,setTimeout(h,n)):l.remove()};h()}static getInstance(s,r){return UA.instance||(UA.instance=new UA(s,r)),UA.instance}setupChatInput(s){let r=s.createEl("div",{cls:"highlight-chat-input-wrapper"});this.textarea=r.createEl("textarea",{cls:"highlight-chat-input",attr:{placeholder:p("Input message..."),rows:"1"}});let n=()=>{this.textarea&&(this.textarea.addClass("highlight-chat-input"),this.textarea.style.height=`${Math.min(this.textarea.scrollHeight,150)}px`)};return this.textarea.addEventListener("input",()=>{n()}),this.textarea.addEventListener("keydown",a=>{a.key==="Enter"&&!a.shiftKey&&(a.preventDefault(),this.textarea&&this.handleSendMessage(this.textarea))}),this.textarea}async handleSendMessage(s){let r=s.value.trim();if(!(!r||this.isProcessing))try{this.isProcessing=!0;let n=r,a=r;if(this.draggedContents.length>0){let h=this.draggedContents.map(g=>g.text).join(`

---

`);if(this.chatHistory.push({role:"user",content:`\u4EE5\u4E0B\u662F\u9700\u8981\u5206\u6790\u7684\u5185\u5BB9\uFF1A

${h}`}),n=r,a=`\u7528\u6237\u63D0\u793A\uFF1A${r}`,this.currentPreviewContainer){let g=this.currentPreviewContainer.closest(".highlight-chat-message-preview");g&&g.addClass("sent"),this.currentPreviewContainer=null,this.draggedContents=[]}}this.chatHistory.push({role:"user",content:a}),requestAnimationFrame(()=>{s.value="",s.addClass("highlight-chat-input"),s.dispatchEvent(new Event("input"))});let l=this.containerEl.querySelector(".highlight-chat-history");if(l){this.addMessage(l,r,"user",!0);let h=await this.chatService.sendMessage(n,this.chatHistory);this.chatHistory.push({role:"assistant",content:h.content}),this.addMessage(l,h.content,"assistant",!0)}}catch(n){}finally{this.isProcessing=!1}}updatePreviewCount(){if(this.currentPreviewContainer){let s=this.currentPreviewContainer.closest(".highlight-chat-message-preview"),r=s==null?void 0:s.querySelector(".highlight-chat-preview-count");r&&(r.textContent=String(this.draggedContents.length),this.draggedContents.length===0&&s&&(s.remove(),this.currentPreviewContainer=null))}}clearChat(){if(this.chatHistory=[],this.draggedContents=[],this.currentPreviewContainer){let r=this.currentPreviewContainer.closest(".highlight-chat-message-preview");r&&r.remove(),this.currentPreviewContainer=null}let s=this.containerEl.querySelector(".highlight-chat-history");if(s)for(;s.firstChild;)s.removeChild(s.firstChild);this.textarea&&(this.textarea.value="",this.textarea.addClass("highlight-chat-input"))}getCurrentModelName(){var n,a,l,h,g,m,w,f;let s=this.plugin.settings.ai;switch(this.chatModelState.provider||s.provider){case"openai":return this.chatModelState.model||((n=s.openai)==null?void 0:n.model)||"GPT-4";case"anthropic":return this.chatModelState.model||((a=s.anthropic)==null?void 0:a.model)||"Claude-3";case"ollama":return this.chatModelState.model||((l=s.ollama)==null?void 0:l.model)||"Ollama";case"gemini":return this.chatModelState.model||((h=s.gemini)==null?void 0:h.model)||"Gemini Pro";case"deepseek":return this.chatModelState.model||((g=s.deepseek)==null?void 0:g.model)||"Deepseek Chat";case"siliconflow":return(m=s.siliconflow)!=null&&m.isCustomModel&&((w=s.siliconflow)!=null&&w.model)?s.siliconflow.model:this.chatModelState.model?this.chatModelState.model:((f=s.siliconflow)==null?void 0:f.model)||"SiliconFlow";default:return"Unknown Model"}}async showModelSelector(s,r){var h,g,m,w,f,U;let n=new DA.Menu,a=this.plugin.settings.ai;switch(a.provider){case"siliconflow":try{if(jA.forEach(Q=>{n.addItem(S=>{var y,V;let b=!((y=a.siliconflow)!=null&&y.isCustomModel)&&(this.chatModelState.model===Q.id||((V=a.siliconflow)==null?void 0:V.model)===Q.id);S.setTitle(Q.name).setChecked(b).onClick(async()=>{a.siliconflow?(a.siliconflow.model=Q.id,a.siliconflow.isCustomModel=!1):a.siliconflow={model:Q.id,isCustomModel:!1},this.chatModelState.provider="siliconflow",this.chatModelState.model=Q.id,this.chatService.updateModel("siliconflow",Q.id),await this.plugin.saveSettings(),s.textContent=Q.name})})}),(h=a.siliconflow)!=null&&h.isCustomModel&&((g=a.siliconflow)!=null&&g.model)){n.addSeparator();let Q=a.siliconflow.model;n.addItem(S=>{var y,V;let b=((y=a.siliconflow)==null?void 0:y.isCustomModel)&&(this.chatModelState.model===Q||((V=a.siliconflow)==null?void 0:V.model)===Q);S.setTitle(Q).setChecked(b).onClick(async()=>{a.siliconflow?(a.siliconflow.model=Q,a.siliconflow.isCustomModel=!0):a.siliconflow={model:Q,isCustomModel:!0},this.chatModelState.provider="siliconflow",this.chatModelState.model=Q,this.chatService.updateModel("siliconflow",Q),await this.plugin.saveSettings(),s.textContent=Q})})}}catch(I){new DA.Notice(p("Unable to get SiliconFlow model list, please check API Key and network connection."))}break;case"openai":try{let I=await this.chatService.aiService.listOpenAIModels(),Q=I.filter(b=>!b.isCustom),S=I.filter(b=>b.isCustom);Q.forEach(b=>{n.addItem(y=>{y.setTitle(b.name).setChecked(this.chatModelState.model===b.id).onClick(async()=>{this.chatModelState.provider="openai",this.chatModelState.model=b.id,this.chatService.updateModel("openai",b.id),s.textContent=this.getCurrentModelName()})})}),S.length>0&&(n.addSeparator(),S.forEach(b=>{n.addItem(y=>{y.setTitle(b.name).setChecked(this.chatModelState.model===b.id).onClick(async()=>{this.chatModelState.provider="openai",this.chatModelState.model=b.id,this.chatService.updateModel("openai",b.id),s.textContent=this.getCurrentModelName()})})}))}catch(I){new DA.Notice(p("Unable to get OpenAI model list, please check API Key and network connection."))}break;case"anthropic":["claude-3-opus-20240229","claude-3-sonnet-20240229","claude-3-haiku-20240307"].forEach(I=>{n.addItem(Q=>{var S;Q.setTitle(I).setChecked(((S=a.anthropic)==null?void 0:S.model)===I).onClick(async()=>{a.anthropic||(a.anthropic={apiKey:"",model:I}),a.anthropic.model=I,await this.plugin.saveSettings(),s.textContent=this.getCurrentModelName()})})});break;case"ollama":try{(await this.chatService.aiService.listOllamaModels()).forEach(Q=>{n.addItem(S=>{var b;S.setTitle(Q).setChecked(((b=a.ollama)==null?void 0:b.model)===Q).onClick(async()=>{a.ollama||(a.ollama={host:"http://localhost:11434",model:Q}),a.ollama.model=Q,await this.plugin.saveSettings(),s.textContent=this.getCurrentModelName()})})})}catch(I){new DA.Notice(p("Unable to access the Ollama model, please check the service."))}break;case"gemini":try{(await this.chatService.aiService.listGeminiModels()).forEach(Q=>{n.addItem(S=>{var b;S.setTitle(Q.name).setChecked(((b=a.gemini)==null?void 0:b.model)===Q.id).onClick(async()=>{this.chatModelState.provider="gemini",this.chatModelState.model=Q.id,this.chatService.updateModel("gemini",Q.id),s.textContent=this.getCurrentModelName()})})}),(m=a.gemini)!=null&&m.isCustomModel&&((w=a.gemini)!=null&&w.model)&&(n.addSeparator(),n.addItem(Q=>{var b,y;let S={id:((b=a.gemini)==null?void 0:b.model)||"",name:(y=a.gemini)==null?void 0:y.model,isCustom:!0};Q.setTitle(S.name).setChecked(this.chatModelState.model===S.id).onClick(async()=>{this.chatModelState.provider="gemini",this.chatModelState.model=S.id,this.chatService.updateModel("gemini",S.id),s.textContent=this.getCurrentModelName()})}))}catch(I){new DA.Notice(p("Unable to get Gemini model list, please check API Key and network connection."))}break;case"deepseek":try{(await this.chatService.aiService.listDeepseekModels()).forEach(Q=>{n.addItem(S=>{var b;S.setTitle(Q.name).setChecked(((b=a.deepseek)==null?void 0:b.model)===Q.id).onClick(async()=>{this.chatModelState.provider="deepseek",this.chatModelState.model=Q.id,this.chatService.updateModel("deepseek",Q.id),s.textContent=this.getCurrentModelName()})})}),(f=a.deepseek)!=null&&f.isCustomModel&&((U=a.deepseek)!=null&&U.model)&&(n.addSeparator(),n.addItem(Q=>{var b,y;let S={id:((b=a.deepseek)==null?void 0:b.model)||"",name:(y=a.deepseek)==null?void 0:y.model,isCustom:!0};Q.setTitle(S.name).setChecked(this.chatModelState.model===S.id).onClick(async()=>{this.chatModelState.provider="deepseek",this.chatModelState.model=S.id,this.chatService.updateModel("deepseek",S.id),s.textContent=this.getCurrentModelName()})}))}catch(I){new DA.Notice(p("Unable to get Deepseek model list, please check API Key and network connection."))}break;default:new DA.Notice(p("Unknown AI provider"));break}let l=s.getBoundingClientRect();n.showAtPosition({x:l.left,y:l.bottom})}};UA.instance=null,UA.savedState=null;var ve=UA;var rt=class{constructor(s){this.STORAGE_KEY="flashcard-license";this.DEVICE_ID_KEY="device-id";this.API_URL="https://hi-note-license-server-production.up.railway.app";this.FEATURES=["flashcard"];this.VERIFICATION_INTERVAL_DAYS=7;this.licenseToken=null;this.plugin=s}async generateDeviceId(){try{let s=await this.plugin.loadData()||{};if(s[this.DEVICE_ID_KEY])return s[this.DEVICE_ID_KEY];let r=this.plugin.app.vault.adapter,n=this.plugin.app.vault.getName();r&&"basePath"in r&&(n=r.basePath+"/"+n);let a=navigator.platform||"",l=[n,a].join("|"),g=new TextEncoder().encode(l),m=await crypto.subtle.digest("SHA-256",g),f=Array.from(new Uint8Array(m)).map(U=>U.toString(16).padStart(2,"0")).join("");return await this.saveDeviceId(f),f}catch(s){let r=this.plugin.app.vault.getName(),a=new TextEncoder().encode(r),l=await crypto.subtle.digest("SHA-256",a),g=Array.from(new Uint8Array(l)).map(m=>m.toString(16).padStart(2,"0")).join("");return await this.saveDeviceId(g),g}}async saveDeviceId(s){let r=await this.plugin.loadData()||{};await this.plugin.saveData({...r,[this.DEVICE_ID_KEY]:s})}async activateLicense(s){try{let r=await this.generateDeviceId(),n=`${this.API_URL}/api/verify`,l=await fetch(n,{method:"POST",headers:{"Content-Type":"application/json",Origin:"app://obsidian.md",Accept:"application/json"},mode:"cors",credentials:"include",body:JSON.stringify({licenseKey:s,deviceId:r})});if(!l.ok)throw new Error(`Server response failed: ${l.status} ${l.statusText}`);let h=await l.json();if(h.valid){let g=await this.plugin.loadData()||{};return await this.plugin.saveData({...g,[this.STORAGE_KEY]:{key:s,token:h.token,features:h.features,deviceId:r,lastVerified:Date.now()}}),this.licenseToken=h.token,!0}return!1}catch(r){let n=r instanceof Error?r.message:"Unknown error";return!1}}async isFeatureEnabled(s){var a;let r=await this.plugin.loadData(),n=r==null?void 0:r[this.STORAGE_KEY];return((a=n==null?void 0:n.features)==null?void 0:a.includes(s))||!1}async isActivated(){try{let s=await this.plugin.loadData(),r=s==null?void 0:s[this.STORAGE_KEY];return r!=null&&r.token?this.shouldVerifyLicense(r.lastVerified)?this.verifyWithServer(r):(this.licenseToken||(this.licenseToken=r.token),!0):!1}catch(s){return!1}}shouldVerifyLicense(s){return s?(Date.now()-s)/(1e3*60*60*24)>=this.VERIFICATION_INTERVAL_DAYS:!0}async verifyWithServer(s){try{let r=await this.generateDeviceId(),n=s.deviceId,a=n&&n!==r,l=await fetch(`${this.API_URL}/api/verify`,{method:"POST",headers:{"Content-Type":"application/json",Origin:"app://obsidian.md",Accept:"application/json",Authorization:`Bearer ${s.token}`},mode:"cors",credentials:"include",body:JSON.stringify({licenseKey:s.key,deviceId:r,isDeviceChanged:a})});if(!l.ok)return!!this.licenseToken;let h=await l.json();if(h.valid){let g=await this.plugin.loadData()||{};return await this.plugin.saveData({...g,[this.STORAGE_KEY]:{...s,token:h.token||s.token,deviceId:r,lastVerified:Date.now()}}),this.licenseToken=h.token||s.token,!0}return!1}catch(r){return!!this.licenseToken}}};var ae="comment-view",se=class extends gA.ItemView{constructor(r,n){super(r);this.currentFile=null;this.isFlashcardMode=!1;this.highlights=[];this.isDraggedToMainView=!1;this.currentBatch=0;this.isLoading=!1;this.BATCH_SIZE=20;this.floatingButton=null;this.aiButtons=[];this.currentEditingHighlightId=null;this.flashcardComponent=null;this.commentStore=n;let a=this.app.plugins;if(a&&a.plugins&&a.plugins["hi-note"])this.plugin=a.plugins["hi-note"];else throw new Error("Hi-Note plugin not found");this.locationService=new Ri(this.app),this.exportService=new Ni(this.app,this.commentStore),this.highlightService=new ne(this.app),this.licenseManager=new rt(this.plugin),this.registerEvent(this.app.workspace.on("file-open",h=>{h&&!this.isDraggedToMainView&&(this.currentFile=h,this.updateHighlights())})),this.registerEvent(this.app.vault.on("modify",h=>{h===this.currentFile&&!this.isDraggedToMainView&&this.updateHighlights()}));let l=h=>{let{highlightId:g,text:m}=h.detail;setTimeout(()=>{this.highlightContainer.querySelectorAll(".highlight-card").forEach(f=>{f.removeClass("selected")});let w=Array.from(this.highlightContainer.querySelectorAll(".highlight-card")).find(f=>{var H;return((H=f.querySelector(".highlight-text-content"))==null?void 0:H.textContent)===m});if(w){w.addClass("selected");let f=w.querySelector(".highlight-action-buttons .highlight-action-buttons-right .highlight-add-comment-btn");f&&f.click(),w.scrollIntoView({behavior:"smooth"})}},100)};window.addEventListener("open-comment-input",l),this.register(()=>window.removeEventListener("open-comment-input",l)),this.registerEvent(this.app.workspace.on("layout-change",()=>{this.checkViewPosition()})),this.loadingIndicator=createEl("div",{cls:"highlight-loading-indicator",text:p("Loading...")}),this.loadingIndicator.addClass("highlight-display-none")}getViewType(){return ae}getDisplayText(){return"HiNote"}getIcon(){return"highlighter"}async onOpen(){let r=this.containerEl.children[1];r.empty();let n=r.createEl("div",{cls:"highlight-main-container"});this.fileListContainer=n.createEl("div",{cls:"highlight-file-list-container"}),this.mainContentContainer=n.createEl("div",{cls:"highlight-content-container"}),this.searchContainer=this.mainContentContainer.createEl("div",{cls:"highlight-search-container"}),this.searchInput=this.searchContainer.createEl("input",{cls:"highlight-search-input",attr:{type:"text",placeholder:p("Search...")}});let a=this.searchContainer.createEl("div",{cls:"highlight-search-icons"}),l=a.createEl("button",{cls:"highlight-icon-button"});(0,gA.setIcon)(l,"message-square-plus"),l.setAttribute("aria-label",p("Add File Comment")),l.addEventListener("click",async()=>{if(!this.currentFile){new gA.Notice(p("Please open a file first."));return}let m=Date.now(),f={id:`file-comment-${m}`,text:`__virtual_highlight_${m}__`,filePath:this.currentFile.path,fileType:this.currentFile.extension,displayText:p("File Comment"),isVirtual:!0,position:0,paragraphOffset:0,paragraphId:`${this.currentFile.path}#^virtual-${m}`,createdAt:m,updatedAt:m,comments:[]};await this.commentStore.addComment(this.currentFile,f),this.highlights.unshift(f),this.renderHighlights(this.highlights),setTimeout(()=>{let U=this.highlightContainer.querySelector(".highlight-card");U&&(this.showCommentInput(U,f),this.highlightContainer.scrollTo({top:0,behavior:"smooth"}))},100)});let h=a.createEl("button",{cls:"highlight-icon-button"});(0,gA.setIcon)(h,"file-symlink"),h.setAttribute("aria-label",p("Export as notes")),h.addEventListener("click",async()=>{if(!this.currentFile){new gA.Notice(p("Please open a file first."));return}try{let m=await this.exportService.exportHighlightsToNote(this.currentFile);new gA.Notice(p("Successfully exported highlights to: ")+m.path),await this.app.workspace.getLeaf().openFile(m)}catch(m){new gA.Notice(p("Failed to export highlights: ")+m.message)}}),this.searchInput.addEventListener("input",this.debounce(()=>{this.updateHighlightsList()},300)),this.highlightContainer=this.mainContentContainer.createEl("div",{cls:"highlight-container"});let g=this.app.workspace.getActiveFile();g&&(this.currentFile=g,await this.updateHighlights()),this.updateViewLayout()}debounce(r,n){let a;return(...l)=>{clearTimeout(a),a=setTimeout(()=>r.apply(this,l),n)}}async updateHighlights(){if(this.isInAllHighlightsView()){await this.updateAllHighlights();return}if(!this.currentFile){this.renderHighlights([]);return}if(!this.highlightService.shouldProcessFile(this.currentFile)){this.renderHighlights([]);return}let r=await this.app.vault.read(this.currentFile),n=this.highlightService.extractHighlights(r),a=this.commentStore.getFileComments(this.currentFile);this.highlights=n.map(h=>{let g=a.find(m=>{let w=m.text===h.text;return w&&h.position!==void 0&&m.position!==void 0?Math.abs(m.position-h.position)<1e3:w});return g?{...h,id:g.id,comments:g.comments,createdAt:g.createdAt,updatedAt:g.updatedAt}:h});let l=a.filter(h=>h.isVirtual&&h.comments&&h.comments.length>0);this.highlights.unshift(...l),this.renderHighlights(this.highlights)}async updateHighlightsList(){let r=this.searchInput.value.toLowerCase().trim();this.highlightContainer.empty();let n=this.highlights.filter(a=>{var l,h;return!!(a.text.toLowerCase().includes(r)||(l=a.comments)!=null&&l.some(g=>g.content.toLowerCase().includes(r))||this.currentFile===null&&((h=a.fileName)!=null&&h.toLowerCase().includes(r)))});this.renderHighlights(n)}renderHighlights(r,n=!1){if(n||this.highlightContainer.empty(),r.length===0&&!n){this.highlightContainer.createEl("div",{cls:"highlight-empty-state",text:this.searchInput.value.trim()?p("No matching content found."):p("The current document has no highlighted content.")});return}let a=this.highlightContainer.querySelector(".highlight-list");a||(a=this.highlightContainer.createEl("div",{cls:"highlight-list"})),r.forEach(l=>{let h;this.currentFile&&!l.filePath&&(l.filePath=this.currentFile.path),h=new Ki(a,l,this.plugin,{onHighlightClick:async m=>await this.jumpToHighlight(m),onCommentAdd:m=>this.showCommentInput(h.getElement(),m),onExport:m=>this.exportHighlightAsImage(m),onCommentEdit:(m,w)=>this.showCommentInput(h.getElement(),m,w),onAIResponse:async m=>{await this.addComment(l,m),await this.updateHighlights()}},this.isDraggedToMainView,this.currentFile===null?l.fileName:this.currentFile.basename);let g=h.getElement();if(this.isDraggedToMainView){g.classList.add("in-main-view");let m=g.querySelector(".highlight-text-content");m&&m.removeAttribute("title")}else{g.classList.remove("in-main-view");let m=g.querySelector(".highlight-text-content");m&&m.setAttribute("aria-label","Click to jump to the document position")}})}async addComment(r,n){let a=await this.getFileForHighlight(r);if(!a){new gA.Notice(p("No corresponding file found."));return}r.id||(r.id=this.generateHighlightId(r)),r.comments||(r.comments=[]);let l={id:`comment-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,content:n,createdAt:Date.now(),updatedAt:Date.now()};r.comments.push(l),r.updatedAt=Date.now(),await this.commentStore.addComment(a,r),window.dispatchEvent(new CustomEvent("comment-updated",{detail:{text:r.text,comments:r.comments}})),await this.refreshView()}async updateComment(r,n,a){let l=await this.getFileForHighlight(r);if(!l||!r.comments)return;let h=r.comments.find(g=>g.id===n);h&&(h.content=a,h.updatedAt=Date.now(),r.updatedAt=Date.now(),await this.commentStore.addComment(l,r),window.dispatchEvent(new CustomEvent("comment-updated",{detail:{text:r.text,comments:r.comments}})),await this.refreshView())}async deleteComment(r,n){let a=await this.getFileForHighlight(r);!a||!r.comments||(r.comments=r.comments.filter(l=>l.id!==n),r.updatedAt=Date.now(),r.isVirtual&&r.comments.length===0?(await this.commentStore.removeComment(a,r),this.highlights=this.highlights.filter(l=>l.id!==r.id)):await this.commentStore.addComment(a,r),window.dispatchEvent(new CustomEvent("comment-updated",{detail:{text:r.text,comments:r.comments}})),await this.refreshView())}async getFileForHighlight(r){if(this.currentFile)return this.currentFile;if(r.filePath){let n=this.app.vault.getAbstractFileByPath(r.filePath);if(n instanceof gA.TFile)return n}if(r.fileName){let a=this.app.vault.getFiles().find(l=>l.basename===r.fileName||l.name===r.fileName);if(a)return a}return null}generateHighlightId(r){return`highlight-${r.position}-${Date.now()}-${Math.random().toString(36).substr(2,9)}`}async jumpToHighlight(r){if(!this.isDraggedToMainView){if(!this.currentFile){new gA.Notice(p("No corresponding file found."));return}await this.locationService.jumpToHighlight(r,this.currentFile.path)}}async exportHighlightAsImage(r){try{let n=(await Promise.resolve().then(()=>ha(cs()))).default;new Ii(this.app,r,n).open()}catch(n){new gA.Notice(p("Export failed: Failed to load necessary components."))}}async showCommentInput(r,n,a){this.currentEditingHighlightId=n.id,new Gi(r,n,a,{onSave:async l=>{a?await this.updateComment(n,a.id,l):await this.addComment(n,l),await this.updateHighlights()},onDelete:a?async()=>{await this.deleteComment(n,a.id)}:void 0,onCancel:async()=>{let l=this.highlights.find(h=>h.id===this.currentEditingHighlightId);if(l!=null&&l.isVirtual&&(!l.comments||l.comments.length===0)){let h=await this.getFileForHighlight(l);h&&(await this.commentStore.removeComment(h,l),this.highlights=this.highlights.filter(g=>g.id!==l.id),await this.refreshView())}}}).show()}async onload(){}checkViewPosition(){let r=this.app.workspace.rootSplit,n=this.isViewInMainArea(this.leaf,r);if(this.isDraggedToMainView!==n){if(this.isDraggedToMainView=n,!n){this.isFlashcardMode&&(this.isFlashcardMode=!1,this.flashcardComponent&&(this.flashcardComponent.deactivate(),this.flashcardComponent=null),this.updateFileListSelection());let a=this.app.workspace.getActiveFile();a&&(this.currentFile=a,this.updateHighlights())}this.updateViewLayout(),this.updateHighlightsList()}}isViewInMainArea(r,n){return n&&n.children?n.children.some(a=>a===r?!0:this.isViewInMainArea(r,a)):!1}async updateViewLayout(){this.fileListContainer.removeClass("highlight-display-block"),this.fileListContainer.removeClass("highlight-display-none"),this.isDraggedToMainView?(this.fileListContainer.addClass("highlight-display-block"),await this.updateFileList(),this.createFloatingButton()):(this.fileListContainer.addClass("highlight-display-none"),this.removeFloatingButton())}async updateFileList(){var Q;if(this.fileListContainer.children.length>0){this.updateFileListSelection();return}this.fileListContainer.empty(),this.fileListContainer.createEl("div",{cls:"highlight-file-list-header"}).createEl("div",{text:"HiNote",cls:"highlight-file-list-title"});let n=this.fileListContainer.createEl("div",{cls:"highlight-file-list"}),a=n.createEl("div",{cls:`highlight-file-item highlight-file-item-all ${this.currentFile===null?"is-active":""}`});a.addEventListener("click",()=>{this.currentFile=null,this.isFlashcardMode=!1,this.updateHighlights(),this.updateFileListSelection(),this.searchContainer.addClass("highlight-display-default");let S=this.searchContainer.querySelector(".highlight-search-icons");S&&S.addClass("highlight-display-none")});let l=a.createEl("div",{cls:"highlight-file-item-left"}),h=l.createEl("span",{cls:"highlight-file-item-icon"});(0,gA.setIcon)(h,"documents"),l.createEl("span",{text:p("All Highlight"),cls:"highlight-file-item-name"});let g=n.createEl("div",{cls:"highlight-file-item highlight-file-item-flashcard"});g.addEventListener("click",async()=>{this.currentFile=null,this.isFlashcardMode=!0,this.updateFileListSelection(),this.searchContainer.addClass("highlight-display-none");let S=await this.getFilesWithHighlights(),b=[];for(let y of S){let R=this.commentStore.getFileComments(y).filter(N=>{var _;return!N.isVirtual&&((_=N.comments)==null?void 0:_.length)>0});b.push(...R)}this.highlightContainer.empty(),this.flashcardComponent||(this.flashcardComponent=new St(this.highlightContainer,this.plugin),this.flashcardComponent.setLicenseManager(this.licenseManager)),await this.flashcardComponent.activate(),this.flashcardComponent.setCards(b)});let m=g.createEl("div",{cls:"highlight-file-item-left"}),w=m.createEl("span",{cls:"highlight-file-item-icon"});(0,gA.setIcon)(w,"book-heart"),m.createEl("span",{text:p("HiCard"),cls:"highlight-file-item-name"});let f=g.createEl("span",{cls:"highlight-file-item-count"}),U=async()=>{let S=this.plugin.fsrsManager.getLatestCards();f.textContent=`${S.length}`};U(),this.registerEvent(this.plugin.eventManager.on("flashcard:changed",()=>{U()})),m.addEventListener("click",async()=>{let b=this.plugin.fsrsManager.getLatestCards().map(y=>({id:y.id,text:y.text,filePath:y.filePath,comments:[{id:y.id+"-answer",content:y.answer,createdAt:y.createdAt,updatedAt:y.lastReview||y.createdAt}],isVirtual:!1,position:0,paragraphOffset:0,paragraphId:"",createdAt:y.createdAt,updatedAt:y.lastReview||y.createdAt}));this.highlightContainer.empty(),this.flashcardComponent||(this.flashcardComponent=new St(this.highlightContainer,this.plugin),this.flashcardComponent.setLicenseManager(this.licenseManager)),await this.flashcardComponent.activate(),this.flashcardComponent.setCards(b),this.updateFileListSelection()});let H=await this.getTotalHighlightsCount();a.createEl("span",{text:`${H}`,cls:"highlight-file-item-count"}),n.createEl("div",{cls:"highlight-file-list-separator"}),a.addEventListener("click",async()=>{this.currentFile=null,this.isFlashcardMode=!1,this.flashcardComponent&&(this.flashcardComponent.deactivate(),this.flashcardComponent=null),this.updateFileListSelection(),await this.updateAllHighlights()});let I=await this.getFilesWithHighlights();for(let S of I){let b=n.createEl("div",{cls:`highlight-file-item ${((Q=this.currentFile)==null?void 0:Q.path)===S.path?"is-active":""}`});b.setAttribute("data-path",S.path);let y=b.createEl("div",{cls:"highlight-file-item-left"}),V=y.createEl("span",{cls:"highlight-file-item-icon",attr:{"aria-label":p("Open (DoubleClick)")}});(0,gA.setIcon)(V,"file-text"),V.addEventListener("dblclick",async _=>{_.stopPropagation(),await this.getPreferredLeaf().openFile(S)});let R=y.createEl("span",{text:S.basename,cls:"highlight-file-item-name"});this.addPagePreview(R,S);let N=await this.getFileHighlightsCount(S);b.createEl("span",{text:`${N}`,cls:"highlight-file-item-count"}),b.addEventListener("click",async()=>{this.currentFile=S,this.isFlashcardMode=!1,this.flashcardComponent&&(this.flashcardComponent.deactivate(),this.flashcardComponent=null),this.updateFileListSelection(),this.searchContainer.addClass("highlight-display-default");let _=this.searchContainer.querySelector(".highlight-search-icons");_&&_.addClass("highlight-display-default"),await this.updateHighlights()})}}updateFileListSelection(){let r=this.fileListContainer.querySelector(".highlight-file-item-all");r&&r.classList.toggle("is-active",this.currentFile===null&&!this.isFlashcardMode);let n=this.fileListContainer.querySelector(".highlight-file-item-flashcard");n&&n.classList.toggle("is-active",this.isFlashcardMode),this.fileListContainer.querySelectorAll(".highlight-file-item:not(.highlight-file-item-all):not(.highlight-file-item-flashcard)").forEach(l=>{var g;let h=((g=this.currentFile)==null?void 0:g.path)===l.getAttribute("data-path");l.classList.toggle("is-active",h)})}async getFilesWithHighlights(){let n=(await this.app.vault.getMarkdownFiles()).filter(l=>this.highlightService.shouldProcessFile(l)),a=[];for(let l of n){let h=await this.app.vault.read(l);this.highlightService.extractHighlights(h).length>0&&a.push(l)}return a}async updateAllHighlights(){this.currentBatch=0,this.highlights=[],this.highlightContainer.empty(),this.highlightContainer.appendChild(this.loadingIndicator),await this.loadMoreHighlights();let r=this.debounce(async n=>{let a=n.target,{scrollTop:l,scrollHeight:h,clientHeight:g}=a;h-l-g<300&&await this.loadMoreHighlights()},100);this.highlightContainer.addEventListener("scroll",r),this.register(()=>this.highlightContainer.removeEventListener("scroll",r))}async loadMoreHighlights(){if(!this.isLoading){this.isLoading=!0,this.loadingIndicator.addClass("highlight-display-block");try{let r=await this.getFilesWithHighlights(),n=this.currentBatch*this.BATCH_SIZE,a=r.slice(n,n+this.BATCH_SIZE);if(a.length===0){this.loadingIndicator.remove();return}let l=[];for(let h of a){let g=await this.app.vault.read(h),m=this.highlightService.extractHighlights(g),w=this.commentStore.getFileComments(h),f=m.map(U=>{let H=w.find(I=>{let Q=I.text===U.text;return Q&&U.position!==void 0&&I.position!==void 0?Math.abs(I.position-U.position)<1e3:Q});if(H){let I=[...H.comments].sort((Q,S)=>S.updatedAt-Q.updatedAt);return{...H,comments:I,position:U.position,paragraphOffset:U.paragraphOffset,fileName:h.basename,filePath:h.path,fileIcon:"file-text"}}return{id:this.generateHighlightId(U),...U,comments:[],createdAt:Date.now(),updatedAt:Date.now(),fileName:h.basename,filePath:h.path,fileIcon:"file-text"}});l.push(...f)}this.highlights.push(...l),await this.renderHighlights(l,!0),this.currentBatch++}catch(r){new gA.Notice("\u52A0\u8F7D\u9AD8\u4EAE\u5185\u5BB9\u65F6\u51FA\u9519")}finally{this.isLoading=!1,this.loadingIndicator.addClass("highlight-display-none")}}}async getFileHighlightsCount(r){let n=await this.app.vault.read(r);return this.highlightService.extractHighlights(n).length}async getTotalHighlightsCount(){let r=await this.getFilesWithHighlights(),n=0;for(let a of r)n+=await this.getFileHighlightsCount(a);return n}createFloatingButton(){if(this.floatingButton)return;this.floatingButton=document.createElement("div"),this.floatingButton.className="highlight-floating-button";let r=document.createElement("span");(0,gA.setIcon)(r,"message-circle"),this.floatingButton.appendChild(r),this.floatingButton.addEventListener("click",()=>{try{ve.getInstance(this.app,this.plugin).show()}catch(n){}}),document.body.appendChild(this.floatingButton)}removeFloatingButton(){this.floatingButton&&(this.floatingButton.remove(),this.floatingButton=null)}onunload(){this.removeFloatingButton()}updateAIDropdowns(){this.aiButtons.forEach(r=>{r.updateDropdownContent()}),this.app.workspace.trigger("comment-view:update-ai-dropdowns")}registerAIButton(r){this.aiButtons.push(r)}unregisterAIButton(r){let n=this.aiButtons.indexOf(r);n!==-1&&this.aiButtons.splice(n,1)}isInAllHighlightsView(){return this.currentFile===null}async refreshView(){this.isInAllHighlightsView()?await this.updateAllHighlights():await this.updateHighlights()}addPagePreview(r,n){let a;r.addEventListener("mouseenter",l=>{a=setTimeout(async()=>{let h=l.target;this.app.workspace.trigger("hover-link",{event:l,source:"hi-note",hoverParent:h,targetEl:h,linktext:n.path})},300)}),r.addEventListener("mouseleave",()=>{a&&clearTimeout(a)})}getPreferredLeaf(){let r=this.app.workspace.getLeavesOfType("markdown");if(this.isDraggedToMainView){let n=r.find(a=>a!==this.leaf);if(n)return n}return this.app.workspace.getLeaf("split","vertical")}};var Fa=require("obsidian");var _i=class{constructor(s){this.data={};this.fileCommentsData={};this.comments=new Map;this.fileComments=new Map;this.commentCache=new Map;this.maxCacheSize=100;this.PERFORMANCE_THRESHOLD=100;this.plugin=s,this.eventManager=new Ce(s.app),this.blockIdService=new it(s.app)}async loadComments(){let s=await this.plugin.loadData();this.data=(s==null?void 0:s.comments)||{},this.fileCommentsData=(s==null?void 0:s.fileComments)||{},this.migrateDataToBlockId(),this.comments=new Map(Object.entries(this.data).map(([r,n])=>[r,Object.values(n)])),this.fileComments=new Map(Object.entries(this.fileCommentsData))}migrateDataToBlockId(){let s=0;for(let r in this.data){let n=this.data[r];for(let a in n){let l=n[a];if(l.paragraphId&&!l.blockId){let h=l.paragraphId.match(/#\^([a-zA-Z0-9-]+)/);h&&h[1]&&(l.blockId=h[1],s++)}}}s>0&&console.info(`[CommentStore] Migrated ${s} highlights from paragraphId to blockId`)}async saveComments(){let r={...await this.plugin.loadData()||{},comments:this.data,fileComments:Object.fromEntries(this.fileComments)};await this.plugin.saveData(r);let n=await this.plugin.loadData()}async handleFileRename(s,r){if(this.data[s]){this.data[r]=this.data[s],delete this.data[s];let l=this.commentCache.get(s);l&&(this.commentCache.delete(s),this.commentCache.set(r,l))}let n=this.fileComments.get(s);n&&(this.fileComments.delete(s),this.fileComments.set(r,n));let a=this.comments.get(s);a&&(this.comments.delete(s),this.comments.set(r,a)),await this.saveComments()}getFileComments(s){let r=this.data[s.path]||{};return Object.values(r).sort((n,a)=>{let l="isVirtual"in n?n.isVirtual:!1,h="isVirtual"in a?a.isVirtual:!1;return l&&!h?-1:!l&&h?1:n.position-a.position})}getFileOnlyComments(s){return this.fileComments.get(s.path)||[]}async addComment(s,r){if(!r.id)throw new Error("Highlight ID is required");if(this.data[s.path]||(this.data[s.path]={}),"isVirtual"in r&&r.isVirtual){this.data[s.path][r.id]=r,await this.saveComments();return}if(!r.blockId){let n=this.plugin.app.workspace.getActiveViewOfType(Fa.MarkdownView),a=n==null?void 0:n.editor,l=n==null?void 0:n.file;if(a&&l&&typeof r.position=="number")try{let h=a.offsetToPos(r.position),g=this.blockIdService.getOrCreateBlockId(a,h.line);r.blockId=g;let m=a.getValue();await this.plugin.app.vault.modify(l,m)}catch(h){console.error("Error creating block ID:",h);let g=Date.now().toString(36)+"-"+Math.random().toString(36).substr(2,5);r.blockId=g}else{let h=Date.now().toString(36)+"-"+Math.random().toString(36).substr(2,5);r.blockId=h}}if(r.paragraphId&&!r.blockId){let n=r.paragraphId.match(/#\^([a-zA-Z0-9-]+)/);n&&n[1]&&(r.blockId=n[1])}this.data[s.path][r.id]=r,await this.saveComments()}async addFileComment(s,r){let n={id:`file-comment-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,content:r,createdAt:Date.now(),updatedAt:Date.now(),filePath:s.path},a=this.fileComments.get(s.path)||[];return a.push(n),this.fileComments.set(s.path,a),await this.saveComments(),n}async updateComment(s,r,n){var a;if((a=this.data[s.path])!=null&&a[r]){let l=this.data[s.path][r],h={id:`comment-${Date.now()}`,content:n,createdAt:Date.now(),updatedAt:Date.now()};l.comments||(l.comments=[]),l.comments.push(h),l.updatedAt=Date.now(),await this.saveComments()}}async updateFileComment(s,r,n){let l=(this.fileComments.get(s.path)||[]).find(h=>h.id===r);l&&(l.content=n,l.updatedAt=Date.now(),await this.saveComments())}async removeComment(s,r){var a;let n=s.path;(a=this.data[n])!=null&&a[r.id]&&(delete this.data[n][r.id],Object.keys(this.data[n]).length===0&&delete this.data[n],await this.saveComments())}async deleteFileComment(s,r){let n=this.fileComments.get(s.path)||[],a=n.findIndex(l=>l.id===r);a!==-1&&(n.splice(a,1),n.length===0?this.fileComments.delete(s.path):this.fileComments.set(s.path,n),await this.saveComments())}async cleanupComments(s){let r=!1;for(let n of Object.keys(this.data))s.has(n)||(delete this.data[n],r=!0);for(let n of Object.keys(this.fileCommentsData))s.has(n)||(delete this.fileCommentsData[n],r=!0);r&&await this.saveComments()}loadVisibleComments(s){let r=this.plugin.app.workspace.getActiveFile();r&&(s.forEach(n=>{let a=this.getCommentsByParagraphId(r,n);this.commentCache.set(n,a)}),this.pruneCache())}pruneCache(){this.commentCache.size>this.maxCacheSize&&Array.from(this.commentCache.keys()).slice(0,this.commentCache.size-this.maxCacheSize).forEach(r=>this.commentCache.delete(r))}batchUpdateComments(s){let r=new Map;s.forEach(({id:n,comment:a})=>{var l;r.has(n)||r.set(n,[]),(l=r.get(n))==null||l.push(a)}),r.forEach((n,a)=>{this.comments.set(a,n)})}checkPerformance(s){let r=performance.now();s(),performance.now()-r>this.PERFORMANCE_THRESHOLD}getCommentsByParagraphId(s,r){let n=this.data[s.path]||{};return Object.values(n).filter(a=>a.paragraphId===r).sort((a,l)=>a.position-l.position)}getCommentsByBlockId(s,r){let n=this.data[s.path]||{};return Object.values(n).filter(a=>a.blockId===r).sort((a,l)=>a.position-l.position)}hasParagraphComments(s,r){return this.getCommentsByParagraphId(s,r).length>0}hasBlockComments(s,r){return this.getCommentsByBlockId(s,r).length>0}async clearAllComments(){this.data={},this.fileCommentsData={},await this.saveComments()}getBlockId(s,r){return this.blockIdService.getOrCreateBlockId(s,r)}getHiNotes(s){let r=this.plugin.app.workspace.getActiveFile();return r?this.getFileComments(r).filter(a=>{let l=a.text===s.text;return l&&typeof a.position=="number"&&typeof s.position=="number"?Math.abs(a.position-s.position)<1e3:l}):[]}};var Qe=require("@codemirror/view"),hs=require("obsidian");var Ua=require("@codemirror/view"),Ea=require("obsidian"),Xi=class M extends Ua.WidgetType{constructor(r,n,a,l){super();this.plugin=r;this.highlight=n;this.highlightItems=a;this.onClick=l}eq(r){return r instanceof M?this.highlight.text===r.highlight.text&&this.highlight.comments.length===r.highlight.comments.length:!1}get estimatedHeight(){return 0}get lineBreaks(){return 0}coordsAt(r,n,a){return null}toDOM(){let r=document.createElement("span");return r.addClass("hi-note-widget"),this.highlight.blockId&&r.setAttribute("data-block-id",this.highlight.blockId),this.highlight.paragraphId&&r.setAttribute("data-paragraph-id",this.highlight.paragraphId),r.setAttribute("data-highlight-text",this.highlight.text),(this.highlight.comments||[]).length>0||r.addClass("hi-note-widget-no-comments"),this.createButton(r),r}createButton(r){let n=(this.highlight.comments||[]).length>0,a=r.createEl("button",{cls:`hi-note-button ${n?"":"hi-note-button-hidden"}`}),l=this.createIconContainer(a),h=this.createTooltip(r);this.setupEventListeners(r,a,h)}createIconContainer(r){let n=r.createEl("span",{cls:"hi-note-icon-container"});(0,Ea.setIcon)(n,"message-circle");let l=(this.highlight.comments||[]).length;return l>0&&n.createEl("span",{cls:"hi-note-count",text:l.toString()}),n}createTooltip(r){let n=document.createElement("div");n.addClass("hi-note-tooltip","hi-note-tooltip-hidden"),n.setAttribute("data-highlight-id",this.highlight.id);let a=n.createEl("div",{cls:"hi-note-tooltip-list"}),l=this.highlight.comments||[];return this.renderTooltipContent(a,l,n),document.body.appendChild(n),{tooltip:n,updateTooltipPosition:()=>{let g=r.getBoundingClientRect();n.style.position="fixed",n.style.top=`${g.bottom+4}px`,n.style.left=`${g.right-n.offsetWidth}px`}}}renderTooltipContent(r,n,a){n.length!==0&&(n.slice(0,3).forEach(l=>{let h=r.createEl("div",{cls:"hi-note-tooltip-item"});h.createEl("div",{cls:"hi-note-tooltip-content",text:l.content}),h.createEl("div",{cls:"hi-note-tooltip-time",text:new Date(l.createdAt).toLocaleString()})}),n.length>3&&a.createEl("div",{cls:"hi-note-tooltip-more",text:`\u8FD8\u6709 ${n.length-3} \u6761\u8BC4\u8BBA...`}))}setupEventListeners(r,n,a){let{tooltip:l,updateTooltipPosition:h}=a;(this.highlight.comments||[]).length>0?(n.removeClass("hi-note-button-hidden"),n.addEventListener("mouseenter",()=>{l.removeClass("hi-note-tooltip-hidden"),h()}),n.addEventListener("mouseleave",()=>{l.addClass("hi-note-tooltip-hidden")})):(n.addClass("hi-note-button-hidden"),r.addEventListener("mouseenter",()=>{n.removeClass("hi-note-button-hidden")}),r.addEventListener("mouseleave",()=>{n.addClass("hi-note-button-hidden")})),n.addEventListener("click",w=>{w.preventDefault(),w.stopPropagation(),this.onClick(),l.addClass("hi-note-tooltip-hidden");let f=new CustomEvent("open-comment-input",{detail:{highlightId:this.highlight.id,text:this.highlight.text}});window.dispatchEvent(f)}),window.addEventListener("resize",h),document.addEventListener("click",w=>{n.contains(w.target)||l.addClass("hi-note-tooltip-hidden")})}destroy(r){let n=document.querySelector(`.hi-note-tooltip[data-highlight-id="${this.highlight.id}"]`);n&&n.remove(),r.remove()}updateDOM(r){return!1}ignoreEvent(){return!1}};var Ji=class{constructor(s,r){this.plugin=s,this.commentStore=r,this.highlightService=new ne(this.plugin.app)}getActiveMarkdownView(){return this.plugin.app.workspace.getActiveViewOfType(hs.MarkdownView)}enable(){let s=this.plugin,r=this.commentStore;this.plugin.registerDomEvent(window,"comment-updated",a=>{let l=document.querySelectorAll(".hi-note-widget"),h=a.detail.text,g=this.getActiveMarkdownView();if(!g||!g.file)return;let m=this.commentStore.getFileComments(g.file);m&&l.forEach(w=>{let f=w.getAttribute("data-highlight-text");if(f===h){let U=m.find(S=>S.text===f),H=(U==null?void 0:U.comments)||[],I=w.querySelector(".hi-note-count");if(I){let S=H.length;I.textContent=S.toString();let b=w.querySelector(".hi-note-button");S===0?b==null||b.addClass("hi-note-button-hidden"):b==null||b.removeClass("hi-note-button-hidden")}let Q=w.querySelector(".hi-note-tooltip");Q&&this.updateTooltipContent(Q,H)}})});let n=Qe.ViewPlugin.fromClass(class{constructor(a){this.plugin=s,this.commentStore=r,this.highlightService=new ne(this.plugin.app),this.decorations=this.buildDecorations(a)}update(a){(a.docChanged||a.viewportChanged)&&(this.decorations=this.buildDecorations(a.view))}buildDecorations(a){var f,U,H,I;let l=[],g=a.state.doc.toString(),m=this.plugin.app.workspace.getActiveViewOfType(hs.MarkdownView);if(!m||!m.file)return Qe.Decoration.none;if(!this.highlightService.shouldProcessFile(m.file))return Qe.Decoration.none;let w=this.highlightService.extractHighlights(g);for(let Q of w){if(Q.position===void 0)continue;let S={...Q,id:Q.id||`highlight-${Date.now()}-${Q.position}`,comments:Q.comments||[],position:Q.position,paragraphOffset:Q.paragraphOffset||0,blockId:Q.blockId,paragraphId:Q.paragraphId||`p-${Q.paragraphOffset||0}`,createdAt:Q.createdAt||Date.now(),updatedAt:Q.updatedAt||Date.now(),text:Q.text};if(S.blockId){let Z=this.commentStore.getCommentsByBlockId(m.file,S.blockId);Z&&Z.length>0&&(S.comments=Z[0].comments||[])}else{let Z=this.commentStore.getHiNotes(S);Z&&Z.length>0&&(S.comments=Z[0].comments||[])}let b,y=(f=Q.originalLength)!=null?f:Q.text.length+4,V=g.slice(Q.position,Q.position+y);if(V.startsWith("<")){let Z=/<[^>]+>/.exec(V),cA=/<\/[^>]+>/.exec(V);b=Q.position+y}else b=Q.position+Q.text.length+4;let N=this.isAtParagraphEnd(g,b),_=this.createCommentWidget(S,[S]);l.push(_.range(b))}for(let Q of w){if(Q.position===void 0)continue;let S={...Q,id:Q.id||`highlight-${Date.now()}-${Q.position}`,comments:Q.comments||[],position:Q.position,paragraphOffset:Q.paragraphOffset||0,blockId:Q.blockId,paragraphId:Q.paragraphId||`p-${Q.paragraphOffset||0}`,createdAt:Q.createdAt||Date.now(),updatedAt:Q.updatedAt||Date.now(),text:Q.text};if(S.blockId){let R=this.commentStore.getCommentsByBlockId(m.file,S.blockId);R&&R.length>0&&(S.comments=R[0].comments||[])}else{let R=this.commentStore.getHiNotes(S);R&&R.length>0&&(S.comments=R[0].comments||[])}let b=(U=Q.originalLength)!=null?U:Q.text.length+4,y=g.slice(Q.position,Q.position+b);if(y.startsWith("<")){let R=/<[^>]+>/.exec(y),N=/<\/[^>]+>/.exec(y);if(R&&N){let _=Q.position+R[0].length,Z=Q.position+b-N[0].length,cA=this.commentStore.getHiNotes(S),mA=cA.length>0?(H=cA[0].comments[0])==null?void 0:H.content:"",rA=Qe.Decoration.mark({class:"cm-highlight",attributes:{title:mA||"","data-highlight-type":"html",style:Q.backgroundColor?`background-color: ${Q.backgroundColor}`:""}});l.push(rA.range(_,Z))}}else{let N=Q.position+Q.text.length+4,_=this.commentStore.getHiNotes(S),Z=_.length>0?(I=_[0].comments[0])==null?void 0:I.content:""}}return Qe.Decoration.set(l.sort((Q,S)=>Q.from-S.from))}createCommentWidget(a,l){return Qe.Decoration.widget({widget:new Xi(this.plugin,a,l,()=>this.openCommentPanel(a)),side:2,stopEvent:h=>!0})}openCommentPanel(a){let l=this.plugin.app.workspace,h=l.getLeavesOfType("comment-view");if(h.length)l.revealLeaf(h[0]);else{let g=l.getRightLeaf(!1);g&&g.setViewState({type:"comment-view",active:!0})}}isAtParagraphEnd(a,l){if(l>=a.length)return!0;let h=a.charAt(l),g=a.substr(l,2),m=h===`
`||h==="\r"||g===`\r
`,w=m;if(m){let f=l+(g===`\r
`?2:1);if(f>=a.length)w=!0;else{let U=a.charAt(f),H=a.substr(f,2);w=U===`
`||U==="\r"||H===`\r
`}}return w}},{decorations:a=>a.decorations});this.highlightPlugin=n,this.plugin.registerEditorExtension([n])}disable(){if(this.highlightPlugin){let s=this.getActiveMarkdownView();s!=null&&s.editor&&s.editor.refresh()}document.querySelectorAll(".hi-note-widget").forEach(s=>s.remove())}updateTooltipContent(s,r){let n=s.querySelector(".hi-note-tooltip-list");n&&(n.empty(),r.slice(0,3).forEach(a=>{let l=n.createEl("div",{cls:"hi-note-tooltip-item"});l.createEl("div",{cls:"hi-note-tooltip-content",text:a.content}),l.createEl("div",{cls:"hi-note-tooltip-time",text:new Date(a.createdAt).toLocaleString()})}))}};var Ha=require("obsidian");var Ke=require("obsidian");var Wi=class{constructor(s,r){this.plugin=s,this.containerEl=r}display(){let s=this.containerEl.createEl("div",{cls:"general-settings-container"});new Ke.Setting(s).setName(p("Export Path")).setDesc(p("Set the path for exported highlight notes. Leave empty to use vault root. The path should be relative to your vault root.")).addText(r=>r.setPlaceholder("Example: Highlights/Export").setValue(this.plugin.settings.export.exportPath||"").onChange(async n=>{n=n.replace(/^\/+/,""),n=n.replace(/\/+$/,""),this.plugin.settings.export.exportPath=n,await this.plugin.saveSettings()})),new Ke.Setting(s).setName(p("Exclusions")).setDesc(p("Comma separated list of paths, tags, note titles or file extensions that will be excluded from highlighting. e.g. folder1, folder1/folder2, [[note1]], [[note2]], *.excalidraw.md")).addTextArea(r=>{r.setPlaceholder("folder1, folder1/folder2, [[note1]], [[note2]], *.excalidraw.md").setValue(this.plugin.settings.excludePatterns||"").onChange(async n=>{let a=n.split(",").map(l=>l.trim()).filter(l=>l.length>0);this.plugin.settings.excludePatterns=n,await this.plugin.saveSettings()}),r.inputEl.rows=4,r.inputEl.cols=40}),new Ke.Setting(s).setName(p("Custom text extraction")).setHeading(),new Ke.Setting(s).setName(p("Use Custom Pattern")).setDesc(p("Enable to use a custom regular expression for extracting text.")).addToggle(r=>r.setValue(this.plugin.settings.useCustomPattern).onChange(async n=>{this.plugin.settings.useCustomPattern=n,await this.plugin.saveSettings()})),new Ke.Setting(s).setName(p("Custom Pattern")).setDesc(p("Enter a custom regular expression for extracting text. Use capture groups () to specify the text to extract. The first non-empty capture group will be used as the extracted text.")).addTextArea(r=>{r.setPlaceholder("==\\s*(.*?)\\s*==|<mark[^>]*>(.*?)</mark>|<span[^>]*>(.*?)</span>").setValue(this.plugin.settings.highlightPattern===W.highlightPattern?"":this.plugin.settings.highlightPattern).onChange(async n=>{this.plugin.settings.highlightPattern=n||W.highlightPattern,await this.plugin.saveSettings()}),r.inputEl.rows=4,r.inputEl.cols=40}),new Ke.Setting(s).setName(p("Default Color")).setDesc(p("Set the default color for decorators when no color is specified. Leave empty to use system default.")).addText(r=>r.setPlaceholder("#ffeb3b").setValue(this.plugin.settings.defaultHighlightColor===W.defaultHighlightColor?"":this.plugin.settings.defaultHighlightColor).onChange(async n=>{(n===""||/^#[0-9A-Fa-f]{6}$/.test(n))&&(this.plugin.settings.defaultHighlightColor=n||W.defaultHighlightColor,await this.plugin.saveSettings())}))}};var Ia=require("obsidian");var vA=require("obsidian");var ya=require("obsidian");var MA=class{constructor(s,r){this.plugin=s,this.containerEl=r}createModelDropdown(s,r,n){new ya.Setting(s).setName(p("Model")).setDesc(p("Select the AI model to use")).addDropdown(a=>{let l=Object.fromEntries(r.map(h=>[h.id,h.name]));return a.addOptions(l).setValue(this.plugin.settings.ai.model||n.id).onChange(async h=>{this.plugin.settings.ai.model=h,await this.plugin.saveSettings()})})}};var Dt=[{id:"gpt-4",name:"GPT-4"},{id:"gpt-4-turbo",name:"GPT-4 Turbo"},{id:"gpt-3.5-turbo",name:"GPT-3.5 Turbo"}],Yi=class extends MA{constructor(r,n){super(r,n);this.modelSelectEl=null;this.customModelContainer=null;this.modelState=this.initializeModelState()}initializeModelState(){this.plugin.settings.ai.openai||(this.plugin.settings.ai.openai={apiKey:"",model:Dt[0].id,apiAddress:"",isCustomModel:!1,lastCustomModel:""});let r=this.plugin.settings.ai.openai,n;if(r.isCustomModel)n={id:r.model,name:r.model,isCustom:!0};else{let a=Dt.find(l=>l.id===r.model);n=a||Dt[0],a||(r.model=n.id)}return{selectedModel:n,apiKey:r.apiKey||""}}async saveModelState(){this.plugin.settings.ai.openai||(this.plugin.settings.ai.openai={});let r=this.plugin.settings.ai.openai,n=this.modelState.selectedModel;r.model=n.id,r.isCustomModel=!!n.isCustom,r.apiKey=this.modelState.apiKey||"",n.isCustom&&n.id&&(r.lastCustomModel=n.id),await this.plugin.saveSettings()}display(r){let n=r.createEl("div",{cls:"ai-service-settings"});new vA.Setting(n).setName(p("OpenAI Settings")).setHeading(),new vA.Setting(n).setName(p("API Key")).setDesc(p("Enter your OpenAI API Key.")).addText(w=>w.setPlaceholder("sk-...").setValue(this.modelState.apiKey).onChange(async f=>{this.modelState.apiKey=f,await this.saveModelState()})).addButton(w=>w.setButtonText(p("Check")).onClick(async()=>{if(!this.modelState.apiKey){new vA.Notice(p("Please enter an API Key first"));return}w.setDisabled(!0),w.setButtonText(p("Checking..."));try{(await this.fetchAvailableModels(this.modelState.apiKey)).length>0?new vA.Notice(p("API Key validated successfully!")):new vA.Notice(p("No models available. Please check your API Key."))}catch(f){new vA.Notice(p("Failed to validate API Key. Please check your key and try again."))}finally{w.setDisabled(!1),w.setButtonText(p("Check"))}}));let a=new vA.Setting(n).setName(p("Model")).setDesc(p("Select a model or use a custom one")).addDropdown(w=>{Dt.forEach(U=>{w.addOption(U.id,U.name)}),w.addOption("custom",p("Custom Model"));let f=this.modelState.selectedModel.isCustom?"custom":this.modelState.selectedModel.id;return w.setValue(f),this.modelSelectEl=w.selectEl,w.onChange(async U=>{if(U==="custom")await this.showCustomModelInput();else{let H=Dt.find(I=>I.id===U);if(H){if(this.modelState.selectedModel.isCustom){let I=this.plugin.settings.ai.openai;I.lastCustomModel=this.modelState.selectedModel.id,await this.plugin.saveSettings()}this.modelState.selectedModel=H,await this.saveModelState(),await this.hideCustomModelInput()}}}),w});this.customModelContainer=a.settingEl.createDiv("custom-model-container"),this.customModelContainer.addClass("custom-model-container");let l=a.settingEl.querySelector(".setting-item-control");l&&(l.addClass("openai-dropdown-container"),l.insertBefore(this.customModelContainer,l.firstChild));let g=new vA.Setting(this.customModelContainer).addText(w=>w.setPlaceholder("model-id").setValue(this.modelState.selectedModel.isCustom?this.modelState.selectedModel.id:"").onChange(async f=>{let U=f.trim();if(U){if(!/^[a-zA-Z0-9-_.]+$/.test(U)){new vA.Notice(p("\u6A21\u578B ID \u53EA\u80FD\u5305\u542B\u5B57\u6BCD\u3001\u6570\u5B57\u3001\u4E0B\u5212\u7EBF\u3001\u70B9\u548C\u77ED\u6760")),w.setValue(this.modelState.selectedModel.id);return}this.modelState.selectedModel={id:U,name:U,isCustom:!0},await this.saveModelState()}})).settingEl;g.addClass("openai-setting-no-border");let m=g.querySelector(".setting-item-control");m&&m.addClass("openai-setting-no-margin"),this.modelState.selectedModel.isCustom&&this.showCustomModelInput(),new vA.Setting(n).setName(p("Custom API Address")).setDesc(p("If using a custom API proxy, please enter the full API address")).addText(w=>{var f;return w.setPlaceholder("https://api.openai.com/v1").setValue(((f=this.plugin.settings.ai.openai)==null?void 0:f.apiAddress)||"").onChange(async U=>{this.plugin.settings.ai.openai||(this.plugin.settings.ai.openai={}),this.plugin.settings.ai.openai.apiAddress=U,await this.plugin.saveSettings()})})}async validateModel(r,n,a){try{let l=await(0,vA.requestUrl)({url:`${a}/models/${n}`,method:"GET",headers:{Authorization:`Bearer ${r}`,"Content-Type":"application/json"}});if(l.status!==200){let g=await l.json.catch(()=>null);return new vA.Notice(p("\u81EA\u5B9A\u4E49\u6A21\u578B\u4E0D\u53EF\u7528\u3002\u8BF7\u68C0\u67E5\u6A21\u578B ID \u662F\u5426\u6B63\u786E\uFF0C\u4EE5\u53CA\u4F60\u662F\u5426\u6709\u6743\u9650\u8BBF\u95EE\u6B64\u6A21\u578B\u3002")),!1}let h=await(0,vA.requestUrl)({url:`${a}/chat/completions`,method:"POST",headers:{Authorization:`Bearer ${r}`,"Content-Type":"application/json"},body:JSON.stringify({model:n,messages:[{role:"user",content:"Hello"}],max_tokens:1})});if(h.status!==200){let g=await h.json.catch(()=>null);return!1}return!0}catch(l){return!1}}async fetchAvailableModels(r){var n;try{let a=((n=this.plugin.settings.ai.openai)==null?void 0:n.apiAddress)||"https://api.openai.com/v1";if(this.modelState.selectedModel.isCustom){let h=this.modelState.selectedModel.id;if(!await this.validateModel(r,h,a))throw new Error(`Custom model not available: ${h}`);return[this.modelState.selectedModel]}let l=await(0,vA.requestUrl)({url:`${a}/models`,method:"GET",headers:{Authorization:`Bearer ${r}`,"Content-Type":"application/json"}});return l.status===200?l.json.data.map(m=>({id:m.id,name:m.id})):[]}catch(a){throw a}}async showCustomModelInput(){if(this.customModelContainer&&this.modelSelectEl){this.customModelContainer.addClass("visible"),this.modelSelectEl.value="custom";let r=this.plugin.settings.ai.openai;if(!this.modelState.selectedModel.isCustom){let a=r.lastCustomModel||"";this.modelState.selectedModel={id:a,name:a,isCustom:!0},r.model=a,r.isCustomModel=!0,await this.plugin.saveSettings();let l=this.customModelContainer.querySelector("input");l&&(l.value=a)}}}async hideCustomModelInput(){this.customModelContainer&&this.customModelContainer.removeClass("visible")}};var VA=require("obsidian");var Kt=[{id:"claude-3-opus-20240229",name:"Claude 3 Opus"},{id:"claude-3-sonnet-20240229",name:"Claude 3 Sonnet"},{id:"claude-3-haiku-20240307",name:"Claude 3 Haiku"},{id:"claude-2",name:"Claude 2"},{id:"claude-instant-1",name:"Claude Instant"}],Zi=class extends MA{constructor(r,n){super(r,n);this.modelSelectEl=null;this.customModelContainer=null;this.modelState=this.initializeModelState()}initializeModelState(){this.plugin.settings.ai.anthropic||(this.plugin.settings.ai.anthropic={apiKey:"",model:Kt[0].id,apiAddress:"",isCustomModel:!1,lastCustomModel:""});let r=this.plugin.settings.ai.anthropic,n;if(r.isCustomModel)n={id:r.model,name:r.model,isCustom:!0};else{let a=Kt.find(l=>l.id===r.model);n=a||Kt[0],a||(r.model=n.id)}return{selectedModel:n,apiKey:r.apiKey||""}}async saveModelState(){this.plugin.settings.ai.anthropic||(this.plugin.settings.ai.anthropic={});let r=this.plugin.settings.ai.anthropic,n=this.modelState.selectedModel;r.model=n.id,r.isCustomModel=!!n.isCustom,r.apiKey=this.modelState.apiKey||"",n.isCustom&&n.id&&(r.lastCustomModel=n.id),await this.plugin.saveSettings()}display(r){let n=r.createEl("div",{cls:"ai-service-settings"});new VA.Setting(n).setName(p("Anthropic Settings")).setHeading(),new VA.Setting(n).setName(p("API Key")).setDesc(p("Enter your Anthropic API Key.")).addText(w=>w.setPlaceholder("sk-ant-...").setValue(this.modelState.apiKey).onChange(async f=>{this.modelState.apiKey=f,await this.saveModelState()})).addButton(w=>w.setButtonText(p("Check")).onClick(async()=>{var f;try{let U=this.modelState.apiKey;if(!U){new VA.Notice(p("Please enter an API Key first"));return}w.setDisabled(!0),w.setButtonText(p("Checking..."));let H=((f=this.plugin.settings.ai.anthropic)==null?void 0:f.apiAddress)||"https://api.anthropic.com",I=this.modelState.selectedModel.id;await new At(U,H,I).testConnection()?new VA.Notice(p("API Key is valid!")):new VA.Notice(p("Failed to validate API Key. Please check your key and try again."))}catch(U){console.error("Anthropic API validation error:",U),new VA.Notice(p("Failed to validate API Key. Please check your key and try again."))}finally{w.setDisabled(!1),w.setButtonText(p("Check"))}}));let a=new VA.Setting(n).setName(p("Model")).setDesc(p("Select a model or use a custom one")).addDropdown(w=>{Kt.forEach(U=>{w.addOption(U.id,U.name)}),w.addOption("custom",p("Custom Model"));let f=this.modelState.selectedModel.isCustom?"custom":this.modelState.selectedModel.id;return w.setValue(f),this.modelSelectEl=w.selectEl,w.onChange(async U=>{if(U==="custom")await this.showCustomModelInput();else{let H=Kt.find(I=>I.id===U);if(H){if(this.modelState.selectedModel.isCustom){let I=this.plugin.settings.ai.anthropic;I.lastCustomModel=this.modelState.selectedModel.id,await this.plugin.saveSettings()}this.modelState.selectedModel=H,await this.saveModelState(),await this.hideCustomModelInput()}}}),w});this.customModelContainer=a.settingEl.createDiv("custom-model-container"),this.customModelContainer.addClass("custom-model-container");let l=a.settingEl.querySelector(".setting-item-control");l&&(l.addClass("openai-dropdown-container"),l.insertBefore(this.customModelContainer,l.firstChild));let g=new VA.Setting(this.customModelContainer).addText(w=>w.setPlaceholder("model-id").setValue(this.modelState.selectedModel.isCustom?this.modelState.selectedModel.id:"").onChange(async f=>{let U=f.trim();if(U){if(!/^[a-zA-Z0-9-_.]+$/.test(U)){new VA.Notice(p("Model ID can only contain letters, numbers, underscores, dots and hyphens")),w.setValue(this.modelState.selectedModel.id);return}this.modelState.selectedModel={id:U,name:U,isCustom:!0},await this.saveModelState()}})).settingEl;g.addClass("openai-setting-no-border");let m=g.querySelector(".setting-item-control");m&&m.addClass("openai-setting-no-margin"),this.modelState.selectedModel.isCustom&&this.showCustomModelInput(),new VA.Setting(n).setName(p("Custom API Address")).setDesc(p("If using a custom API proxy, please enter the full API address")).addText(w=>{var f;return w.setPlaceholder("https://api.anthropic.com").setValue(((f=this.plugin.settings.ai.anthropic)==null?void 0:f.apiAddress)||"").onChange(async U=>{this.plugin.settings.ai.anthropic||(this.plugin.settings.ai.anthropic={}),this.plugin.settings.ai.anthropic.apiAddress=U,await this.plugin.saveSettings()})})}async showCustomModelInput(){if(this.customModelContainer&&this.modelSelectEl){this.customModelContainer.addClass("visible"),this.modelSelectEl.value="custom";let r=this.plugin.settings.ai.anthropic;if(!this.modelState.selectedModel.isCustom){let a=r.lastCustomModel||"";this.modelState.selectedModel={id:a,name:a,isCustom:!0},r.model=a,r.isCustomModel=!0,await this.plugin.saveSettings();let l=this.customModelContainer.querySelector("input");l&&(l.value=a)}}}async hideCustomModelInput(){this.customModelContainer&&this.customModelContainer.removeClass("visible")}};var KA=require("obsidian");var $i=class extends MA{constructor(r,n){super(r,n);this.modelSelectEl=null;this.customModelContainer=null;this.modelState=this.initializeModelState()}initializeModelState(){this.plugin.settings.ai.deepseek||(this.plugin.settings.ai.deepseek={apiKey:"",model:qe[0].id,apiAddress:"",isCustomModel:!1,lastCustomModel:""});let r=this.plugin.settings.ai.deepseek,n;if(r.isCustomModel)n={id:r.model,name:r.model,isCustom:!0};else{let a=qe.find(l=>l.id===r.model);n=a||qe[0],a||(r.model=n.id)}return{selectedModel:n,apiKey:r.apiKey||""}}async saveModelState(){this.plugin.settings.ai.deepseek||(this.plugin.settings.ai.deepseek={});let r=this.plugin.settings.ai.deepseek,n=this.modelState.selectedModel;r.model=n.id,r.isCustomModel=!!n.isCustom,r.apiKey=this.modelState.apiKey||"",r.apiAddress=r.apiAddress||"",n.isCustom&&n.id&&(r.lastCustomModel=n.id),await this.plugin.saveSettings()}async validateApiKey(r){var n;try{let a="https://api.deepseek.com",l=(n=this.plugin.settings.ai.deepseek)==null?void 0:n.apiAddress,h=l&&l.trim()?l:a,g=this.modelState.selectedModel.id,m=`${h}/models/${g}`,w=await fetch(m,{headers:{Authorization:`Bearer ${r}`,"Content-Type":"application/json"}});if(!w.ok){if(this.modelState.selectedModel.isCustom)return new KA.Notice(p("\u81EA\u5B9A\u4E49\u6A21\u578B\u4E0D\u53EF\u7528\uFF0C\u8BF7\u68C0\u67E5\u6A21\u578B ID \u548C API \u5730\u5740")),!1;if(g!=="deepseek-chat"){let H=`${h}/models/deepseek-chat`;if((await fetch(H,{headers:{Authorization:`Bearer ${r}`,"Content-Type":"application/json"}})).ok)return new KA.Notice(p("\u5F53\u524D\u9009\u62E9\u7684\u6A21\u578B\u4E0D\u53EF\u7528\uFF0C\u4F46 API Key \u662F\u6709\u6548\u7684")),!1}return new KA.Notice(p("Failed to validate API Key. Please check your key and try again.")),!1}let f=await w.json(),U=!!(f&&f.id);return U&&new KA.Notice(p("API Key and the current model are both available!")),U}catch(a){return new KA.Notice(p("Failed to validate API Key. Please check your key and try again.")),!1}}display(r){let n=r.createEl("div",{cls:"ai-service-settings"});new KA.Setting(n).setName(p("Deepseek Settings")).setHeading(),new KA.Setting(n).setName(p("API Key")).setDesc(p("Enter your Deepseek API Key")).addText(h=>h.setPlaceholder("dsk-...").setValue(this.modelState.apiKey).onChange(async g=>{this.modelState.apiKey=g,await this.saveModelState()})).addButton(h=>h.setButtonText(p("Check")).onClick(async()=>{if(!this.modelState.apiKey){new KA.Notice(p("Please enter an API Key first"));return}h.setDisabled(!0),h.setButtonText(p("Checking..."));try{await this.validateApiKey(this.modelState.apiKey)}finally{h.setDisabled(!1),h.setButtonText(p("Check"))}}));let a=new KA.Setting(n).setName(p("Model")).setDesc(p("Select a model or use a custom one")).addDropdown(h=>{qe.forEach(m=>{h.addOption(m.id,m.name)}),h.addOption("custom",p("Custom Model"));let g=this.modelState.selectedModel.isCustom?"custom":this.modelState.selectedModel.id;return h.setValue(g),this.modelSelectEl=h.selectEl,h.onChange(async m=>{if(m==="custom")await this.showCustomModelInput();else{let w=qe.find(f=>f.id===m);if(w){if(this.modelState.selectedModel.isCustom){let f=this.plugin.settings.ai.deepseek;f.lastCustomModel=this.modelState.selectedModel.id,await this.plugin.saveSettings()}this.modelState.selectedModel=w,await this.saveModelState(),await this.hideCustomModelInput()}}}),h});this.customModelContainer=a.settingEl.createDiv("custom-model-container"),this.customModelContainer.addClass("custom-model-container");let l=a.settingEl.querySelector(".setting-item-control");l&&(l.addClass("openai-dropdown-container"),l.insertBefore(this.customModelContainer,l.firstChild)),this.modelState.selectedModel.isCustom&&this.showCustomModelInput(),new KA.Setting(n).setName(p("Custom API Address")).setDesc(p("If using a custom API proxy, enter the full API address")).addText(h=>{var g;return h.setPlaceholder("https://api.deepseek.com/v1").setValue(((g=this.plugin.settings.ai.deepseek)==null?void 0:g.apiAddress)||"").onChange(async m=>{this.plugin.settings.ai.deepseek||(this.plugin.settings.ai.deepseek={}),this.plugin.settings.ai.deepseek.apiAddress=m,await this.plugin.saveSettings()})})}async showCustomModelInput(){if(!this.customModelContainer)return;this.customModelContainer.addClass("visible"),this.customModelContainer.empty();let r=document.createElement("input");r.type="text",r.className="custom-model-input",r.placeholder="model-id",r.value=this.modelState.selectedModel.isCustom?this.modelState.selectedModel.id:"",r.addEventListener("input",async()=>{let n=r.value.trim();if(n){if(!/^[a-zA-Z0-9-_.]+$/.test(n)){new KA.Notice(p("\u6A21\u578B ID \u53EA\u80FD\u5305\u542B\u5B57\u6BCD\u3001\u6570\u5B57\u3001\u4E0B\u5212\u7EBF\u3001\u70B9\u548C\u77ED\u6760")),r.value=this.modelState.selectedModel.id;return}this.modelState.selectedModel={id:n,name:n,isCustom:!0},await this.saveModelState()}}),this.customModelContainer.appendChild(r)}hideCustomModelInput(){this.customModelContainer&&(this.customModelContainer.removeClass("visible"),this.customModelContainer.empty())}};var EA=require("obsidian");var qi=class extends MA{constructor(r,n){super(r,n);this.modelSelectEl=null;this.customModelContainer=null;this.modelState=this.initializeModelState()}initializeModelState(){this.plugin.settings.ai.gemini||(this.plugin.settings.ai.gemini={apiKey:"",model:we[0].id,baseUrl:"",isCustomModel:!1,lastCustomModel:""});let r=this.plugin.settings.ai.gemini,n;if(r.isCustomModel)n={id:r.model,name:r.model,isCustom:!0};else{let a=we.find(l=>l.id===r.model);n=a||we[0],a||(r.model=n.id)}return{selectedModel:n,apiKey:r.apiKey||""}}async saveModelState(){this.plugin.settings.ai.gemini||(this.plugin.settings.ai.gemini={});let r=this.plugin.settings.ai.gemini,n=this.modelState.selectedModel;r.model=n.id,r.isCustomModel=!!n.isCustom,r.apiKey=this.modelState.apiKey||"",r.baseUrl=r.baseUrl||"",n.isCustom&&n.id&&(r.lastCustomModel=n.id),await this.plugin.saveSettings()}async validateApiKey(r){var n;try{let a="https://generativelanguage.googleapis.com",l=(n=this.plugin.settings.ai.gemini)==null?void 0:n.baseUrl,h=l&&l.trim()?l:a,g=this.modelState.selectedModel.id,m=`${h}/v1/models/${g}?key=${r}`,w=await fetch(m);if(!w.ok){let H=await w.json().catch(()=>null);if(g.includes("-exp-")){let Q=`${h}/v1/models/gemini-pro?key=${r}`;throw(await fetch(Q)).ok?(new EA.Notice(p("API Key \u6709\u6548\uFF0C\u4F46\u65E0\u6CD5\u8BBF\u95EE\u5B9E\u9A8C\u6027\u6A21\u578B\u3002\u8BF7\u786E\u4FDD\u4F60\u6709\u6743\u9650\u8BBF\u95EE\u6B64\u6A21\u578B\uFF0C\u6216\u7B49\u5F85\u6A21\u578B\u6B63\u5F0F\u53D1\u5E03\u3002")),new Error(`Experimental model not accessible: ${g}`)):(new EA.Notice(p("API Key \u65E0\u6548\u3002\u8BF7\u68C0\u67E5\u4F60\u7684 API Key \u662F\u5426\u6B63\u786E\u3002")),new Error("Invalid API Key"))}if(this.modelState.selectedModel.isCustom)throw new EA.Notice(p("\u81EA\u5B9A\u4E49\u6A21\u578B\u4E0D\u53EF\u7528\u3002\u8BF7\u68C0\u67E5\u6A21\u578B ID \u662F\u5426\u6B63\u786E\uFF0C\u4EE5\u53CA\u4F60\u662F\u5426\u6709\u6743\u9650\u8BBF\u95EE\u6B64\u6A21\u578B\u3002")),new Error(`Custom model not available: ${g}`);if(g!=="gemini-pro"){let Q=`${h}/v1/models/gemini-pro?key=${r}`;if((await fetch(Q)).ok)throw new EA.Notice(p("API Key \u6709\u6548\uFF0C\u4F46\u5F53\u524D\u9009\u62E9\u7684\u6A21\u578B\u4E0D\u53EF\u7528\u3002\u53EF\u80FD\u662F\u6A21\u578B\u672A\u53D1\u5E03\u6216\u4F60\u6CA1\u6709\u8BBF\u95EE\u6743\u9650\u3002")),new Error(`Selected model not available: ${g}`)}throw new EA.Notice(p("API Key is invalid or there is a server error. Please check if your API Key is correct.")),new Error(`HTTP error! status: ${w.status}`)}let f=await w.json(),U=!!(f&&f.name);return U&&new EA.Notice(p("API Key and the current model are both available\uFF01")),U}catch(a){return!1}}display(r){let n=r.createEl("div",{cls:"ai-service-settings"});new EA.Setting(n).setName(p("Gemini Settings")).setHeading(),new EA.Setting(n).setName(p("API Key")).setDesc(p("Enter your Gemini API Key")).addText(w=>w.setPlaceholder("Enter your API key").setValue(this.modelState.apiKey).onChange(async f=>{this.modelState.apiKey=f,await this.saveModelState()})).addButton(w=>w.setButtonText(p("Check")).onClick(async()=>{if(!this.modelState.apiKey){new EA.Notice(p("Please enter an API Key first"));return}w.setDisabled(!0),w.setButtonText(p("Checking..."));try{await this.validateApiKey(this.modelState.apiKey)}finally{w.setDisabled(!1),w.setButtonText(p("Check"))}}));let a=new EA.Setting(n).setName(p("Model")).setDesc(p("Select a model or use a custom one")).addDropdown(w=>{we.forEach(U=>{w.addOption(U.id,U.name)}),w.addOption("custom",p("Custom Model"));let f=this.modelState.selectedModel.isCustom?"custom":this.modelState.selectedModel.id;return w.setValue(f),this.modelSelectEl=w.selectEl,w.onChange(async U=>{if(U==="custom")await this.showCustomModelInput();else{let H=we.find(I=>I.id===U);if(H){if(this.modelState.selectedModel.isCustom){let I=this.plugin.settings.ai.gemini;I.lastCustomModel=this.modelState.selectedModel.id,await this.plugin.saveSettings()}this.modelState.selectedModel=H,await this.saveModelState(),await this.hideCustomModelInput()}}}),w});this.customModelContainer=a.settingEl.createDiv("custom-model-container"),this.customModelContainer.addClass("custom-model-container");let l=a.settingEl.querySelector(".setting-item-control");l&&(l.addClass("openai-dropdown-container"),l.insertBefore(this.customModelContainer,l.firstChild));let g=new EA.Setting(this.customModelContainer).addText(w=>w.setPlaceholder("model-id").setValue(this.modelState.selectedModel.isCustom?this.modelState.selectedModel.id:"").onChange(async f=>{let U=f.trim();if(U){if(!/^[a-zA-Z0-9-_.]+$/.test(U)){new EA.Notice(p("\u6A21\u578B ID \u53EA\u80FD\u5305\u542B\u5B57\u6BCD\u3001\u6570\u5B57\u3001\u4E0B\u5212\u7EBF\u3001\u70B9\u548C\u77ED\u6760")),w.setValue(this.modelState.selectedModel.id);return}this.modelState.selectedModel={id:U,name:U,isCustom:!0},await this.saveModelState()}})).settingEl;g.addClass("openai-setting-no-border");let m=g.querySelector(".setting-item-control");m&&m.addClass("openai-setting-no-margin"),this.modelState.selectedModel.isCustom&&this.showCustomModelInput(),new EA.Setting(n).setName(p("Custom API Address")).setDesc(p("Enter your custom API endpoint")).addText(w=>{var H;let f="https://generativelanguage.googleapis.com",U=(H=this.plugin.settings.ai.gemini)==null?void 0:H.baseUrl;return w.setPlaceholder(f).setValue(U||"").onChange(async I=>{this.plugin.settings.ai.gemini||(this.plugin.settings.ai.gemini={}),this.plugin.settings.ai.gemini.baseUrl=I||"",await this.plugin.saveSettings()}),w})}async showCustomModelInput(){if(this.customModelContainer&&this.modelSelectEl){this.customModelContainer.addClass("visible"),this.modelSelectEl.value="custom";let r=this.plugin.settings.ai.gemini;if(!this.modelState.selectedModel.isCustom){let a=r.lastCustomModel||"";this.modelState.selectedModel={id:a,name:a,isCustom:!0},r.model=a,r.isCustomModel=!0,await this.plugin.saveSettings();let l=this.customModelContainer.querySelector("input");l&&(l.value=a)}}}async hideCustomModelInput(){if(this.customModelContainer){this.customModelContainer.removeClass("visible");let r=this.plugin.settings.ai.gemini,n=this.modelState.selectedModel;if(n.isCustom&&n.id){r.lastCustomModel=n.id;let a=this.customModelContainer.querySelector("input");a&&(a.value=n.id)}r.isCustomModel=!1,await this.plugin.saveSettings()}}};var Fe=require("obsidian");var ji=class extends MA{async display(s){var h,g,m;let r=s.createEl("div",{cls:"ai-service-settings"});new Fe.Setting(r).setName(p("Ollama Settings")).setHeading();let n="http://localhost:11434";(h=this.plugin.settings.ai.ollama)!=null&&h.host||(this.plugin.settings.ai.ollama||(this.plugin.settings.ai.ollama={}),this.plugin.settings.ai.ollama.host=n,await this.plugin.saveSettings());let a=new Fe.Setting(r).setName(p("Server Address")).setDesc(p("Ollama server address (default: http://localhost:11434)")).addText(w=>{var f;return w.setPlaceholder(n).setValue(((f=this.plugin.settings.ai.ollama)==null?void 0:f.host)||n).onChange(async U=>{this.plugin.settings.ai.ollama||(this.plugin.settings.ai.ollama={}),this.plugin.settings.ai.ollama.host=U||n,await this.plugin.saveSettings()}),w}),l;a.addButton(w=>(l=w.buttonEl,w.setButtonText(p("Check")).onClick(async()=>{var I;let f=((I=this.plugin.settings.ai.ollama)==null?void 0:I.host)||n;l.disabled=!0;let U=l.textContent;l.textContent=p("Checking...");let H=new ze(f);try{let Q=await H.listModels();l.disabled=!1,l.textContent=U,Q&&Q.length>0?(this.plugin.settings.ai.ollama.availableModels=Q,await this.plugin.saveSettings(),this.displayOllamaModelDropdown(r,Q),new Fe.Notice(p("Successfully connected to Ollama service"))):new Fe.Notice(p("No models found. Please download models using ollama"))}catch(Q){l.disabled=!1,l.textContent=U,new Fe.Notice(p("Failed to connect to Ollama service. Please check the server address."))}}))),(m=(g=this.plugin.settings.ai.ollama)==null?void 0:g.availableModels)!=null&&m.length&&this.displayOllamaModelDropdown(r,this.plugin.settings.ai.ollama.availableModels)}displayOllamaModelDropdown(s,r){let n=s.querySelector(".model-setting");n&&n.remove(),new Fe.Setting(s).setName(p("Model")).setDesc(p("Select the Ollama model to use")).addDropdown(l=>{var w;let h=Object.fromEntries(r.map(f=>[f,f])),g=(w=this.plugin.settings.ai.ollama)==null?void 0:w.model,m=r.includes(g)?g:r[0];return l.addOptions(h).setValue(m).onChange(async f=>{this.plugin.settings.ai.ollama||(this.plugin.settings.ai.ollama={}),this.plugin.settings.ai.ollama.model=f,await this.plugin.saveSettings()})}).settingEl.addClass("model-setting")}};var zA=require("obsidian");var zi=class extends MA{constructor(r,n){super(r,n);this.modelSelectEl=null;this.customModelContainer=null;this.modelState=this.initializeModelState()}initializeModelState(){this.plugin.settings.ai.siliconflow||(this.plugin.settings.ai.siliconflow={apiKey:"",model:jA[0].id,baseUrl:"",isCustomModel:!1,lastCustomModel:""});let r=this.plugin.settings.ai.siliconflow,n;if(r.isCustomModel)n={id:r.model,name:r.model,isCustom:!0};else{let a=jA.find(l=>l.id===r.model);n=a||jA[0],a||(r.model=n.id)}return{selectedModel:n,apiKey:r.apiKey||""}}async saveModelState(){if(!this.plugin.settings.ai.siliconflow)this.plugin.settings.ai.siliconflow={apiKey:this.modelState.apiKey||"",model:this.modelState.selectedModel.id,baseUrl:"",isCustomModel:!!this.modelState.selectedModel.isCustom,lastCustomModel:this.modelState.selectedModel.isCustom?this.modelState.selectedModel.id:void 0};else{let r=this.plugin.settings.ai.siliconflow,n=this.modelState.selectedModel;r.model=n.id,r.isCustomModel=!!n.isCustom,r.apiKey=this.modelState.apiKey||"",n.isCustom&&n.id&&(r.lastCustomModel=n.id)}await this.plugin.saveSettings()}async validateApiKey(r){var n;try{let a="https://api.siliconflow.cn/v1",l=(n=this.plugin.settings.ai.siliconflow)==null?void 0:n.baseUrl,h=l&&l.trim()?l:a,g=this.modelState.selectedModel.id,m=`${h}/chat/completions`,w=await fetch(m,{method:"POST",headers:{Authorization:`Bearer ${r}`,"Content-Type":"application/json"},body:JSON.stringify({model:g,messages:[{role:"user",content:"Hello"}],max_tokens:1})});if(!w.ok){let f=await w.json().catch(()=>null);return{isValid:!1,message:p("API Key \u65E0\u6548\u6216\u6A21\u578B\u4E0D\u53EF\u7528\u3002\u8BF7\u68C0\u67E5\u4F60\u7684 API Key \u548C\u6A21\u578B ID \u662F\u5426\u6B63\u786E\u3002")}}return{isValid:!0,message:p("API Key and the current model are both available!")}}catch(a){return{isValid:!1,message:p("API Key \u65E0\u6548\u6216\u670D\u52A1\u5668\u9519\u8BEF\u3002")}}}async showCustomModelInput(){this.customModelContainer&&this.modelSelectEl&&this.customModelContainer.addClass("visible")}async hideCustomModelInput(){this.customModelContainer&&this.modelSelectEl&&this.customModelContainer.removeClass("visible")}display(r){let n=r.createEl("div",{cls:"ai-service-settings"});new zA.Setting(n).setName(p("SiliconFlow service")).setHeading();let a=new zA.Setting(n).setName(p("API key")).setDesc(p("Enter your SiliconFlow API key.")).addText(f=>f.setPlaceholder("sk-...").setValue(this.modelState.apiKey).onChange(async U=>{this.modelState.apiKey=U,await this.saveModelState()})).addButton(f=>f.setButtonText(p("Check")).onClick(async()=>{let U=this.modelState.apiKey;if(!U){new zA.Notice(p("Please input API Key"));return}f.setButtonText(p("Checking...")),f.setDisabled(!0);let H=await this.validateApiKey(U);f.setButtonText(p("Check")),f.setDisabled(!1),H.message&&new zA.Notice(H.message)})),l=new zA.Setting(n).setName(p("Model")).setDesc(p("Select the SiliconFlow model to use")).addDropdown(f=>{jA.forEach(H=>{f.addOption(H.id,H.name)}),f.addOption("custom","Custom Model");let U=this.modelState.selectedModel;return f.setValue(U.isCustom?"custom":U.id),this.modelSelectEl=f.selectEl,f.onChange(async H=>{var I;if(H==="custom"){let Q=(I=this.plugin.settings.ai.siliconflow)==null?void 0:I.lastCustomModel;this.modelState.selectedModel={id:Q||"",name:Q||"",isCustom:!0},await this.saveModelState(),await this.showCustomModelInput()}else{let Q=jA.find(S=>S.id===H);Q&&(this.modelState.selectedModel.isCustom&&this.plugin.settings.ai.siliconflow&&(this.plugin.settings.ai.siliconflow.lastCustomModel=this.modelState.selectedModel.id,await this.plugin.saveSettings()),this.modelState.selectedModel=Q,await this.saveModelState(),await this.hideCustomModelInput())}}),f});this.customModelContainer=l.settingEl.createDiv("custom-model-container"),this.customModelContainer.addClass("custom-model-container");let h=l.settingEl.querySelector(".setting-item-control");h&&(h.addClass("openai-dropdown-container"),h.insertBefore(this.customModelContainer,h.firstChild));let m=new zA.Setting(this.customModelContainer).addText(f=>f.setPlaceholder("model-id").setValue(this.modelState.selectedModel.isCustom?this.modelState.selectedModel.id:"").onChange(async U=>{let H=U.trim();if(H){if(!/^[a-zA-Z0-9-_./]+$/.test(H)){new zA.Notice("\u6A21\u578B ID \u53EA\u80FD\u5305\u542B\u5B57\u6BCD\u3001\u6570\u5B57\u3001\u4E0B\u5212\u7EBF\u3001\u70B9\u3001\u77ED\u6760\u548C\u659C\u6760"),f.setValue(this.modelState.selectedModel.id);return}this.modelState.selectedModel={id:H,name:H,isCustom:!0},await this.saveModelState()}})).settingEl;m.addClass("openai-setting-no-border");let w=m.querySelector(".setting-item-control");w&&w.addClass("openai-setting-no-margin"),this.modelState.selectedModel.isCustom&&this.showCustomModelInput(),new zA.Setting(n).setName(p("Custom API address")).setDesc(p("If using a custom API proxy, please enter the full API address")).addText(f=>{var I;let U="https://api.siliconflow.cn/v1",H=(I=this.plugin.settings.ai.siliconflow)==null?void 0:I.baseUrl;return f.setPlaceholder(U).setValue(H||"").onChange(async Q=>{this.plugin.settings.ai.siliconflow?this.plugin.settings.ai.siliconflow.baseUrl=Q||"":this.plugin.settings.ai.siliconflow={apiKey:this.modelState.apiKey||"",model:this.modelState.selectedModel.id,baseUrl:Q||"",isCustomModel:!!this.modelState.selectedModel.isCustom,lastCustomModel:this.modelState.selectedModel.isCustom?this.modelState.selectedModel.id:void 0},await this.plugin.saveSettings()}),f})}};var st=require("obsidian"),Ar=require("obsidian");var er=class{constructor(s,r){this.plugin=s,this.containerEl=r}display(){let s=this.containerEl.createEl("div",{cls:"prompt-settings-container"}),r=s.createEl("div",{cls:"prompt-settings-header setting-item-heading"});r.createEl("h4",{text:p("Prompt settings"),cls:"prompt-settings-title"});let n=r.createEl("button",{cls:"prompt-add-btn",attr:{"aria-label":p("Add prompt")}});(0,Ar.setIcon)(n,"plus"),n.onclick=()=>{if(s.querySelector(".new-prompt-section"))return;let a=s.querySelector(".prompt-list");a&&this.createNewPromptForm(s,a)},this.displayPromptList(s)}createNewPromptForm(s,r){var w;let n=s.createEl("div",{cls:"new-prompt-section"});(w=r.parentElement)==null||w.insertBefore(n,r);let a=n.createEl("input",{cls:"prompt-name-input",attr:{placeholder:p("Input Prompt Name"),type:"text"}}),l=new st.TextAreaComponent(n);l.setPlaceholder(p(`Input Prompt Content
Available parameters:
{{highlight}} - Current highlighted text
{{comment}} - Existing comment`)).setValue(""),l.inputEl.addClass("prompt-textarea");let h=n.createEl("div",{cls:"prompt-buttons"}),g=h.createEl("button",{cls:"prompt-save-btn",text:p("Save")});g.onclick=async()=>{var H;let f=a.value,U=l.getValue();if(f&&U){this.plugin.settings.ai.prompts||(this.plugin.settings.ai.prompts={}),this.plugin.settings.ai.prompts[f]=U,await this.plugin.saveSettings(),n.remove(),this.displayPromptList(s),new st.Notice(p("Prompt added"));let I=(H=this.plugin.app.workspace.getLeavesOfType("comment-view")[0])==null?void 0:H.view;I&&I.updateAIDropdowns()}};let m=h.createEl("button",{cls:"prompt-cancel-btn",text:p("Cancel")});m.onclick=()=>{n.remove()}}displayPromptList(s){let r=s.querySelector(".prompt-list");r&&r.remove();let n=s.createEl("div",{cls:"prompt-list"}),a=this.plugin.settings.ai.prompts||{};for(let[l,h]of Object.entries(a)){let g=n.createEl("div",{cls:"prompt-item"}),m=g.createEl("div",{cls:"prompt-display-mode"}),w=m.createEl("div",{cls:"prompt-info"});w.createEl("div",{cls:"prompt-name",text:l});let f=h.replace(/\n/g," ");w.createEl("div",{cls:"prompt-content-preview",text:f});let H=m.createEl("div",{cls:"prompt-buttons"}).createEl("button",{cls:"prompt-edit-btn",attr:{"aria-label":p("Edit")}});(0,Ar.setIcon)(H,"square-pen");let I=g.createEl("div",{cls:"prompt-edit-mode hi-note-hidden"}),Q=I.createEl("input",{cls:"prompt-name-input",attr:{value:l,type:"text"}}),S=new st.TextAreaComponent(I);S.setValue(h),S.inputEl.classList.add("prompt-content-input"),S.inputEl.addClass("prompt-textarea");let b=I.createEl("div",{cls:"prompt-edit-buttons"}),y=b.createEl("button",{cls:"prompt-save-btn",text:p("Save")}),V=b.createEl("button",{cls:"prompt-cancel-btn",text:p("Cancel")}),R=b.createEl("button",{cls:"prompt-delete-btn",attr:{"aria-label":p("Delete")}});(0,Ar.setIcon)(R,"trash-2"),H.onclick=()=>{m.addClass("hi-note-hidden"),I.removeClass("hi-note-hidden")},R.onclick=async()=>{var _;delete this.plugin.settings.ai.prompts[l],await this.plugin.saveSettings(),g.remove();let N=(_=this.plugin.app.workspace.getLeavesOfType("comment-view")[0])==null?void 0:_.view;N&&N.updateAIDropdowns()},y.onclick=async()=>{var Z;let N=Q.value,_=S.getValue();if(N&&_){N!==l&&delete this.plugin.settings.ai.prompts[l],this.plugin.settings.ai.prompts[N]=_,await this.plugin.saveSettings(),this.displayPromptList(s),new st.Notice(p("Prompt updated"));let cA=(Z=this.plugin.app.workspace.getLeavesOfType("comment-view")[0])==null?void 0:Z.view;cA&&cA.updateAIDropdowns()}},V.onclick=()=>{m.removeClass("hi-note-hidden"),I.addClass("hi-note-hidden")}}}};var tr=class{constructor(s,r){this.plugin=s,this.containerEl=r}display(){switch(new Ia.Setting(this.containerEl).setName("AI service").setDesc(p("Select the AI service provider")).addDropdown(s=>{let r={openai:"OpenAI",gemini:"Gemini",anthropic:"Anthropic",deepseek:"Deepseek",siliconflow:"SiliconFlow",ollama:"Ollama (Local)"};return s.addOptions(r).setValue(this.plugin.settings.ai.provider).onChange(async n=>{this.plugin.settings.ai.provider=n,await this.plugin.saveSettings(),this.containerEl.empty(),this.display()})}),this.plugin.settings.ai.provider){case"openai":new Yi(this.plugin,this.containerEl).display(this.containerEl);break;case"gemini":new qi(this.plugin,this.containerEl).display(this.containerEl);break;case"anthropic":new Zi(this.plugin,this.containerEl).display(this.containerEl);break;case"ollama":new ji(this.plugin,this.containerEl).display(this.containerEl);break;case"deepseek":new $i(this.plugin,this.containerEl).display(this.containerEl);break;case"siliconflow":new zi(this.plugin,this.containerEl).display(this.containerEl);break}new er(this.plugin,this.containerEl).display()}};var PA=require("obsidian");var ir=class{constructor(s,r){this.plugin=s,this.containerEl=r,this.fsrsService=s.fsrsManager.fsrsService}display(){let s=this.containerEl.createEl("div",{cls:"flashcard-settings-container"});new PA.Setting(s).setName(p("Flashcard learning")).setHeading(),new PA.Setting(s).setName(p("New cards per day")).setDesc(p("Maximum number of new cards to learn each day")).addSlider(r=>{var l;let n=this.fsrsService.getParameters();r.setLimits(1,200,1).setValue(n.newCardsPerDay).setDynamicTooltip().onChange(async h=>{let g=this.fsrsService.getParameters();g.newCardsPerDay=h,this.fsrsService.setParameters(g),await this.plugin.saveSettings()});let a=createEl("span",{cls:"slider-value",text:String(n.newCardsPerDay)});(l=r.sliderEl.parentElement)==null||l.appendChild(a),r.sliderEl.addEventListener("input",()=>{a.textContent=String(r.getValue())})}),new PA.Setting(s).setName(p("Reviews per day")).setDesc(p("Maximum number of cards to review each day")).addSlider(r=>{var l;let n=this.fsrsService.getParameters();r.setLimits(10,500,10).setValue(n.reviewsPerDay).setDynamicTooltip().onChange(async h=>{let g=this.fsrsService.getParameters();g.reviewsPerDay=h,this.fsrsService.setParameters(g),await this.plugin.saveSettings()});let a=createEl("span",{cls:"slider-value",text:String(n.reviewsPerDay)});(l=r.sliderEl.parentElement)==null||l.appendChild(a),r.sliderEl.addEventListener("input",()=>{a.textContent=String(r.getValue())})}),new PA.Setting(s).setName(p("Target retention")).setDesc(p("Target memory retention rate (0.8 = 80%)")).addSlider(r=>{var l;let n=this.fsrsService.getParameters();r.setLimits(.7,.95,.01).setValue(n.request_retention).setDynamicTooltip().onChange(async h=>{let g=this.fsrsService.getParameters();g.request_retention=h,this.fsrsService.setParameters(g),await this.plugin.saveSettings()});let a=createEl("span",{cls:"slider-value",text:`${Math.round(n.request_retention*100)}%`});(l=r.sliderEl.parentElement)==null||l.appendChild(a),r.sliderEl.addEventListener("input",()=>{a.textContent=`${Math.round(r.getValue()*100)}%`})}),new PA.Setting(s).setName(p("Maximum interval")).setDesc(p("Maximum interval in days between reviews")).addSlider(r=>{var l;let n=this.fsrsService.getParameters();r.setLimits(365,3650,365).setValue(n.maximum_interval).setDynamicTooltip().onChange(async h=>{let g=this.fsrsService.getParameters();g.maximum_interval=h,this.fsrsService.setParameters(g),await this.plugin.saveSettings()});let a=createEl("span",{cls:"slider-value",text:`${n.maximum_interval} ${p("days")}`});(l=r.sliderEl.parentElement)==null||l.appendChild(a),r.sliderEl.addEventListener("input",()=>{a.textContent=`${r.getValue()} ${p("days")}`})}),new PA.Setting(s).setName(p("Reset daily stats")).setDesc(p("Reset today's learning statistics")).addButton(r=>r.setButtonText(p("Reset")).onClick(async()=>{let n=new Date;n.setHours(0,0,0,0);let a=this.plugin.fsrsManager.storage.dailyStats,l=a.findIndex(h=>h.date===n.getTime());l>=0?(a.splice(l,1),await this.plugin.fsrsManager.saveStorage(),new PA.Notice(p("Daily statistics have been reset"))):new PA.Notice(p("No statistics to reset for today"))})),new PA.Setting(s).setName(p("Advanced")).setHeading(),s.createEl("p",{text:p("These settings control the FSRS algorithm parameters. Only change them if you understand the algorithm."),cls:"setting-item-description"}),new PA.Setting(s).setName(p("Reset algorithm parameters")).setDesc(p("Reset the FSRS algorithm parameters to default values")).addButton(r=>r.setButtonText(p("Reset to default")).onClick(async()=>{this.fsrsService.resetParameters(),await this.plugin.saveSettings(),this.display(),new PA.Notice(p("FSRS parameters have been reset to default values"))}))}};var rr=class extends Ha.PluginSettingTab{constructor(s,r){super(s,r),this.plugin=r,this.DEFAULT_SETTINGS=r.DEFAULT_SETTINGS,this.licenseManager=new rt(this.plugin)}async display(){let{containerEl:s}=this;s.empty();let r=await this.licenseManager.isActivated();s.createEl("h1",{text:"Obsidian HiNote Plugin"});let n=s.createEl("div",{cls:"setting-tabs"}),a=s.createEl("div",{cls:"setting-tab-content"}),l=n.createEl("div",{text:"General",cls:"setting-tab-btn active",attr:{role:"button",tabindex:"0"}}),h=n.createEl("div",{text:"AI service",cls:"setting-tab-btn",attr:{role:"button",tabindex:"0"}}),g=null;r&&(g=n.createEl("div",{text:"HiCard",cls:"setting-tab-btn",attr:{role:"button",tabindex:"0"}}));let m=a.createEl("div",{cls:"setting-tab-pane active"}),w=a.createEl("div",{cls:"setting-tab-pane"}),f=null;r&&(f=a.createEl("div",{cls:"setting-tab-pane"}));let U=(H,I)=>{n.findAll(".setting-tab-btn").forEach(Q=>Q.removeClass("active")),a.findAll(".setting-tab-pane").forEach(Q=>Q.removeClass("active")),H.addClass("active"),I.addClass("active")};l.onclick=()=>U(l,m),h.onclick=()=>U(h,w),g&&f&&(g.onclick=()=>U(g,f)),new Wi(this.plugin,m).display(),new tr(this.plugin,w).display(),f&&new ir(this.plugin,f).display()}};var xa=ha(cs());var sr=class{constructor(s={}){this.params={...es,...s}}calculateRetrievability(s,r){return Math.pow(1+.2345679012345679*(s/r),-.5)}calculateNextInterval(s,r){let l=r/.2345679012345679*(Math.pow(s,1/-.5)-1);return Math.min(Math.max(1,l),this.params.maximum_interval)}calculateInitialDifficulty(s){let r=this.params.w;return r[3]-Math.exp(r[4]*(s-1))+1}updateDifficulty(s,r){let n=this.params.w,a=-n[5]*(r-3),l=s+a*(10-s)/9,h=this.calculateInitialDifficulty(FA.GOOD);return l=n[6]*h+(1-n[6])*l,Math.min(Math.max(1,l),10)}updateStability(s,r,n){let a=this.params.w,l=0;n===FA.AGAIN?l=a[7]:n===FA.HARD?l=a[8]:n===FA.GOOD?l=a[9]+a[10]*(1-r):n===FA.EASY&&(l=a[11]+a[12]*(1-r));let h=s*l;return Math.max(.1,h)}initializeCard(s,r,n){let a=Date.now();return{id:`card-${a}-${Math.random().toString(36).substr(2,9)}`,difficulty:5,stability:.1,retrievability:1,lastReview:0,nextReview:a,reviews:0,lapses:0,reviewHistory:[],text:s,answer:r,filePath:n,createdAt:a}}reviewCard(s,r){let n=Date.now(),a=s.lastReview===0?0:(n-s.lastReview)/(24*60*60*1e3),l=(s.reviews||0)+1,h=(s.lapses||0)+(r===FA.AGAIN?1:0);if(s.lastReview===0){let U=this.calculateInitialDifficulty(r),H=r===FA.AGAIN?.1:r===FA.HARD?.5:r===FA.GOOD?2:4;return{...s,difficulty:U,stability:H,retrievability:1,lastReview:n,nextReview:n+H*24*60*60*1e3,reviews:l,lapses:h,reviewHistory:[...s.reviewHistory,{timestamp:n,rating:r,elapsed:a}]}}let g=this.calculateRetrievability(a,s.stability),m=this.updateDifficulty(s.difficulty,r),w=this.updateStability(s.stability,g,r),f=this.calculateNextInterval(this.params.request_retention,w);return{...s,difficulty:m,stability:w,retrievability:g,lastReview:n,nextReview:n+f*24*60*60*1e3,reviews:l,lapses:h,reviewHistory:[...s.reviewHistory,{timestamp:n,rating:r,elapsed:a}]}}isDue(s){return Date.now()>=s.nextReview}getReviewableCards(s){return s.filter(r=>this.isDue(r)).sort((r,n)=>r.nextReview-n.nextReview)}getParameters(){return{...this.params}}setParameters(s){this.params={...this.params,...s}}resetParameters(){this.params={...es}}};var Sa=require("obsidian"),nr=class{constructor(s){this.plugin=s,this.fsrsService=new sr,this.storage={version:"1.0",cards:{},globalStats:{totalReviews:0,averageRetention:1,streakDays:0,lastReviewDate:0},cardGroups:[],uiState:{currentGroupName:"All Cards",currentIndex:0,isFlipped:!1,completionMessage:null,groupCompletionMessages:{},groupProgress:{}},dailyStats:[]},this.saveStorageDebounced=(0,Sa.debounce)(this.saveStorage.bind(this),1e3,!0),this.loadStorage().then(r=>{this.storage=r}).catch(r=>{})}async loadStorage(){let s={version:"1.0",cards:{},globalStats:{totalReviews:0,averageRetention:1,streakDays:0,lastReviewDate:0},cardGroups:[],uiState:{currentGroupName:"All Cards",currentIndex:0,isFlipped:!1,completionMessage:null,groupCompletionMessages:{},groupProgress:{}},dailyStats:[]};try{let r=await this.plugin.loadData();if(!(r!=null&&r.fsrs))return s;let n=Array.isArray(r.fsrs.cardGroups)?r.fsrs.cardGroups:[];return{...s,...r.fsrs,cardGroups:n}}catch(r){return s}}async saveStorage(){try{let s=await this.plugin.loadData()||{};Array.isArray(this.storage.cardGroups)||(this.storage.cardGroups=[]);let r={...s,fsrs:this.storage};await this.plugin.saveData(r);let n=await this.plugin.loadData()}catch(s){throw s}}updateGlobalStats(s,r){let n=this.storage.globalStats,a=Date.now(),l=new Date(a).setHours(0,0,0,0);if(n.totalReviews++,n.averageRetention=(n.averageRetention*(n.totalReviews-1)+r)/n.totalReviews,n.lastReviewDate===0)n.streakDays=1;else{let h=new Date(n.lastReviewDate).setHours(0,0,0,0),g=(l-h)/(24*60*60*1e3);g===1?n.streakDays++:g>1&&(n.streakDays=1)}n.lastReviewDate=a}addCard(s,r,n){let a=this.fsrsService.initializeCard(s,r,n);return this.storage.cards[a.id]=a,this.saveStorageDebounced(),this.plugin.eventManager.emitFlashcardChanged(),a}updateCardContent(s,r,n){let a=this.getCardsByFile(n);for(let l of a)(l.text===s||l.answer===r)&&(this.storage.cards[l.id]={...l,text:s,answer:r},this.saveStorageDebounced(),this.plugin.eventManager.emitFlashcardChanged())}deleteCardsByContent(s,r,n){let a=this.getCardsByFile(s),l=!1;for(let h of a)(!r&&!n||r&&h.text===r||n&&h.answer===n)&&(delete this.storage.cards[h.id],l=!0);l&&(this.saveStorageDebounced(),this.plugin.eventManager.emitFlashcardChanged())}rateCard(s,r){let n=this.storage.cards[s];if(!n)return;let a=n.reviews===0,l=this.fsrsService.reviewCard(n,r);this.storage.cards[s]=l,this.storage.globalStats.totalReviews++,this.updateTodayStats(a);let h=new Date;h.setHours(0,0,0,0);let g=h.getTime();this.storage.globalStats.lastReviewDate===0?(this.storage.globalStats.lastReviewDate=g,this.storage.globalStats.streakDays=1):this.storage.globalStats.lastReviewDate===g||(this.storage.globalStats.lastReviewDate===g-864e5?(this.storage.globalStats.lastReviewDate=g,this.storage.globalStats.streakDays++):this.storage.globalStats.lastReviewDate<g&&(this.storage.globalStats.lastReviewDate=g,this.storage.globalStats.streakDays=1)),this.saveStorageDebounced()}reviewCard(s,r){let n=this.storage.cards[s];if(!n)return null;let a=this.fsrsService.reviewCard(n,r);return this.storage.cards[s]=a,this.updateGlobalStats(r,a.retrievability),this.saveStorageDebounced(),this.plugin.eventManager.emitFlashcardChanged(),a}getDueCards(){let s=this.fsrsService.getReviewableCards(Object.values(this.storage.cards)),r=this.getRemainingReviewsToday();return r<=0?[]:s.slice(0,r)}getNewCards(){let s=Object.values(this.storage.cards).filter(n=>n.reviews===0),r=this.getRemainingNewCardsToday();return r<=0?[]:s.slice(0,r)}getLatestCards(){let s=Object.values(this.storage.cards).reduce((r,n)=>((!r[n.text]||r[n.text].id<n.id)&&(r[n.text]=n),r),{});return Object.values(s)}getProgress(){let s=this.getLatestCards(),r=Date.now();return{due:s.filter(n=>n.nextReview<=r).length,newCards:s.filter(n=>n.lastReview===0).length,learned:s.filter(n=>n.lastReview>0).length,retention:this.storage.globalStats.averageRetention}}getStats(){return{...this.storage.globalStats}}getUIState(){return{...this.storage.uiState}}updateUIState(s){this.storage.uiState={...this.storage.uiState,...s},this.saveStorageDebounced()}deleteCard(s){return this.storage.cards[s]?(delete this.storage.cards[s],this.saveStorageDebounced(),this.plugin.eventManager.emitFlashcardChanged(),!0):!1}getCardsByFile(s){return Object.values(this.storage.cards).filter(n=>n.filePath===s)}exportData(){return JSON.parse(JSON.stringify(this.storage))}importData(s){try{if(typeof s.version!="string"||!s.cards||!s.globalStats)throw new Error("Invalid FSRS data structure");return this.storage=s,this.saveStorage(),!0}catch(r){return!1}}async reset(){try{this.storage=await this.loadStorage(),await this.saveStorage()}catch(s){throw s}}getCardGroups(){return Array.isArray(this.storage.cardGroups)||(this.storage.cardGroups=[],this.saveStorageDebounced()),this.storage.cardGroups}generateUUID(){let s=Date.now().toString(36),r=Math.random().toString(36).substring(2,15);return`${s}-${r}`}async createCardGroup(s){this.storage.cardGroups||(this.storage.cardGroups=[]);let r=this.fsrsService.getParameters(),n={...s,id:this.generateUUID(),settings:s.settings||{useGlobalSettings:!0,newCardsPerDay:r.newCardsPerDay,reviewsPerDay:r.reviewsPerDay}};this.storage.cardGroups.push(n);try{return await this.saveStorage(),n}catch(a){throw this.storage.cardGroups.pop(),a}}async updateCardGroup(s,r){if(!this.storage.cardGroups)return!1;let n=this.storage.cardGroups.findIndex(a=>a.id===s);if(n===-1)return!1;this.storage.cardGroups[n]={...this.storage.cardGroups[n],...r,id:s};try{return await this.saveStorage(),!0}catch(a){return!1}}async deleteCardGroup(s){if(!this.storage.cardGroups)return!1;let r=this.storage.cardGroups.findIndex(a=>a.id===s);if(r===-1)return!1;let n=this.storage.cardGroups[r];this.storage.cardGroups.splice(r,1);try{return await this.saveStorage(),!0}catch(a){return this.storage.cardGroups.splice(r,0,n),!1}}getGroupProgress(s){let r=this.storage.cardGroups.find(l=>l.id===s);if(!r)return null;let n=this.getCardsInGroup(r),a=Date.now();return{due:n.filter(l=>l.nextReview<=a).length,newCards:n.filter(l=>l.lastReview===0).length,learned:n.filter(l=>l.lastReview>0).length,retention:this.calculateGroupRetention(n)}}calculateGroupRetention(s){let r=s.filter(a=>a.lastReview>0);return r.length===0?1:r.reduce((a,l)=>a+l.retrievability,0)/r.length}getCardsInGroup(s){return this.getLatestCards().filter(a=>{let l=s.filter.split(",").map(f=>f.trim().toLowerCase()),h=a.text.toLowerCase(),g=a.answer.toLowerCase(),m=(a.filePath||"").toLowerCase();return l.some(f=>{if(f.startsWith("#")){let I=f.substring(1),Q=this.extractTagsFromText(h),S=this.extractTagsFromText(g),b=[...Q,...S],y=h.includes(f)||g.includes(f),V=b.some(N=>N.toLowerCase()===I||N.toLowerCase().includes(I));return y||V}if(f.startsWith("[[")&&f.endsWith("]]")){let I=f.slice(2,-2);return m.includes(I)}if(f.includes("*")){let I=f.replace(/\./g,"\\.").replace(/\*/g,".*");return new RegExp(I,"i").test(m)}return m.includes(f)?!0:h.includes(f)||g.includes(f)})})}extractTagsFromText(s){let r=/#([^\s#]+)/g,n=s.match(r);return n?n.map(a=>a.substring(1)):[]}getTodayTimestamp(){let s=new Date;return s.setHours(0,0,0,0),s.getTime()}getTodayStats(){let s=this.getTodayTimestamp(),r=this.storage.dailyStats.find(n=>n.date===s);return r||(r={date:s,newCardsLearned:0,cardsReviewed:0},this.storage.dailyStats.push(r),this.storage.dailyStats.length>30&&(this.storage.dailyStats.sort((n,a)=>a.date-n.date),this.storage.dailyStats=this.storage.dailyStats.slice(0,30))),r}updateTodayStats(s){let r=this.getTodayStats();s?r.newCardsLearned++:r.cardsReviewed++,this.saveStorageDebounced()}canLearnNewCardsToday(s){let r=this.getTodayStats(),n=this.fsrsService.getParameters();if(s){let a=this.storage.cardGroups.find(l=>l.id===s);if(a&&a.settings&&!a.settings.useGlobalSettings&&a.settings.newCardsPerDay!==void 0)return r.newCardsLearned<a.settings.newCardsPerDay}return r.newCardsLearned<n.newCardsPerDay}canReviewCardsToday(s){let r=this.getTodayStats(),n=this.fsrsService.getParameters();if(s){let a=this.storage.cardGroups.find(l=>l.id===s);if(a&&a.settings&&!a.settings.useGlobalSettings&&a.settings.reviewsPerDay!==void 0)return r.cardsReviewed<a.settings.reviewsPerDay}return r.cardsReviewed<n.reviewsPerDay}getRemainingNewCardsToday(s){let r=this.getTodayStats(),n=this.fsrsService.getParameters();if(s){let a=this.storage.cardGroups.find(l=>l.id===s);if(a&&a.settings&&!a.settings.useGlobalSettings&&a.settings.newCardsPerDay!==void 0)return Math.max(0,a.settings.newCardsPerDay-r.newCardsLearned)}return Math.max(0,n.newCardsPerDay-r.newCardsLearned)}getRemainingReviewsToday(s){let r=this.getTodayStats(),n=this.fsrsService.getParameters();if(s){let a=this.storage.cardGroups.find(l=>l.id===s);if(a&&a.settings&&!a.settings.useGlobalSettings&&a.settings.reviewsPerDay!==void 0)return Math.max(0,a.settings.reviewsPerDay-r.cardsReviewed)}return Math.max(0,n.reviewsPerDay-r.cardsReviewed)}};var ar=class extends ba.Plugin{constructor(){super(...arguments);this.DEFAULT_SETTINGS={...W,anthropic:{apiKey:"",model:"claude-2",apiAddress:"",isCustomModel:!1,lastCustomModel:""}}}async onload(){let r=await this.loadData();this.settings=Object.assign({},W,r),window.html2canvas=xa.default,this.commentStore=new _i(this),await this.commentStore.loadComments(),this.eventManager=new Ce(this.app),this.fsrsManager=new nr(this),this.highlightDecorator=new Ji(this,this.commentStore),this.highlightDecorator.enable(),this.registerView(ae,a=>new se(a,this.commentStore)),this.addRibbonIcon("highlighter","HiNote",async()=>{let{workspace:a}=this.app,l=a.getLeavesOfType(ae);if(l.length){a.revealLeaf(l[0]);return}let h=a.getRightLeaf(!1);h&&await h.setViewState({type:ae,active:!0})}),this.addCommand({id:"open-comment-window",name:p("Open in right sidebar"),callback:async()=>{let{workspace:a}=this.app,l=a.getLeavesOfType(ae);if(l.length){a.revealLeaf(l[0]);return}let h=a.getRightLeaf(!1);h&&await h.setViewState({type:ae,active:!0})}}),this.addSettingTab(new rr(this.app,this)),this.registerInterval(window.setInterval(async()=>{let a=new Set(this.app.vault.getFiles().map(l=>l.path));await this.commentStore.cleanupComments(a)},24*60*60*1e3));let n=()=>{let a=performance.now(),l=performance.now()};this.registerEvent(this.app.vault.on("rename",(a,l)=>{this.commentStore.handleFileRename(l,a.path)})),this.addCommand({id:"open-chat-window",name:p("Open AI chat window"),callback:()=>{ve.getInstance(this.app,this).show()}}),this.addCommand({id:"open-comment-main-window",name:p("Open in main window"),callback:async()=>{let{workspace:a}=this.app,l=a.getLeavesOfType(ae);if(l.length){a.revealLeaf(l[0]);let g=l[0].view;g&&g instanceof se&&(g.isDraggedToMainView=!0,g.updateViewLayout(),g.updateHighlights());return}let h=a.getLeaf(!1);h&&(await h.setViewState({type:ae,active:!0}),setTimeout(()=>{let g=h.view;g&&g instanceof se&&(g.isDraggedToMainView=!0,g.updateViewLayout(),g.updateHighlights())},100))}})}async onunload(){try{await this.commentStore.saveComments();let r=await this.loadData()}catch(r){}this.highlightDecorator&&this.highlightDecorator.disable(),ve.instance&&ve.instance.close()}async loadSettings(){let r=await this.loadData();this.settings||(this.settings={excludePatterns:W.excludePatterns,useCustomPattern:W.useCustomPattern,highlightPattern:W.highlightPattern,defaultHighlightColor:W.defaultHighlightColor,ai:{provider:W.ai.provider,openai:W.ai.openai?{...W.ai.openai}:void 0,anthropic:W.ai.anthropic?{...W.ai.anthropic}:void 0,gemini:W.ai.gemini?{...W.ai.gemini}:void 0,ollama:W.ai.ollama?{...W.ai.ollama}:void 0,deepseek:W.ai.deepseek?{...W.ai.deepseek}:void 0,prompts:{...W.ai.prompts}},export:{exportPath:W.export.exportPath}}),(r==null?void 0:r.excludePatterns)!==void 0&&(this.settings.excludePatterns=r.excludePatterns),r!=null&&r.ai&&(r.ai.provider&&(this.settings.ai.provider=r.ai.provider),r.ai.openai&&this.settings.ai.openai&&(this.settings.ai.openai={apiKey:r.ai.openai.apiKey||this.settings.ai.openai.apiKey,model:r.ai.openai.model||this.settings.ai.openai.model,baseUrl:r.ai.openai.baseUrl}),r.ai.anthropic&&this.settings.ai.anthropic&&(this.settings.ai.anthropic={apiKey:r.ai.anthropic.apiKey||this.settings.ai.anthropic.apiKey,model:r.ai.anthropic.model||this.settings.ai.anthropic.model,availableModels:r.ai.anthropic.availableModels,apiAddress:r.ai.anthropic.apiAddress||r.ai.anthropic.baseUrl,isCustomModel:r.ai.anthropic.isCustomModel||!1,lastCustomModel:r.ai.anthropic.lastCustomModel||""}),r.ai.gemini&&this.settings.ai.gemini&&(this.settings.ai.gemini={apiKey:r.ai.gemini.apiKey||this.settings.ai.gemini.apiKey,model:r.ai.gemini.model||this.settings.ai.gemini.model,baseUrl:r.ai.gemini.baseUrl,isCustomModel:r.ai.gemini.isCustomModel||!1}),r.ai.ollama&&this.settings.ai.ollama&&(this.settings.ai.ollama={host:r.ai.ollama.host||this.settings.ai.ollama.host,model:r.ai.ollama.model||this.settings.ai.ollama.model,availableModels:r.ai.ollama.availableModels}),r.ai.deepseek&&this.settings.ai.deepseek&&(this.settings.ai.deepseek={apiKey:r.ai.deepseek.apiKey||this.settings.ai.deepseek.apiKey,model:r.ai.deepseek.model||this.settings.ai.deepseek.model,baseUrl:r.ai.deepseek.baseUrl}),r.ai.prompts&&(this.settings.ai.prompts={...this.settings.ai.prompts,...r.ai.prompts})),r!=null&&r.export&&r.export.exportPath&&(this.settings.export.exportPath=r.export.exportPath),r!=null&&r.comments&&(this.settings.comments=r.comments),r!=null&&r.fileComments&&(this.settings.fileComments=r.fileComments),await this.saveSettings()}async saveSettings(){var n,a,l,h,g,m,w,f,U;this.settings||(this.settings={...W});let r=await this.loadData();r!=null&&r["flashcard-license"]&&(this.settings["flashcard-license"]=r["flashcard-license"]),this.settings.excludePatterns=(n=this.settings.excludePatterns)!=null?n:W.excludePatterns,this.settings.useCustomPattern=(a=this.settings.useCustomPattern)!=null?a:W.useCustomPattern,this.settings.highlightPattern=this.settings.highlightPattern||W.highlightPattern,this.settings.defaultHighlightColor=this.settings.defaultHighlightColor||W.defaultHighlightColor,this.settings.ai||(this.settings.ai={...W.ai}),this.settings.export||(this.settings.export={...W.export}),this.settings.ai.openai||(this.settings.ai.openai={apiKey:"",model:((l=W.ai.openai)==null?void 0:l.model)||"gpt-4o",baseUrl:(h=W.ai.openai)==null?void 0:h.baseUrl}),this.settings.ai.anthropic||(this.settings.ai.anthropic={apiKey:"",model:"claude-2",availableModels:(g=W.ai.anthropic)==null?void 0:g.availableModels,apiAddress:(m=W.ai.anthropic)==null?void 0:m.apiAddress,isCustomModel:!1,lastCustomModel:""}),this.settings.ai.gemini||(this.settings.ai.gemini={apiKey:"",model:"gemini-1.5-flash",isCustomModel:!1,baseUrl:(w=W.ai.gemini)==null?void 0:w.baseUrl}),this.settings.ai.ollama||(this.settings.ai.ollama={host:"http://localhost:11434",model:"",availableModels:(f=W.ai.ollama)==null?void 0:f.availableModels}),this.settings.ai.deepseek||(this.settings.ai.deepseek={apiKey:"",model:"deepseek-chat",baseUrl:(U=W.ai.deepseek)==null?void 0:U.baseUrl}),this.settings.ai.prompts||(this.settings.ai.prompts={...W.ai.prompts}),this.settings.export||(this.settings.export={exportPath:W.export.exportPath}),await this.saveData(this.settings)}};
/*! Bundled license information:

html2canvas/dist/html2canvas.js:
  (*!
   * html2canvas 1.4.1 <https://html2canvas.hertzen.com>
   * Copyright (c) 2022 Niklas von Hertzen <https://hertzen.com>
   * Released under MIT License
   *)
  (*! *****************************************************************************
      Copyright (c) Microsoft Corporation.
  
      Permission to use, copy, modify, and/or distribute this software for any
      purpose with or without fee is hereby granted.
  
      THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
      REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
      AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
      INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
      LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
      OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
      PERFORMANCE OF THIS SOFTWARE.
      ***************************************************************************** *)
*/
