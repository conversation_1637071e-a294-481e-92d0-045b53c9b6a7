---
tags:
  - 学习
  - python
  - Numpy
---

# 数组与张量

1. N<PERSON><PERSON>和Pytorch的基础语法几乎一致，具体表现为：
    - np对于torch;
    - np.array对于torch.tensor;
    - Numpy的n维数组对应Pytorch的n维张量;

2. 数组与张量之间的转换：
    - 将数组转换为张量：`ts = torch.tensor(np_array)`  # 将numpy数组转换为张量
    - 将张量转换为numpy数组：`arr = np.arr(ts)`  # 将张量转换为numpy数组

# 语法的不同点

| 课件位置 | NumPy 的函数 | PyTorch 的函数 | 用法区别 |
|----------|--------------|----------------|----------|
| 1.1 数据类型 | `.astype()` | `.type()` | 无 |
| 2.4 随机数组 | `np.random.random()` | `torch.rand()` | 无 |
| 2.4 随机数组 | `np.random.randint()` | `torch.randint()` | 不接纳向量 |
| 2.4 随机数组 | `np.random.normal()` | `torch.normal()` | 不接纳向量 |
| 2.4 随机数组 | `np.random.randn()` | `torch.randn()` | 无 |
| 3.4 数组切片 | `.copy()` | `.clone()` | 无 |
| 4.4 数组拼接 | `np.concatenate()` | `torch.cat()` | 无 |
| 4.5 数组分裂 | `np.split()` | `torch.split()` | 参数含义优化 |
| 6.1 矩阵乘积 | `np.dot()` | `torch.matmul()` | 无 |
| 6.1 矩阵乘积 | `np.dot(v,v)` | `torch.dot()` | 无 |
| 6.1 矩阵乘积 | `np.dot(m,v)` | `torch.mv()` | 无 |
| 6.1 矩阵乘积 | `np.dot(m,m)` | `torch.mm()` | 无 |
| 6.2 数学函数 | `np.exp()` | `torch.exp()` | 必须传入张量 |
| 6.2 数学函数 | `np.log()` | `torch.log()` | 必须传入张量 |
| 6.3 聚合函数 | `np.mean()` | `torch.mean()` | 必须传入浮点型数组 |
| 6.3 聚合函数 | `np.std()` | `torch.std()` | 必须传入浮点型数组 |