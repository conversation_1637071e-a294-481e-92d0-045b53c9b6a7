---
tags:
  - 学习
  - python
  - Numpy
---

# 创建指定数组
当明确知道数组每一个元素的值时，可以直接使用`np.array()`函数创建数组

```python
import numpy as np
# 创建一维数组-向量
arr1 = np.array([1, 2, 3])
print(arr1)  # 输出结果为[1 2 3]，中间是没有逗号的

# 创建二维数组-行矩阵
arr2 = np.array([[1,2,3]])
print(arr2)  # 输出结果为[[1 2 3]]

# 创建二维数组-列矩阵
arr3 = np.array([[1],[2],[3]])
print(arr3)  # 输出结果为[[1] [2] [3]]

# 创建二维数组-矩阵
arr4 = np.array([[1,2,3],[4,5,6]])
print(arr4)  # 输出结果为[[1 2 3] [4 5 6]]
    
```

## 创建递增数组

递增数组用`np.arange()`函数创建，`np.arange()`函数的参数为`start`、`stop`、`step`，分别表示起始值、结束值和步长

```python
import numpy as np
# 创建递增数组
arr1 = np.arrange(10)
print(arr1)  # 输出结果为[0 1 2 3 4 5 6 7 8 9]，默认起始值为0，结束值为10，步长为1
arr2 = np.arrange(10, 20)
print(arr2)  # 输出结果为[10 11 12 13 14 15 16 17 18 19]，起始值为10，结束值为20，步长为1
arr3 = np.arrange(10, 20, 2)
print(arr3)  # 输出结果为[10 12 14 16 18]，起始值为10，结束值为20，步长为2
```

## 创建同值数组

需要创建同值数组时，可以使用以下函数

- `np.ones()` ：创建全1数组
- `np.zeros()` ：创建全0数组

```python
import numpy as np
# 创建全1数组
arr1 = np.ones(10)
print(arr1)  # 输出结果为[1. 1. 1. 1. 1. 1. 1. 1. 1. 1.]，创建10个1的数组
arr2 = np.ones((3, 4))  # 创建3行4列的全1数组

# 创建全部元素为3的数组，可以先创建全1数组，然后乘以3
arr3 = np.ones((3, 4)) * 3  # 创建3行4列的全3数组
```

## 创建随机数组

如果需要创建随机数组，可以使用`np.random`模块中的函数

- `np.random.rand()` ：创建均匀分布的随机数数组，参数为数组的形状
- `np.random.randn()` ：创建标准正态分布的随机数数组，参数为数组的形状
- `np.random.randint()` ：创建随机整数数组，参数为起始值、结束值和数组的形状

```python
import numpy as np
# 创建均匀分布的随机数数组
arr1 = np.random.rand(3, 4)  # 创建3行4列的均匀分布的随机数数组
print(arr1)  # 输出结果为[[0.5488135  0.71518937 0.60276338 0.54488318] [0.4236548  0.64589411 0.43758721 0.891773] [0.96366276 0.38344152 0.79172504 0.52889492]]

# 创建标准正态分布的随机数数组
arr2 = np.random.randn(3, 4)  # 创建3行4列的标准正态分布的随机数数组
print(arr2)  # 输出结果为[[ 0.14404357  1.45427351  0.76103773  0.12167502] [ 0.44386323  0.33367433  1.49407907 -0.20515826] [ 0.3130677   0.14590426 -0.85409574 -2.55298982]]

# 创建随机整数数组
arr3 = np.random.randint(0, 10, (3, 4))  # 创建3行4列的随机整数数组，随机整数的范围为0到10
print(arr3)  # 输出结果为[[5 0 3 3] [3 7 5 2] [4 6 8 7]]
```




