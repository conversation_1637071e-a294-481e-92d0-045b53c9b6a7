{"excludePatterns": "", "useCustomPattern": true, "highlightPattern": "<span style=\"background:#fff88f\">(.*?)<\\/span>", "defaultHighlightColor": "#ffeb3b", "ai": {"provider": "deepseek", "ollama": {"host": "http://localhost:11434", "model": ""}, "gemini": {"apiKey": "AIzaSyA5vQU0WP6pB8p4WIc2Un5TR-5rGY4dVGs", "model": "gemini-1.5-pro", "baseUrl": "", "isCustomModel": false, "lastCustomModel": "gemini-2.5-pro-exp-03-25"}, "openai": {"apiKey": "", "model": "gpt-4o", "baseUrl": ""}, "anthropic": {"apiKey": "", "model": "claude-2", "apiAddress": "", "isCustomModel": false, "lastCustomModel": ""}, "deepseek": {"apiKey": "***********************************", "model": "deepseek-chat", "baseUrl": "", "isCustomModel": false, "apiAddress": ""}, "prompts": {"🤔 Key Insight": "{{highlight}}你是一个笔记总结的小助手，请以200字总结卡片中的内容"}}, "export": {"exportPath": ""}, "comments": {"学习库/c/6 数组.md": {"highlight-1743386063178-1769": {"text": "返回-1", "position": 1769, "paragraphOffset": 1702, "backgroundColor": "#fff88f", "id": "highlight-1743386063178-1769", "comments": [{"id": "comment-1743386084560-pig0u8m3a", "content": "c语言中的-1索引是不存在的", "createdAt": 1743386084560, "updatedAt": 1743386084560}], "createdAt": 1743386063178, "updatedAt": 1743386084560, "originalLength": 44, "blockId": "m8wf285s-rfsu1", "filePath": "学习库/c/6 数组.md"}, "highlight-1743388687177-2682": {"text": "中间元素", "position": 2693, "paragraphOffset": 2539, "backgroundColor": "#fff88f", "id": "highlight-1743388687177-2682", "comments": [{"id": "comment-1743388763222-vf5uszfvu", "content": "c语言中的中位数是向下取整，在这里就相当于索引0和索引1的中位数是索引0", "createdAt": 1743388763222, "updatedAt": 1743389041087}, {"id": "comment-1743389037268-o52kx1dx4", "content": "mid = (min + max)/2", "createdAt": 1743389037268, "updatedAt": 1743389037268}], "createdAt": 1743388687177, "updatedAt": 1743389041087, "originalLength": 44, "blockId": "m8wgtihg-s8cr7", "filePath": "学习库/c/6 数组.md"}}, "学习库/stm32/2 GPIO.md": {"highlight-1743425583145-466": {"text": "GPIO 的输出", "position": 466, "paragraphOffset": 0, "backgroundColor": "#fff88f", "id": "highlight-1743425583145-466", "comments": [{"id": "comment-1743425630517-df1fzsyrs", "content": "通用模式时通过cpu 直接控制寄存器，从而控制 P-MOS 和 N-MOS 的状态\n复用模式就是通过其他的片上外设来控制的", "createdAt": 1743425630517, "updatedAt": 1743425757589}], "createdAt": 1743425583145, "updatedAt": 1743425757589, "originalLength": 48, "blockId": "m8x2lu0l-79ufo", "filePath": "学习库/stm32/GPIO.md", "fileName": "GPIO", "fileIcon": "file-text"}, "highlight-1752371172462-4929": {"text": "时钟", "position": 4038, "paragraphOffset": 3877, "backgroundColor": "#fff88f", "id": "highlight-1752371172462-4929", "comments": [{"id": "comment-1752371175433-1xhvjo71u", "content": "时钟是所有外设工作的动力源。开启时钟，就相当于心脏给手（GPIOC）供血，手（GPIOC）才能控制手指（PC 13）的运动（点亮和熄灭）", "createdAt": 1752371175433, "updatedAt": 1752371292135}], "createdAt": 1752371172462, "updatedAt": 1752371292135, "originalLength": 42, "blockId": "md10lzpf-f098y", "filePath": "学习库/stm32/2 GPIO.md"}}, "学习库/stm32/3 串口.md": {"highlight-1743146060209-2081": {"text": "二进制", "position": 2081, "paragraphOffset": 0, "backgroundColor": "#ffeb3b", "id": "highlight-1743146060209-2081", "comments": [{"id": "comment-1743146080548-jzb1mf2hu", "content": "整数部分就是不断除2，取余数，然后逆序排列；小数部分就是不断乘2取整数，正序排列", "createdAt": 1743146080548, "updatedAt": 1743147810643}], "createdAt": 1743146060209, "updatedAt": 1743150227666, "originalLength": 7, "blockId": "m8sh76tv-p78pw", "filePath": "学习库/stm32/串口.md"}, "highlight-1743424190957-2071": {"text": "转换成二进制", "position": 2071, "paragraphOffset": 0, "backgroundColor": "#fff88f", "id": "highlight-1743424190957-2071", "comments": [{"id": "comment-1743424329312-utke5m3gb", "content": "将带有小数的十进制数转换为二进制\n对于整数部分，需要不断对二进行取余，最后将余数倒序\n对于小数部分，需要不断乘2取整数部分，最后进行顺序排序\n比如这题的0.0625，就是0.0625 * 2 = 0.0125 （取0）\n0.0125 * 2 = 0.25 （取0）\n0.25 * 2 = 0.5 （取0）\n0.5 * 2 = 1 （取1）", "createdAt": 1743424329312, "updatedAt": 1743424461087}], "createdAt": 1743424190957, "updatedAt": 1743424461087, "originalLength": 46, "blockId": "m8x1wrof-99fpd", "filePath": "学习库/stm32/串口.md"}}, "学习库/c/7 指针.md": {"highlight-1743557665763-164": {"text": "数据类型", "position": 164, "paragraphOffset": 0, "backgroundColor": "#fff88f", "id": "highlight-1743557665763-164", "comments": [{"id": "comment-1743557703051-c2ns7zulm", "content": "指针的数据类型，要和指向空间中的数据类型是保持一致的", "createdAt": 1743557703051, "updatedAt": 1743557703051}], "createdAt": 1743557665763, "updatedAt": 1743557703051, "originalLength": 44, "blockId": "m8z98lu3-lryxb", "filePath": "学习库/c/7 指针.md"}, "highlight-1743736545593-672": {"text": "类型", "position": 672, "paragraphOffset": 669, "backgroundColor": "#fff88f", "id": "highlight-1743736545593-672", "comments": [{"id": "comment-1743736576800-f3410cr2p", "content": "指针的类型要与其所指向的数据类型一致", "createdAt": 1743736576800, "updatedAt": 1743736576800}], "createdAt": 1743736545593, "updatedAt": 1743736576800, "originalLength": 42, "blockId": "m927qhpd-2pgpi", "filePath": "学习库/c/7 指针.md"}}, "学习库/stm32/6 中断.md": {"highlight-1744272821139-539": {"text": "优先级高的中断可以抢占（打断）正在执行的低优先级中断", "position": 539, "paragraphOffset": 0, "backgroundColor": "#fff88f", "id": "highlight-1744272821139-539", "comments": [{"id": "comment-1744272846007-yxt1urzma", "content": "中断嵌套，更高优先级的中断打断正在执行的中断", "createdAt": 1744272846007, "updatedAt": 1744272846007}], "createdAt": 1744272821139, "updatedAt": 1744272846007, "originalLength": 66, "blockId": "m9b30lo7-pz768", "filePath": "学习库/stm32/6 中断.md"}}, "学习库/stm32/9 定时器.md": {"highlight-1744448716098-1355": {"text": "占空比", "position": 1355, "paragraphOffset": 1296, "backgroundColor": "#fff88f", "id": "highlight-1744448716098-1355", "comments": [{"id": "comment-1744448734645-nflfgub94", "content": "高电压占整个周期的比例", "createdAt": 1744448734645, "updatedAt": 1744448734645}], "createdAt": 1744448716098, "updatedAt": 1744448734645, "originalLength": 43, "blockId": "m9dzqi7p-v2kfo", "filePath": "学习库/stm32/9 定时器.md"}}, "学习库/Anki/Artificial Intelligence/未命名.md": {"highlight-1752279946835-273": {"text": "TESTSETSET", "position": 285, "paragraphOffset": 285, "backgroundColor": "#fff88f", "id": "highlight-1752279946835-273", "comments": [{"id": "comment-1752279963112-etacv9gl6", "content": "这是一个测试", "createdAt": 1752279963112, "updatedAt": 1752280810925}], "createdAt": 1752279946835, "updatedAt": 1752280810925, "originalLength": 50, "blockId": "mczir2y5-dfyyd", "filePath": "学习库/An<PERSON>/Artificial Intelligence/未命名.md"}, "highlight-1752281213045-330": {"text": "ASDSADASDSA", "position": 330, "paragraphOffset": 185, "backgroundColor": "#fff88f", "id": "highlight-1752281213045-330", "comments": [{"id": "comment-1752281217505-nskwn1u9r", "content": "213123132", "createdAt": 1752281217505, "updatedAt": 1752281217505}], "createdAt": 1752281213045, "updatedAt": 1752281217505, "originalLength": 51, "blockId": "mczizso1-5eskd", "filePath": "学习库/An<PERSON>/Artificial Intelligence/未命名.md"}}}, "fileComments": {}, "fsrs": {"version": "1.0", "cards": {"card-1743149869769-70ca3er1x": {"id": "card-1743149869769-70ca3er1x", "difficulty": 1, "stability": 0.1, "retrievability": 0.998725870003143, "lastReview": 1752280027310, "nextReview": 1752366427310, "reviews": 5, "lapses": 1, "reviewHistory": [{"timestamp": 1743150163126, "rating": 4, "elapsed": 0}, {"timestamp": 1743424495217, "rating": 3, "elapsed": 3.1751399421296296}, {"timestamp": 1743424516870, "rating": 3, "elapsed": 0.00025061342592592594}, {"timestamp": 1752279838942, "rating": 2, "elapsed": 102.*************}, {"timestamp": 1752280027310, "rating": 1, "elapsed": 0.002180185185185185}], "text": "二进制", "answer": "整数部分就是不断除2，取余数，然后逆序排列；小数部分就是不断乘2取整数，正序排列", "filePath": "学习库/stm32/串口.md", "createdAt": 1743149869769}, "card-1743424473489-izbqnk3p2": {"id": "card-1743424473489-izbqnk3p2", "difficulty": 1, "stability": 0.1, "retrievability": 0.9994344210808739, "lastReview": 1752280028097, "nextReview": 1752366428097, "reviews": 4, "lapses": 1, "reviewHistory": [{"timestamp": 1743424499282, "rating": 3, "elapsed": 0}, {"timestamp": 1743424517909, "rating": 3, "elapsed": 0.0002155902777777778}, {"timestamp": 1752279840262, "rating": 2, "elapsed": 102.**************}, {"timestamp": 1752280028097, "rating": 1, "elapsed": 0.0021740162037037036}], "text": "转换成二进制", "answer": "将带有小数的十进制数转换为二进制\n对于整数部分，需要不断对二进行取余，最后将余数倒序\n对于小数部分，需要不断乘2取整数部分，最后进行顺序排序\n比如这题的0.0625，就是0.0625 * 2 = 0.0125 （取0）\n0.0125 * 2 = 0.25 （取0）\n0.25 * 2 = 0.5 （取0）\n0.5 * 2 = 1 （取1）", "filePath": "学习库/stm32/串口.md", "createdAt": 1743424473489}, "card-1743424473489-7xi7bx5zx": {"id": "card-1743424473489-7xi7bx5zx", "difficulty": 1, "stability": 0.1, "retrievability": 0.9994354413872623, "lastReview": 1752280028780, "nextReview": 1752366428780, "reviews": 4, "lapses": 1, "reviewHistory": [{"timestamp": 1743424501771, "rating": 3, "elapsed": 0}, {"timestamp": 1743424519517, "rating": 3, "elapsed": 0.00020539351851851852}, {"timestamp": 1752279841285, "rating": 2, "elapsed": 102.*************}, {"timestamp": 1752280028780, "rating": 1, "elapsed": 0.0021700810185185187}], "text": "返回-1", "answer": "c语言中的-1索引是不存在的", "filePath": "学习库/c/6 数组.md", "createdAt": 1743424473489}, "card-1743424473489-cq4nftakq": {"id": "card-1743424473489-cq4nftakq", "difficulty": 1, "stability": 0.1, "retrievability": 0.9994356426462436, "lastReview": 1752280029660, "nextReview": 1752366429660, "reviews": 4, "lapses": 1, "reviewHistory": [{"timestamp": 1743424504986, "rating": 3, "elapsed": 0}, {"timestamp": 1743424520571, "rating": 3, "elapsed": 0.00018038194444444444}, {"timestamp": 1752279842234, "rating": 2, "elapsed": 102.**************}, {"timestamp": 1752280029660, "rating": 1, "elapsed": 0.0021692824074074075}], "text": "中间元素", "answer": "c语言中的中位数是向下取整，在这里就相当于索引0和索引1的中位数是索引0<hr>mid = (min + max)/2", "filePath": "学习库/c/6 数组.md", "createdAt": 1743424473489}, "card-1743425725090-laghy7uh2": {"id": "card-1743425725090-laghy7uh2", "difficulty": 1, "stability": 0.1, "retrievability": 0.9996914010129808, "lastReview": 1752280030471, "nextReview": 1752366430471, "reviews": 3, "lapses": 1, "reviewHistory": [{"timestamp": 1752279793579, "rating": 2, "elapsed": 0}, {"timestamp": 1752279843127, "rating": 2, "elapsed": 0.0005734722222222222}, {"timestamp": 1752280030471, "rating": 1, "elapsed": 0.0021683333333333333}], "text": "GPIO 的输出", "answer": "通用模式时通过cpu 直接控制寄存器，从而控制 P-MOS 和 N-MOS 的状态\n复用模式就是通过其他的片上外设来控制的", "filePath": "学习库/stm32/GPIO.md", "createdAt": 1743425725090}, "card-1752279738996-zyqai41cs": {"id": "card-1752279738996-zyqai41cs", "difficulty": 1, "stability": 0.1, "retrievability": 0.9996912215478713, "lastReview": 1752280031412, "nextReview": 1752366431412, "reviews": 3, "lapses": 1, "reviewHistory": [{"timestamp": 1752279812904, "rating": 2, "elapsed": 0}, {"timestamp": 1752279843959, "rating": 2, "elapsed": 0.00035943287037037036}, {"timestamp": 1752280031412, "rating": 1, "elapsed": 0.0021695949074074074}], "text": "占空比", "answer": "高电压占整个周期的比例", "filePath": "学习库/stm32/9 定时器.md", "createdAt": 1752279738996}, "card-1752279738996-u7y2nwqrp": {"id": "card-1752279738996-u7y2nwqrp", "difficulty": 1, "stability": 0.1, "retrievability": 0.9996908626179422, "lastReview": 1752280032321, "nextReview": 1752366432321, "reviews": 3, "lapses": 1, "reviewHistory": [{"timestamp": 1752279822992, "rating": 2, "elapsed": 0}, {"timestamp": 1752279844650, "rating": 2, "elapsed": 0.0002506712962962963}, {"timestamp": 1752280032321, "rating": 1, "elapsed": 0.0021721180555555556}], "text": "优先级高的中断可以抢占（打断）正在执行的低优先级中断", "answer": "中断嵌套，更高优先级的中断打断正在执行的中断", "filePath": "学习库/stm32/6 中断.md", "createdAt": 1752279738996}, "card-1752279738996-hqgrbxgu1": {"id": "card-1752279738996-hqgrbxgu1", "difficulty": 1, "stability": 0.8237, "retrievability": 0.9999488116581183, "lastReview": 1752279845474, "nextReview": 1752366245474, "reviews": 2, "lapses": 0, "reviewHistory": [{"timestamp": 1752279826618, "rating": 2, "elapsed": 0}, {"timestamp": 1752279845474, "rating": 2, "elapsed": 0.00021824074074074074}], "text": "数据类型", "answer": "指针的数据类型，要和指向空间中的数据类型是保持一致的", "filePath": "学习库/c/7 指针.md", "createdAt": 1752279738996}, "card-1752279738996-81gnmo5qe": {"id": "card-1752279738996-81gnmo5qe", "difficulty": 1, "stability": 0.8237, "retrievability": 0.9999520174854822, "lastReview": 1752279846391, "nextReview": 1752366246391, "reviews": 2, "lapses": 0, "reviewHistory": [{"timestamp": 1752279828716, "rating": 2, "elapsed": 0}, {"timestamp": 1752279846391, "rating": 2, "elapsed": 0.00020457175925925927}], "text": "类型", "answer": "指针的类型要与其所指向的数据类型一致", "filePath": "学习库/c/7 指针.md", "createdAt": 1752279738996}, "card-1752279980545-7g1h7jrel": {"id": "card-1752279980545-7g1h7jrel", "difficulty": 5, "stability": 0.1, "retrievability": 1, "lastReview": 0, "nextReview": 1752279980545, "reviews": 0, "lapses": 0, "reviewHistory": [], "text": "TESTSETSET", "answer": "这是一个测试", "filePath": "学习库/An<PERSON>/Artificial Intelligence/未命名.md", "createdAt": 1752279980545}, "card-1752281235374-7v9mlrag1": {"id": "card-1752281235374-7v9mlrag1", "difficulty": 5, "stability": 0.1, "retrievability": 1, "lastReview": 0, "nextReview": 1752281235374, "reviews": 0, "lapses": 0, "reviewHistory": [], "text": "ASDSADASDSA", "answer": "213123132", "filePath": "学习库/An<PERSON>/Artificial Intelligence/未命名.md", "createdAt": 1752281235374}}, "globalStats": {"totalReviews": 30, "averageRetention": 0.8767631100205034, "streakDays": 1, "lastReviewDate": 1752280032321}, "cardGroups": [{"name": "AI", "filter": "#AI_ANKI", "sortOrder": 0, "createdTime": 1752280186531, "isReversed": false, "settings": {"useGlobalSettings": true, "newCardsPerDay": 20, "reviewsPerDay": 100}, "id": "mczidp5v-5tv8ejkwh24"}], "uiState": {"currentGroupName": "今日到期", "currentIndex": 0, "isFlipped": false, "completionMessage": null, "groupCompletionMessages": {"All Cards": null, "全部卡片": null, "今日到期": null, "已学习": null, "新卡片": null, "AI": null}, "groupProgress": {"All Cards": {"currentIndex": 0, "isFlipped": false}, "全部卡片": {"currentIndex": 0, "isFlipped": false}, "今日到期": {"currentIndex": 0, "isFlipped": false}, "已学习": {"currentIndex": 0, "isFlipped": false}, "新卡片": {"currentIndex": 0, "isFlipped": true}, "AI": {"currentIndex": 0, "isFlipped": false}}}, "dailyStats": []}, "device-id": "2f87a5e4e3cda5ce6072fcc87e8912235fcdfcff3b628b2fa64cd2479ed0caae", "flashcard-license": {"key": "qhlPc4egQeNFJne6GBs3Zf4qG+IQWnWL5WeRWSEHq34=", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************.24xqeXRCDgvxoCCTSsijRoJNU3_FYG6vgxurDVgjX2w", "features": ["flashcard"], "deviceId": "2f87a5e4e3cda5ce6072fcc87e8912235fcdfcff3b628b2fa64cd2479ed0caae", "lastVerified": 1752242835526}}