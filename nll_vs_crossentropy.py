import torch
import torch.nn.functional as F

print("=== NLL vs CrossEntropy 详细对比 ===")

# 准备数据
Y = torch.LongTensor([2, 0, 1])  # 真实标签
logits = torch.Tensor([[0.1, 0.2, 0.9],
                       [1.1, 0.1, 0.2], 
                       [0.2, 2.1, 0.1]])

print("输入数据:")
print(f"真实标签: {Y}")
print(f"模型logits:\n{logits}")

print("\n" + "="*50)
print("方法1: 使用 CrossEntropyLoss")
print("="*50)

# CrossEntropyLoss: 直接从logits计算
ce_loss = torch.nn.CrossEntropyLoss()
ce_result = ce_loss(logits, Y)

print("CrossEntropyLoss 内部步骤:")
print("1. 自动对logits应用softmax")
print("2. 计算负对数似然")
print("3. 求平均")
print(f"结果: {ce_result:.4f}")

print("\n" + "="*50)
print("方法2: 使用 NLLLoss")
print("="*50)

# NLLLoss: 需要手动先计算log_softmax
log_probs = F.log_softmax(logits, dim=1)
nll_loss = torch.nn.NLLLoss()
nll_result = nll_loss(log_probs, Y)

print("NLLLoss 需要的步骤:")
print("1. 手动计算 log_softmax")
print("2. 将log概率传给NLLLoss")
print("3. NLLLoss计算负对数似然并求平均")
print(f"log_softmax结果:\n{log_probs}")
print(f"NLLLoss结果: {nll_result:.4f}")

print("\n" + "="*50)
print("验证两者是否相等")
print("="*50)
print(f"CrossEntropyLoss: {ce_result:.6f}")
print(f"NLLLoss:          {nll_result:.6f}")
print(f"是否相等: {torch.allclose(ce_result, nll_result)}")

print("\n" + "="*50)
print("手动验证计算过程")
print("="*50)

# 手动计算验证
print("手动计算 CrossEntropy:")
manual_softmax = F.softmax(logits, dim=1)
manual_ce = 0
for i in range(len(Y)):
    true_class = Y[i].item()
    prob = manual_softmax[i][true_class]
    loss = -torch.log(prob)
    manual_ce += loss
    print(f"样本{i+1}: 真实类别{true_class}, 概率{prob:.4f}, 损失{loss:.4f}")

manual_ce /= len(Y)
print(f"手动计算平均损失: {manual_ce:.6f}")

print("\n手动计算 NLL (从log_softmax):")
manual_nll = 0
for i in range(len(Y)):
    true_class = Y[i].item()
    log_prob = log_probs[i][true_class]
    loss = -log_prob  # NLL就是负的log概率
    manual_nll += loss
    print(f"样本{i+1}: 真实类别{true_class}, log概率{log_prob:.4f}, 损失{loss:.4f}")

manual_nll /= len(Y)
print(f"手动计算平均NLL: {manual_nll:.6f}")

print("\n" + "="*50)
print("关键区别总结")
print("="*50)
print("1. 输入要求:")
print("   - CrossEntropyLoss: 接受原始logits")
print("   - NLLLoss: 接受log概率 (需要先log_softmax)")
print()
print("2. 内部计算:")
print("   - CrossEntropyLoss = softmax + log + NLL")
print("   - NLLLoss = 直接计算负对数似然")
print()
print("3. 数学关系:")
print("   CrossEntropyLoss(logits, Y) = NLLLoss(log_softmax(logits), Y)")
print()
print("4. 使用场景:")
print("   - CrossEntropyLoss: 最常用，直接从网络输出计算")
print("   - NLLLoss: 当你已经有log概率时使用")

print("\n" + "="*50)
print("实际代码对比")
print("="*50)
print("# 方法1: 直接使用CrossEntropyLoss")
print("loss = nn.CrossEntropyLoss()(logits, targets)")
print()
print("# 方法2: 手动softmax + NLLLoss")
print("log_probs = F.log_softmax(logits, dim=1)")
print("loss = nn.NLLLoss()(log_probs, targets)")
print()
print("# 两种方法结果完全相同！")
