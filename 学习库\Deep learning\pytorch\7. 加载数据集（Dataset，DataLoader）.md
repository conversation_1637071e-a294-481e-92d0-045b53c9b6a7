---
tags:
  - 学习
  - deep_learning
  - pytorch
---

## `Epoch`，`Batch-Size`，`Iteration`

- `Epoch`：一个完整的训练周期，所有训练数据都被用来更新模型一次。
- `Batch-Size`：每次训练（前馈，反馈，更新权重）中使用的样本数量。通常将训练数据分成多个小批次进行训练。
- `Iteration`：一次更新模型参数的过程。每个`Epoch`包含多个`Iteration`，每个`Iteration`使用一个`Batch`的数据进行训练。

### 举例：

- 数据集：有一个包含 10,000 张图像的数据集，用于训练一个猫狗分类器。
- 选定的 Batch Size: 决定 batch size 为 64 张图像。
- 选定的 Epoch 数量: 计划将模型训练 50 个 epoch。

计算过程：
- 每个`Epoch`的批次（`batch`）数量：`10,000 / 64 = 156.25`，向上取整为 `157`。
- 每个`Epoch`的迭代（`Iteration`）次数：每处理一个 `batch` 就会进行一次 `iteration`，所以每个`Epoch`会进行 157 次迭代。
- 整个训练过程中的迭代次数：`50 * 157 = 7850` 次迭代。
- ![[学习库/Deep learning/pytorch/Drawing 2025-06-17 10.45.31.excalidraw.md|计算过程.png]]


训练流程描述：
1. **Epoch 1 开始**:
	- 模型取第一个批次的 64 张图像。它执行一次前向传播（进行预测）和一次反向传播（计算误差并更新其参数）。(Iteration 1 完成)。
	- 它取第二个批次的 64 张图像，执行前向/反向传播，更新参数。(Iteration 2 完成)。
	- ...这个过程对另外 155 个包含 64 张图像的批次继续进行...
	- 最后，它处理最后一个包含 16 张图像的批次，执行前向/反向传播，更新参数。(Iteration 157 完成)。
	- Epoch 1 至此完成。模型已经将所有 10,000 张图像都“看”了一遍，期间其参数被更新了 157 次。

2. **Epoch 2 开始**:
	- 同样的过程重复——所有 10,000 张图像再次以 157 个批次被处理，从而产生另外 157 次 iteration (参数更新)。
3. ...这个过程持续进行，直到完成所有 50 个 epoch...
4. **训练结束**：在 50 个 epoch 之后，模型将完整地处理了整个数据集 50 次，其参数总共被更新了 7,850 次。

在使用小批量样本进行模型的训练的时候，循环通常要写成嵌套的形式

```python
# 训练代码
for epoch in range(num_epochs):
  # 外层进行整个Epoch的循环
    for i in range(total_batches):
        # 内层进行每个Batch的循环
        # 在每个Batch中，使用训练数据进行前馈，反馈，更新权重
```
## `DATASET` 和 `DATALOADER` 

`Dataset` 和 `Dataloader` 是 PyTorch 中用于加载和处理数据集的类。

- `Dataset` 是抽象类，定义了数据集的接口，==只能被其它子类继承，不能实例化==。主要用来构建数据集，以便能够快速获取数据集中的单个样本。
- `Dataloader` 是 `Dataset` 的子类，用于加载数据集，主要用来将数据集分成多个小批次(`Batch`)进行训练，可以跨过训练过程中可能出现的鞍点 [[2. 梯度下降算法（Gradient Descent Algorithm）#小批量梯度下降（Mini-batch Gradient Descent）]]。

假设我们设置 `DataLoader` 的 `Batch-Size` 为 `2`，并且设置 `shuffle` 为 `True`，那么 `DataLoader` 会从 `Dataset` 中先随机打乱顺序，然后按照 `Batch-Size` 的大小将数据分成多个 `batch`。如下图所示，第一次迭代为 `batch 1`，第二次迭代为 `batch 2`......

![[7. 加载数据集-2025-05-09-10-01-01.png]]

```python
import torch
from torch.utils.data import Dataset, DataLoader
# Dataset 是一个抽象类，必须通过继承来实现具体的数据集
# 实现 __init__ 和 __len__ 方法
class MyDataset(Dataset):
  ## MyDataset 继承了 Dataset 类，并实现了 __init__ 和 __len__ 方法
    def __init__(self, data):
        self.data = data

    def __len__(self):
        ## 实现 __len__ 方法，以便能够快速获取数据集的长度
        return len(self.data)

    def __getitem__(self, index):
        ## 实现 __getitem__ 方法，以便能够快速获取数据集中的单个样本，索引从0开始
        return self.data[index]

dataset = MyDataset(data)
dataloader = DataLoader(dataset, batch_size=2, shuffle=True)

for batch in dataloader:
    print(batch)
```

## 训练流程

1. **数据准备**：使用 `Dataset` 和 `Dataloader` 准备好训练数据。
2. **模型定义**：定义一个神经网络模型。
3. **损失函数和优化器**：选择合适的损失函数和优化器。
4. **训练循环**：
   - 对于每个 `epoch`：
     - 对于每个 `batch`：
       - 前向传播：将输入数据传入模型，得到输出。
       - 计算损失：使用损失函数计算输出与真实标签之间的差异。
       - 反向传播：计算梯度。
       - 更新参数：使用优化器更新模型参数。




