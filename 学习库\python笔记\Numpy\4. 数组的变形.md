---
tags:
  - 学习
  - python
  - Numpy
---

# 数组的转置

数组的转置方法有两种：`T`和`transpose()`，两者的效果是一样的，都是将数组的行和列互换。

```python
import numpy as np
# 创建一维数组-向量
arr1 = np.array([1, 2, 3])
print(arr1.T)  # 输出结果为[1 2 3]，一维数组的转置没有变化
arr1 = arr1.reshape(-1, 1)  # 将一维数组转换为二维数组-列矩阵
print(arr1.T)  # 输出结果为[[1] [2] [3]]，转置成了行矩阵
```

# 数组的翻转

数组的翻转方法有三种：`flip()`、`fliplr()`和`flipud()`

- `flip()` 可以指定翻转的轴
- `fliplr()` 可以左右反转
- `flipud()` 可以上下翻转


```python
import numpy as np
# 创建一维数组-向量
arr1 = np.array([1, 2, 3])
print(np.flip(arr1))  # 输出结果为[3 2 1]，表示将数组翻转
print(np.fliplr(arr1.reshape(1, -1)))  # 输出结果为[[3 2 1]]，表示将数组左右翻转

# 创建二维数组-矩阵
arr2 = np.array([[1, 2, 3], [4, 5, 6]])
print(np.flip(arr2))  # 输出结果为[[6 5 4] [3 2 1]]，表示将数组翻转
print(np.fliplr(arr2))  # 输出结果为[[3 2 1] [6 5 4]]，表示将数组左右翻转
print(np.flipud(arr2))  # 输出结果为[[4 5 6] [1 2 3]]，表示将数组上下翻转
```

# 数组的重塑

数组的重塑方法有三种：`reshape()`、`resize()`和`ravel()`

- `reshape()` 可以指定重塑后的形状，还可以将其中的一个维度设置为-1，表示自动计算该维度的大小，比如把一个5行6列的矩阵重塑为3行10列的矩阵，可以填入列参数10，行参数-1，表示自动计算行数为3；如果不指定-1，则需要手动计算行数为$5*6/10=3$
- `resize()` 可以指定重塑后的形状，但会修改原数组
- `ravel()` 可以将数组重塑为一维数组

# 数组的拼接

两个向量拼接可以得到一个新的加长版的向量，数组的拼接方法有两种：`concatenate()`和`stack()`

- `concatenate()` 可以指定拼接的轴
- `stack()` 可以在新的轴上拼接

```python
import numpy as np
# 创建一维数组-向量1
arr1 = np.array([1, 2, 3])
# 创建一维数组-向量2
arr2 = np.array([4, 5, 6])
# 拼接向量
arr3 = np.concatenate((arr1, arr2))  # 沿着第0维拼接
print(arr3)  # 输出结果为[1 2 3 4 5 6]，表示拼接后的向量

# 创建二维数组-矩阵1
arr4 = np.array([[1, 2, 3], [4, 5, 6]])
# 创建二维数组-矩阵2
arr5 = np.array([[7, 8, 9], [10, 11, 12]])
# 拼接矩阵
arr6 = np.concatenate((arr4, arr5))  # 默认的axis=0，沿着第0维拼接，在这里也就是行拼接
print(arr6)  # 输出结果为[[ 1  2  3] [ 4  5  6] [ 7  8  9] [10 11 12]]，表示拼接后的矩阵
arr7 = np.concatenate((arr4, arr5), axis=1)  # 沿着第1维拼接，在这里也就是列拼接
print(arr7)  # 输出结果为[[ 1  2  3  7  8  9] [ 4  5  6 10 11 12]]，表示拼接后的矩阵
```

# 数组的分裂

数组的分裂方法有两种：`split()`和`hsplit()`

- `split()` 可以指定分裂的轴
- `hsplit()` 可以水平分裂

```python
import numpy as np
# 创建一维数组-向量
arr1 = np.array([1, 2, 3, 4, 5, 6])
# 分裂向量
arr2, arr3, arr4 = np.split(arr1, [1,3])  # 沿着第0维分裂，分裂成3个向量，分割的位置为1和3
print(arr2)  # 输出结果为[1]，表示第一个向量
print(arr3)  # 输出结果为[2 3]，表示第二个向量
print(arr4)  # 输出结果为[4 5 6]，表示第三个向量

# 创建二维数组-矩阵
arr5 = np.arange(10).reshape(5,-1)
print(arr5) # 输出结果为[[0 1 2] [3 4 5] [6 7 8] [9 10 11] [12 13 14]]，表示一个5行2列的矩阵
# 分裂矩阵
arr6, arr7, arr8 = np.split(arr5, [1,3]) # 沿着第0维分裂，分裂成3个矩阵，分割的位置为1和3
print(arr6) # 输出结果为[[0 1]]，表示第一个矩阵
print(arr7) # 输出结果为[[3 4 5] [6 7 8]]，表示第二个矩阵
print(arr8) # 输出结果为[[9 10 11] [12 13 14]]，表示第三个矩阵
```


