/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin (https://github.com/phibr0/obsidian-commander)
*/

var Oe=Object.defineProperty,Hn=Object.defineProperties,Rn=Object.getOwnPropertyDescriptor,zn=Object.getOwnPropertyDescriptors,On=Object.getOwnPropertyNames,ze=Object.getOwnPropertySymbols;var vo=Object.prototype.hasOwnProperty,$o=Object.prototype.propertyIsEnumerable;var Yo=(e,t,o)=>t in e?Oe(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o,Fe=(e,t)=>{for(var o in t||(t={}))vo.call(t,o)&&Yo(e,o,t[o]);if(ze)for(var o of ze(t))$o.call(t,o)&&Yo(e,o,t[o]);return e},Go=(e,t)=>Hn(e,zn(t));var Ko=(e,t)=>{var o={};for(var n in e)vo.call(e,n)&&t.indexOf(n)<0&&(o[n]=e[n]);if(e!=null&&ze)for(var n of ze(e))t.indexOf(n)<0&&$o.call(e,n)&&(o[n]=e[n]);return o};var Fn=(e,t)=>{for(var o in t)Oe(e,o,{get:t[o],enumerable:!0})},Vn=(e,t,o,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of On(t))!vo.call(e,a)&&a!==o&&Oe(e,a,{get:()=>t[a],enumerable:!(n=Rn(t,a))||n.enumerable});return e};var Wn=e=>Vn(Oe({},"__esModule",{value:!0}),e);var Fa={};Fn(Fa,{default:()=>lo});module.exports=Wn(Fa);var Ve=require("obsidian");var Co=require("obsidian");var Qo={};var et={"Open Commander Settings":"Otev\u0159\xEDt nastaven\xED Commandera","Open Macro Builder":"Otev\u0159\xEDt tv\u016Frce Maker","Change Icon":"Zm\u011Bnit ikonu",Rename:"P\u0159ejmenovat",Delete:"Smazat","Add command":"P\u0159idat p\u0159\xEDkaz","Add new":"P\u0159idat nov\xFD","This Command seems to have been removed. {{command_name}}":"Tento p\u0159\xEDkaz se zd\xE1 b\xFDt odstran\u011Bn. {{command_name}}","Choose a Command to add":"Vyberte p\u0159\xEDkaz k p\u0159id\xE1n\xED","to navigate":"pro navigaci","to choose an icon":"pro v\xFDb\u011Br ikony","to cancel":"pro zru\u0161en\xED","Use a custom name":"Pou\u017E\xEDt vlastn\xED jm\xE9no","Choose a custom Name for your new Command":"Vyberte vlastn\xED jm\xE9no pro v\xE1\u0161 nov\xFD p\u0159\xEDkaz","to save":"pro ulo\u017Een\xED","Choose a Icon for your new Command":"Vyberte ikonu pro v\xE1\u0161 nov\xFD p\u0159\xEDkaz","to choose a custom icon":"pro v\xFDb\u011Br vlastn\xED ikony","Remove Command":"Odstranit p\u0159\xEDkaz","Double click to rename":"Pro p\u0159ejmenov\xE1n\xED dvakr\xE1t klikn\u011Bte","This device":"Toto za\u0159\xEDzen\xED","Added by {{plugin_name}}.":"P\u0159id\xE1no pomoc\xED {{plugin_name}}.","Warning: This is a checked Command, meaning it might not run under every circumstance.":"Varov\xE1n\xED: Tento p\u0159\xEDkaz je kontrolov\xE1n a nemus\xED fungovat ve v\u0161ech p\u0159\xEDpadech.","Move down":"P\u0159esunout dol\u016F","Move up":"P\u0159esunout nahoru","Change Mode (Currently: {{current_mode}})":"Zm\u011Bnit re\u017Eim (Nyn\xED: {{current_mode}})","Are you sure you want to delete the Command?":"Opravdu chcete smazat tento p\u0159\xEDkaz?","Remove and don't ask again":"Odstranit a u\u017E se neptat",Remove:"Odstranit",Cancel:"Zru\u0161it","Always ask before removing?":"V\u017Edy se pt\xE1t p\u0159ed odstran\u011Bn\xEDm?","Always show a Popup to confirm deletion of a Command.":"V\u017Edy zobrazovat vyskakovac\xED okno pro potvrzen\xED odstran\u011Bn\xED p\u0159\xEDkazu.",'Show "Add Command" Button':'Zobrazit tla\u010D\xEDtko "P\u0159idat p\u0159\xEDkaz"','Show the "Add Command" Button in every Menu. Requires restart.':'Zobrazit tla\u010D\xEDtko "P\u0159idat p\u0159\xEDkaz" ve v\u0161ech nab\xEDdk\xE1ch. Vy\u017Eaduje restart.',"Please restart Obsidian for these changes to take effect.":"Pros\xEDm restartujte Obsidian, aby se zm\u011Bny projevily.","Enable debugging":"Povolit lad\u011Bn\xED","Enable console output.":"Povolit v\xFDstup do konzole.",General:"Obecn\xE9","Editor Menu":"Kontextov\xE1 nab\xEDdka v editoru","File Menu":"Nab\xEDdka souboru","Left Ribbon":"Lev\xFD Ribbon","Right Ribbon":"Prav\xFD Ribbon",Titlebar:"Li\u0161ta aplikace",Statusbar:"Stavov\xE1 li\u0161ta","Page Header":"Hlavi\u010Dka str\xE1nky","Support development":"Podpo\u0159te v\xFDvoj","No commands here!":"Nejsou zde \u017E\xE1dn\xE9 p\u0159\xEDkazy!","Would you like to add one now?":"Chcete nyn\xED jeden p\u0159idat?","Hide Commands":"Skr\xFDt p\u0159\xEDkazy","Choose new":"Vyberte nov\xFD","Hide Commands of other Plugins":"Skr\xFDt p\u0159\xEDkazy jin\xFDch roz\u0161\xED\u0159en\xED",Icon:"Ikona",Name:"N\xE1zev","Custom Name":"Vlastn\xED n\xE1zev","Add command to all devices":"P\u0159idat p\u0159\xEDkaz na v\u0161echna za\u0159\xEDzen\xED","Add command only to mobile devices":"P\u0159idat p\u0159\xEDkaz pouze na mobiln\xED za\u0159\xEDzen\xED","Add command only to desktop devices":"P\u0159idat p\u0159\xEDkaz pouze na stoln\xED za\u0159\xEDzen\xED","Add command only to this device":"P\u0159idat p\u0159\xEDkaz pouze na toto za\u0159\xEDzen\xED",Done:"Hotovo","By Johnny\u2728 and phibr0":"Vytvo\u0159il Johnny\u2728 a phibr0","Leave feedback":"Zanechat zp\u011Btnou vazbu",Donate:"P\u0159isp\u011Bt","Share feedback, issues, and ideas with our feedback form.":"Sd\xEDlejte zp\u011Btnou vazbu, probl\xE9my a n\xE1pady pomoc\xED na\u0161eho formul\xE1\u0159e.","Consider donating to support development.":"Zva\u017Ete p\u0159\xEDsp\u011Bvek na podporu v\xFDvoje.",Save:"Ulo\u017Eit","This Command is not available on this device.":"Tento p\u0159\xEDkaz nen\xED dostupn\xFD na tomto za\u0159\xEDzen\xED.",Show:"Zobrazit",Hide:"Skr\xFDt","Hide other Commands":"Skr\xFDt ostatn\xED p\u0159\xEDkazy","Double click to enter custom value":"Dvakr\xE1t klikn\u011Bte pro zad\xE1n\xED vlastn\xED hodnoty","Choose custom spacing for Command Buttons":"Vyberte vlastn\xED odsazen\xED pro tla\u010D\xEDtka p\u0159\xEDkaz\u016F","Change the spacing between commands. You can set different values on mobile and desktop.":"Zm\u011Bna odsazen\xED mezi p\u0159\xEDkazy. M\u016F\u017Eete nastavit r\u016Fzn\xE9 hodnoty na mobiln\xEDch a stoln\xEDch za\u0159\xEDzen\xEDch.",Warning:"Varov\xE1n\xED","As of Obsidian 0.16.0 you need to explicitly enable the View Header.":"Od verze Obsidian 0.16.0 je nutn\xE9 explicitn\u011B povolit z\xE1hlav\xED zobrazen\xED. Po povolen\xED je mo\u017En\xE9, \u017Ee budete muset restartovat Obsidian.","Open Appearance Settings":"Otev\u0159\xEDt nastaven\xED vzhledu",Explorer:"Pr\u016Fzkumn\xEDk"};var ot={};var tt={"Open Commander Settings":"Commander Einstellungen \xF6ffnen","Open Macro Builder":"Makro Baukasten \xF6ffnen","Change Icon":"Symbol ver\xE4ndern",Rename:"Umbenennen",Delete:"L\xF6schen","Add command":"Befehl hinzuf\xFCgen","Add new":"Neuen Befehl hinzuf\xFCgen","This Command seems to have been removed. {{command_name}}":"Dieser Befehl wurde entfernt. {{command_name}}","Choose a Command to add":"W\xE4hle einen Befehl zum hinzuf\xFCgen","to navigate":"zum navigieren","to choose an icon":"um ein symbol auszuw\xE4hlen","to cancel":"zum abbrechen","Use a custom name":"Nutze einen benutzerdefinierten Namen","Choose a custom Name for your new Command":"W\xE4hle einen benutzerdefinierten Namen f\xFCr deinen neuen Befehl","to save":"zum speichern","Choose a Icon for your new Command":"W\xE4hle ein Symbol f\xFCr deinen neuen Befehl","to choose a custom icon":"um ein benutzerdefiniertes Symbol auszuw\xE4hlen","Remove Command":"Befehl entfernen","Double click to rename":"Zum umbenennen doppelklicken","This device":"Dieses Ger\xE4t","Added by {{plugin_name}}.":"Hinzugef\xFCgt von {{plugin_name}}.","Warning: This is a checked Command, meaning it might not run under every circumstance.":"Warnung: Dieser Befehl wird nur unter bestimmten Vorraussetzungen ausgef\xFChrt.","Move down":"Nach unten","Move up":"Nach oben","Change Mode (Currently: {{current_mode}})":"Modus ver\xE4ndern (Momentan: {{current_mode}})","Are you sure you want to delete the Command?":"Bist du dir sicher, dass du diesen Befehl entfernen m\xF6chtest?","Remove and don't ask again":"Entfernen und Auswahl speichern",Remove:"Entfernen",Cancel:"Abbrechen","Always ask before removing?":"Immer fragen, bevor ein Befehl gel\xF6scht wird?","Always show a Popup to confirm deletion of a Command.":"Zeige immer ein Popup um L\xF6schen zu best\xE4tigen.",'Show "Add Command" Button':'Zeige "Befehl hinzuf\xFCgen" Knopf','Show the "Add Command" Button in every Menu. Requires restart.':'Zeige den "Befehl hinzuf\xFCgen" Knopf in jedem Men\xFC. Erfordert neustart.',"Please restart Obsidian for these changes to take effect.":"Bitte starte Obsidian neu, damit diese \xC4nderungen in Kraft treten.","Enable debugging":"Aktiviere debugging","Enable console output.":"Aktiviere Konsolen-Output (F\xFCr Entwickler)",General:"Allgemein","Editor Menu":"Editor-Men\xFC","File Menu":"Datei-Men\xFC","Left Ribbon":"Band","Right Ribbon":"Rechtes Band",Titlebar:"Titelleiste",Statusbar:"Statusleiste","Page Header":"Kopfzeile","Support development":"Entwicklung unterst\xFCtzen","No commands here!":"Keine Befehle da!","Would you like to add one now?":"M\xF6chtest du jetzt einen hinzuf\xFCgen?","Hide Commands":"Befehle verstecken","Choose new":"W\xE4hle neu","Hide Commands of other Plugins":"Hide Commands of other Plugins",Icon:"Symbol",Name:"Name","Custom Name":"Benutzerdefinierter Name","Add command to all devices":"F\xFCge Befehl allen Ger\xE4ten hinzu","Add command only to mobile devices":"F\xFCge Befehl nur Mobilen Ger\xE4ten hinzu","Add command only to desktop devices":"F\xFCge Befehl nur Desktop Ger\xE4ten hinzu","Add command only to this device":"F\xFCge Befehl nur diesem Ger\xE4t hinzu",Done:"Fertig","By Johnny\u2728 and phibr0":"Von Johnny\u2728 und phibr0","Leave feedback":"Feedback geben",Donate:"Spenden","Share feedback, issues, and ideas with our feedback form.":"Teile Feedback, Probleme und Ideen mit unserem Feedback Formular!","Consider donating to support development.":"Spende um die Entwicklung zu unterst\xFCtzen.",Save:"Speichern","This Command is not available on this device.":"Dieser Befehl ist auf diesem Ger\xE4t nicht verf\xFCgbar.",Show:"Anzeigen",Hide:"Verstecken","Hide other Commands":"Andere Befehle verstecken","Double click to enter custom value":"Doppelklicken um eigenen Wert einzutragen","Choose custom spacing for Command Buttons":"W\xE4hle den Abstand zwischen Befehlen","Change the spacing between commands. You can set different values on mobile and desktop.":"Ver\xE4ndert den Abstand zwischen Befehlen.",Warning:"Achtung","As of Obsidian 0.16.0 you need to explicitly enable the View Header.":'Ab Obsidian Version 0.16.0 m\xFCssen Sie den "View Header" explizit aktivieren. Anschlie\xDFend muss Obsidian neugestartet werden.',"Open Appearance Settings":"\xD6ffne Darstellungs-Einstellungen",Explorer:"Explorer"};var go={"Open Commander Settings":"Open Commander Settings","Open Macro Builder":"Open Macro Builder","Change Icon":"Change Icon",Rename:"Rename",Delete:"Delete","Add command":"Add command","Add new":"Add new command","This Command seems to have been removed. {{command_name}}":"This Command seems to have been removed. {{command_name}}","Choose a Command to add":"Choose a Command to add","to navigate":"to navigate","to choose an icon":"to choose an icon","to cancel":"to cancel","Use a custom name":"Use a custom name","Choose a custom Name for your new Command":"Choose a custom Name for your new Command","to save":"to save","Choose a Icon for your new Command":"Choose a Icon for your new Command","to choose a custom icon":"to choose a custom icon","Remove Command":"Remove Command","Double click to rename":"Double click to rename","This device":"This device","Added by {{plugin_name}}.":"Added by {{plugin_name}}.","Warning: This is a checked Command, meaning it might not run under every circumstance.":"Warning: This is a checked Command, meaning it might not run under every circumstance.","Move down":"Move down","Move up":"Move up","Change Mode (Currently: {{current_mode}})":"Change Mode (Currently: {{current_mode}})","Are you sure you want to delete the Command?":"Are you sure you want to delete the Command?","Remove and don't ask again":"Remove and don't ask again",Remove:"Remove",Cancel:"Cancel","Always ask before removing?":"Always ask before removing?","Always show a Popup to confirm deletion of a Command.":"Always show a Popup to confirm deletion of a Command.",'Show "Add Command" Button':'Show "Add Command" Button','Show the "Add Command" Button in every Menu. Requires restart.':'Show the "Add Command" Button in every Menu. Requires restart.',"Please restart Obsidian for these changes to take effect.":"Please restart Obsidian for these changes to take effect.","Enable debugging":"Enable debugging","Enable console output.":"Enable console output.",General:"General","Editor Menu":"Editor Menu","File Menu":"File Menu","Left Ribbon":"Ribbon","Right Ribbon":"Right Ribbon",Titlebar:"Titlebar",Statusbar:"Status Bar","Page Header":"Tab Bar","Support development":"Support development","No commands here!":"No commands here!","Would you like to add one now?":"Would you like to add one now?","Hide Commands":"Hide Commands","Choose new":"Choose new","Hide Commands of other Plugins":"Hide Commands of other Plugins",Icon:"Icon",Name:"Name","Custom Name":"Custom Name","Add command to all devices":"Add command to all devices","Add command only to mobile devices":"Add command only to mobile devices","Add command only to desktop devices":"Add command only to desktop devices","Add command only to this device":"Add command only to this device",Done:"Done","By Johnny\u2728 and phibr0":"By Johnny\u2728 and phibr0","Leave feedback":"Leave feedback",Donate:"Donate","Share feedback, issues, and ideas with our feedback form.":"Share feedback, issues, and ideas with our feedback form.","Consider donating to support development.":"Consider donating to support development.",Save:"Save","This Command is not available on this device.":"This Command is not available on this device.",Show:"Show",Hide:"Hide","Hide other Commands":"Hide other Commands","Double click to enter custom value":"Double click to enter custom value","Choose custom spacing for Command Buttons":"Choose custom spacing for Command Buttons","Change the spacing between commands. You can set different values on mobile and desktop.":"Change the spacing between commands.",Warning:"Warning","As of Obsidian 0.16.0 you need to explicitly enable the View Header.":"As of Obsidian 0.16.0 you need to explicitly enable the Tab Title Bar. Once enabled, you might need to restart Obsidian.","Open Appearance Settings":"Open Appearance Settings",Explorer:"Explorer"};var nt={};var at={};var it={"Open Commander Settings":"Ouvrir les param\xE8tres de Commander","Open Macro Builder":"Ouvrir le constructeur de Macro","Change Icon":"Changer l'ic\xF4ne",Rename:"Renommer",Delete:"Supprimer","Add command":"Ajouter une commande","Add new":"Ajouter une nouvelle commande","This Command seems to have been removed. {{command_name}}":"Cette commande semble avoir \xE9t\xE9 supprim\xE9e. {{command_name}}","Choose a Command to add":"Choisissez une commande \xE0 ajouter","to navigate":"pour naviguer","to choose an icon":"pour choisir une ic\xF4ne","to cancel":"pour annuler","Use a custom name":"Utiliser un nom personnalis\xE9","Choose a custom Name for your new Command":"Choisissez un nom personnalis\xE9 pour votre nouvelle commande","to save":"pour enregistrer","Choose a Icon for your new Command":"Choisissez une ic\xF4ne pour votre nouvelle commande","to choose a custom icon":"pour choisir une ic\xF4ne personnalis\xE9e","Remove Command":"Supprimer la commande","Double click to rename":"Double-cliquez pour renommer","This device":"Cet appareil","Added by {{plugin_name}}.":"Ajout\xE9 par {{plugin_name}}.","Warning: This is a checked Command, meaning it might not run under every circumstance.":"Attention : Cette commande est coch\xE9e, ce qui signifie qu'elle pourrait ne pas fonctionner dans toutes les situations.","Move down":"Descendre","Move up":"Monter","Change Mode (Currently: {{current_mode}})":"Changer de mode (Actuellement : {{current_mode}})","Are you sure you want to delete the Command?":"\xCAtes-vous s\xFBr de vouloir supprimer la commande ?","Remove and don't ask again":"Supprimer et ne plus demander",Remove:"Supprimer",Cancel:"Annuler","Always ask before removing?":"Toujours demander avant de supprimer ?","Always show a Popup to confirm deletion of a Command.":"Toujours afficher une fen\xEAtre contextuelle pour confirmer la suppression d'une commande.",'Show "Add Command" Button':'Afficher le bouton "Ajouter une commande"','Show the "Add Command" Button in every Menu. Requires restart.':'Afficher le bouton "Ajouter une commande" dans chaque menu. N\xE9cessite un red\xE9marrage.',"Please restart Obsidian for these changes to take effect.":"Veuillez red\xE9marrer Obsidian pour que ces modifications prennent effet.","Enable debugging":"Activer le d\xE9bogage","Enable console output.":"Activer la sortie console.",General:"G\xE9n\xE9ral","Editor Menu":"Menu \xE9diteur","File Menu":"Menu fichier","Left Ribbon":"Ruban gauche","Right Ribbon":"Ruban droit",Titlebar:"Barre de titre",Statusbar:"Barre d'\xE9tat","Page Header":"En-t\xEAte de page","Support development":"Soutenir le d\xE9veloppement","No commands here!":"Aucune commande ici !","Would you like to add one now?":"Voulez-vous en ajouter une maintenant ?","Hide Commands":"Masquer les commandes","Choose new":"Choisir nouveau","Hide Commands of other Plugins":"Masquer les commandes d'autres plugins",Icon:"Ic\xF4ne",Name:"Nom","Custom Name":"Nom personnalis\xE9","Add command to all devices":"Ajouter la commande \xE0 tous les appareils","Add command only to mobile devices":"Ajouter la commande uniquement sur les appareils mobiles","Add command only to desktop devices":"Ajouter la commande uniquement sur les ordinateurs de bureau","Add command only to this device":"Ajouter la commande uniquement sur cet appareil",Done:"Termin\xE9","By Johnny\u2728 and phibr0":"Par Johnny\u2728 et phibr0","Leave feedback":"Laisser un commentaire",Donate:"Faire un don","Share feedback, issues, and ideas with our feedback form.":"Partagez vos commentaires, probl\xE8mes et id\xE9es avec notre formulaire de retour d'information.","Consider donating to support development.":"Envisagez de faire un don pour soutenir le d\xE9veloppement.",Save:"Enregistrer","This Command is not available on this device.":"Cette commande n'est pas disponible sur cet appareil.",Show:"Afficher",Hide:"Masquer","Hide other Commands":"Masquer les autres commandes","Double click to enter custom value":"Double-cliquez pour entrer une valeur personnalis\xE9e","Choose custom spacing for Command Buttons":"Choisissez un espacement personnalis\xE9 pour les boutons de commande","Change the spacing between commands.":"Modifier l'espacement entre les commandes.",Warning:"Avertissement","As of Obsidian 0.16.0 you need to explicitly enable the Tab Title Bar. Once enabled, you might need to restart Obsidian.":"\xC0 partir d'Obsidian 0.16.0, vous devez activer explicitement la barre de titre des onglets. Une fois activ\xE9e, il se peut que vous deviez red\xE9marrer Obsidian.","Open Appearance Settings":"Ouvrir les param\xE8tres d'apparence",Explorer:"Explorateur"};var rt={};var st={};var ct={};var dt={};var mt={};var lt={"Open Commander Settings":"Open Commander Instellingen","Open Macro Builder":"Open Macro Bouwer","Change Icon":"Verander Icoon",Rename:"Hernoem",Delete:"Verwijder","Add command":"Voeg commando toe","Add new":"Voeg nieuw commando toe","This Command seems to have been removed. {{command_name}}":"Het lijkt er op dat dit commando is verwijderd. {{command_name}}","Choose a Command to add":"Kies een commando om toe te voegen","to navigate":"naar navigatie","to choose an icon":"naar kies een icoon","to cancel":"naar annuleren","Use a custom name":"Gebruik een aangepaste naam","Choose a custom Name for your new Command":"Kies een aangepaste naam voor je nieuwe commando","to save":"naar opslaan","Choose a Icon for your new Command":"Kies een icoon voor je nieuwe commando","to choose a custom icon":"to choose a custom icon","Remove Command":"Verwijder commando","Double click to rename":"Dubbel klik om te hernoemen","This device":"Dit apparaat","Added by {{plugin_name}}.":"Toegevoegd door {{plugin_name}}.","Warning: This is a checked Command, meaning it might not run under every circumstance.":"Waarschuwing: Dit is een aangevinkte opdracht, wat betekent dat deze mogelijk niet onder alle omstandigheden wordt uitgevoerd.","Move down":"Naar beneden","Move up":"Naar boven","Change Mode (Currently: {{current_mode}})":"Verander modus (Currently: {{current_mode}})","Are you sure you want to delete the Command?":"Weet je zeker dat je dit commando wilt verwijderen??","Remove and don't ask again":"Verwijder en vraag niet opnieuw",Remove:"Verwijder",Cancel:"Annuleer","Always ask before removing?":"Altijd vragen voor verwijderen?","Always show a Popup to confirm deletion of a Command.":"Laat altijd een venster zien om het verwijderen van een commando te bevestigen.",'Show "Add Command" Button':'Laat "Voeg Commando toe" knop zien','Show the "Add Command" Button in every Menu. Requires restart.':'Laat de "Voeg Commenado toe" knop zien in elk menu. Vereist herstart.',"Please restart Obsidian for these changes to take effect.":"Start Obsidian a.u.b. opnieuw op om deze wijzigingen toe te passen.","Enable debugging":"Activeer debugging","Enable console output.":"Activeer console output.",General:"Algemeen","Editor Menu":"Editor Menu","File Menu":"Bestand Menu","Left Ribbon":"Linkse Lint","Right Ribbon":"Rechtse Lint",Titlebar:"Titelbalk",Statusbar:"Statusbalk","Page Header":"Pagina Kop","Support development":"Steun ontwikkeling","No commands here!":"Geen commando's hier!","Would you like to add one now?":"Zou je er \xE9\xE9n willen toevoegen?","Hide Commands":"Verberg Commando's","Choose new":"Kies nieuw","Hide Commands of other Plugins":"Verberg Commando's van andere Plugins",Icon:"Icoon",Name:"Naam","Custom Name":"Aangepaste naam","Add command to all devices":"Voeg commando toe aan alle apparaten","Add command only to mobile devices":"Voeg commando toe aan alleen mobiele apparaten","Add command only to desktop devices":"Voeg commando toe aan alleen dekstop apparaten","Add command only to this device":"Voed commando toe aan alleen dit apparaat",Done:"Klaar","By Johnny\u2728 and phibr0":"Door Johnny\u2728 en phibr0","Leave feedback":"Laat feedback achter",Donate:"Doneer","Share feedback, issues, and ideas with our feedback form.":"Deel feedback, problemen en idee\xEBn met ons feedback formulier.","Consider donating to support development.":"Overweeg te doneren om ontwikkeling te steunen.",Save:"Opslaan","This Command is not available on this device.":"Dit Commando is niet beschikbaar op dit apparaat.",Show:"Laat zien",Hide:"Verberg","Hide other Commands":"Verberg andere Commando's","Double click to enter custom value":"Dubbel klik om een aangepaste waarde in te vullen","Choose custom spacing for Command Buttons":"Kies aangepaste regelafstand voor Commando Knoppen","Change the spacing between commands. You can set different values on mobile and desktop.":"Verander regelafstand tussen Commando's. Dit kan verschillen tussen mobiel en dekstop.",Warning:"Waarschuwing","As of Obsidian 0.16.0 you need to explicitly enable the View Header.":"Sinds Obsidian 0.16.0 moet je de kop expliciet inschakelen. Wanneer ingeschakeld moet je mogelijk Obsidian herstarten.","Open Appearance Settings":"Open Weergave Instellingen",Explorer:"Verkenner"};var ut={};var pt={};var ft={};var ht={};var vt={};var gt={"Open Commander Settings":'\u041E\u0442\u043A\u0440\u044B\u0442\u044C \u043D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 "Commander"',"Open Macro Builder":"\u041E\u0442\u043A\u0440\u044B\u0442\u044C \u043A\u043E\u043D\u0441\u0442\u0440\u0443\u043A\u0442\u043E\u0440 \u043C\u0430\u043A\u0440\u043E\u0441\u043E\u0432","Change Icon":"\u0418\u0437\u043C\u0435\u043D\u0438\u0442\u044C \u0437\u043D\u0430\u0447\u043E\u043A",Rename:"\u041F\u0435\u0440\u0435\u0438\u043C\u0435\u043D\u043E\u0432\u0430\u0442\u044C",Delete:"\u0423\u0434\u0430\u043B\u0438\u0442\u044C","Add command":"\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043A\u043E\u043C\u0430\u043D\u0434\u0443","Add new":"\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043D\u043E\u0432\u0443\u044E \u043A\u043E\u043C\u0430\u043D\u0434\u0443","This Command seems to have been removed. {{command_name}}":"\u042D\u0442\u0430 \u043A\u043E\u043C\u0430\u043D\u0434\u0430, \u043A\u0430\u0436\u0435\u0442\u0441\u044F, \u0431\u044B\u043B\u0430 \u0443\u0434\u0430\u043B\u0435\u043D\u0430. {{command_name}}","Choose a Command to add":"\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u043A\u043E\u043C\u0430\u043D\u0434\u0443 \u0434\u043B\u044F \u0434\u043E\u0431\u0430\u0432\u043B\u0435\u043D\u0438\u044F","to navigate":"\u0434\u043B\u044F \u043D\u0430\u0432\u0438\u0433\u0430\u0446\u0438\u0438","to choose an icon":"\u0432\u044B\u0431\u0440\u0430\u0442\u044C \u0437\u043D\u0430\u0447\u043E\u043A","to cancel":"\u043E\u0442\u043C\u0435\u043D\u0438\u0442\u044C","Use a custom name":"\u0418\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u044C \u0441\u043E\u0431\u0441\u0442\u0432\u0435\u043D\u043D\u043E\u0435 \u0438\u043C\u044F","Choose a custom Name for your new Command":"\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C\u0441\u043A\u043E\u0435 \u0438\u043C\u044F \u0434\u043B\u044F \u0432\u0430\u0448\u0435\u0439 \u043D\u043E\u0432\u043E\u0439 \u043A\u043E\u043C\u0430\u043D\u0434\u044B","to save":"\u0441\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C","Choose a Icon for your new Command":"\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0437\u043D\u0430\u0447\u043E\u043A \u0434\u043B\u044F \u0432\u0430\u0448\u0435\u0439 \u043D\u043E\u0432\u043E\u0439 \u043A\u043E\u043C\u0430\u043D\u0434\u044B","to choose a custom icon":"\u0432\u044B\u0431\u0440\u0430\u0442\u044C \u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C\u0441\u043A\u0438\u0439 \u0437\u043D\u0430\u0447\u043E\u043A","Remove Command":"\u0423\u0434\u0430\u043B\u0438\u0442\u044C \u043A\u043E\u043C\u0430\u043D\u0434\u0443","Double click to rename":"\u0414\u0432\u0430\u0436\u0434\u044B \u0449\u0435\u043B\u043A\u043D\u0438\u0442\u0435, \u0447\u0442\u043E\u0431\u044B \u043F\u0435\u0440\u0435\u0438\u043C\u0435\u043D\u043E\u0432\u0430\u0442\u044C","This device":"\u042D\u0442\u043E \u0443\u0441\u0442\u0440\u043E\u0439\u0441\u0442\u0432\u043E","Added by {{plugin_name}}.":"\u0414\u043E\u0431\u0430\u0432\u043B\u0435\u043D \u043F\u043B\u0430\u0433\u0438\u043D\u043E\u043C {{plugin_name}}.","Warning: This is a checked Command, meaning it might not run under every circumstance.":"\u041F\u0440\u0435\u0434\u0443\u043F\u0440\u0435\u0436\u0434\u0435\u043D\u0438\u0435: \u044D\u0442\u043E \u043F\u0440\u043E\u0432\u0435\u0440\u0435\u043D\u043D\u0430\u044F \u043A\u043E\u043C\u0430\u043D\u0434\u0430, \u0442\u043E \u0435\u0441\u0442\u044C \u043E\u043D\u0430 \u043C\u043E\u0436\u0435\u0442 \u043D\u0435 \u0432\u044B\u043F\u043E\u043B\u043D\u044F\u0442\u044C\u0441\u044F \u043F\u0440\u0438 \u043B\u044E\u0431\u044B\u0445 \u043E\u0431\u0441\u0442\u043E\u044F\u0442\u0435\u043B\u044C\u0441\u0442\u0432\u0430\u0445.","Move down":"\u0412\u043D\u0438\u0437","Move up":"\u0412\u0432\u0435\u0440\u0445","Change Mode (Currently: {{current_mode}})":"\u0418\u0437\u043C\u0435\u043D\u0438\u0442\u044C \u0440\u0435\u0436\u0438\u043C (\u0421\u0435\u0439\u0447\u0430\u0441: {{current_mode}})","Are you sure you want to delete the Command?":"\u0412\u044B \u0443\u0432\u0435\u0440\u0435\u043D\u044B, \u0447\u0442\u043E \u0445\u043E\u0442\u0438\u0442\u0435 \u0443\u0434\u0430\u043B\u0438\u0442\u044C \u043A\u043E\u043C\u0430\u043D\u0434\u0443?","Remove and don't ask again":"\u0423\u0434\u0430\u043B\u0438\u0442\u0435 \u0438 \u0431\u043E\u043B\u044C\u0448\u0435 \u043D\u0435 \u0441\u043F\u0440\u0430\u0448\u0438\u0432\u0430\u0439\u0442\u0435",Remove:"\u0423\u0434\u0430\u043B\u0438\u0442\u044C",Cancel:"\u041E\u0442\u043C\u0435\u043D\u0430","Always ask before removing?":"\u0412\u0441\u0435\u0433\u0434\u0430 \u0441\u043F\u0440\u0430\u0448\u0438\u0432\u0430\u0442\u044C \u043F\u0435\u0440\u0435\u0434 \u0443\u0434\u0430\u043B\u0435\u043D\u0438\u0435\u043C?","Always show a Popup to confirm deletion of a Command.":"\u0412\u0441\u0435\u0433\u0434\u0430 \u043F\u043E\u043A\u0430\u0437\u044B\u0432\u0430\u0442\u044C \u0432\u0441\u043F\u043B\u044B\u0432\u0430\u044E\u0449\u0435\u0435 \u043E\u043A\u043D\u043E \u0434\u043B\u044F \u043F\u043E\u0434\u0442\u0432\u0435\u0440\u0436\u0434\u0435\u043D\u0438\u044F \u0443\u0434\u0430\u043B\u0435\u043D\u0438\u044F \u043A\u043E\u043C\u0430\u043D\u0434\u044B.",'Show "Add Command" Button':"\u041F\u043E\u043A\u0430\u0437\u0430\u0442\u044C \u043A\u043D\u043E\u043F\u043A\u0443 \xAB\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043A\u043E\u043C\u0430\u043D\u0434\u0443\xBB",'Show the "Add Command" Button in every Menu. Requires restart.':"\u041F\u043E\u043A\u0430\u0437\u044B\u0432\u0430\u0442\u044C \u043A\u043D\u043E\u043F\u043A\u0443 \xAB\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043A\u043E\u043C\u0430\u043D\u0434\u0443\xBB \u0432 \u043A\u0430\u0436\u0434\u043E\u043C \u043C\u0435\u043D\u044E. \u0422\u0440\u0435\u0431\u0443\u0435\u0442\u0441\u044F \u043F\u0435\u0440\u0435\u0437\u0430\u0433\u0440\u0443\u0437\u043A\u0430.","Please restart Obsidian for these changes to take effect.":"\u041F\u043E\u0436\u0430\u043B\u0443\u0439\u0441\u0442\u0430, \u043F\u0435\u0440\u0435\u0437\u0430\u043F\u0443\u0441\u0442\u0438\u0442\u0435 Obsidian, \u0447\u0442\u043E\u0431\u044B \u044D\u0442\u0438 \u0438\u0437\u043C\u0435\u043D\u0435\u043D\u0438\u044F \u0432\u0441\u0442\u0443\u043F\u0438\u043B\u0438 \u0432 \u0441\u0438\u043B\u0443.","Enable debugging":"\u0412\u043A\u043B\u044E\u0447\u0438\u0442\u044C \u043E\u0442\u043B\u0430\u0434\u043A\u0443","Enable console output.":"\u0412\u043A\u043B\u044E\u0447\u0438\u0442\u044C \u0432\u044B\u0432\u043E\u0434 \u043A\u043E\u043D\u0441\u043E\u043B\u0438.",General:"\u041E\u0431\u0449\u0435\u0435","Editor Menu":"\u041C\u0435\u043D\u044E \u0440\u0435\u0434\u0430\u043A\u0442\u043E\u0440\u0430","File Menu":"\u041C\u0435\u043D\u044E \xAB\u0424\u0430\u0439\u043B\xBB","Left Ribbon":"\u041B\u0435\u0432\u0430\u044F \u041B\u0435\u043D\u0442\u0430","Right Ribbon":"\u041F\u0440\u0430\u0432\u0430\u044F \u041B\u0435\u043D\u0442\u0430",Titlebar:"\u0417\u0430\u0433\u043E\u043B\u043E\u0432\u043E\u043A",Statusbar:"\u0421\u0442\u0430\u0442\u0443\u0441 \u0431\u0430\u0440","Page Header":"\u041F\u0430\u043D\u0435\u043B\u044C \u0432\u043A\u043B\u0430\u0434\u043E\u043A","Support development":"\u041F\u043E\u0434\u0434\u0435\u0440\u0436\u043A\u0430 \u0440\u0430\u0437\u0440\u0430\u0431\u043E\u0442\u043A\u0438","No commands here!":"\u0417\u0434\u0435\u0441\u044C \u043D\u0435\u0442 \u043A\u043E\u043C\u0430\u043D\u0434!","Would you like to add one now?":"\u0425\u043E\u0442\u0438\u0442\u0435 \u0434\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0441\u0435\u0439\u0447\u0430\u0441?","Hide Commands":"\u0421\u043A\u0440\u044B\u0442\u044C \u043A\u043E\u043C\u0430\u043D\u0434\u044B","Choose new":"\u0412\u044B\u0431\u0440\u0430\u0442\u044C \u043D\u043E\u0432\u0443\u044E","Hide Commands of other Plugins":"\u0421\u043A\u0440\u044B\u0442\u044C \u043A\u043E\u043C\u0430\u043D\u0434\u044B \u0434\u0440\u0443\u0433\u0438\u0445 \u043F\u043B\u0430\u0433\u0438\u043D\u043E\u0432",Icon:"\u0418\u043A\u043E\u043D\u043A\u0430",Name:"\u0418\u043C\u044F","Custom Name":"\u041F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C\u0441\u043A\u043E\u0435 \u0438\u043C\u044F","Add command to all devices":"\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043A\u043E\u043C\u0430\u043D\u0434\u0443 \u043D\u0430 \u0432\u0441\u0435 \u0443\u0441\u0442\u0440\u043E\u0439\u0441\u0442\u0432\u0430","Add command only to mobile devices":"\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043A\u043E\u043C\u0430\u043D\u0434\u0443 \u0442\u043E\u043B\u044C\u043A\u043E \u0434\u043B\u044F \u043C\u043E\u0431\u0438\u043B\u044C\u043D\u044B\u0445 \u0443\u0441\u0442\u0440\u043E\u0439\u0441\u0442\u0432","Add command only to desktop devices":'\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043A\u043E\u043C\u0430\u043D\u0434\u0443 \u0442\u043E\u043B\u044C\u043A\u043E \u0434\u043B\u044F "Desktop" \u0443\u0441\u0442\u0440\u043E\u0439\u0441\u0442\u0432',"Add command only to this device":"\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043A\u043E\u043C\u0430\u043D\u0434\u0443 \u0442\u043E\u043B\u044C\u043A\u043E \u043D\u0430 \u044D\u0442\u043E \u0443\u0441\u0442\u0440\u043E\u0439\u0441\u0442\u0432\u043E",Done:"\u0413\u043E\u0442\u043E\u0432\u043E","By Johnny\u2728 and phibr0":'\u0421\u0434\u0435\u043B\u0430\u043B\u0438: "Johnny\u2728" \u0438 "phibr0"',"Leave feedback":"\u041E\u0441\u0442\u0430\u0432\u0438\u0442\u044C \u043E\u0442\u0437\u044B\u0432",Donate:"\u041F\u043E\u0436\u0435\u0440\u0442\u0432\u043E\u0432\u0430\u0442\u044C","Share feedback, issues, and ideas with our feedback form.":"\u041F\u043E\u0434\u0435\u043B\u0438\u0442\u0435\u0441\u044C \u043E\u0442\u0437\u044B\u0432\u0430\u043C\u0438, \u043F\u0440\u043E\u0431\u043B\u0435\u043C\u0430\u043C\u0438 \u0438 \u0438\u0434\u0435\u044F\u043C\u0438 \u0441 \u043F\u043E\u043C\u043E\u0449\u044C\u044E \u043D\u0430\u0448\u0435\u0439 \u0444\u043E\u0440\u043C\u044B \u043E\u0431\u0440\u0430\u0442\u043D\u043E\u0439 \u0441\u0432\u044F\u0437\u0438.","Consider donating to support development.":"\u041F\u043E\u0434\u0443\u043C\u0430\u0439\u0442\u0435 \u043E \u043F\u043E\u0436\u0435\u0440\u0442\u0432\u043E\u0432\u0430\u043D\u0438\u0438 \u0434\u043B\u044F \u043F\u043E\u0434\u0434\u0435\u0440\u0436\u043A\u0438 \u0440\u0430\u0437\u0432\u0438\u0442\u0438\u044F.",Save:"\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C","This Command is not available on this device.":"This Command is not available on this device.",Show:"\u041F\u043E\u043A\u0430\u0437\u0430\u0442\u044C",Hide:"\u0421\u043F\u0440\u044F\u0442\u0430\u0442\u044C","Hide other Commands":"\u0421\u043F\u0440\u044F\u0442\u0430\u0442\u044C \u0434\u0440\u0443\u0433\u0438\u0435 \u043A\u043E\u043C\u0430\u043D\u0434\u044B","Double click to enter custom value":"\u0414\u0432\u0430\u0436\u0434\u044B \u0449\u0435\u043B\u043A\u043D\u0438\u0442\u0435, \u0447\u0442\u043E\u0431\u044B \u0432\u0432\u0435\u0441\u0442\u0438 \u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C\u0441\u043A\u043E\u0435 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435","Choose custom spacing for Command Buttons":"\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C\u0441\u043A\u0438\u0439 \u0438\u043D\u0442\u0435\u0440\u0432\u0430\u043B \u043C\u0435\u0436\u0434\u0443 \u043A\u043E\u043C\u0430\u043D\u0434\u043D\u044B\u043C\u0438 \u043A\u043D\u043E\u043F\u043A\u0430\u043C\u0438","Change the spacing between commands. You can set different values on mobile and desktop.":"\u0418\u0437\u043C\u0435\u043D\u0438\u0442\u0435 \u0440\u0430\u0441\u0441\u0442\u043E\u044F\u043D\u0438\u0435 \u043C\u0435\u0436\u0434\u0443 \u043A\u043E\u043C\u0430\u043D\u0434\u0430\u043C\u0438.",Warning:"\u041F\u0440\u0435\u0434\u0443\u043F\u0440\u0435\u0436\u0434\u0435\u043D\u0438\u0435","As of Obsidian 0.16.0 you need to explicitly enable the View Header.":"\u041D\u0430\u0447\u0438\u043D\u0430\u044F \u0441 Obsidian 0.16.0 \u0432\u0430\u043C \u043D\u0435\u043E\u0431\u0445\u043E\u0434\u0438\u043C\u043E \u044F\u0432\u043D\u043E \u0432\u043A\u043B\u044E\u0447\u0438\u0442\u044C \u0441\u0442\u0440\u043E\u043A\u0443 \u0437\u0430\u0433\u043E\u043B\u043E\u0432\u043A\u0430 \u0432\u043A\u043B\u0430\u0434\u043A\u0438. \u041F\u043E\u0441\u043B\u0435 \u0432\u043A\u043B\u044E\u0447\u0435\u043D\u0438\u044F \u0432\u0430\u043C \u043C\u043E\u0436\u0435\u0442 \u043F\u043E\u0442\u0440\u0435\u0431\u043E\u0432\u0430\u0442\u044C\u0441\u044F \u043F\u0435\u0440\u0435\u0437\u0430\u043F\u0443\u0441\u0442\u0438\u0442\u044C Obsidian.","Open Appearance Settings":"\u041E\u0442\u043A\u0440\u043E\u0439\u0442\u0435 \u043D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 \u0432\u043D\u0435\u0448\u043D\u0435\u0433\u043E \u0432\u0438\u0434\u0430",Explorer:"\u0424\u0430\u0439\u043B\u043E\u0432\u044B\u0439 \u043C\u0435\u043D\u0435\u0434\u0436\u0435\u0440"};var bt={};var Ct={"Open Commander Settings":"\u6253\u5F00 Commander \u8BBE\u7F6E","Open Macro Builder":"\u6253\u5F00\u5B8F\u6307\u4EE4\u751F\u6210\u5668","Change Icon":"\u66F4\u6362\u56FE\u6807",Rename:"\u91CD\u547D\u540D",Delete:"\u5220\u9664","Add command":"\u6DFB\u52A0\u547D\u4EE4","Add new":"\u6DFB\u52A0\u65B0\u547D\u4EE4","This Command seems to have been removed. {{command_name}}":"\u8BE5\u547D\u4EE4\u4F3C\u4E4E\u5DF2\u88AB\u79FB\u9664\u3002{{command_name}}","Choose a Command to add":"\u9009\u62E9\u4E00\u4E2A\u547D\u4EE4\u5E76\u6DFB\u52A0","to navigate":"\u5BFC\u822A","to choose an icon":"\u9009\u4E2D\u4E00\u4E2A\u56FE\u6807","to cancel":"\u53D6\u6D88","Use a custom name":"\u4F7F\u7528\u4E00\u4E2A\u81EA\u5B9A\u4E49\u540D\u79F0","Choose a custom Name for your new Command":"\u4E3A\u4F60\u7684\u65B0\u547D\u4EE4\u9009\u62E9\u4E00\u4E2A\u81EA\u5B9A\u4E49\u540D\u79F0","to save":"\u4FDD\u5B58","Choose a Icon for your new Command":"\u4E3A\u4F60\u7684\u65B0\u547D\u4EE4\u9009\u62E9\u4E00\u4E2A\u56FE\u6807","to choose a custom icon":"\u9009\u62E9\u4E00\u4E2A\u81EA\u5B9A\u4E49\u56FE\u6807","Remove Command":"\u79FB\u9664\u547D\u4EE4","Double click to rename":"\u53CC\u51FB\u4EE5\u91CD\u547D\u540D","This device":"\u8BE5\u8BBE\u5907","Added by {{plugin_name}}.":"\u7531{{plugin_name}}\u6DFB\u52A0\u3002","Warning: This is a checked Command, meaning it might not run under every circumstance.":"\u8B66\u544A\uFF1A\u8FD9\u662F\u4E00\u4E2A\u53D7\u68C0\u7684\u547D\u4EE4\uFF0C\u8FD9\u610F\u5473\u7740\u5B83\u672A\u5FC5\u80FD\u5728\u6240\u4EE5\u73AF\u5883\u4E0B\u8FD0\u884C\u3002","Move down":"\u5411\u4E0B\u79FB\u52A8","Move up":"\u5411\u4E0A\u79FB\u52A8","Change Mode (Currently: {{current_mode}})":"\u5207\u6362\u6A21\u5F0F\uFF08\u5F53\u524D\uFF1A{{current_mode}}\uFF09","Are you sure you want to delete the Command?":"\u662F\u5426\u786E\u8BA4\u79FB\u9664\u8BE5\u547D\u4EE4\uFF1F","Remove and don't ask again":"\u79FB\u9664\u4E14\u4E0D\u8981\u518D\u8BE2\u95EE",Remove:"\u79FB\u9664",Cancel:"\u53D6\u6D88","Always ask before removing?":"\u5728\u79FB\u9664\u524D\u603B\u662F\u8BE2\u95EE\uFF1F","Always show a Popup to confirm deletion of a Command.":"\u5728\u786E\u8BA4\u79FB\u9664\u547D\u4EE4\u524D\u603B\u662F\u5F39\u7A97\u3002",'Show "Add Command" Button':"\u663E\u793A\u201C\u6DFB\u52A0\u547D\u4EE4\u201D\u6309\u94AE",'Show the "Add Command" Button in every Menu. Requires restart.':"\u5728\u6BCF\u4E2A\u83DC\u5355\u90FD\u5C55\u793A\u201C\u6DFB\u52A0\u547D\u4EE4\u201D\u6309\u94AE\u3002\u9700\u8981\u91CD\u542F\u3002","Please restart Obsidian for these changes to take effect.":"\u8BF7\u91CD\u542F Obsidian \u4EE5\u4F7F\u8FD9\u4E9B\u66F4\u6539\u751F\u6548\u3002","Enable debugging":"\u542F\u7528\u9664\u9519","Enable console output.":"\u542F\u7528\u63A7\u5236\u53F0\u8F93\u51FA\u3002",General:"\u901A\u7528","Editor Menu":"\u7F16\u8F91\u5668\u83DC\u5355","File Menu":"\u6587\u4EF6\u83DC\u5355","Left Ribbon":"\u5DE6\u4FA7\u8FB9\u680F","Right Ribbon":"\u53F3\u4FA7\u8FB9\u680F",Titlebar:"\u6807\u9898\u680F",Statusbar:"\u72B6\u6001\u680F","Page Header":"\u9875\u9996","Support development":"\u652F\u6301\u5F00\u53D1","No commands here!":"\u8FD9\u91CC\u6CA1\u6709\u547D\u4EE4\uFF01","Would you like to add one now?":"\u4F60\u73B0\u5728\u60F3\u8981\u52A0\u4E00\u4E2A\u5417\uFF1F","Hide Commands":"\u9690\u85CF\u547D\u4EE4","Choose new":"\u9009\u62E9\u65B0\u7684","Hide Commands of other Plugins":"\u9690\u85CF\u5176\u4ED6\u63D2\u4EF6\u7684\u547D\u4EE4",Icon:"\u56FE\u6807",Name:"\u540D\u79F0","Custom Name":"\u81EA\u5B9A\u4E49\u540D\u79F0","Add command to all devices":"\u5411\u6240\u6709\u8BBE\u5907\u6DFB\u52A0\u547D\u4EE4","Add command only to mobile devices":"\u53EA\u5411\u79FB\u52A8\u8BBE\u5907\u6DFB\u52A0\u547D\u4EE4","Add command only to desktop devices":"\u53EA\u5411\u684C\u9762\u8BBE\u5907\u6DFB\u52A0\u547D\u4EE4","Add command only to this device":"\u53EA\u5411\u5F53\u524D\u8BBE\u5907\u6DFB\u52A0\u547D\u4EE4",Done:"\u5B8C\u6210","By Johnny\u2728 and phibr0":"\u7531 Johnny\u2728 \u548C phibr0 \u5F00\u53D1","Leave feedback":"\u7559\u4E0B\u53CD\u9988",Donate:"\u6350\u8D60","Share feedback, issues, and ideas with our feedback form.":"\u4EE5\u6211\u4EEC\u7684\u53CD\u9988\u8868\uFF0C\u5206\u4EAB\u53CD\u9988\u3001\u8BAE\u9898\u6216\u8005\u4F60\u7684\u60F3\u6CD5\u3002","Consider donating to support development.":"\u8003\u8651\u6350\u8D60\u4EE5\u652F\u6301\u5F00\u53D1\u3002",Save:"\u4FDD\u5B58","This Command is not available on this device.":"\u8FD9\u4E00\u547D\u4EE4\u5728\u5F53\u524D\u8BBE\u5907\u4E0D\u53EF\u7528\u3002",Show:"\u663E\u793A",Hide:"\u9690\u85CF","Hide other Commands":"\u9690\u85CF\u5176\u4F59\u547D\u4EE4","Double click to enter custom value":"\u53CC\u51FB\u4EE5\u6DFB\u52A0\u81EA\u5B9A\u4E49\u503C","Choose custom spacing for Command Buttons":"\u4E3A\u547D\u4EE4\u6309\u94AE\u9009\u62E9\u81EA\u5B9A\u4E49\u95F4\u8DDD","Change the spacing between commands. You can set different values on mobile and desktop.":"\u6539\u53D8\u547D\u4EE4\u4E4B\u95F4\u7684\u95F4\u8DDD\u3002\u4F60\u53EF\u4EE5\u4E3A\u79FB\u52A8\u548C\u684C\u9762\u8BBE\u5907\u8BBE\u7F6E\u4E0D\u540C\u7684\u503C\u3002"};var _t={};var ua={ar:Qo,cs:et,da:ot,de:tt,en:go,"en-gb":nt,es:at,fr:it,hi:rt,id:st,it:ct,ja:dt,ko:mt,nl:lt,nn:ut,pl:pt,pt:ft,"pt-br":ht,ro:vt,ru:gt,tr:bt,"zh-cn":Ct,"zh-tw":_t},bo=ua[Co.moment.locale()];function u(e){return bo||console.error("Error: dictionary locale not found",Co.moment.locale()),bo&&bo[e]||go[e]}var ne=class extends Ve.FuzzySuggestModal{constructor(o){super(app);this.plugin=o,this.commands=Object.values(app.commands.commands),this.setPlaceholder(u("Choose a Command to add")),this.setInstructions([{command:"\u2191\u2193",purpose:u("to navigate")},{command:"\u21B5",purpose:u("to choose an icon")},{command:"esc",purpose:u("to cancel")}])}async awaitSelection(){return this.open(),new Promise((o,n)=>{this.onChooseItem=a=>o(a),this.onClose=()=>window.setTimeout(()=>n("No Command selected"),0)})}renderSuggestion(o,n){if(n.addClass("mod-complex"),n.createDiv({cls:"suggestion-content"}).createDiv({cls:"suggestion-title"}).setText(o.item.name),o.item.icon){let i=n.createDiv({cls:"suggestion-aux"});(0,Ve.setIcon)(i.createSpan({cls:"suggestion-flair"}),o.item.icon)}}getItems(){return this.commands}getItemText(o){return o.name}onChooseItem(o,n){}};var yt={confirmDeletion:!0,showAddCommand:!0,debug:!1,editorMenu:[],fileMenu:[],leftRibbon:[],rightRibbon:[],titleBar:[],statusBar:[],pageHeader:[],macros:[],explorer:[],hide:{statusbar:[],leftRibbon:[]},spacing:8,advancedToolbar:{rowHeight:48,rowCount:1,spacing:0,buttonWidth:48,columnLayout:!1,mappedIcons:[],tooltips:!1,heightOffset:0}},wt=["activity","airplay","alarm-check","alarm-clock-off","alarm-clock","alarm-minus","alarm-plus","album","alert-circle","alert-octagon","alert-triangle","align-center-horizontal","align-center-vertical","align-center","align-end-horizontal","align-end-vertical","align-horizontal-distribute-center","align-horizontal-distribute-end","align-horizontal-distribute-start","align-horizontal-justify-center","align-horizontal-justify-end","align-horizontal-justify-start","align-horizontal-space-around","align-horizontal-space-between","align-justify","align-left","align-right","align-start-horizontal","align-start-vertical","align-vertical-distribute-center","align-vertical-distribute-end","align-vertical-distribute-start","align-vertical-justify-center","align-vertical-justify-end","align-vertical-justify-start","align-vertical-space-around","align-vertical-space-between","anchor","aperture","archive","arrow-big-down","arrow-big-left","arrow-big-right","arrow-big-up","arrow-down-circle","arrow-down-left","arrow-down-right","arrow-down","arrow-left-circle","arrow-left-right","arrow-left","arrow-right-circle","arrow-right","arrow-up-circle","arrow-up-left","arrow-up-right","arrow-up","asterisk","at-sign","award","axe","banknote","bar-chart-2","bar-chart","baseline","battery-charging","battery-full","battery-low","battery-medium","battery","beaker","bell-minus","bell-off","bell-plus","bell-ring","bell","bike","binary","bitcoin","bluetooth-connected","bluetooth-off","bluetooth-searching","bluetooth","bold","book-open","book","bookmark-minus","bookmark-plus","bookmark","bot","box-select","box","briefcase","brush","bug","building-2","building","bus","calculator","calendar","camera-off","camera","car","carrot","cast","check-circle-2","check-circle","check-square","check","chevron-down","chevron-first","chevron-last","chevron-left","chevron-right","chevron-up","chevrons-down-up","chevrons-down","chevrons-left","chevrons-right","chevrons-up-down","chevrons-up","chrome","circle-slashed","circle","clipboard-check","clipboard-copy","clipboard-list","clipboard-x","clipboard","clock-1","clock-10","clock-11","clock-12","clock-2","clock-3","clock-4","clock-5","clock-6","clock-7","clock-8","clock-9","lucide-clock","cloud-drizzle","cloud-fog","cloud-hail","cloud-lightning","cloud-moon","cloud-off","cloud-rain-wind","cloud-rain","cloud-snow","cloud-sun","lucide-cloud","cloudy","clover","code-2","code","codepen","codesandbox","coffee","coins","columns","command","compass","contact","contrast","cookie","copy","copyleft","copyright","corner-down-left","corner-down-right","corner-left-down","corner-left-up","corner-right-down","corner-right-up","corner-up-left","corner-up-right","cpu","credit-card","crop","lucide-cross","crosshair","crown","currency","database","delete","dice-1","dice-2","dice-3","dice-4","dice-5","dice-6","disc","divide-circle","divide-square","divide","dollar-sign","download-cloud","download","dribbble","droplet","droplets","drumstick","edit-2","edit-3","edit","egg","equal-not","equal","eraser","euro","expand","external-link","eye-off","eye","facebook","fast-forward","feather","figma","file-check-2","file-check","file-code","file-digit","file-input","file-minus-2","file-minus","file-output","file-plus-2","file-plus","file-search","file-text","file-x-2","file-x","file","files","film","filter","flag-off","flag-triangle-left","flag-triangle-right","flag","flame","flashlight-off","flashlight","flask-conical","flask-round","folder-minus","folder-open","folder-plus","lucide-folder","form-input","forward","frame","framer","frown","function-square","gamepad-2","gamepad","gauge","gavel","gem","ghost","gift","git-branch-plus","git-branch","git-commit","git-fork","git-merge","git-pull-request","github","gitlab","glasses","globe-2","globe","grab","graduation-cap","grid","grip-horizontal","grip-vertical","hammer","hand-metal","hand","hard-drive","hard-hat","hash","haze","headphones","heart","help-circle","hexagon","highlighter","history","home","image-minus","image-off","image-plus","image","import","inbox","indent","indian-rupee","infinity","lucide-info","inspect","instagram","italic","japanese-yen","key","keyboard","landmark","lucide-languages","laptop-2","laptop","lasso-select","lasso","layers","layout-dashboard","layout-grid","layout-list","layout-template","layout","library","life-buoy","lightbulb-off","lightbulb","link-2-off","link-2","lucide-link","linkedin","list-checks","list-minus","list-ordered","list-plus","list-x","list","loader-2","loader","locate-fixed","locate-off","locate","lock","log-in","log-out","mail","map-pin","map","maximize-2","maximize","megaphone","meh","menu","message-circle","message-square","mic-off","mic","minimize-2","minimize","minus-circle","minus-square","minus","monitor-off","monitor-speaker","monitor","moon","more-horizontal","more-vertical","mountain-snow","mountain","mouse-pointer-2","mouse-pointer-click","mouse-pointer","mouse","move-diagonal-2","move-diagonal","move-horizontal","move-vertical","move","music","navigation-2","navigation","network","octagon","option","outdent","package-check","package-minus","package-plus","package-search","package-x","package","palette","palmtree","paperclip","pause-circle","pause-octagon","pause","pen-tool","lucide-pencil","percent","person-standing","phone-call","phone-forwarded","phone-incoming","phone-missed","phone-off","phone-outgoing","phone","pie-chart","piggy-bank","lucide-pin","pipette","plane","play-circle","play","plug-zap","plus-circle","plus-square","plus","pocket","podcast","pointer","pound-sterling","power-off","power","printer","qr-code","quote","radio-receiver","radio","redo","refresh-ccw","refresh-cw","regex","repeat-1","repeat","reply-all","reply","rewind","rocket","rocking-chair","rotate-ccw","rotate-cw","rss","ruler","russian-ruble","save","scale","scan-line","scan","scissors","screen-share-off","screen-share","lucide-search","send","separator-horizontal","separator-vertical","server-crash","server-off","server","settings-2","settings","share-2","share","sheet","shield-alert","shield-check","shield-close","shield-off","shield","shirt","shopping-bag","shopping-cart","shovel","shrink","shuffle","sidebar-close","sidebar-open","sidebar","sigma","signal-high","signal-low","signal-medium","signal-zero","signal","skip-back","skip-forward","skull","slack","slash","sliders","smartphone-charging","smartphone","smile","snowflake","sort-asc","sort-desc","speaker","sprout","square","star-half","lucide-star","stop-circle","stretch-horizontal","stretch-vertical","strikethrough","subscript","sun","sunrise","sunset","superscript","swiss-franc","switch-camera","table","tablet","tag","target","tent","terminal-square","terminal","text-cursor-input","text-cursor","thermometer-snowflake","thermometer-sun","thermometer","thumbs-down","thumbs-up","ticket","timer-off","timer-reset","timer","toggle-left","toggle-right","tornado","trash-2","lucide-trash","trello","trending-down","trending-up","triangle","truck","tv-2","tv","twitch","twitter","type","umbrella","underline","undo","unlink-2","unlink","unlock","upload-cloud","upload","user-check","user-minus","user-plus","user-x","user","users","verified","vibrate","video-off","video","view","voicemail","volume-1","volume-2","volume-x","volume","wallet","wand","watch","waves","webcam","wifi-off","wifi","wind","wrap-text","wrench","x-circle","x-octagon","x-square","x","youtube","zap-off","zap","zoom-in","zoom-out","search-large"];var We=require("obsidian");var R=class extends We.FuzzySuggestModal{constructor(o){super(app);this.plugin=o,this.setPlaceholder(u("Choose a Icon for your new Command")),this.setInstructions([{command:"\u2191\u2193",purpose:u("to navigate")},{command:"\u21B5",purpose:u("to choose a custom icon")},{command:"esc",purpose:u("to cancel")}])}async awaitSelection(){return this.open(),new Promise((o,n)=>{this.onChooseItem=a=>o(a),this.onClose=()=>window.setTimeout(()=>n("No Icon selected"),0)})}renderSuggestion(o,n){n.addClass("mod-complex"),n.createDiv({cls:"suggestion-content"}).createDiv({cls:"suggestion-title"}).setText(o.item.replace(/-/g," ").replace(/(^\w{1})|(\s+\w{1})/g,c=>c.toUpperCase()));let i=n.createDiv({cls:"suggestion-aux"});(0,We.setIcon)(i.createSpan({cls:"suggestion-flair"}),o.item)}getItems(){return wt}getItemText(o){return o}onChooseItem(o,n){}};var eo=require("obsidian");var kt=require("obsidian");var U=class extends kt.SuggestModal{constructor(o){super(app);this.defaultName=o;this.setPlaceholder(u("Use a custom name")),this.resultContainerEl.style.display="none",this.setInstructions([{command:"",purpose:u("Choose a custom Name for your new Command")},{command:"\u21B5",purpose:u("to save")},{command:"esc",purpose:u("to cancel")}])}onOpen(){var a;super.onOpen(),this.inputEl.value=this.defaultName;let o=createDiv({cls:"cmdr-name-input-wrapper"});(a=this.inputEl.parentNode)==null||a.insertBefore(o,this.inputEl),o.appendChild(this.inputEl),o.parentElement.style.display="block";let n=createEl("button",{text:u("Save"),cls:"mod-cta"});n.onclick=i=>this.selectSuggestion(this.inputEl.value,i),o.appendChild(n)}async awaitSelection(){return this.open(),new Promise((o,n)=>{this.onChooseSuggestion=a=>o(a),this.onClose=()=>window.setTimeout(()=>n("No Name selected"),0)})}getSuggestions(o){return[o]}renderSuggestion(o,n){}onChooseSuggestion(o,n){}};var Je,_,Pt,pa,_e,Mt,It,Ue={},At=[],fa=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function ae(e,t){for(var o in t)e[o]=t[o];return e}function Tt(e){var t=e.parentNode;t&&t.removeChild(e)}function r(e,t,o){var n,a,i,c={};for(i in t)i=="key"?n=t[i]:i=="ref"?a=t[i]:c[i]=t[i];if(arguments.length>2&&(c.children=arguments.length>3?Je.call(arguments,2):o),typeof e=="function"&&e.defaultProps!=null)for(i in e.defaultProps)c[i]===void 0&&(c[i]=e.defaultProps[i]);return je(e,c,n,a,null)}function je(e,t,o,n,a){var i={type:e,props:t,key:o,ref:n,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:a==null?++Pt:a};return a==null&&_.vnode!=null&&_.vnode(i),i}function P(e){return e.children}function G(e,t){this.props=e,this.context=t}function ye(e,t){if(t==null)return e.__?ye(e.__,e.__.__k.indexOf(e)+1):null;for(var o;t<e.__k.length;t++)if((o=e.__k[t])!=null&&o.__e!=null)return o.__e;return typeof e.type=="function"?ye(e):null}function Lt(e){var t,o;if((e=e.__)!=null&&e.__c!=null){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if((o=e.__k[t])!=null&&o.__e!=null){e.__e=e.__c.base=o.__e;break}return Lt(e)}}function _o(e){(!e.__d&&(e.__d=!0)&&_e.push(e)&&!qe.__r++||Mt!==_.debounceRendering)&&((Mt=_.debounceRendering)||setTimeout)(qe)}function qe(){for(var e;qe.__r=_e.length;)e=_e.sort(function(t,o){return t.__v.__b-o.__v.__b}),_e=[],e.some(function(t){var o,n,a,i,c,m;t.__d&&(c=(i=(o=t).__v).__e,(m=o.__P)&&(n=[],(a=ae({},i)).__v=i.__v+1,yo(m,i,a,o.__n,m.ownerSVGElement!==void 0,i.__h!=null?[c]:null,n,c==null?ye(i):c,i.__h),Ht(n,i),i.__e!=c&&Lt(i)))})}function Nt(e,t,o,n,a,i,c,m,l,h){var s,g,C,f,b,y,v,A=n&&n.__k||At,w=A.length;for(o.__k=[],s=0;s<t.length;s++)if((f=o.__k[s]=(f=t[s])==null||typeof f=="boolean"?null:typeof f=="string"||typeof f=="number"||typeof f=="bigint"?je(null,f,null,null,f):Array.isArray(f)?je(P,{children:f},null,null,null):f.__b>0?je(f.type,f.props,f.key,f.ref?f.ref:null,f.__v):f)!=null){if(f.__=o,f.__b=o.__b+1,(C=A[s])===null||C&&f.key==C.key&&f.type===C.type)A[s]=void 0;else for(g=0;g<w;g++){if((C=A[g])&&f.key==C.key&&f.type===C.type){A[g]=void 0;break}C=null}yo(e,f,C=C||Ue,a,i,c,m,l,h),b=f.__e,(g=f.ref)&&C.ref!=g&&(v||(v=[]),C.ref&&v.push(C.ref,null,f),v.push(g,f.__c||b,f)),b!=null?(y==null&&(y=b),typeof f.type=="function"&&f.__k===C.__k?f.__d=l=Dt(f,l,e):l=Bt(e,f,C,A,b,l),typeof o.type=="function"&&(o.__d=l)):l&&C.__e==l&&l.parentNode!=e&&(l=ye(C))}for(o.__e=y,s=w;s--;)A[s]!=null&&zt(A[s],A[s]);if(v)for(s=0;s<v.length;s++)Rt(v[s],v[++s],v[++s])}function Dt(e,t,o){for(var n,a=e.__k,i=0;a&&i<a.length;i++)(n=a[i])&&(n.__=e,t=typeof n.type=="function"?Dt(n,t,o):Bt(o,n,n,a,n.__e,t));return t}function we(e,t){return t=t||[],e==null||typeof e=="boolean"||(Array.isArray(e)?e.some(function(o){we(o,t)}):t.push(e)),t}function Bt(e,t,o,n,a,i){var c,m,l;if(t.__d!==void 0)c=t.__d,t.__d=void 0;else if(o==null||a!=i||a.parentNode==null)e:if(i==null||i.parentNode!==e)e.appendChild(a),c=null;else{for(m=i,l=0;(m=m.nextSibling)&&l<n.length;l+=1)if(m==a)break e;e.insertBefore(a,i),c=i}return c!==void 0?c:a.nextSibling}function ha(e,t,o,n,a){var i;for(i in o)i==="children"||i==="key"||i in t||Ze(e,i,null,o[i],n);for(i in t)a&&typeof t[i]!="function"||i==="children"||i==="key"||i==="value"||i==="checked"||o[i]===t[i]||Ze(e,i,t[i],o[i],n)}function Et(e,t,o){t[0]==="-"?e.setProperty(t,o):e[t]=o==null?"":typeof o!="number"||fa.test(t)?o:o+"px"}function Ze(e,t,o,n,a){var i;e:if(t==="style")if(typeof o=="string")e.style.cssText=o;else{if(typeof n=="string"&&(e.style.cssText=n=""),n)for(t in n)o&&t in o||Et(e.style,t,"");if(o)for(t in o)n&&o[t]===n[t]||Et(e.style,t,o[t])}else if(t[0]==="o"&&t[1]==="n")i=t!==(t=t.replace(/Capture$/,"")),t=t.toLowerCase()in e?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+i]=o,o?n||e.addEventListener(t,i?St:xt,i):e.removeEventListener(t,i?St:xt,i);else if(t!=="dangerouslySetInnerHTML"){if(a)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if(t!=="href"&&t!=="list"&&t!=="form"&&t!=="tabIndex"&&t!=="download"&&t in e)try{e[t]=o==null?"":o;break e}catch(c){}typeof o=="function"||(o==null||o===!1&&t.indexOf("-")==-1?e.removeAttribute(t):e.setAttribute(t,o))}}function xt(e){this.l[e.type+!1](_.event?_.event(e):e)}function St(e){this.l[e.type+!0](_.event?_.event(e):e)}function yo(e,t,o,n,a,i,c,m,l){var h,s,g,C,f,b,y,v,A,w,H,ee,Be,ue,pe,j=t.type;if(t.constructor!==void 0)return null;o.__h!=null&&(l=o.__h,m=t.__e=o.__e,t.__h=null,i=[m]),(h=_.__b)&&h(t);try{e:if(typeof j=="function"){if(v=t.props,A=(h=j.contextType)&&n[h.__c],w=h?A?A.props.value:h.__:n,o.__c?y=(s=t.__c=o.__c).__=s.__E:("prototype"in j&&j.prototype.render?t.__c=s=new j(v,w):(t.__c=s=new G(v,w),s.constructor=j,s.render=ga),A&&A.sub(s),s.props=v,s.state||(s.state={}),s.context=w,s.__n=n,g=s.__d=!0,s.__h=[],s._sb=[]),s.__s==null&&(s.__s=s.state),j.getDerivedStateFromProps!=null&&(s.__s==s.state&&(s.__s=ae({},s.__s)),ae(s.__s,j.getDerivedStateFromProps(v,s.__s))),C=s.props,f=s.state,g)j.getDerivedStateFromProps==null&&s.componentWillMount!=null&&s.componentWillMount(),s.componentDidMount!=null&&s.__h.push(s.componentDidMount);else{if(j.getDerivedStateFromProps==null&&v!==C&&s.componentWillReceiveProps!=null&&s.componentWillReceiveProps(v,w),!s.__e&&s.shouldComponentUpdate!=null&&s.shouldComponentUpdate(v,s.__s,w)===!1||t.__v===o.__v){for(s.props=v,s.state=s.__s,t.__v!==o.__v&&(s.__d=!1),s.__v=t,t.__e=o.__e,t.__k=o.__k,t.__k.forEach(function(fe){fe&&(fe.__=t)}),H=0;H<s._sb.length;H++)s.__h.push(s._sb[H]);s._sb=[],s.__h.length&&c.push(s);break e}s.componentWillUpdate!=null&&s.componentWillUpdate(v,s.__s,w),s.componentDidUpdate!=null&&s.__h.push(function(){s.componentDidUpdate(C,f,b)})}if(s.context=w,s.props=v,s.__v=t,s.__P=e,ee=_.__r,Be=0,"prototype"in j&&j.prototype.render){for(s.state=s.__s,s.__d=!1,ee&&ee(t),h=s.render(s.props,s.state,s.context),ue=0;ue<s._sb.length;ue++)s.__h.push(s._sb[ue]);s._sb=[]}else do s.__d=!1,ee&&ee(t),h=s.render(s.props,s.state,s.context),s.state=s.__s;while(s.__d&&++Be<25);s.state=s.__s,s.getChildContext!=null&&(n=ae(ae({},n),s.getChildContext())),g||s.getSnapshotBeforeUpdate==null||(b=s.getSnapshotBeforeUpdate(C,f)),pe=h!=null&&h.type===P&&h.key==null?h.props.children:h,Nt(e,Array.isArray(pe)?pe:[pe],t,o,n,a,i,c,m,l),s.base=t.__e,t.__h=null,s.__h.length&&c.push(s),y&&(s.__E=s.__=null),s.__e=!1}else i==null&&t.__v===o.__v?(t.__k=o.__k,t.__e=o.__e):t.__e=va(o.__e,t,o,n,a,i,c,l);(h=_.diffed)&&h(t)}catch(fe){t.__v=null,(l||i!=null)&&(t.__e=m,t.__h=!!l,i[i.indexOf(m)]=null),_.__e(fe,t,o)}}function Ht(e,t){_.__c&&_.__c(t,e),e.some(function(o){try{e=o.__h,o.__h=[],e.some(function(n){n.call(o)})}catch(n){_.__e(n,o.__v)}})}function va(e,t,o,n,a,i,c,m){var l,h,s,g=o.props,C=t.props,f=t.type,b=0;if(f==="svg"&&(a=!0),i!=null){for(;b<i.length;b++)if((l=i[b])&&"setAttribute"in l==!!f&&(f?l.localName===f:l.nodeType===3)){e=l,i[b]=null;break}}if(e==null){if(f===null)return document.createTextNode(C);e=a?document.createElementNS("http://www.w3.org/2000/svg",f):document.createElement(f,C.is&&C),i=null,m=!1}if(f===null)g===C||m&&e.data===C||(e.data=C);else{if(i=i&&Je.call(e.childNodes),h=(g=o.props||Ue).dangerouslySetInnerHTML,s=C.dangerouslySetInnerHTML,!m){if(i!=null)for(g={},b=0;b<e.attributes.length;b++)g[e.attributes[b].name]=e.attributes[b].value;(s||h)&&(s&&(h&&s.__html==h.__html||s.__html===e.innerHTML)||(e.innerHTML=s&&s.__html||""))}if(ha(e,C,g,a,m),s)t.__k=[];else if(b=t.props.children,Nt(e,Array.isArray(b)?b:[b],t,o,n,a&&f!=="foreignObject",i,c,i?i[0]:o.__k&&ye(o,0),m),i!=null)for(b=i.length;b--;)i[b]!=null&&Tt(i[b]);m||("value"in C&&(b=C.value)!==void 0&&(b!==e.value||f==="progress"&&!b||f==="option"&&b!==g.value)&&Ze(e,"value",b,g.value,!1),"checked"in C&&(b=C.checked)!==void 0&&b!==e.checked&&Ze(e,"checked",b,g.checked,!1))}return e}function Rt(e,t,o){try{typeof e=="function"?e(t):e.current=t}catch(n){_.__e(n,o)}}function zt(e,t,o){var n,a;if(_.unmount&&_.unmount(e),(n=e.ref)&&(n.current&&n.current!==e.__e||Rt(n,null,t)),(n=e.__c)!=null){if(n.componentWillUnmount)try{n.componentWillUnmount()}catch(i){_.__e(i,t)}n.base=n.__P=null,e.__c=void 0}if(n=e.__k)for(a=0;a<n.length;a++)n[a]&&zt(n[a],t,o||typeof e.type!="function");o||e.__e==null||Tt(e.__e),e.__=e.__e=e.__d=void 0}function ga(e,t,o){return this.constructor(e,o)}function W(e,t,o){var n,a,i;_.__&&_.__(e,t),a=(n=typeof o=="function")?null:o&&o.__k||t.__k,i=[],yo(t,e=(!n&&o||t).__k=r(P,null,[e]),a||Ue,Ue,t.ownerSVGElement!==void 0,!n&&o?[o]:a?null:t.firstChild?Je.call(t.childNodes):null,i,!n&&o?o:a?a.__e:t.firstChild,n),Ht(i,e)}function Xe(e,t){var o={__c:t="__cC"+It++,__:e,Consumer:function(n,a){return n.children(a)},Provider:function(n){var a,i;return this.getChildContext||(a=[],(i={})[t]=this,this.getChildContext=function(){return i},this.shouldComponentUpdate=function(c){this.props.value!==c.value&&a.some(_o)},this.sub=function(c){a.push(c);var m=c.componentWillUnmount;c.componentWillUnmount=function(){a.splice(a.indexOf(c),1),m&&m.call(c)}}),n.children}};return o.Provider.__=o.Consumer.contextType=o}Je=At.slice,_={__e:function(e,t,o,n){for(var a,i,c;t=t.__;)if((a=t.__c)&&!a.__)try{if((i=a.constructor)&&i.getDerivedStateFromError!=null&&(a.setState(i.getDerivedStateFromError(e)),c=a.__d),a.componentDidCatch!=null&&(a.componentDidCatch(e,n||{}),c=a.__d),c)return a.__E=a}catch(m){e=m}throw e}},Pt=0,pa=function(e){return e!=null&&e.constructor===void 0},G.prototype.setState=function(e,t){var o;o=this.__s!=null&&this.__s!==this.state?this.__s:this.__s=ae({},this.state),typeof e=="function"&&(e=e(ae({},o),this.props)),e&&ae(o,e),e!=null&&this.__v&&(t&&this._sb.push(t),_o(this))},G.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),_o(this))},G.prototype.render=P,_e=[],qe.__r=0,It=0;var ke,z,wo,Ot,Ge=0,Zt=[],Ye=[],Ft=_.__b,Vt=_.__r,Wt=_.diffed,jt=_.__c,Ut=_.unmount;function Ke(e,t){_.__h&&_.__h(z,e,Ge||t),Ge=0;var o=z.__H||(z.__H={__:[],__h:[]});return e>=o.__.length&&o.__.push({__V:Ye}),o.__[e]}function D(e){return Ge=1,Jt(Xt,e)}function Jt(e,t,o){var n=Ke(ke++,2);if(n.t=e,!n.__c&&(n.__=[o?o(t):Xt(void 0,t),function(i){var c=n.__N?n.__N[0]:n.__[0],m=n.t(c,i);c!==m&&(n.__N=[m,n.__[1]],n.__c.setState({}))}],n.__c=z,!z.u)){z.u=!0;var a=z.shouldComponentUpdate;z.shouldComponentUpdate=function(i,c,m){if(!n.__c.__H)return!0;var l=n.__c.__H.__.filter(function(s){return s.__c});if(l.every(function(s){return!s.__N}))return!a||a.call(this,i,c,m);var h=!1;return l.forEach(function(s){if(s.__N){var g=s.__[0];s.__=s.__N,s.__N=void 0,g!==s.__[0]&&(h=!0)}}),!(!h&&n.__c.props===i)&&(!a||a.call(this,i,c,m))}}return n.__N||n.__}function O(e,t){var o=Ke(ke++,3);!_.__s&&Eo(o.__H,t)&&(o.__=e,o.i=t,z.__H.__h.push(o))}function Mo(e,t){var o=Ke(ke++,4);!_.__s&&Eo(o.__H,t)&&(o.__=e,o.i=t,z.__h.push(o))}function K(e){return Ge=5,Qe(function(){return{current:e}},[])}function Qe(e,t){var o=Ke(ke++,7);return Eo(o.__H,t)?(o.__V=e(),o.i=t,o.__h=e,o.__V):o.__}function ba(){for(var e;e=Zt.shift();)if(e.__P&&e.__H)try{e.__H.__h.forEach($e),e.__H.__h.forEach(ko),e.__H.__h=[]}catch(t){e.__H.__h=[],_.__e(t,e.__v)}}_.__b=function(e){z=null,Ft&&Ft(e)},_.__r=function(e){Vt&&Vt(e),ke=0;var t=(z=e.__c).__H;t&&(wo===z?(t.__h=[],z.__h=[],t.__.forEach(function(o){o.__N&&(o.__=o.__N),o.__V=Ye,o.__N=o.i=void 0})):(t.__h.forEach($e),t.__h.forEach(ko),t.__h=[])),wo=z},_.diffed=function(e){Wt&&Wt(e);var t=e.__c;t&&t.__H&&(t.__H.__h.length&&(Zt.push(t)!==1&&Ot===_.requestAnimationFrame||((Ot=_.requestAnimationFrame)||Ca)(ba)),t.__H.__.forEach(function(o){o.i&&(o.__H=o.i),o.__V!==Ye&&(o.__=o.__V),o.i=void 0,o.__V=Ye})),wo=z=null},_.__c=function(e,t){t.some(function(o){try{o.__h.forEach($e),o.__h=o.__h.filter(function(n){return!n.__||ko(n)})}catch(n){t.some(function(a){a.__h&&(a.__h=[])}),t=[],_.__e(n,o.__v)}}),jt&&jt(e,t)},_.unmount=function(e){Ut&&Ut(e);var t,o=e.__c;o&&o.__H&&(o.__H.__.forEach(function(n){try{$e(n)}catch(a){t=a}}),o.__H=void 0,t&&_.__e(t,o.__v))};var qt=typeof requestAnimationFrame=="function";function Ca(e){var t,o=function(){clearTimeout(n),qt&&cancelAnimationFrame(t),setTimeout(e)},n=setTimeout(o,100);qt&&(t=requestAnimationFrame(o))}function $e(e){var t=z,o=e.__c;typeof o=="function"&&(e.__c=void 0,o()),z=t}function ko(e){var t=z;e.__c=e.__(),z=t}function Eo(e,t){return!e||e.length!==t.length||t.some(function(o,n){return o!==e[n]})}function Xt(e,t){return typeof t=="function"?t(e):t}var xo={};(function e(t,o,n,a){var i=!!(t.Worker&&t.Blob&&t.Promise&&t.OffscreenCanvas&&t.OffscreenCanvasRenderingContext2D&&t.HTMLCanvasElement&&t.HTMLCanvasElement.prototype.transferControlToOffscreen&&t.URL&&t.URL.createObjectURL);function c(){}function m(p){var d=o.exports.Promise,S=d!==void 0?d:t.Promise;return typeof S=="function"?new S(p):(p(c,c),null)}var l=function(){var p=Math.floor(16.666666666666668),d,S,M={},T=0;return typeof requestAnimationFrame=="function"&&typeof cancelAnimationFrame=="function"?(d=function(I){var E=Math.random();return M[E]=requestAnimationFrame(function x(B){T===B||T+p-1<B?(T=B,delete M[E],I()):M[E]=requestAnimationFrame(x)}),E},S=function(I){M[I]&&cancelAnimationFrame(M[I])}):(d=function(I){return setTimeout(I,p)},S=function(I){return clearTimeout(I)}),{frame:d,cancel:S}}(),h=function(){var p,d,S={};function M(T){function I(E,x){T.postMessage({options:E||{},callback:x})}T.init=function(x){var B=x.transferControlToOffscreen();T.postMessage({canvas:B},[B])},T.fire=function(x,B,oe){if(d)return I(x,null),d;var L=Math.random().toString(36).slice(2);return d=m(function($){function Y(N){N.data.callback===L&&(delete S[L],T.removeEventListener("message",Y),d=null,oe(),$())}T.addEventListener("message",Y),I(x,L),S[L]=Y.bind(null,{data:{callback:L}})}),d},T.reset=function(){T.postMessage({reset:!0});for(var x in S)S[x](),delete S[x]}}return function(){if(p)return p;if(!n&&i){var T=["var CONFETTI, SIZE = {}, module = {};","("+e.toString()+")(this, module, true, SIZE);","onmessage = function(msg) {","  if (msg.data.options) {","    CONFETTI(msg.data.options).then(function () {","      if (msg.data.callback) {","        postMessage({ callback: msg.data.callback });","      }","    });","  } else if (msg.data.reset) {","    CONFETTI && CONFETTI.reset();","  } else if (msg.data.resize) {","    SIZE.width = msg.data.resize.width;","    SIZE.height = msg.data.resize.height;","  } else if (msg.data.canvas) {","    SIZE.width = msg.data.canvas.width;","    SIZE.height = msg.data.canvas.height;","    CONFETTI = module.exports.create(msg.data.canvas);","  }","}"].join(`
`);try{p=new Worker(URL.createObjectURL(new Blob([T])))}catch(I){return typeof console!==void 0&&typeof console.warn=="function"&&console.warn("\u{1F38A} Could not load worker",I),null}M(p)}return p}}(),s={particleCount:50,angle:90,spread:45,startVelocity:45,decay:.9,gravity:1,drift:0,ticks:200,x:.5,y:.5,shapes:["square","circle"],zIndex:100,colors:["#26ccff","#a25afd","#ff5e7e","#88ff5a","#fcff42","#ffa62d","#ff36ff"],disableForReducedMotion:!1,scalar:1};function g(p,d){return d?d(p):p}function C(p){return p!=null}function f(p,d,S){return g(p&&C(p[d])?p[d]:s[d],S)}function b(p){return p<0?0:Math.floor(p)}function y(p,d){return Math.floor(Math.random()*(d-p))+p}function v(p){return parseInt(p,16)}function A(p){return p.map(w)}function w(p){var d=String(p).replace(/[^0-9a-f]/gi,"");return d.length<6&&(d=d[0]+d[0]+d[1]+d[1]+d[2]+d[2]),{r:v(d.substring(0,2)),g:v(d.substring(2,4)),b:v(d.substring(4,6))}}function H(p){var d=f(p,"origin",Object);return d.x=f(d,"x",Number),d.y=f(d,"y",Number),d}function ee(p){p.width=document.documentElement.clientWidth,p.height=document.documentElement.clientHeight}function Be(p){var d=p.getBoundingClientRect();p.width=d.width,p.height=d.height}function ue(p){var d=document.createElement("canvas");return d.style.position="fixed",d.style.top="0px",d.style.left="0px",d.style.pointerEvents="none",d.style.zIndex=p,d}function pe(p,d,S,M,T,I,E,x,B){p.save(),p.translate(d,S),p.rotate(I),p.scale(M,T),p.arc(0,0,1,E,x,B),p.restore()}function j(p){var d=p.angle*(Math.PI/180),S=p.spread*(Math.PI/180);return{x:p.x,y:p.y,wobble:Math.random()*10,wobbleSpeed:Math.min(.11,Math.random()*.1+.05),velocity:p.startVelocity*.5+Math.random()*p.startVelocity,angle2D:-d+(.5*S-Math.random()*S),tiltAngle:(Math.random()*(.75-.25)+.25)*Math.PI,color:p.color,shape:p.shape,tick:0,totalTicks:p.ticks,decay:p.decay,drift:p.drift,random:Math.random()+2,tiltSin:0,tiltCos:0,wobbleX:0,wobbleY:0,gravity:p.gravity*3,ovalScalar:.6,scalar:p.scalar}}function fe(p,d){d.x+=Math.cos(d.angle2D)*d.velocity+d.drift,d.y+=Math.sin(d.angle2D)*d.velocity+d.gravity,d.wobble+=d.wobbleSpeed,d.velocity*=d.decay,d.tiltAngle+=.1,d.tiltSin=Math.sin(d.tiltAngle),d.tiltCos=Math.cos(d.tiltAngle),d.random=Math.random()+2,d.wobbleX=d.x+10*d.scalar*Math.cos(d.wobble),d.wobbleY=d.y+10*d.scalar*Math.sin(d.wobble);var S=d.tick++/d.totalTicks,M=d.x+d.random*d.tiltCos,T=d.y+d.random*d.tiltSin,I=d.wobbleX+d.random*d.tiltCos,E=d.wobbleY+d.random*d.tiltSin;if(p.fillStyle="rgba("+d.color.r+", "+d.color.g+", "+d.color.b+", "+(1-S)+")",p.beginPath(),d.shape==="circle")p.ellipse?p.ellipse(d.x,d.y,Math.abs(I-M)*d.ovalScalar,Math.abs(E-T)*d.ovalScalar,Math.PI/10*d.wobble,0,2*Math.PI):pe(p,d.x,d.y,Math.abs(I-M)*d.ovalScalar,Math.abs(E-T)*d.ovalScalar,Math.PI/10*d.wobble,0,2*Math.PI);else if(d.shape==="star")for(var x=Math.PI/2*3,B=4*d.scalar,oe=8*d.scalar,L=d.x,$=d.y,Y=5,N=Math.PI/Y;Y--;)L=d.x+Math.cos(x)*oe,$=d.y+Math.sin(x)*oe,p.lineTo(L,$),x+=N,L=d.x+Math.cos(x)*B,$=d.y+Math.sin(x)*B,p.lineTo(L,$),x+=N;else p.moveTo(Math.floor(d.x),Math.floor(d.y)),p.lineTo(Math.floor(d.wobbleX),Math.floor(T)),p.lineTo(Math.floor(I),Math.floor(E)),p.lineTo(Math.floor(M),Math.floor(d.wobbleY));return p.closePath(),p.fill(),d.tick<d.totalTicks}function Pn(p,d,S,M,T){var I=d.slice(),E=p.getContext("2d"),x,B,oe=m(function(L){function $(){x=B=null,E.clearRect(0,0,M.width,M.height),T(),L()}function Y(){n&&!(M.width===a.width&&M.height===a.height)&&(M.width=p.width=a.width,M.height=p.height=a.height),!M.width&&!M.height&&(S(p),M.width=p.width,M.height=p.height),E.clearRect(0,0,M.width,M.height),I=I.filter(function(N){return fe(E,N)}),I.length?x=l.frame(Y):$()}x=l.frame(Y),B=$});return{addFettis:function(L){return I=I.concat(L),oe},canvas:p,promise:oe,reset:function(){x&&l.cancel(x),B&&B()}}}function jo(p,d){var S=!p,M=!!f(d||{},"resize"),T=f(d,"disableForReducedMotion",Boolean),I=i&&!!f(d||{},"useWorker"),E=I?h():null,x=S?ee:Be,B=p&&E?!!p.__confetti_initialized:!1,oe=typeof matchMedia=="function"&&matchMedia("(prefers-reduced-motion)").matches,L;function $(N,po,fo){for(var he=f(N,"particleCount",b),He=f(N,"angle",Number),Re=f(N,"spread",Number),re=f(N,"startVelocity",Number),In=f(N,"decay",Number),An=f(N,"gravity",Number),Tn=f(N,"drift",Number),qo=f(N,"colors",A),Ln=f(N,"ticks",Number),Zo=f(N,"shapes"),Nn=f(N,"scalar"),Jo=H(N),Xo=he,ho=[],Dn=p.width*Jo.x,Bn=p.height*Jo.y;Xo--;)ho.push(j({x:Dn,y:Bn,angle:He,spread:Re,startVelocity:re,color:qo[Xo%qo.length],shape:Zo[y(0,Zo.length)],ticks:Ln,decay:In,gravity:An,drift:Tn,scalar:Nn}));return L?L.addFettis(ho):(L=Pn(p,ho,x,po,fo),L.promise)}function Y(N){var po=T||f(N,"disableForReducedMotion",Boolean),fo=f(N,"zIndex",Number);if(po&&oe)return m(function(re){re()});S&&L?p=L.canvas:S&&!p&&(p=ue(fo),document.body.appendChild(p)),M&&!B&&x(p);var he={width:p.width,height:p.height};E&&!B&&E.init(p),B=!0,E&&(p.__confetti_initialized=!0);function He(){if(E){var re={getBoundingClientRect:function(){if(!S)return p.getBoundingClientRect()}};x(re),E.postMessage({resize:{width:re.width,height:re.height}});return}he.width=he.height=null}function Re(){L=null,M&&t.removeEventListener("resize",He),S&&p&&(document.body.removeChild(p),p=null,B=!1)}return M&&t.addEventListener("resize",He,!1),E?E.fire(N,he,Re):$(N,he,Re)}return Y.reset=function(){E&&E.reset(),L&&L.reset()},Y}var uo;function Uo(){return uo||(uo=jo(null,{useWorker:!0,resize:!0})),uo}o.exports=function(){return Uo().apply(this,arguments)},o.exports.reset=function(){Uo().reset()},o.exports.create=jo})(function(){return typeof window!="undefined"?window:typeof self!="undefined"?self:this||{}}(),xo,!1);var Yt=xo.exports,Yi=xo.exports.create;async function q(e){let t=await new ne(e).awaitSelection(),o;t.hasOwnProperty("icon")||(o=await new R(e).awaitSelection());let n=await new U(t.name).awaitSelection();return{id:t.id,icon:o!=null?o:t.icon,name:n||t.name,mode:"any"}}function Q(e){var t;return(t=app.commands.commands[e])!=null?t:null}function k(n){var a=n,{icon:e,size:t}=a,o=Ko(a,["icon","size"]);let i=K(null);return Mo(()=>{(0,eo.setIcon)(i.current,e)},[e,t]),r("div",Fe({ref:i},o))}function X(e){let{isMobile:t,appId:o}=app;return e==="any"||e===o||e==="mobile"&&t||e==="desktop"&&!t}function Me(e){var o,n;let t="";for(let a of(o=e.hide.leftRibbon)!=null?o:[])t+=`div.side-dock-ribbon-action[aria-label="${a}"] {display: none !important; content-visibility: hidden;}`;for(let a of e.hide.statusbar)t+=`div.status-bar-item.plugin-${a} {display: none !important; content-visibility: hidden;}`;(n=document.head.querySelector("style#cmdr"))==null||n.remove(),t&&document.head.appendChild(createEl("style",{attr:{id:"cmdr"},text:t,type:"text/css"}))}async function So({target:e}){let t=activeDocument.createElement("canvas");activeDocument.body.appendChild(t),t.style.position="fixed",t.style.width="100vw",t.style.height="100vh",t.style.top="0px",t.style.left="0px",t.style["pointer-events"]="none",t.style["z-index"]="100";let o=Yt.create(t,{resize:!0,useWorker:!0}),n=e.getBoundingClientRect();await o({particleCount:eo.Platform.isDesktop?160:80,startVelocity:55,spread:75,angle:90,drift:-1,ticks:250,origin:{x:(n.x+n.width/2)/activeWindow.innerWidth,y:(n.y+n.height/2)/activeWindow.innerHeight}}),t.remove()}function oo(e){activeDocument.body.style.setProperty("--cmdr-spacing",`${e}px`)}function Ee(e){let t=Object.keys(app.commands.commands).filter(n=>n.startsWith("cmdr:macro-"));for(let n of t)app.commands.removeCommand(n);let o=e.settings.macros;for(let[n,a]of Object.entries(o))e.addCommand({id:`macro-${n}`,name:a.name,callback:()=>{e.executeMacro(parseInt(n))}})}function te(e){var n,a;let{classList:t,style:o}=document.body;o.setProperty("--at-button-height",((n=e.rowHeight)!=null?n:48)+"px"),o.setProperty("--at-button-width",((a=e.buttonWidth)!=null?a:48)+"px"),o.setProperty("--at-row-count",e.rowCount.toString()),o.setProperty("--at-spacing",e.spacing+"px"),o.setProperty("--at-offset",e.heightOffset+"px"),t.toggle("AT-multirow",e.rowCount>1),t.toggle("AT-row",!e.columnLayout),t.toggle("AT-column",e.columnLayout),t.toggle("AT-no-toolbar",e.rowCount===0)}function $t(){let{classList:e,style:t}=document.body;t.removeProperty("--at-button-height"),t.removeProperty("--at-button-width"),t.removeProperty("--at-row-count"),t.removeProperty("--at-spacing"),t.removeProperty("--at-offset"),e.remove("AT-multirow"),e.remove("AT-row"),e.remove("AT-column"),e.remove("AT-no-toolbar"),e.remove("advanced-toolbar")}function to(e){e.mappedIcons.forEach(t=>{let o=app.commands.commands[t.commandID];o?o.icon=t.iconID:e.mappedIcons.remove(t)})}var Sn=require("obsidian");var xe=require("obsidian");var Kt=require("obsidian");function Gt({modal:e}){return r(P,null,r("p",null,u("Are you sure you want to delete the Command?")),r("div",{className:"modal-button-container"},r("button",{className:"mod-warning",onClick:async()=>{e.plugin.settings.confirmDeletion=!1,e.plugin.saveSettings(),e.remove=!0,e.close()}},u("Remove and don't ask again")),r("button",{className:"mod-warning",onClick:()=>{e.remove=!0,e.close()}},u("Remove")),r("button",{onClick:()=>{e.remove=!1,e.close()}},u("Cancel"))))}var F=class extends Kt.Modal{constructor(o){super(app);this.plugin=o}async onOpen(){this.titleEl.innerText=u("Remove Command"),this.containerEl.style.zIndex="99",this.reactComponent=r(Gt,{modal:this}),W(this.reactComponent,this.contentEl)}async didChooseRemove(){return this.open(),new Promise(o=>{this.onClose=()=>{var n;return o((n=this.remove)!=null?n:!1)}})}onClose(){W(null,this.contentEl)}};var Z=class{constructor(t,o){this.plugin=t,this.pairs=o}};var ve=class extends Z{constructor(o,n){super(o,n);this.actions=new Map;this.init(),this.plugin.register(()=>this.actions.forEach((a,i)=>this.removeAction(i)))}getFileExplorers(){return app.workspace.getLeavesOfType("file-explorer")}init(){app.workspace.onLayoutReady(()=>{for(let o of this.pairs)X(o.mode)&&(app.workspace.onLayoutReady(()=>{this.getFileExplorers().forEach(a=>{this.addAction(o,a)})}),this.plugin.registerEvent(app.workspace.on("layout-change",()=>{this.getFileExplorers().forEach(a=>{this.addAction(o,a)})})))})}reorder(){this.actions.forEach((o,n)=>this.removeAction(n,!0)),this.init()}async addCommand(o){this.pairs.push(o),app.workspace.onLayoutReady(()=>{this.getFileExplorers().forEach(a=>{this.addAction(o,a)})}),this.plugin.registerEvent(app.workspace.on("layout-change",()=>{this.getFileExplorers().forEach(a=>{this.addAction(o,a)})})),await this.plugin.saveSettings()}async removeCommand(o){this.pairs.remove(o),this.removeAction(o),await this.plugin.saveSettings()}buttonExists(o,n){return[...o.view.containerEl.querySelectorAll("div.nav-buttons-container > .cmdr.clickable-icon")].some(a=>a.getAttribute("data-cmdr")===n.icon+n.name)}addAction(o,n){var l,h,s,g,C;if(this.buttonExists(n,o))return;let a=createDiv({cls:"cmdr clickable-icon",attr:{"aria-label-position":"top","aria-label":o.name,"data-cmdr":o.icon+o.name}});this.actions.set(o,a),a.style.color=o.color==="#000000"||o.color===void 0?"inherit":o.color;let i=!1,c=()=>{a.empty(),(0,xe.setIcon)(a,o.icon),a.onclick=()=>app.commands.executeCommandById(o.id)},m=()=>{a.empty(),(0,xe.setIcon)(a,"trash"),a.onclick=async()=>{(!this.plugin.settings.confirmDeletion||await new F(this.plugin).didChooseRemove())&&this.removeCommand(o)}};a.addEventListener("mouseleave",()=>{c(),i=!1}),a.addEventListener("mousemove",f=>{f.preventDefault(),f.stopImmediatePropagation(),f.shiftKey&&(i||m(),i=!0)}),a.addEventListener("contextmenu",f=>{f.stopImmediatePropagation(),new xe.Menu().addItem(b=>{b.setTitle(u("Add command")).setIcon("command").onClick(async()=>{let y=await q(this.plugin);this.addCommand(y)})}).addSeparator().addItem(b=>{b.setTitle(u("Change Icon")).setIcon("box").onClick(async()=>{let y=await new R(this.plugin).awaitSelection();y&&y!==o.icon&&(o.icon=y,await this.plugin.saveSettings(),this.reorder())})}).addItem(b=>{b.setTitle(u("Rename")).setIcon("text-cursor-input").onClick(async()=>{let y=await new U(o.name).awaitSelection();y&&y!==o.name&&(o.name=y,await this.plugin.saveSettings(),this.reorder())})}).addItem(b=>{b.dom.addClass("is-warning"),b.setTitle(u("Delete")).setIcon("lucide-trash").onClick(async()=>{(!this.plugin.settings.confirmDeletion||await new F(this.plugin).didChooseRemove())&&this.removeCommand(o)})}).showAtMouseEvent(f)}),c(),(C=(g=(s=(h=(l=n.view)==null?void 0:l.containerEl)==null?void 0:h.querySelector)==null?void 0:s.call(h,"div.nav-buttons-container"))==null?void 0:g.appendChild)==null||C.call(g,a)}removeAction(o,n=!1){let a=this.actions.get(o);if(!!a){if(n){a.remove(),this.actions.delete(o);return}a.addClass("cmdr-ribbon-removing"),a.addEventListener("transitionend",async()=>{a.remove(),this.actions.delete(o)})}}};var se=require("obsidian");var no=class extends Z{async addCommand(t){this.pairs.push(t),await this.plugin.saveSettings()}async removeCommand(t){this.pairs.remove(t),await this.plugin.saveSettings()}reorder(){}addRemovableCommand(t,o,n,a,i){return c=>{var f;c.dom.addClass("cmdr"),c.dom.style.color=o.color==="#000000"||o.color===void 0?"inherit":o.color,c.setSection("cmdr"),c.dom.style.display="flex";let m=createDiv({cls:"cmdr-menu-more-options"}),l=null;m.addEventListener("click",b=>{b.preventDefault(),b.stopImmediatePropagation(),l?(l.hide(),l=null):l=new se.Menu().addItem(y=>{y.setTitle(u("Change Icon")).setIcon("box").onClick(async()=>{let v=await new R(n).awaitSelection();v&&v!==o.icon&&(o.icon=v,await n.saveSettings())})}).addItem(y=>{y.setTitle(u("Rename")).setIcon("text-cursor-input").onClick(async()=>{let v=await new U(o.name).awaitSelection();v&&v!==o.name&&(o.name=v,await n.saveSettings())})}).addItem(y=>{y.dom.addClass("is-warning"),y.setTitle(u("Delete")).setIcon("lucide-trash").onClick(async()=>{(!n.settings.confirmDeletion||await new F(n).didChooseRemove())&&C()})}).showAtMouseEvent(b)}),(0,se.setIcon)(m,"more-vertical"),c.dom.append(m),c.setTitle((f=o.name)!=null?f:t.name).setIcon(o.icon).onClick(()=>app.commands.executeCommandById(o.id));let h=!1,s=()=>{m.style.display="none"},g=()=>{m.style.display="block"},C=async()=>{c.dom.addClass("cmdr-removing"),a.registerDomEvent(c.dom,"transitionend",()=>{c.dom.remove()}),i.remove(o),await n.saveSettings()};a.registerDomEvent(c.dom,"mousemove",b=>{b.preventDefault(),b.stopImmediatePropagation(),h||g(),h=!0}),a.registerDomEvent(c.dom,"mouseleave",()=>{s(),h=!1}),s()}}addCommandAddButton(t,o,n){t.settings.showAddCommand&&o.addItem(a=>{a.setTitle(u("Add command")).setIcon("plus-circle").setSection("cmdr").onClick(async()=>{try{let i=await q(t);n.push(i),await t.saveSettings()}catch(i){console.log(i)}})})}},Se=class extends no{applyEditorMenuCommands(t){return async(o,n,a)=>{this.addCommandAddButton(t,o,t.settings.editorMenu);for(let i of t.settings.editorMenu){let c=Q(i.id);!c||!X(i.mode)||c.checkCallback&&!c.checkCallback(!0)||c.editorCheckCallback&&!c.editorCheckCallback(!0,n,a)||o.addItem(this.addRemovableCommand.call(this,c,i,t,o,t.settings.editorMenu))}}}},Pe=class extends no{applyFileMenuCommands(t){return async(o,n,a,i)=>{this.addCommandAddButton(t,o,t.settings.fileMenu);for(let c of t.settings.fileMenu){let m=Q(c.id);if(!!m&&!(m.checkCallback&&!m.checkCallback(!0))){if(m.editorCallback){if(!((i==null?void 0:i.view)instanceof se.MarkdownView))continue}else if(m.editorCheckCallback)if((i==null?void 0:i.view)instanceof se.MarkdownView){if(!m.editorCheckCallback(!0,i.view.editor,i.view))continue}else continue;o.addItem(this.addRemovableCommand.call(this,m,c,t,o,t.settings.fileMenu))}}}}};var ce=require("obsidian");var ge=class extends Z{constructor(o,n){super(o,n);this.buttons=new WeakMap;this.init()}addPageHeaderButton(o,n){let{id:a,icon:i,name:c}=n,{view:m}=o;if(!(m instanceof ce.ItemView))return;let l=this.buttonsFor(o,!0);if(!l||l.has(a))return;let h=m.addAction(i,c,()=>{app.workspace.setActiveLeaf(o,{focus:!0}),app.commands.executeCommandById(a)});l.set(a,h),h.addClasses(["cmdr-page-header",a]),h.style.color=n.color==="#000000"||n.color===void 0?"inherit":n.color,h.addEventListener("contextmenu",s=>{s.stopImmediatePropagation(),new ce.Menu().addItem(g=>{g.setTitle(u("Add command")).setIcon("command").onClick(async()=>{let C=await q(this.plugin);this.addCommand(C)})}).addSeparator().addItem(g=>{g.setTitle(u("Change Icon")).setIcon("box").onClick(async()=>{let C=await new R(this.plugin).awaitSelection();C&&C!==n.icon&&(n.icon=C,await this.plugin.saveSettings(),this.reorder())})}).addItem(g=>{g.setTitle(u("Rename")).setIcon("text-cursor-input").onClick(async()=>{let C=await new U(n.name).awaitSelection();C&&C!==n.name&&(n.name=C,await this.plugin.saveSettings(),this.reorder())})}).addItem(g=>{g.dom.addClass("is-warning"),g.setTitle(u("Delete")).setIcon("lucide-trash").onClick(async()=>{(!this.plugin.settings.confirmDeletion||await new F(this.plugin).didChooseRemove())&&this.removeCommand(n)})}).showAtMouseEvent(s)})}init(){this.plugin.register(()=>{this.removeButtonsFromAllLeaves()}),this.plugin.registerEvent(app.workspace.on("layout-change",()=>{this.addButtonsToAllLeaves()})),app.workspace.onLayoutReady(()=>setTimeout(()=>this.addButtonsToAllLeaves(),100))}addAdderButton(o){var c;let{view:n}=o,a="cmdr-adder";if(!(n instanceof ce.ItemView)||(c=this.buttons.get(n))!=null&&c.has(a))return;let i=n.addAction("plus",u("Add new"),async()=>{this.addCommand(await q(this.plugin))});i.addClasses(["cmdr",a]),this.buttons.has(n)||this.buttons.set(n,new Map),this.buttons.get(n).set(a,i)}addButtonsToAllLeaves(o=!1){activeWindow.requestAnimationFrame(()=>app.workspace.iterateAllLeaves(n=>this.addButtonsToLeaf(n,o)))}removeButtonsFromAllLeaves(){activeWindow.requestAnimationFrame(()=>app.workspace.iterateAllLeaves(o=>this.removeButtonsFromLeaf(o)))}buttonsFor(o,n=!1){if(o.view instanceof ce.ItemView)return n&&!this.buttons.has(o.view)&&this.buttons.set(o.view,new Map),this.buttons.get(o.view)}addButtonsToLeaf(o,n=!1){var a;if(o.view instanceof ce.ItemView){if(n)this.removeButtonsFromLeaf(o);else if((a=this.buttonsFor(o))!=null&&a.size)return;for(let i=this.pairs.length-1;i>=0;i--){let c=this.pairs[i];X(c.mode)&&this.addPageHeaderButton(o,c)}this.plugin.settings.showAddCommand&&this.addAdderButton(o)}}removeButtonsFromLeaf(o){let n=this.buttonsFor(o);if(n){for(let a of n.values())a.detach();n==null||n.clear()}}reorder(){this.addButtonsToAllLeaves(!0)}async addCommand(o){this.pairs.push(o),this.addButtonsToAllLeaves(!0),await this.plugin.saveSettings()}async removeCommand(o){this.pairs.remove(o),this.addButtonsToAllLeaves(!0),await this.plugin.saveSettings()}};var de=require("obsidian");var be=class extends Z{constructor(o,n){super(o,n);this.actions=new Map;this.addBtn=createDiv({cls:"cmdr status-bar-item cmdr-adder",attr:{"aria-label-position":"top","aria-label":u("Add new")}});this.init(),this.plugin.register(()=>this.actions.forEach((a,i)=>this.removeAction(i)))}init(){app.workspace.onLayoutReady(()=>{this.container=app.statusBar.containerEl;for(let o of this.pairs)Q(o.id)||this.pairs.remove(o),X(o.mode)&&this.addAction(o);this.plugin.saveSettings(),this.plugin.registerDomEvent(this.container,"contextmenu",o=>{o.target===this.container&&new de.Menu().addItem(n=>{n.setTitle(u("Add command")).setIcon("command").onClick(async()=>{let a=await q(this.plugin);this.addCommand(a)})}).showAtMouseEvent(o)}),this.plugin.register(()=>this.addBtn.remove()),(0,de.setIcon)(this.addBtn,"plus"),this.addBtn.onclick=async()=>{let o=await q(this.plugin);this.addCommand(o),this.reorder()},this.plugin.settings.showAddCommand&&this.container.prepend(this.addBtn)})}reorder(){this.addBtn.remove(),this.actions.forEach((o,n)=>this.removeAction(n,!0)),this.init()}async addCommand(o){this.pairs.push(o),this.addAction(o),await this.plugin.saveSettings()}async removeCommand(o){this.pairs.remove(o),this.removeAction(o),await this.plugin.saveSettings()}addAction(o){let n=createDiv({cls:"cmdr status-bar-item clickable-icon",attr:{"aria-label-position":"top","aria-label":o.name}});this.actions.set(o,n),n.style.color=o.color==="#000000"||o.color===void 0?"inherit":o.color;let a=!1,i=()=>{n.empty(),(0,de.setIcon)(n,o.icon),n.onclick=()=>app.commands.executeCommandById(o.id)},c=()=>{n.empty(),(0,de.setIcon)(n,"trash"),n.onclick=async()=>{(!this.plugin.settings.confirmDeletion||await new F(this.plugin).didChooseRemove())&&this.removeCommand(o)}};n.addEventListener("mouseleave",()=>{i(),a=!1}),n.addEventListener("mousemove",m=>{m.preventDefault(),m.stopImmediatePropagation(),m.shiftKey&&(a||c(),a=!0)}),n.addEventListener("contextmenu",m=>{m.stopImmediatePropagation(),new de.Menu().addItem(l=>{l.setTitle(u("Add command")).setIcon("command").onClick(async()=>{let h=await q(this.plugin);this.addCommand(h)})}).addSeparator().addItem(l=>{l.setTitle(u("Change Icon")).setIcon("box").onClick(async()=>{let h=await new R(this.plugin).awaitSelection();h&&h!==o.icon&&(o.icon=h,await this.plugin.saveSettings(),this.reorder())})}).addItem(l=>{l.setTitle(u("Rename")).setIcon("text-cursor-input").onClick(async()=>{let h=await new U(o.name).awaitSelection();h&&h!==o.name&&(o.name=h,await this.plugin.saveSettings(),this.reorder())})}).addItem(l=>{l.dom.addClass("is-warning"),l.setTitle(u("Delete")).setIcon("lucide-trash").onClick(async()=>{(!this.plugin.settings.confirmDeletion||await new F(this.plugin).didChooseRemove())&&this.removeCommand(o)})}).showAtMouseEvent(m)}),i(),this.container.prepend(n)}removeAction(o,n=!1){let a=this.actions.get(o);if(!!a){if(n){a.remove(),this.actions.delete(o);return}a.addClass("cmdr-ribbon-removing"),a.addEventListener("transitionend",async()=>{a.remove(),this.actions.delete(o)})}}};var Qt=require("obsidian");var co=require("obsidian");var J=require("obsidian");var To=require("obsidian");var en=["https://github.com/jsmorabito","https://github.com/phibr0","https://www.youtube.com/watch?v=dQw4w9WgXcQ"];function Po(){let[e,t]=D(0);return r("div",{className:"cmdr-credits"},r("span",{onClick:()=>{t(o=>o+1),location.replace(en[e%en.length])}},u("By Johnny\u2728 and phibr0")))}function ya(e,t){for(var o in t)e[o]=t[o];return e}function Ao(e,t){for(var o in e)if(o!=="__source"&&!(o in t))return!0;for(var n in t)if(n!=="__source"&&e[n]!==t[n])return!0;return!1}function on(e){this.props=e}function mn(e,t){function o(a){var i=this.props.ref,c=i==a.ref;return!c&&i&&(i.call?i(null):i.current=null),t?!t(this.props,a)||!c:Ao(this.props,a)}function n(a){return this.shouldComponentUpdate=o,r(e,a)}return n.displayName="Memo("+(e.displayName||e.name)+")",n.prototype.isReactComponent=!0,n.__f=!0,n}(on.prototype=new G).isPureReactComponent=!0,on.prototype.shouldComponentUpdate=function(e,t){return Ao(this.props,e)||Ao(this.state,t)};var tn=_.__b;_.__b=function(e){e.type&&e.type.__f&&e.ref&&(e.props.ref=e.ref,e.ref=null),tn&&tn(e)};var xs=typeof Symbol!="undefined"&&Symbol.for&&Symbol.for("react.forward_ref")||3911;var wa=_.__e;_.__e=function(e,t,o,n){if(e.then){for(var a,i=t;i=i.__;)if((a=i.__c)&&a.__c)return t.__e==null&&(t.__e=o.__e,t.__k=o.__k),a.__c(e,t)}wa(e,t,o,n)};var nn=_.unmount;function ln(e,t,o){return e&&(e.__c&&e.__c.__H&&(e.__c.__H.__.forEach(function(n){typeof n.__c=="function"&&n.__c()}),e.__c.__H=null),(e=ya({},e)).__c!=null&&(e.__c.__P===o&&(e.__c.__P=t),e.__c=null),e.__k=e.__k&&e.__k.map(function(n){return ln(n,t,o)})),e}function un(e,t,o){return e&&(e.__v=null,e.__k=e.__k&&e.__k.map(function(n){return un(n,t,o)}),e.__c&&e.__c.__P===t&&(e.__e&&o.insertBefore(e.__e,e.__d),e.__c.__e=!0,e.__c.__P=o)),e}function Io(){this.__u=0,this.t=null,this.__b=null}function pn(e){var t=e.__.__c;return t&&t.__a&&t.__a(e)}function ao(){this.u=null,this.o=null}_.unmount=function(e){var t=e.__c;t&&t.__R&&t.__R(),t&&e.__h===!0&&(e.type=null),nn&&nn(e)},(Io.prototype=new G).__c=function(e,t){var o=t.__c,n=this;n.t==null&&(n.t=[]),n.t.push(o);var a=pn(n.__v),i=!1,c=function(){i||(i=!0,o.__R=null,a?a(m):m())};o.__R=c;var m=function(){if(!--n.__u){if(n.state.__a){var h=n.state.__a;n.__v.__k[0]=un(h,h.__c.__P,h.__c.__O)}var s;for(n.setState({__a:n.__b=null});s=n.t.pop();)s.forceUpdate()}},l=t.__h===!0;n.__u++||l||n.setState({__a:n.__b=n.__v.__k[0]}),e.then(c,c)},Io.prototype.componentWillUnmount=function(){this.t=[]},Io.prototype.render=function(e,t){if(this.__b){if(this.__v.__k){var o=document.createElement("div"),n=this.__v.__k[0].__c;this.__v.__k[0]=ln(this.__b,o,n.__O=n.__P)}this.__b=null}var a=t.__a&&r(P,null,e.fallback);return a&&(a.__h=null),[r(P,null,t.__a?null:e.children),a]};var an=function(e,t,o){if(++o[1]===o[0]&&e.o.delete(t),e.props.revealOrder&&(e.props.revealOrder[0]!=="t"||!e.o.size))for(o=e.u;o;){for(;o.length>3;)o.pop()();if(o[1]<o[0])break;e.u=o=o[2]}};(ao.prototype=new G).__a=function(e){var t=this,o=pn(t.__v),n=t.o.get(e);return n[0]++,function(a){var i=function(){t.props.revealOrder?(n.push(a),an(t,e,n)):a()};o?o(i):i()}},ao.prototype.render=function(e){this.u=null,this.o=new Map;var t=we(e.children);e.revealOrder&&e.revealOrder[0]==="b"&&t.reverse();for(var o=t.length;o--;)this.o.set(t[o],this.u=[1,0,this.u]);return e.children},ao.prototype.componentDidUpdate=ao.prototype.componentDidMount=function(){var e=this;this.o.forEach(function(t,o){an(e,o,t)})};var ka=typeof Symbol!="undefined"&&Symbol.for&&Symbol.for("react.element")||60103,Ma=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,Ea=typeof document!="undefined",xa=function(e){return(typeof Symbol!="undefined"&&typeof Symbol()=="symbol"?/fil|che|rad/i:/fil|che|ra/i).test(e)};G.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach(function(e){Object.defineProperty(G.prototype,e,{configurable:!0,get:function(){return this["UNSAFE_"+e]},set:function(t){Object.defineProperty(this,e,{configurable:!0,writable:!0,value:t})}})});var rn=_.event;function Sa(){}function Pa(){return this.cancelBubble}function Ia(){return this.defaultPrevented}_.event=function(e){return rn&&(e=rn(e)),e.persist=Sa,e.isPropagationStopped=Pa,e.isDefaultPrevented=Ia,e.nativeEvent=e};var Aa,sn={configurable:!0,get:function(){return this.class}},cn=_.vnode;_.vnode=function(e){var t=e.type,o=e.props,n=o;if(typeof t=="string"){var a=t.indexOf("-")===-1;for(var i in n={},o){var c=o[i];Ea&&i==="children"&&t==="noscript"||i==="value"&&"defaultValue"in o&&c==null||(i==="defaultValue"&&"value"in o&&o.value==null?i="value":i==="download"&&c===!0?c="":/ondoubleclick/i.test(i)?i="ondblclick":/^onchange(textarea|input)/i.test(i+t)&&!xa(o.type)?i="oninput":/^onfocus$/i.test(i)?i="onfocusin":/^onblur$/i.test(i)?i="onfocusout":/^on(Ani|Tra|Tou|BeforeInp|Compo)/.test(i)?i=i.toLowerCase():a&&Ma.test(i)?i=i.replace(/[A-Z0-9]/g,"-$&").toLowerCase():c===null&&(c=void 0),/^oninput$/i.test(i)&&(i=i.toLowerCase(),n[i]&&(i="oninputCapture")),n[i]=c)}t=="select"&&n.multiple&&Array.isArray(n.value)&&(n.value=we(o.children).forEach(function(m){m.props.selected=n.value.indexOf(m.props.value)!=-1})),t=="select"&&n.defaultValue!=null&&(n.value=we(o.children).forEach(function(m){m.props.selected=n.multiple?n.defaultValue.indexOf(m.props.value)!=-1:n.defaultValue==m.props.value})),e.props=n,o.class!=o.className&&(sn.enumerable="className"in o,o.className!=null&&(n.class=o.className),Object.defineProperty(n,"className",sn))}e.$$typeof=ka,cn&&cn(e)};var dn=_.__r;_.__r=function(e){dn&&dn(e),Aa=e.__c};var fn='<svg viewbox="0 0 118 105" width="118" xmlns="http://www.w3.org/2000/svg" height="105" style="-webkit-print-color-adjust:exact" fill="none"><defs><clipPath id="a" class="frame-clip"><rect rx="0" ry="0" width="118" height="105"/></clipPath></defs><g clip-path="url(#a)"><rect rx="0" ry="0" width="118" height="105" class="frame-background"/><g class="frame-children"><g class="any-key" style="fill:#000"><path d="M35.2 1C25.7 1 18 8.7 18 18.2v51.6C18 79.3 25.7 87 35.2 87h45.6C90.3 87 98 79.3 98 69.8V18.2C98 8.7 90.3 1 80.8 1H35.2Zm0 4h45.6C88.1 5 94 10.9 94 18.2v37.6C94 63.1 88.1 69 80.8 69H35.2C27.9 69 22 63.1 22 55.8V18.2C22 10.9 27.9 5 35.2 5ZM56 19v14.5L43.6 26l-2.1 3.4L54.1 37l-12.6 7.6 2.1 3.4L56 40.5V55h4V40.5L72.4 48l2.1-3.4L61.9 37l12.6-7.6-2.1-3.4L60 33.5V19h-4ZM22 66.8c3.2 3.8 7.9 6.2 13.2 6.2h45.6c5.3 0 10-2.4 13.2-6.2v3C94 77.1 88.1 83 80.8 83H35.2C27.9 83 22 77.1 22 69.8v-3Z" style="fill:var(--text-accent);fill-opacity:1"/><path d="M35.2 1C25.7 1 18 8.7 18 18.2v51.6C18 79.3 25.7 87 35.2 87h45.6C90.3 87 98 79.3 98 69.8V18.2C98 8.7 90.3 1 80.8 1H35.2Zm0 4h45.6C88.1 5 94 10.9 94 18.2v37.6C94 63.1 88.1 69 80.8 69H35.2C27.9 69 22 63.1 22 55.8V18.2C22 10.9 27.9 5 35.2 5ZM56 19v14.5L43.6 26l-2.1 3.4L54.1 37l-12.6 7.6 2.1 3.4L56 40.5V55h4V40.5L72.4 48l2.1-3.4L61.9 37l12.6-7.6-2.1-3.4L60 33.5V19h-4ZM22 66.8c3.2 3.8 7.9 6.2 13.2 6.2h45.6c5.3 0 10-2.4 13.2-6.2v3C94 77.1 88.1 83 80.8 83H35.2C27.9 83 22 77.1 22 69.8v-3Z" style="fill:none;stroke-width:1;stroke:var(--text-accent);stroke-opacity:1" class="stroke-shape"/></g><path d="M20.11 53.587 3 63.9"/><g class="stroke-shape"><defs><marker refX="3" fill-opacity="1" orient="auto-start-reverse" id="b" viewBox="0 0 6 6" fill="var(--text-accent)" refY="3" markerWidth="6" markerHeight="6"><path d="M3 2.5a.5.5 0 0 1 0 1"/></marker></defs><path d="M20.11 53.587 3 63.9" style="fill:none;stroke-width:5;stroke:var(--text-accent);stroke-opacity:1;marker-end:url(#b)"/></g><path d="m96 53.567 19.161-11.55"/><g class="stroke-shape"><defs><marker refX="3" fill-opacity="1" orient="auto-start-reverse" id="c" viewBox="0 0 6 6" fill="var(--text-accent)" refY="3" markerWidth="6" markerHeight="6"><path d="M3 2.5a.5.5 0 0 1 0 1"/></marker></defs><path d="m96 53.567 19.161-11.55" style="fill:none;stroke-width:5;stroke:var(--text-accent);stroke-opacity:1;marker-end:url(#c)"/></g><path d="M20.11 53.587 3 63.9"/><g class="stroke-shape"><defs><marker refX="3" fill-opacity="1" orient="auto-start-reverse" id="d" viewBox="0 0 6 6" fill="var(--text-accent)" refY="3" markerWidth="6" markerHeight="6"><path d="M3 2.5a.5.5 0 0 1 0 1"/></marker></defs><path d="M20.11 53.587 3 63.9" style="fill:none;stroke-width:5;stroke:var(--text-accent);stroke-opacity:1;marker-end:url(#d)"/></g><path d="M42 84v18h12"/><g class="stroke-shape"><defs><marker refX="3" fill-opacity="1" orient="auto-start-reverse" id="e" viewBox="0 0 6 6" fill="var(--text-accent)" refY="3" markerWidth="6" markerHeight="6"><path d="M3 2.5a.5.5 0 0 1 0 1"/></marker></defs><path d="M42 84v18h12" style="fill:none;stroke-width:5;stroke:var(--text-accent);stroke-opacity:1;marker-end:url(#e)"/></g><path d="M74 84v18h12"/><g class="stroke-shape"><defs><marker refX="3" fill-opacity="1" orient="auto-start-reverse" id="f" viewBox="0 0 6 6" fill="var(--text-accent)" refY="3" markerWidth="6" markerHeight="6"><path d="M3 2.5a.5.5 0 0 1 0 1"/></marker></defs><path d="M74 84v18h12" style="fill:none;stroke-width:5;stroke:var(--text-accent);stroke-opacity:1;marker-end:url(#f)"/></g><path d="m96 53.567 19.161-11.55"/><g class="stroke-shape"><defs><marker refX="3" fill-opacity="1" orient="auto-start-reverse" id="g" viewBox="0 0 6 6" fill="var(--text-accent)" refY="3" markerWidth="6" markerHeight="6"><path d="M3 2.5a.5.5 0 0 1 0 1"/></marker></defs><path d="m96 53.567 19.161-11.55" style="fill:none;stroke-width:5;stroke:var(--text-accent);stroke-opacity:1;marker-end:url(#g)"/></g><path d="M42 84v18h12"/><g class="stroke-shape"><defs><marker refX="3" fill-opacity="1" orient="auto-start-reverse" id="h" viewBox="0 0 6 6" fill="var(--text-accent)" refY="3" markerWidth="6" markerHeight="6"><path d="M3 2.5a.5.5 0 0 1 0 1"/></marker></defs><path d="M42 84v18h12" style="fill:none;stroke-width:5;stroke:var(--text-accent);stroke-opacity:1;marker-end:url(#h)"/></g><path d="M74 84v18h12"/><g class="stroke-shape"><defs><marker refX="3" fill-opacity="1" orient="auto-start-reverse" id="i" viewBox="0 0 6 6" fill="var(--text-accent)" refY="3" markerWidth="6" markerHeight="6"><path d="M3 2.5a.5.5 0 0 1 0 1"/></marker></defs><path d="M74 84v18h12" style="fill:none;stroke-width:5;stroke:var(--text-accent);stroke-opacity:1;marker-end:url(#i)"/></g></g></g></svg>';var hn='<svg width="124" height="189" viewBox="0 0 124 189" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M120 175.999L104.432 133.999C74.7524 140.282 54.9222 146.129 23.5771 137.861C16.3206 150.681 13.2565 163.179 6 175.999C11.7765 173.272 14.6163 173.349 19.0573 175.999C25.2389 172.439 27.3603 173.689 31.1101 175.999C39.3142 169.983 43.4376 171.766 50.696 175.999C57.2083 171.119 60.7022 171.597 66.7665 175.999C76.3874 170.399 80.6872 172.41 88.3505 175.994L88.3612 175.999C94.0886 172.481 97.1438 172.819 102.423 175.999C109.021 172.023 112.937 173.03 120 175.999Z" fill="#A80000" stroke="#A80000" stroke-width="4" /><path d="M37.156 80.2386L85.6308 78.676L53.8425 8.1636L37.156 80.2386Z" fill="#B50D0D" stroke="#B50D0D" stroke-width="4" /><ellipse cx="85" cy="173.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="93" cy="175.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="101" cy="173.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="112" cy="175.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="116" cy="177.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="76" cy="175.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="67" cy="173.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="58" cy="173.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="49" cy="175.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="42" cy="173.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="37" cy="175.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="31" cy="174.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="29" cy="173.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="25" cy="175.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="20" cy="173.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="14" cy="175.499" rx="8" ry="6.5" fill="#E9E9E9" /><ellipse cx="8" cy="177.499" rx="8" ry="6.5" fill="#E9E9E9" /><path d="M47 166.999V183.999H59" stroke="#28CC39" stroke-width="5" stroke-linecap="round" /><path d="M79 166.999V183.999H91" stroke="#28CC39" stroke-width="5" stroke-linecap="round" /><path d="M40.2 82.9993C30.7 82.9993 23 90.6993 23 100.199V151.799C23 161.299 30.7 168.999 40.2 168.999H85.8C95.3 168.999 103 161.299 103 151.799V100.199C103 90.6993 95.3 82.9993 85.8 82.9993H40.2ZM40.2 86.9993H85.8C93.1 86.9993 99 92.8993 99 100.199V137.799C99 145.099 93.1 150.999 85.8 150.999H40.2C32.9 150.999 27 145.099 27 137.799V100.199C27 92.8993 32.9 86.9993 40.2 86.9993ZM61 100.999V115.499L48.6 107.999L46.5 111.399L59.1 118.999L46.5 126.599L48.6 129.999L61 122.499V136.999H65V122.499L77.4 129.999L79.5 126.599L66.9 118.999L79.5 111.399L77.4 107.999L65 115.499V100.999H61ZM27 148.799C30.2 152.599 34.9 154.999 40.2 154.999H85.8C91.1 154.999 95.8 152.599 99 148.799V151.799C99 159.099 93.1 164.999 85.8 164.999H40.2C32.9 164.999 27 159.099 27 151.799V148.799Z" fill="#28CC39" stroke="#28CC39" /><path d="M25 135.999L7.99997 146.603" stroke="#28CC39" stroke-width="5" stroke-linecap="round" /><path d="M120.104 123.488L101 135.614" stroke="#28CC39" stroke-width="5" stroke-linecap="round" /><path d="M84.5 177.999V173.499H75.5V177.999H84.5ZM80 183.999H75.5V188.499H80V183.999ZM90 188.499C92.4853 188.499 94.5 186.485 94.5 183.999C94.5 181.514 92.4853 179.499 90 179.499V188.499ZM75.5 177.999V183.999H84.5V177.999H75.5ZM80 188.499H90V179.499H80V188.499Z" fill="#303030" /><path d="M52.5 177.999V173.499H43.5V177.999H52.5ZM48 183.999H43.5V188.499H48V183.999ZM58 188.499C60.4853 188.499 62.5 186.485 62.5 183.999C62.5 181.514 60.4853 179.499 58 179.499V188.499ZM43.5 177.999V183.999H52.5V177.999H43.5ZM48 188.499H58V179.499H48V188.499Z" fill="#303030" /><ellipse cx="38.1555" cy="80.2062" rx="8" ry="6.5" transform="rotate(-1.84634 38.1555 80.2062)" fill="#E9E9E9" /><ellipse cx="43.0885" cy="78.046" rx="8" ry="6.5" transform="rotate(-1.84634 43.0885 78.046)" fill="#E9E9E9" /><ellipse cx="46.1513" cy="79.9483" rx="8" ry="6.5" transform="rotate(-1.84634 46.1513 79.9483)" fill="#E9E9E9" /><ellipse cx="54.0827" cy="77.692" rx="8" ry="6.5" transform="rotate(-1.84634 54.0827 77.692)" fill="#E9E9E9" /><ellipse cx="59.1445" cy="79.5299" rx="8" ry="6.5" transform="rotate(-1.84634 59.1445 79.5299)" fill="#E9E9E9" /><ellipse cx="67.0759" cy="77.2731" rx="8" ry="6.5" transform="rotate(-1.84634 67.0759 77.2731)" fill="#E9E9E9" /><ellipse cx="70.1389" cy="79.1754" rx="8" ry="6.5" transform="rotate(-1.84634 70.1389 79.1754)" fill="#E9E9E9" /><ellipse cx="80.0692" cy="76.8541" rx="8" ry="6.5" transform="rotate(-1.84634 80.0692 76.8541)" fill="#E9E9E9" /><ellipse cx="83.1321" cy="78.7565" rx="8" ry="6.5" transform="rotate(-1.84634 83.1321 78.7565)" fill="#E9E9E9" /><ellipse cx="53.8585" cy="7.66343" rx="8" ry="6.5" transform="rotate(-1.84634 53.8585 7.66343)" fill="#E9E9E9" /><path d="M104.5 127.999C75.5109 146.65 55.8196 154.503 21.5 133.999" stroke="#750000" stroke-width="4" /><path d="M68.2248 148.783C69.0243 149.525 69.5328 150.357 69.7415 151.062C69.9573 151.791 69.8141 152.195 69.6516 152.37C69.4892 152.545 69.0976 152.718 68.3543 152.557C67.6357 152.402 66.7679 151.957 65.9684 151.215C65.1688 150.473 64.6603 149.641 64.4517 148.936C64.2359 148.207 64.379 147.803 64.5415 147.628C64.7039 147.453 65.0955 147.28 65.8389 147.441C66.5574 147.596 67.4252 148.041 68.2248 148.783Z" stroke="#750000" stroke-width="2" /><path d="M62.5372 151.611C61.7935 152.57 60.9314 153.229 60.1818 153.547C59.398 153.88 58.9595 153.766 58.7766 153.624C58.5937 153.482 58.3744 153.086 58.5013 152.244C58.6227 151.439 59.0467 150.44 59.7903 149.481C60.534 148.522 61.3961 147.863 62.1457 147.545C62.9296 147.212 63.3681 147.326 63.551 147.468C63.7339 147.61 63.9532 148.006 63.8262 148.848C63.7048 149.653 63.2809 150.652 62.5372 151.611Z" stroke="#750000" stroke-width="2" /></svg>';var vn='<svg width="152" height="220" viewBox="0 0 127 184" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M7.82568 174.501L23.3257 131.001C52.8749 137.508 72.6181 143.564 103.826 135.001C111.05 148.279 114.101 161.223 121.326 174.501C115.575 171.676 112.747 171.756 108.326 174.501C102.171 170.814 100.059 172.108 96.3257 174.501C88.1576 168.27 84.0522 170.116 76.8257 174.501C70.342 169.446 66.8634 169.941 60.8257 174.501C51.247 168.701 46.9661 170.784 39.3364 174.496L39.3257 174.501C33.6234 170.857 30.5816 171.207 25.3257 174.501C18.7562 170.383 14.8574 171.426 7.82568 174.501Z" fill="#8B6CEF" stroke="#8B6CEF" stroke-width="4" /><path d="M80.3257 164.501V181.501H68.3257" stroke="#FF820F" stroke-width="5" stroke-linecap="round" /><path d="M48.3257 164.501V181.501H36.3257" stroke="#FF820F" stroke-width="5" stroke-linecap="round" /><path d="M87.1257 80.501C96.6257 80.501 104.326 88.201 104.326 97.701V149.301C104.326 158.801 96.6257 166.501 87.1257 166.501H41.5257C32.0257 166.501 24.3257 158.801 24.3257 149.301V97.701C24.3257 88.201 32.0257 80.501 41.5257 80.501H87.1257ZM87.1257 84.501H41.5257C34.2257 84.501 28.3257 90.401 28.3257 97.701V135.301C28.3257 142.601 34.2257 148.501 41.5257 148.501H87.1257C94.4257 148.501 100.326 142.601 100.326 135.301V97.701C100.326 90.401 94.4257 84.501 87.1257 84.501ZM66.3257 98.501V113.001L78.7257 105.501L80.8257 108.901L68.2257 116.501L80.8257 124.101L78.7257 127.501L66.3257 120.001V134.501H62.3257V120.001L49.9257 127.501L47.8257 124.101L60.4257 116.501L47.8257 108.901L49.9257 105.501L62.3257 113.001V98.501H66.3257ZM100.326 146.301C97.1257 150.101 92.4257 152.501 87.1257 152.501H41.5257C36.2257 152.501 31.5257 150.101 28.3257 146.301V149.301C28.3257 156.601 34.2257 162.501 41.5257 162.501H87.1257C94.4257 162.501 100.326 156.601 100.326 149.301V146.301Z" fill="#FF820F" stroke="#FF820F" /><path d="M102.326 133.501L119.326 144.105" stroke="#FF820F" stroke-width="5" stroke-linecap="round" /><path d="M7.22161 120.99L26.3257 133.116" stroke="#FF820F" stroke-width="5" stroke-linecap="round" /><path d="M9.32568 136.501L3.32568 105.501" stroke="black" stroke-width="4" /><path d="M3.36682 105.807L1.95891 99.5009" stroke="white" stroke-width="4" /><path d="M39.8257 78.501H88.3257L58.8257 7.00098L39.8257 78.501Z" fill="#8B6CEF" stroke="#8B6CEF" stroke-width="4" /><path d="M70.3257 57.119L69.6786 59.1104L69.5664 59.4559H69.2031H67.1092L68.8032 60.6866L69.0971 60.9002L68.9849 61.2457L68.3378 63.237L70.0318 62.0063L70.3257 61.7928L70.6196 62.0063L72.3136 63.237L71.6665 61.2457L71.5543 60.9002L71.8481 60.6866L73.5421 59.4559H71.4483H71.085L70.9727 59.1104L70.3257 57.119Z" fill="#FFF50F" stroke="#FFF50F" /><path d="M58.3537 35.403L55.5616 39.5509L55.3588 39.8523L55.0095 39.7525L50.2018 38.3788L53.2839 42.316L53.5078 42.602L53.3049 42.9034L50.5129 47.0512L55.2098 45.3367L55.551 45.2121L55.7749 45.4982L58.857 49.4353L58.6778 44.4385L58.6647 44.0755L59.006 43.9509L63.7029 42.2364L58.8952 40.8627L58.5459 40.7629L58.5329 40.3999L58.3537 35.403Z" fill="#FFF50F" stroke="#FFF50F" /><path d="M22.8257 125.501C51.8148 144.152 71.5061 151.504 105.826 131.001" stroke="#5845CF" stroke-width="4" /><path d="M58.101 145.285C57.3014 146.027 56.7929 146.859 56.5843 147.564C56.3685 148.293 56.5117 148.696 56.6741 148.871C56.8366 149.046 57.2281 149.219 57.9715 149.059C58.69 148.903 59.5579 148.458 60.3574 147.716C61.1569 146.975 61.6654 146.142 61.8741 145.437C62.0899 144.708 61.9467 144.305 61.7843 144.13C61.6218 143.955 61.2302 143.782 60.4869 143.942C59.7683 144.098 58.9005 144.543 58.101 145.285Z" stroke="#5845CF" stroke-width="2" /><path d="M63.7886 148.113C64.5322 149.072 65.3943 149.731 66.1439 150.049C66.9278 150.381 67.3663 150.268 67.5492 150.126C67.7321 149.984 67.9514 149.588 67.8244 148.746C67.703 147.94 67.2791 146.941 66.5354 145.982C65.7917 145.023 64.9296 144.364 64.18 144.046C63.3962 143.714 62.9577 143.827 62.7748 143.969C62.5919 144.111 62.3726 144.508 62.4995 145.349C62.6209 146.155 63.0449 147.154 63.7886 148.113Z" stroke="#5845CF" stroke-width="2" /><ellipse rx="41.5" ry="4" transform="matrix(-1 0 0 1 62.8257 79.501)" fill="#8B6CEF" /><path d="M48.7999 64.3399L48.7051 67.8856L48.6954 68.2487L48.3471 68.3517L44.9456 69.3573L48.2885 70.5431L48.6309 70.6645L48.6212 71.0276L48.5264 74.5733L50.6872 71.7605L50.9085 71.4724L51.2508 71.5939L54.5937 72.7796L52.5863 69.8554L52.3807 69.5559L52.602 69.2679L54.7627 66.455L51.3613 67.4606L51.0129 67.5636L50.8073 67.2641L48.7999 64.3399Z" fill="#FFF50F" stroke="#FFF50F" /></svg>';var gn=require("obsidian"),Da={9:vn,11:hn};function Ba(){var e;return r("div",{class:"cmdr-icon-wrapper",dangerouslySetInnerHTML:{__html:(e=Da[(0,gn.moment)().month()])!=null?e:fn}})}var Ce=mn(Ba);function Lo({manifest:e}){let t=r("button",{className:"mod-cta",onClick:n=>{So(n),setTimeout(()=>location.replace("https://forms.gle/hPjn61G9bqqFb3256"),Math.random()*800+500)}},r(k,{icon:"message-square",size:20}),u("Leave feedback")),o=r("button",{className:"mod-cta",onClick:n=>{So(n),setTimeout(()=>location.replace("https://ko-fi.com/phibr0"),Math.random()*800+500)}},r(k,{icon:"coffee",size:20}),u("Support development"));return r("div",{className:"cmdr-about"},To.Platform.isMobile&&[r("hr",null),t,o],To.Platform.isDesktop&&[r("div",{className:"setting-item mod-toggle",style:{width:"100%",borderTop:"1px solid var(--background-modifier-border)",paddingTop:"18px"}},r("div",{className:"setting-item-info"},r("div",{className:"setting-item-name"},u("Leave feedback")),r("div",{className:"setting-item-description"},u("Share feedback, issues, and ideas with our feedback form."))),r("div",{className:"setting-item-control"},t)),r("div",{className:"setting-item mod-toggle",style:{width:"100%"}},r("div",{className:"setting-item-info"},r("div",{className:"setting-item-name"},u("Donate")),r("div",{className:"setting-item-description"},u("Consider donating to support development."))),r("div",{className:"setting-item-control"},o)),r("hr",null)],r(Ce,null),r("b",null,e.name),r(Po,null),r("a",{className:"cmdr-version",href:"https://github.com/phibr0/obsidian-commander/releases/tag/"+e.version},e.version))}var V=require("obsidian");function No(e,t){if(e.empty(),new V.Setting(e).setName("Toolbar Row Count").setDesc("Set how many Rows the Mobile Toolbar should have. Set this to 0 to remove the Toolbar.").addSlider(n=>n.setLimits(0,5,1).setValue(t.settings.advancedToolbar.rowCount).setDynamicTooltip().onChange(async a=>{t.settings.advancedToolbar.rowCount=a,await t.saveSettings(),te(t.settings.advancedToolbar)})),new V.Setting(e).setName("Column Layout").setDesc("Use a column based layout instead of the default row. This makes it easier to arrange the Commands.").addToggle(n=>n.setValue(t.settings.advancedToolbar.columnLayout).onChange(async a=>{t.settings.advancedToolbar.columnLayout=a,await t.saveSettings(),te(t.settings.advancedToolbar)})),new V.Setting(e).setName("Bottom Offset").setDesc("Offset the Toolbar from the Bottom of the Screen. This is useful if the toolbar is partially obscured by other UI Elements.").addSlider(n=>n.setLimits(0,32,1).setValue(t.settings.advancedToolbar.heightOffset).setDynamicTooltip().onChange(async a=>{t.settings.advancedToolbar.heightOffset=a,await t.saveSettings(),te(t.settings.advancedToolbar)})),V.Platform.isMobile){let n=document.createDocumentFragment();n.appendChild(createEl("h3",{text:"Custom Icons"})),e.appendChild(n),t.getCommandsWithoutIcons().forEach(a=>{new V.Setting(e).setName(a.name).setDesc(`ID: ${a.id}`).addButton(i=>{var m;let c=i.buttonEl.createDiv({cls:"AT-settings-icon"});if(a.icon)(0,V.setIcon)(c,a.icon);else{let l=(m=t.settings.advancedToolbar.mappedIcons.find(h=>h.commandID===a.id))==null?void 0:m.iconID;l?(0,V.setIcon)(c,l):i.setButtonText("No Icon")}i.onClick(async()=>{let l=await new R(t).awaitSelection(),h=t.settings.advancedToolbar.mappedIcons.find(s=>s.commandID===a.id);h?h.iconID=l:t.settings.advancedToolbar.mappedIcons.push({commandID:a.id,iconID:l}),await t.saveSettings(),to(t.settings.advancedToolbar),No(e,t)})}).addExtraButton(i=>{i.setIcon("reset").setTooltip("Reset to default - Requires a restart").onClick(async()=>{t.settings.advancedToolbar.mappedIcons=t.settings.advancedToolbar.mappedIcons.filter(c=>c.commandID!==a.id),delete a.icon,delete app.commands.commands[a.id].icon,await t.saveSettings(),No(e,t),new V.Notice("If the default Icon doesn't appear, you might have to restart Obsidian.")})})})}let o=e.appendChild(createEl("div",{cls:"cmdr-sep-con",attr:{style:"margin-top: 64px"}}));o.appendChild(createEl("div",{text:"Advanced Settings",attr:{style:"margin-bottom: 8px; font-weight: bold"}})),new V.Setting(o).setName("Button Height").setDesc("Change the Height of each Button inside the Mobile Toolbar (in px).").addText(n=>{var a,i;return n.setValue((i=(a=t.settings.advancedToolbar.rowHeight)==null?void 0:a.toString())!=null?i:"48").setPlaceholder("48").onChange(async c=>{let m=Number(c),l=isNaN(m);n.inputEl.toggleClass("is-invalid",l),l||(t.settings.advancedToolbar.rowHeight=m,await t.saveSettings(),te(t.settings.advancedToolbar))})}),new V.Setting(o).setName("Button Width").setDesc("Change the Width of each Button inside the Mobile Toolbar (in px).").addText(n=>{var a,i;return n.setValue((i=(a=t.settings.advancedToolbar.buttonWidth)==null?void 0:a.toString())!=null?i:"48").setPlaceholder("48").onChange(async c=>{let m=Number(c),l=isNaN(m);n.inputEl.toggleClass("is-invalid",l),l||(t.settings.advancedToolbar.buttonWidth=m,await t.saveSettings(),te(t.settings.advancedToolbar))})}),new V.Setting(o).setName("Toolbar Extra Spacing").setDesc("Some Themes need extra spacing in the toolbar. If your Toolbar doesn't wrap properly, try increasing this value.").addSlider(n=>n.setLimits(0,64,1).setValue(t.settings.advancedToolbar.spacing).setDynamicTooltip().onChange(async a=>{t.settings.advancedToolbar.spacing=a,await t.saveSettings(),te(t.settings.advancedToolbar)}))}function Do({plugin:e}){let t=K(null);return O(()=>(t.current&&No(t.current,e),()=>t.current&&t.current.empty()),[]),r(P,null,r("div",{className:"cmdr-sep-con callout","data-callout":"info"},r("span",{className:"cmdr-callout-warning"},r(k,{icon:"alert-circle"})," ","Info"),r("p",{className:"cmdr-warning-description"},"The Toolbar is only available in Obsidian Mobile. ",V.Platform.isMobile&&r(P,null,"To configure which Commands show up in the Toolbar, open the Mobile Settings.")),V.Platform.isMobile&&r("button",{onClick:()=>{app.setting.openTabById("mobile")},className:"mod-cta"},"Open Mobile Settings")),r("div",{ref:t,style:{paddingBottom:"128px"}}))}var le=require("obsidian");var Cn=require("obsidian");var bn=require("obsidian");var io=({initialColor:e,onChange:t})=>{let o=K(null);return O(()=>(o.current&&new bn.ColorComponent(o.current).setValue(e).onChange(t),()=>{var n,a;return(a=(n=o.current)==null?void 0:n.empty)==null?void 0:a.call(n)}),[t,e]),r("div",{ref:o,className:"cmdr-flex cmdr-items-center"})};function Bo({modal:e}){var t;return O(()=>{let o=()=>{this.forceUpdate()};return addEventListener("cmdr-icon-changed",o),()=>removeEventListener("cmdr-icon-changed",o)},[]),r("div",{className:"cmdr-mobile-modify-grid"},r("div",{className:"cmdr-mobile-modify-option",onClick:e.handleNewIcon},r("span",null,u("Icon")),r("span",{className:"cmdr-flex cmdr-gap-1"},r(k,{icon:e.pair.icon,size:20,className:"clickable-icon",style:{marginRight:"0px"}}),r(io,{initialColor:(t=e.pair.color)!=null?t:"#000",onChange:e.handleColorChange}))),r("div",{className:"cmdr-mobile-modify-option"},r("span",null,u("Name")),r("input",{onBlur:({currentTarget:o})=>e.handleRename(o.value),type:"text",placeholder:u("Custom Name"),value:e.pair.name})),r("div",{className:"cmdr-mobile-modify-option"},r("select",{className:"dropdown",value:e.pair.mode,onChange:({currentTarget:o})=>e.handleModeChange(o.value)},r("option",{value:"any"},u("Add command to all devices")),r("option",{value:"mobile"},u("Add command only to mobile devices")),r("option",{value:"desktop"},u("Add command only to desktop devices")),r("option",{value:app.appId},u("Add command only to this device")))),r("div",{className:"modal-button-container"},r("button",{className:"mod-cta",onClick:()=>e.close()},u("Done"))))}var me=class extends Cn.Modal{constructor(o,n,a,i,c){super(app);this.pair=o;this.handleRename=n;this.handleNewIcon=a;this.handleModeChange=i;this.handleColorChange=c}async onOpen(){this.titleEl.innerText=this.pair.name,this.reactComponent=r(Bo,{modal:this}),W(this.reactComponent,this.contentEl)}onClose(){W(null,this.contentEl)}};function Ie({value:e,handleChange:t,ariaLabel:o}){let[n,a]=D(!1),i=K(null),[c,m]=D(0);return O(()=>{var l,h;(l=i==null?void 0:i.current)==null||l.select(),(h=i==null?void 0:i.current)==null||h.focus()}),r("div",{class:"cmdr-editable"},n?r("input",{type:"text",value:e,style:{width:c+25+"px"},onKeyDown:l=>{l.key==="Enter"&&l.target.value.length>0&&(a(!1),t(l))},onBlur:()=>a(!1),ref:i}):r("span",{onDblClick:({target:l})=>{m(l==null?void 0:l.offsetWidth),a(!0)},"aria-label":o},e))}function Ho({pair:e,handleRemove:t,handleDown:o,handleUp:n,handleNewIcon:a,handleRename:i,handleModeChange:c,handleColorChange:m,sortable:l=!0}){var v;let h=Q(e.id);if(!h)return r(P,null,le.Platform.isDesktop&&r("div",{className:"setting-item mod-toggle"},r(k,{icon:"alert-triangle",size:20,className:"cmdr-icon clickable-icon mod-warning"}),r("div",{className:"setting-item-info"},r("div",{className:"setting-item-name"},e.name),r("div",{className:"setting-item-description"},u("This Command is not available on this device."))),r("div",{className:"setting-item-control"},r("button",{className:"mod-warning",style:"display: flex",onClick:t,"aria-label":u("Delete")},r(k,{icon:"lucide-trash"})))),le.Platform.isMobile&&r("div",{className:"mobile-option-setting-item",onClick:()=>{new le.Notice(u("This Command is not available on this device."))}},r("span",{className:"mobile-option-setting-item-remove-icon",onClick:t},r(k,{icon:"minus-with-circle",size:22,style:{color:"var(--text-error)"}})),r("span",{className:"mobile-option-setting-item-option-icon mod-warning"},r(k,{icon:"alert-triangle",size:22})),r("span",{className:"mobile-option-setting-item-name"},e.name)));let s=h.id.split(":").first(),g=app.plugins.manifests[s],C=!g,f=h.hasOwnProperty("checkCallback")||h.hasOwnProperty("editorCheckCallback"),b=Ha(e.mode),y=e.mode.match(/desktop|mobile|any/)?e.mode[0].toUpperCase()+e.mode.substring(1):u("This device");return r(P,null,le.Platform.isDesktop&&r("div",{className:"setting-item mod-toggle"},r(k,{icon:e.icon,size:20,"aria-label":u("Choose new"),onClick:a,className:"cmdr-icon clickable-icon"}),r("div",{className:"setting-item-info"},r("div",{className:"setting-item-name"},r(Ie,{ariaLabel:u("Double click to rename"),handleChange:({target:A})=>{i(A==null?void 0:A.value)},value:e.name}),e.name!==h.name&&r("span",{style:"margin-left: .8ex"},"(",h.name,")")),r("div",{className:"setting-item-description"},u("Added by {{plugin_name}}.".replace("{{plugin_name}}",C?"Obsidian":g.name))," ",f?u("Warning: This is a checked Command, meaning it might not run under every circumstance."):"")),r("div",{className:"setting-item-control"},r(io,{initialColor:(v=e.color)!=null?v:"#000",onChange:m}),l&&r(P,null,r(k,{icon:"arrow-down",className:"setting-editor-extra-setting-button clickable-icon",onClick:o,"aria-label":u("Move down")}),r(k,{icon:"arrow-up",className:"setting-editor-extra-setting-button clickable-icon",onClick:n,"aria-label":u("Move up")})),r(k,{icon:b,className:"setting-editor-extra-setting-button clickable-icon",onClick:()=>c(),"aria-label":u("Change Mode (Currently: {{current_mode}})").replace("{{current_mode}}",y)}),r("button",{className:"mod-warning",style:"display: flex",onClick:t,"aria-label":u("Delete")},r(k,{icon:"lucide-trash"})))),le.Platform.isMobile&&r("div",{className:"mobile-option-setting-item"},r("span",{className:"mobile-option-setting-item-remove-icon",onClick:t},r(k,{icon:"minus-with-circle",size:22,style:{color:"var(--text-error)"}})),r("span",{className:"mobile-option-setting-item-option-icon"},r(k,{icon:e.icon,size:22,onClick:()=>{new me(e,i,a,c,m).open()}})),r("span",{className:"mobile-option-setting-item-name",onClick:()=>{new me(e,i,a,c,m).open()}},e.name,e.name!==h.name&&r("span",{className:"cmdr-option-setting-name"},"(",h.name,")")),r("span",{className:"mobile-option-setting-item-option-icon"},l&&r(P,null,r(k,{icon:"arrow-down",className:"clickable-icon",onClick:o}),r(k,{icon:"arrow-up",className:"clickable-icon",onClick:n})),r(k,{icon:"three-horizontal-bars",className:"clickable-icon",onClick:()=>{new me(e,i,a,c,m).open()}}))))}function Ha(e){return e==="mobile"?"smartphone":e==="desktop"?"monitor":e==="any"?"cmdr-all-devices":"airplay"}function Ro(e,t,o){let n=t<0?e.length+t:t;if(n>=0&&n<e.length){let a=o<0?e.length+o:o,[i]=e.splice(t,1);e.splice(a,0,i)}}var _n=require("obsidian");var Ra=Xe(null);function ie({manager:e,plugin:t,children:o,sortable:n=!0}){return r(P,null,r(Ra.Provider,{value:e},r("div",{className:"cmdr-sep-con"},e.pairs.map((a,i)=>{if(a.mode.match(/desktop|mobile|any/)||a.mode===app.appId)return r(Ho,{sortable:n,key:a.id,pair:a,handleRemove:async()=>{(!t.settings.confirmDeletion||await new F(t).didChooseRemove())&&(await e.removeCommand(a),this.forceUpdate())},handleUp:()=>{Ro(e.pairs,i,i-1),e.reorder(),this.forceUpdate()},handleDown:()=>{Ro(e.pairs,i,i+1),e.reorder(),this.forceUpdate()},handleRename:async c=>{a.name=c,await t.saveSettings(),e.reorder(),this.forceUpdate()},handleNewIcon:async()=>{let c=await new R(t).awaitSelection();c&&c!==a.icon&&(a.icon=c,await t.saveSettings(),e.reorder(),this.forceUpdate()),dispatchEvent(new Event("cmdr-icon-changed"))},handleModeChange:async c=>{let m=["any","desktop","mobile",app.appId],l=m.indexOf(a.mode);l===3&&(l=-1),a.mode=c||m[l+1],await t.saveSettings(),e.reorder(),this.forceUpdate()},handleColorChange:async c=>{a.color=c,await t.saveSettings(),e.reorder()}})})),!e.pairs.some(a=>X(a.mode)||a.mode.match(/mobile|desktop/))&&r("div",{class:"cmdr-commands-empty"},r(Ce,null),r("h3",null,u("No commands here!")),r("span",null,u("Would you like to add one now?"))),_n.Platform.isMobile&&r("hr",null),r("div",{className:"cmdr-add-new-wrapper"},r("button",{className:"mod-cta",onClick:async()=>{let a=await q(t);await e.addCommand(a),e.reorder(),this.forceUpdate()}},u("Add command")))),o)}function ro({title:e,children:t}){let[o,n]=D(!1);return r("div",{className:"cmdr-accordion cmdr-sep-con","aria-expanded":o},r("div",{className:"cmdr-accordion-header cmdr-mb-1",onClick:()=>{n(!o)}},r(k,{className:"cmdr-accordion-chevron clickable-icon",icon:"chevron-down",size:24}),r("span",null,e)),r("div",{className:"cmdr-accordion-content",style:{maxHeight:[t].flat().length*120+"px"}},t))}function zo({name:e,description:t,children:o,className:n}){return r("div",{className:`setting-item ${n}`},r("div",{className:"setting-item-info"},r("div",{className:"setting-item-name"},e),r("div",{className:"setting-item-description"},t)),r("div",{className:"setting-item-control"},o))}function Oo(e){let[t,o]=D(e.value);return r(zo,{name:e.name,description:e.description,className:"mod-toggle"},r("div",{className:`checkbox-container ${t?"is-enabled":""}`,onClick:()=>{o(!t),e.changeHandler(t)}}))}function Fo({name:e,description:t,changeHandler:o,value:n,hideLabel:a,showLabel:i}){let[c,m]=D(n);return r(zo,{name:e,description:t,className:"mod-toggle"},r(k,{"aria-label":c?i:a,icon:c?"eye-off":"eye",size:20,className:"clickable-icon",onClick:()=>{m(!c),o(c)}}))}function so(e){var n,a,i;let[t,o]=D(e.value);return r(zo,{description:e.description,name:e.name,className:"cmdr-slider"},r("div",null,r(Ie,{ariaLabel:u("Double click to enter custom value"),value:t.toString(),handleChange:({target:c})=>{let m=Number(c.value);!isNaN(m)&&t!==m&&(o(m),e.changeHandler(m))}}),r("input",{class:"slider",type:"range",min:(n=e.min)!=null?n:"0",max:(a=e.max)!=null?a:"32",step:(i=e.step)!=null?i:"1",value:t,onPointerMove:({target:c})=>{t!==c.value&&(o(c.value),e.changeHandler(c.value))}})))}function yn({plugin:e}){let[t,o]=D([]),n=e.settings.hide.leftRibbon;return O(()=>{o(app.workspace.leftRibbon.items.map(a=>({name:a.title,icon:a.icon})))},[]),r(P,null,r("hr",null),r(ro,{title:u("Hide other Commands")},t.map(a=>r(Fo,{name:a.name,description:"",hideLabel:u("Hide"),showLabel:u("Show"),changeHandler:async i=>{i?n.contains(a.name)&&n.remove(a.name):n.push(a.name),Me(e.settings),await e.saveSettings()},value:n.contains(a.name)}))))}function wn({plugin:e}){let t=e.settings.hide.statusbar,[o,n]=D([]);return O(()=>{let i=[...app.statusBar.containerEl.getElementsByClassName("status-bar-item")].map(c=>[...c.classList].find(m=>m.startsWith("plugin-"))).filter(c=>c).map(c=>c.substring(7));n(i.map(c=>app.plugins.manifests[c]||{id:c,name:c.replace(/-/g," ").replace(/(^\w{1})|(\s+\w{1})/g,m=>m.toUpperCase()),description:"Core Plugin"}))},[]),r(P,null,r("hr",null),r(ro,{title:u("Hide other Commands")},o.map(a=>r(Fo,{name:a.name,description:a.description,value:t.contains(a.id),hideLabel:u("Hide"),showLabel:u("Show"),changeHandler:async i=>{i?t.contains(a.id)&&t.remove(a.id):t.push(a.id),Me(e.settings),await e.saveSettings()}}))))}var En=require("obsidian");var Mn=require("obsidian");function kn({plugin:e,macro:t,onSave:o,onCancel:n}){let[a,i]=D(t.name||"Macro Name"),[c,m]=D(t.icon||"star"),[l,h]=D(t.startup||!1),[s,g]=D(JSON.parse(JSON.stringify(t.macro))||[]),C=this.forceUpdate.bind(this),f=async()=>{let y=await new ne(e).awaitSelection();y&&g([...s,{action:0,commandId:y.id}])},b=async()=>{g([...s,{action:1,delay:250}])};return r("div",null,r("div",{class:"setting-item cmdr-mm-item"},r("div",null,r("span",null,"Name"),r("input",{type:"text",placeholder:"Macro Name",value:a,onChange:y=>i(y.currentTarget.value),width:"100%"})),r("div",null,r("span",null,"Icon"),r("button",{onClick:async()=>m(await new R(e).awaitSelection())},r(k,{icon:c})))),s.map((y,v)=>{switch(y.action){case 0:let A=Q(y.commandId);return r("div",{class:"setting-item cmdr-mm-item"},r("div",null,r("button",{onClick:async()=>{let w=await new ne(e).awaitSelection();g(s.map((H,ee)=>ee===v?Go(Fe({},H),{commandId:w.id}):H))}},(A==null?void 0:A.name)||"Cannot find Command")),r("div",null,r("div",{class:"cmdr-mm-action-options"},r(k,{class:"clickable-icon",icon:"arrow-down",onClick:()=>{if(v===s.length-1)return;let w=[...s],H=w[v];w[v]=w[v+1],w[v+1]=H,g(w)}}),r(k,{class:"clickable-icon",icon:"arrow-up",onClick:()=>{if(v===0)return;let w=[...s],H=w[v];w[v]=w[v-1],w[v-1]=H,g(w)}}),r(k,{class:"clickable-icon",icon:"cross",onClick:()=>{g(s.filter((w,H)=>H!==v))}}))));case 1:return r("div",{class:"setting-item cmdr-mm-item"},r("div",null,r(so,{name:"Delay",min:0,max:1e4,step:50,description:"Delay in milliseconds",value:y.delay,changeHandler:w=>y.delay=w})),r("div",null,r("div",{class:"cmdr-mm-action-options"},r(k,{class:"clickable-icon",icon:"arrow-down",onClick:()=>{if(v===s.length-1)return;let w=[...s],H=w[v];w[v]=w[v+1],w[v+1]=H,g(w)}}),r(k,{class:"clickable-icon",icon:"arrow-up",onClick:()=>{if(v===0)return;let w=[...s],H=w[v];w[v]=w[v-1],w[v-1]=H,g(w)}}),r(k,{class:"clickable-icon",icon:"cross",onClick:()=>{g(s.filter((w,H)=>H!==v))}}))));case 2:return r("div",null,"Editor: ",y.action);case 3:return r("div",null,"Loop: ",y.times)}}),r("div",{className:"setting-item cmdr-mm-actions cmdr-justify-between"},r("div",{className:"cmdr-flex cmdr-items-center cmdr-justify-self-start"},r("input",{type:"checkbox",id:"checkbox",checked:l,onChange:({target:y})=>{var v;h((v=y==null?void 0:y.checked)!=null?v:!1)}}),r("label",{htmlFor:"checkbox"},"Auto-Run on Startup")),r("div",null,r("button",{onClick:f},"Add Command"),r("button",{onClick:b},"Add Delay"))),r("div",{className:"cmdr-mm-control"},r("button",{class:s.length===0?"disabled":"mod-cta",disabled:s.length===0,onClick:()=>s.length&&o({macro:s,name:a,icon:c,startup:l})},"Save"),r("button",{onClick:n},"Cancel")))}var Ae=class extends Mn.Modal{constructor(o,n,a){super(app);this.macro=n,this.plugin=o,this.onSave=a}onOpen(){this.titleEl.setText("Macro Builder"),W(r(kn,{plugin:this.plugin,macro:this.macro,onSave:this.onSave,onCancel:this.close.bind(this)}),this.contentEl)}onClose(){W(null,this.contentEl)}};function Vo({plugin:e,macros:t}){let o=(a,i)=>{let c=l=>{t.splice(i!==void 0?i:t.length,i!==void 0?1:0,l),e.saveSettings(),this.forceUpdate(),Ee(e),m.close()},m=new Ae(e,a,c);m.open()},n=a=>{t.splice(a,1),e.saveSettings(),this.forceUpdate(),Ee(e)};return r(P,null,r("div",{className:"cmdr-sep-con"},t.map((a,i)=>r("div",{class:"setting-item mod-toggle"},r("div",{className:"setting-item-info"},r("div",{className:"setting-item-name"},a.name),r("div",{className:"setting-item-description"},a.macro.length," Actions")),r("div",{className:"setting-item-control"},r("button",{"aria-label":"Edit Macro",onClick:()=>o(a,i)},r(k,{icon:"lucide-pencil"})),r("button",{"aria-label":"Delete",class:"mod-warning",onClick:async()=>{(!e.settings.confirmDeletion||await new F(e).didChooseRemove())&&n(i)}},r(k,{icon:"trash"})))))),!t.length&&r("div",{class:"cmdr-commands-empty"},r(Ce,null),r("h3",null,"No Macros yet!"),r("span",null,u("Would you like to add one now?"))),En.Platform.isMobile&&r("hr",null),r("div",{className:"cmdr-add-new-wrapper"},r("button",{class:"mod-cta",onClick:()=>o({name:"",macro:[],icon:"star"})},"Add Macro")))}function Te({plugin:e,mobileMode:t}){let[o,n]=D(0),[a,i]=D(!0),c=({key:l,shiftKey:h})=>{h&&l==="Tab"?o>0?n((o-1)%m.length):n(m.length-1):l==="Tab"&&n((o+1)%m.length)};O(()=>(addEventListener("keydown",c),()=>removeEventListener("keydown",c)),[o]),J.Platform.isMobile&&O(()=>{let l=document.querySelector(".modal-setting-back-button"),h=l.cloneNode(!0);l.parentNode.replaceChild(h,l),i(!0)},[]),O(()=>{let l=document.querySelector(".modal-setting-back-button");!l||(a?(l.parentElement.lastChild.textContent="Commander",l.onclick=()=>app.setting.closeActiveTab()):(l.parentElement.lastChild.textContent=m[o].name,l.onclick=()=>i(!0)))},[a]);let m=Qe(()=>[{name:u("General"),tab:r(P,null,r(Oo,{name:u("Always ask before removing?"),description:u("Always show a Popup to confirm deletion of a Command."),value:e.settings.confirmDeletion,changeHandler:async l=>{e.settings.confirmDeletion=!l,await e.saveSettings()}}),r(Oo,{value:e.settings.showAddCommand,name:u('Show "Add Command" Button'),description:'Show the "Add Command" Button in every Menu.',changeHandler:async l=>{e.settings.showAddCommand=!l,e.manager.pageHeader.reorder(),await e.saveSettings()}}),r(so,{value:e.settings.spacing,name:u("Choose custom spacing for Command Buttons"),description:u("Change the spacing between commands. You can set different values on mobile and desktop."),changeHandler:async l=>{oo(l),e.settings.spacing=l,await e.saveSettings()}}))},{name:u("Left Ribbon"),tab:r(ie,{manager:e.manager.leftRibbon,plugin:e,sortable:!1},r(yn,{plugin:e}),r("div",{className:"cmdr-sep-con callout","data-callout":"warning"},r("span",{className:"cmdr-callout-warning"},r(k,{icon:"alert-triangle"})," ","Reordering and Sorting"),r("p",{className:"cmdr-warning-description"},"As of Obsidian 1.1.0 you can reorder the Buttons in the left ribbon by dragging. This will replace the old sorting feature.")))},{name:u("Page Header"),tab:r(ie,{manager:e.manager.pageHeader,plugin:e},r("hr",null),r("div",{className:"cmdr-sep-con callout","data-callout":"warning"},r("span",{className:"cmdr-callout-warning"},r(k,{icon:"alert-triangle"})," ",u("Warning")),r("p",{className:"cmdr-warning-description"},u("As of Obsidian 0.16.0 you need to explicitly enable the View Header.")),r("button",{onClick:()=>{app.setting.openTabById("appearance"),setTimeout(()=>{var l,h,s,g;app.setting.activeTab.containerEl.scroll({behavior:"smooth",top:250}),(g=(s=(h=(l=app.setting.activeTab.containerEl.querySelectorAll(".setting-item-heading")[1].nextSibling)==null?void 0:l.nextSibling)==null?void 0:h.nextSibling)==null?void 0:s.addClass)==null||g.call(s,"cmdr-cta")},50)},className:"mod-cta"},u("Open Appearance Settings"))))},{name:u("Statusbar"),tab:r(ie,{manager:e.manager.statusBar,plugin:e},r(wn,{plugin:e}))},{name:u("Editor Menu"),tab:r(ie,{manager:e.manager.editorMenu,plugin:e})},{name:u("File Menu"),tab:r(ie,{manager:e.manager.fileMenu,plugin:e})},{name:u("Explorer"),tab:r(ie,{manager:e.manager.explorerManager,plugin:e},r("hr",null),r("div",{className:"cmdr-sep-con callout","data-callout":"warning"},r("span",{className:"cmdr-callout-warning"},r(k,{icon:"alert-triangle"})," ",u("Warning")),r("p",{className:"cmdr-warning-description"},"When clicking on a Command in the Explorer, the Explorer view will become focused. This might interfere with Commands that are supposed to be executed on an active File/Explorer.")))},{name:J.Platform.isMobile?"Mobile Toolbar":"Toolbar",tab:r(Do,{plugin:e})},{name:"Macros",tab:r(Vo,{plugin:e,macros:e.settings.macros})}],[]);return r(P,null,J.Platform.isDesktop&&r("div",{className:"cmdr-setting-title"},r("h1",null,e.manifest.name)),(J.Platform.isDesktop||a)&&r(Oa,{tabs:m,activeTab:o,setActiveTab:n,setOpen:i}),r("div",{class:`cmdr-setting-content ${t?"cmdr-mobile":""}`},(J.Platform.isDesktop||!a)&&m[o].tab,(J.Platform.isMobile&&a||J.Platform.isDesktop&&o===0)&&r(Lo,{manifest:e.manifest})))}function Oa({tabs:e,activeTab:t,setActiveTab:o,setOpen:n}){let a=K(null),i=c=>{var m;c.preventDefault(),(m=a.current)==null||m.scrollBy({left:c.deltaY>0?16:-16})};return O(()=>{let c=a.current;if(!(!c||J.Platform.isMobile))return c.addEventListener("wheel",i),()=>c.removeEventListener("wheel",i)},[]),O(()=>{var c;return(c=document.querySelector(".cmdr-tab-active"))==null?void 0:c.scrollIntoView({behavior:"smooth",block:"nearest"})},[t]),r("nav",{class:`cmdr-setting-header ${J.Platform.isMobile?"cmdr-mobile":""}`,ref:a},r("div",{class:`cmdr-setting-tab-group ${J.Platform.isMobile?"vertical-tab-header-group-items":""}`},e.map((c,m)=>r("div",{className:`cmdr-tab ${t===m?"cmdr-tab-active":""} ${J.Platform.isMobile?"vertical-tab-nav-item":""}`,onClick:()=>{o(m),n(!1)}},c.name,J.Platform.isMobile&&r(k,{className:"vertical-tab-nav-item-chevron cmdr-block",icon:"chevron-right",size:24})))),J.Platform.isDesktop&&r("div",{className:"cmdr-fill"}))}var Le=class extends co.PluginSettingTab{constructor(o){super(app,o);this.plugin=o}display(){W(r(Te,{plugin:this.plugin,mobileMode:co.Platform.isMobile}),this.containerEl)}hide(){W(null,this.containerEl)}};var mo=require("obsidian");var Ne=class extends mo.Modal{constructor(o){super(app);this.plugin=o,this.containerEl.addClass("cmdr-setting-modal")}onOpen(){let o=mo.Platform.isMobile;W(r(Te,{plugin:this.plugin,mobileMode:o}),this.contentEl)}onClose(){W(null,this.contentEl)}};var xn=require("obsidian");function Wo(){(0,xn.addIcon)("cmdr-all-devices",'<g style="fill: currentColor;"><path d="M 12.5 16.667969 L 83.332031 16.667969 C 87.9375 16.667969 91.667969 20.398438 91.667969 25 L 91.667969 33.332031 L 75 33.332031 L 75 25 L 20.832031 25 L 20.832031 75 L 58.332031 75 L 58.332031 83.332031 L 12.5 83.332031 C 7.898438 83.332031 4.167969 79.601562 4.167969 75 L 4.167969 25 C 4.167969 20.398438 7.898438 16.667969 12.5 16.667969 M 70.832031 41.667969 L 95.832031 41.667969 C 98.132812 41.667969 100 43.53125 100 45.832031 L 100 87.5 C 100 89.800781 98.132812 91.667969 95.832031 91.667969 L 70.832031 91.667969 C 68.53125 91.667969 66.667969 89.800781 66.667969 87.5 L 66.667969 45.832031 C 66.667969 43.53125 68.53125 41.667969 70.832031 41.667969 M 75 50 L 75 79.167969 L 91.667969 79.167969 L 91.667969 50 Z M 75 50 "/></g>')}var De=class extends Z{constructor(o){super(o,o.settings.leftRibbon);this.plugin=o,this.plugin.settings.leftRibbon.forEach(n=>this.addCommand(n,!1)),app.workspace.onLayoutReady(()=>{})}async addCommand(o,n=!0){if(n&&(this.plugin.settings.leftRibbon.push(o),await this.plugin.saveSettings()),X(o.mode)){this.plugin.addRibbonIcon(o.icon,o.name,()=>app.commands.executeCommandById(o.id));let a=app.workspace.leftRibbon.items.find(i=>i.icon===o.icon&&i.name===i.name);a&&(a.buttonEl.style.color=o.color==="#000000"||o.color===void 0?"inherit":o.color),this.plugin.register(()=>this.removeCommand(o,!1))}}async removeCommand(o,n=!0){n&&(this.plugin.settings.leftRibbon.remove(o),await this.plugin.saveSettings());let a=app.workspace.leftRibbon.items.find(i=>i.icon===o.icon&&i.name===i.name);a&&a.buttonEl.remove(),app.workspace.leftRibbon.items.remove(a)}reorder(){this.plugin.settings.leftRibbon.forEach(o=>{this.removeCommand(o,!1),this.addCommand(o,!1)})}};var lo=class extends Sn.Plugin{async executeStartupMacros(){this.settings.macros.forEach((o,n)=>{o.startup&&this.executeMacro(n)})}async executeMacro(o){let n=this.settings.macros[o];if(!n)throw new Error("Macro not found");for(let a of n.macro)switch(a.action){case 0:{await app.commands.executeCommandById(a.commandId);continue}case 1:{await new Promise(i=>setTimeout(i,a.delay));continue}case 2:continue;case 3:{for(let i=0;i<a.times;i++)await app.commands.executeCommandById(a.commandId);continue}}}async onload(){var o,n;await this.loadSettings(),(n=(o=this.settings.hide).leftRibbon)!=null||(o.leftRibbon=[]),Wo(),this.manager={editorMenu:new Se(this,this.settings.editorMenu),fileMenu:new Pe(this,this.settings.fileMenu),leftRibbon:new De(this),statusBar:new be(this,this.settings.statusBar),pageHeader:new ge(this,this.settings.pageHeader),explorerManager:new ve(this,this.settings.explorer)},this.addSettingTab(new Le(this)),this.addCommand({name:u("Open Commander Settings"),id:"open-commander-settings",callback:()=>new Ne(this).open()}),this.registerEvent(app.workspace.on("editor-menu",this.manager.editorMenu.applyEditorMenuCommands(this))),this.registerEvent(app.workspace.on("file-menu",this.manager.fileMenu.applyFileMenuCommands(this))),app.workspace.onLayoutReady(()=>{Me(this.settings),Ee(this),oo(this.settings.spacing),te(this.settings.advancedToolbar),to(this.settings.advancedToolbar),this.executeStartupMacros()})}onunload(){var o;(o=document.head.querySelector("style#cmdr"))==null||o.remove(),$t()}async loadSettings(){let o=Object.assign({},yt,await this.loadData());this.settings=o}async saveSettings(){await this.saveData(this.settings)}listActiveToolbarCommands(){return this.app.vault.getConfig("mobileToolbarCommands")}getCommands(){let o=[];return this.listActiveToolbarCommands().forEach(n=>{let a=this.app.commands.commands[n];a&&o.push(a)}),o}getCommandsWithoutIcons(o=!0){let n=[];return this.getCommands().forEach(a=>{a&&!a.icon&&n.push(a)}),o&&this.getCommands().forEach(a=>{this.settings.advancedToolbar.mappedIcons.find(i=>i.commandID===a.id)&&n.push(a)}),n}};

/* by phibr0 */
