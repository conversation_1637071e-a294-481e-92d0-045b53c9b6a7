{"path": "Deep learning/训练实践/mmsegmentation/Attachments/Pasted image 20250227160140.png", "text": "vmmsesMENtaTon [3 BF O @ > B3 demo > B diagrams > BJ docker p=E > B3 _pycache_ > B3 mim > B apis > B3 configs > B _pycache_ > B3 transforms D _init_py ™M [ adepy [ basesegdataset.py O bdd100k.py [ chase_db1.py D cityscapes.py D coco_stuftpy D dark zurich.py [ dataset_wrappers.py ™ decathlon oy", "libVersion": "0.3.2", "langs": "eng"}