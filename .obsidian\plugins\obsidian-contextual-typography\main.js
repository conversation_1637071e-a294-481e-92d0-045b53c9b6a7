/*
THIS IS A GENERATED/BUNDLED FILE BY ROLLUP
if you want to view the source visit the plugins github repository
*/

'use strict';

var obsidian = require('obsidian');

/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, <PERSON>GL<PERSON>ENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */
/* global Reflect, Promise */

var extendStatics = function(d, b) {
    extendStatics = Object.setPrototypeOf ||
        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
    return extendStatics(d, b);
};

function __extends(d, b) {
    if (typeof b !== "function" && b !== null)
        throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
    extendStatics(d, b);
    function __() { this.constructor = d; }
    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
}

var imageExt = ["bmp", "png", "jpg", "jpeg", "gif", "svg", "webp"];
var audioExt = ["mp3", "wav", "m4a", "3gp", "flac", "ogg", "oga"];
var videoExt = ["mp4", "webm", "ogv"];
function isInternalEmbed(node) {
    var _a;
    if (node.nodeType === 3) {
        return false;
    }
    var child = node.firstChild;
    return child && ((_a = child.classList) === null || _a === void 0 ? void 0 : _a.contains("internal-embed"));
}
function getEmbedType(node) {
    var _a;
    if (node.nodeType === 3) {
        return null;
    }
    var child = node.firstChild;
    var src = child.getAttr("src");
    if (!src) {
        return null;
    }
    var ext = (_a = src.split(".").pop()) === null || _a === void 0 ? void 0 : _a.toLowerCase();
    if (imageExt.contains(ext))
        return "image";
    if (audioExt.contains(ext))
        return "audio";
    if (videoExt.contains(ext))
        return "video";
    if (/#\^[^\^]+$/.test(src))
        return "block";
    if (/#[^#]+$/.test(src))
        return "heading";
    return "page";
}
function isExternalImageEmbed(node) {
    var _a;
    if (node.nodeType === 3) {
        return false;
    }
    var child = node.firstChild;
    return child && ((_a = child.tagName) === null || _a === void 0 ? void 0 : _a.toLowerCase()) === "img";
}
function getBlockLanguage(node) {
    if (node.nodeType === 3) {
        return null;
    }
    var lang = null;
    node.classList.forEach(function (cls) {
        if (cls.startsWith("block-language")) {
            lang = cls.replace(/^block\-language\-/, "");
        }
    });
    return lang;
}
function fixMarkdownLinkEmbeds(node) {
    if (node.children.length <= 1) {
        return;
    }
    var containsNakedEmbed = false;
    var childNodes = [];
    node.childNodes.forEach(function (n) {
        var _a;
        if (n.nodeValue === "\n")
            return;
        switch (n.nodeName) {
            case "P": {
                if (n.children.length === 0) {
                    return;
                }
                else {
                    n.childNodes.forEach(function (pn) {
                        if (pn.nodeName !== "BR" && pn.nodeValue !== "\n")
                            childNodes.push(pn);
                    });
                    return;
                }
            }
            case "BR": {
                return;
            }
        }
        if (n.nodeType === 1 &&
            ((_a = n.classList) === null || _a === void 0 ? void 0 : _a.contains("internal-embed"))) {
            containsNakedEmbed = true;
        }
        childNodes.push(n);
    });
    if (!containsNakedEmbed)
        return;
    node.empty();
    node.createEl("p", {}, function (p) {
        childNodes.forEach(function (c, i, arr) {
            var _a;
            p.append(c);
            var nodeIsEmbed = c.nodeType === 1 &&
                !!c.getAttribute("src") &&
                i < arr.length - 1;
            var nodeIsTextFollowedByEmbed = c.nodeType === 3 &&
                ((_a = arr[i + 1]) === null || _a === void 0 ? void 0 : _a.nodeType) === 1 &&
                !!arr[i + 1].getAttribute("src");
            if (nodeIsEmbed || nodeIsTextFollowedByEmbed) {
                p.createEl("br");
            }
        });
    });
}
function tagNode(node, ctx) {
    if (node.nodeType === 3) {
        return;
    }
    var nodeEl = node;
    var isPrint = nodeEl.hasClass("markdown-preview-view");
    if (!isPrint &&
        !nodeEl.dataset.tagName &&
        nodeEl.hasChildNodes() &&
        nodeEl.firstChild.nodeType !== 3) {
        fixMarkdownLinkEmbeds(nodeEl);
        var childEl_1 = node.firstChild;
        Object.keys(childEl_1.dataset).forEach(function (k) { return (nodeEl.dataset[k] = childEl_1.dataset[k]); });
        nodeEl.findAll("a.tag").forEach(function (tagEl) {
            var tag = tagEl.innerText
                .slice(1)
                .replace("/", "");
            nodeEl.addClass("tag-" + tag);
        });
        var tagName = childEl_1.tagName.toLowerCase();
        if (isExternalImageEmbed(childEl_1)) {
            nodeEl.dataset.isEmbed = "true";
            nodeEl.dataset.embedType = "image";
            nodeEl.addClass("el-embed-image");
        }
        else if (isInternalEmbed(childEl_1)) {
            var embedType = getEmbedType(childEl_1);
            nodeEl.dataset.isEmbed = "true";
            nodeEl.dataset.embedType = embedType;
            nodeEl.addClass("el-embed-" + embedType);
        }
        else {
            var blockLang = getBlockLanguage(childEl_1);
            if (blockLang) {
                nodeEl.dataset.blockLanguage = blockLang;
                nodeEl.addClass("el-lang-" + blockLang);
            }
        }
        nodeEl.dataset.tagName = tagName;
        nodeEl.addClass("el-" + tagName);
    }
    else if (isPrint && nodeEl.children.length > 0) {
        var children = nodeEl.children;
        var _loop_1 = function (i) {
            var child = children[i];
            child.findAll("a.tag").forEach(function (tagEl) {
                var tag = tagEl.innerText
                    .slice(1)
                    .replace("/", "");
                child.addClass("tag-" + tag);
            });
        };
        for (var i = 0; i < children.length; i++) {
            _loop_1(i);
        }
    }
}
var ContextualTypography = /** @class */ (function (_super) {
    __extends(ContextualTypography, _super);
    function ContextualTypography() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    ContextualTypography.prototype.onload = function () {
        document.body.classList.add("contextual-typography");
        this.registerMarkdownPostProcessor(tagNode);
    };
    ContextualTypography.prototype.unload = function () {
        document.body.classList.remove("contextual-typography");
    };
    return ContextualTypography;
}(obsidian.Plugin));

module.exports = ContextualTypography;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
