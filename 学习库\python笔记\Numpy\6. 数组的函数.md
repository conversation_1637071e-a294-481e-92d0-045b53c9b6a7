---
tags:
  - 学习
  - python
  - Numpy
---

# 矩阵乘积

[[5. 数组的运算#数组与系数之间的运算|第五章]] 介绍的运算都是逐元素运算，Numpy还提供了矩阵乘积的运算符 `@` 和函数 `dot()` 来实现线性代数中的矩阵乘法。

## 向量与向量的乘积

设有两个向量的形状分别是$(1,5)$ 和 $(5,1)$，从矩阵乘法的角度，它们的乘积是一个$(1,1)$的矩阵，表示两个向量的内积

```python
import numpy as np
# 创建一维数组-向量
arr1 = np.array([1, 2, 3, 4, 5])
arr2 = np.array([1, 2, 3, 4, 5])
print(arr1)  # 输出结果为[1 2 3 4 5]
print(arr2)  # 输出结果为[1 2 3 4 5]
print(np.dot(arr1, arr2))  # 输出结果为55
print(arr1 @ arr2)  # 输出结果为55
```

## 向量与矩阵的乘积

设有一个向量的形状是$(1,5)$，一个矩阵的形状是$(5,3)$，从矩阵乘法的角度，它们的乘积是一个$(1,3)$的向量，而不是矩阵

```python
import numpy as np  
# 创建一维数组-向量
arr1 = np.array([1, 2, 3, 4, 5])
# 创建二维数组-矩阵
arr2 = np.arange(1, 16).reshape(5, 3)
print(arr1)  # 输出结果为[1 2 3 4 5]
print(arr2)  # 输出结果为[[ 1  2  3]
             #          [ 4  5  6]
             #          [ 7  8  9]
             #          [10 11 12]
             #          [13 14 15]]
print(np.dot(arr1, arr2))  # 输出结果为[ 70  80  90]
print(arr1 @ arr2)  # 输出结果为[ 70  80  90]
```

## 矩阵与向量的乘积

设有一个矩阵的形状是$(3,5)$，一个向量的形状是$(5,1)$，从矩阵乘法的角度，它们的乘积是一个$(3,1)$的向量，而不是矩阵

```python
import numpy as np
# 创建二维数组-矩阵
arr1 = np.arange(1, 16).reshape(3, 5)
# 创建一维数组-向量
arr2 = np.array([1, 2, 3, 4, 5])
print(arr1)  # 输出结果为[[ 1  2  3  4  5]
                #          [ 6  7  8  9 10]
                #          [11 12 13 14 15]]
print(arr2)  # 输出结果为[1 2 3 4 5]
print(np.dot(arr1, arr2))  # 输出结果为[ 70  80  90]
print(arr1 @ arr2)  # 输出结果为[ 70  80  90]
``` 

## 矩阵与矩阵的乘积

设有两个矩阵的形状分别是$(5,2)$ 和 $(2,8)$，从矩阵乘法的角度，它们的乘积是一个$(5,8)$的矩阵

```python   
import numpy as np
# 创建二维数组-矩阵
arr1 = np.arange(1, 11).reshape(5, 2)
# 创建二维数组-矩阵
arr2 = np.arange(1, 17).reshape(2, 8)
print(arr1)  # 输出结果为[[ 1  2]
                #          [ 3  4]
                #          [ 5  6]
                #          [ 7  8]
                #          [ 9 10]]
print(arr2)  # 输出结果为[[ 1  2  3  4  5  6  7  8]
                #          [ 9 10 11 12 13 14 15 16]]   
print(np.dot(arr1, arr2))  # 输出结果为[[ 19  22  25  28  31  34  37  40]
                            #          [ 43  50  57  64  71  78  85  92]
                            #          [107 124 141 158 175 192 209 226]
                            #          [171 198 225 252 279 306 333 360]
                            #          [235 272 309 346 383 420 457]]
print(arr1 @ arr2)  # 结果相同
```

# 数学函数

Numpy提供了大量的数学函数来对数组进行操作，下面是一些常用的数学函数：

- `np.sum()`：计算数组元素的和
- `np.mean()`：计算数组元素的平均值
- `np.std()`：计算数组元素的标准差
- `np.abs()`：计算数组元素的绝对值
- `np.sin()`：计算数组元素的正弦值
- `np.cos()`：计算数组元素的余弦值
- `np.tan()`：计算数组元素的正切值
- `np.exp()`：计算数组元素的指数值
- `np.log()`：计算数组元素的自然对数值

# 聚合函数

聚合很有用，`np.max()`、`np.min()`、`np.min()`、``np.sum()`、`np.mean()`、`np.std()`等函数可以对数组进行聚合操作，返回一个标量值。聚合函数的第一个参数是要聚合的数组，第二个参数是轴（axis），表示沿着哪个轴进行聚合操作。

```python
import numpy as np
# 最大值函数与最小值函数
arr = np.array([[1, 2, 3], [4, 5, 6], [7, 8, 9]])
print(np.max(arr))  # 输出结果为9，表示整个数组的最大值
print(np.min(arr))  # 输出结果为1，表示整个数组的最小值
print(np.max(arr, axis=0))  # 输出结果为[7 8 9]，表示每一行的最大值
print(np.min(arr, axis=1))  # 输出结果为[1 4 7]，表示每一列的最小值

# 求和函数
print(np.sum(arr))  # 输出结果为45，表示整个数组的和
print(np.sum(arr, axis=0))  # 输出结果为[12 15 18]，表示每一行的和
print(np.sum(arr, axis=1))  # 输出结果为[ 6 15 24]，表示每一列的和

# 平均值函数和标准差函数
print(np.mean(arr))  # 输出结果为5.0，表示整个数组的平均值
print(np.std(arr))  # 输出结果为2.581988897471611，表示整个数组的标准差
print(np.mean(arr, axis=0))  # 输出结果为[4. 5. 6.]，表示每一行的平均值
print(np.std(arr, axis=1))  # 输出结果为[0.81649658 0.81649658 0.81649658]，表示每一列的标准差
```
