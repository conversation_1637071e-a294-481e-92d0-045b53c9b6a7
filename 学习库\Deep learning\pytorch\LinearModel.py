import torch
import torch.nn as nn
from torch.nn import functional as F
import time

class Net(nn.Module):
    def __init__(self):
        super(Net, self).__init__()
        self.conv1 = nn.Conv2d(in_channels=1, out_channels=10, kernel_size=5)
        self.conv2 = nn.Conv2d(in_channels=10, out_channels=20, kernel_size=5)
        self.pooling = nn.MaxPool2d(2)
        self.fc = nn.Linear(20 * 4 * 4, 10)
        self.relu = F.relu
    
    
    def forward(self, x):
        x = self.conv1(x)
        x = self.pooling(x)
        x = self.relu(x)  
        x = self.conv2(x)
        x = self.pooling(x)
        x = self.relu(x)
        x = x.view(x.size(0), -1)
        x = self.fc(x)
        return x
    
time_start = time.time()
model = Net()
batch = 64
input = torch.randn(batch, 1, 28, 28)
print(input.shape)  # 输出形状应为 (64, 1, 28, 28)
output = model(input)
time_end = time.time()
print(output.shape)  # 输出形状应为 (64, 10)
print("time cost:", time_end - time_start)  # 输出时间消耗



class Net2(nn.Module):
    def __init__(self):
        super(Net2, self).__init__()
        self.conv1 = nn.Conv2d(in_channels=1, out_channels=10, kernel_size=5)
        self.conv2 = nn.Conv2d(in_channels=10, out_channels=20, kernel_size=5)
        self.pooling = nn.MaxPool2d(2)
        self.fc = nn.Linear(20 * 4 * 4, 10)
        self.relu = F.relu
    
    
    def forward(self, x):
        x = self.conv1(x)
        x = self.pooling(x)
        x = self.relu(x)  
        x = self.conv2(x)
        x = self.pooling(x)
        x = self.relu(x)
        x = x.view(x.size(0), -1)
        x = self.fc(x)
        return x
    
# 改进版本：分别计时初始化和推理
print("=== GPU版本测试 ===")
init_start = time.time()
model = Net2()
device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
print(f"使用设备: {device}")
model.to(device)
init_end = time.time()
print(f"模型初始化时间: {init_end - init_start:.4f}s")

# 预热GPU（避免第一次运行的初始化开销）
batch = 64
input = torch.randn(batch, 1, 28, 28).to(device)
_ = model(input)  # 预热运行

# 正式计时推理
inference_start = time.time()
output = model(input)
inference_end = time.time()

print(f"推理时间: {inference_end - inference_start:.4f}s")
print(f"输出形状: {output.shape}")
print(f"总时间: {inference_end - init_start:.4f}s")