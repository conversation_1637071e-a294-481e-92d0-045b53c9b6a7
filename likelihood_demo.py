import torch
import numpy as np
import matplotlib.pyplot as plt

print("=== 为什么要用对数似然？ ===")

# 假设我们有3个独立事件，每个事件的概率
probs = [0.8, 0.6, 0.9]
print(f"三个事件的概率: {probs}")

# 联合概率（似然）= 各概率相乘
likelihood = np.prod(probs)
print(f"联合似然 = {probs[0]} × {probs[1]} × {probs[2]} = {likelihood:.4f}")

# 对数似然 = 各对数概率相加
log_likelihood = np.sum(np.log(probs))
print(f"对数似然 = log({probs[0]}) + log({probs[1]}) + log({probs[2]}) = {log_likelihood:.4f}")

print(f"验证: exp({log_likelihood:.4f}) = {np.exp(log_likelihood):.4f}")

print("\n=== 数值稳定性演示 ===")
# 很小的概率
small_probs = [0.001, 0.0001, 0.00001]
print(f"很小的概率: {small_probs}")

likelihood_small = np.prod(small_probs)
log_likelihood_small = np.sum(np.log(small_probs))

print(f"直接相乘: {likelihood_small}")  # 可能会下溢
print(f"对数相加: {log_likelihood_small:.4f}")  # 数值稳定

print("\n=== 交叉熵中的负对数似然 ===")

# 模拟分类问题
true_labels = [2, 0, 1]  # 真实类别
predicted_probs = [
    [0.1, 0.2, 0.7],  # 样本1的预测概率
    [0.8, 0.1, 0.1],  # 样本2的预测概率  
    [0.2, 0.6, 0.2]   # 样本3的预测概率
]

print("样本 | 真实类别 | 预测概率分布 | 正确类别概率 | 负对数似然")
print("-" * 65)

total_nll = 0
for i, (true_class, probs) in enumerate(zip(true_labels, predicted_probs)):
    correct_prob = probs[true_class]
    nll = -np.log(correct_prob)
    total_nll += nll
    
    print(f" {i+1}   |    {true_class}     | {probs} |     {correct_prob:.3f}      |   {nll:.4f}")

average_nll = total_nll / len(true_labels)
print(f"\n平均负对数似然 (交叉熵损失): {average_nll:.4f}")

print("\n=== 直观理解 ===")
print("概率越高 → 对数似然越大 → 负对数似然越小 → 损失越小")
print("概率越低 → 对数似然越小 → 负对数似然越大 → 损失越大")

# 演示概率与负对数似然的关系
probs_range = np.linspace(0.01, 1.0, 100)
nll_values = -np.log(probs_range)

print(f"\n概率 = 1.0 时，负对数似然 = {-np.log(1.0):.4f}")
print(f"概率 = 0.5 时，负对数似然 = {-np.log(0.5):.4f}")
print(f"概率 = 0.1 时，负对数似然 = {-np.log(0.1):.4f}")
print(f"概率 = 0.01时，负对数似然 = {-np.log(0.01):.4f}")
