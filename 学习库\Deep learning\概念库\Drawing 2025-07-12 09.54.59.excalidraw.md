---

excalidraw-plugin: parsed
tags: [excalidraw]

---
==⚠  Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠== You can decompress Drawing data with the command palette: 'Decompress current Excalidraw file'. For more info check in plugin settings under 'Saving'


# Excalidraw Data

## Text Elements
场景一：使用线性函数 ^3w1PL2sP

原始数据点 (非线性分布) ^ZRatUFTw

线性模型 ^7Oxnh3dG

拟合结果 ^ENfhsfKw

拟合效果差: 欠拟合 ^Ha6bpddq

无法捕捉数据的曲线趋势 ^cDP5lUM9

场景二：使用非线性函数 (神经网络) ^Pr8FNkV9

原始数据点 (非线性分布) ^2fZY2YPe

神经网络 ^MfNVlZyM

拟合结果 ^EaR6UGd4

拟合效果好 ^lw4uoiG1

包含非线性激活函数 ^bkfoyZik

能够学习并拟合数据的曲线趋势 ^f38xJJqU

%%
## Drawing
```compressed-json
N4KAkARALgngDgUwgLgAQQQDwMYEMA2AlgCYBOuA7hADTgQBuCpAzoQPYB2KqATLZMzYBXUtiRoIACyhQ4zZAHoFAc0JRJQgEYA6bGwC2CgF7N6hbEcK4OCtptbErHALRY8RMpWdx8Q1TdIEfARcZgRmBShcZQUebQBGeO0ABho6IIR9BA4oZm4AbXAwUDBSiBJuCApiADV8egAxAHFsAGEagGsAFgAFAHUAfQBFAE4egFEAdhg00shYRErA7CiO

ZWDZssxuZwA2AGY4/YBWLsnTkcmr492+IsgZtGcu5KTdgA5k5PeR45uR97xXb8MoUEjqbj7XbJbQ/Ebw/Y/XZdd5dLogyCSBCEZTSbiTHhJSbxHjHHho+IvW7HDEQazrcSoZK05hQUhsDoIVpsfBsUiVADE8QQwuFm0gmlw2A6ynZQg4xG5vP5EjZ1mYcFwgRy4ogADNCPh8ABlWAbCSCDy61nszl9cGSbh3OYQG0chCmmDm9CWiq0uW4jjhPJoe

K0tia7BqR6oeJfWmy4RwACSxBDqEKc0gyWTAGkAIL6AByPAG+Eker6zE0xGTAA1JsxmPEIPcALq0vXkLKp7gcIRG2mEBVYSq4Fv+4QKoNNvsD/AshAIYjcG5naEo4H3BhMVicSFh7eMFjsDhFzhibifb6XY77ZnbwjMAAiGSgy+4eoIYVpminxHGYIshydNMzmEos2geBGWgLAoHFMoKgkfYKHiHoABkeGYHpWyzABfdtaSEOBiFwd8V1Dc4eB4d

5jnOVFjkPF0iA4Do50HbdeWlD80C/fAwiKPCQQgyAkPQFC0Mw7DdQWGD30weDaW2J4/iSEZkjOY5Ph4X5KVpGNnH2EZdm0D4vh+P5dgBIFaTBYgITQHh423LEcTxRyaW3elvQfF03U5JU+UFUURSQX8pRlOUFUClV0DVDgNS1bJFO3A0jU9b1XR5P1t38hB7Xsx1HJZNl3QymDfRXSc/EkGd0yYsoIylaNuDjXyykTYjU1A+5szzQsSzLCsqxret

G2bXDSg7VLuwQXs0H7DiXWHYhRwkXAeF1KLiDq9iF1ypcKNQF54kYyZ3mdMpjz3Lg0EOWlrtPc8OEvUNPlRRIui3ZaXzfHjUD4n9tz/eUAKA5LQOml1iNI8jWqomi6K0rpGNpFi2IW+c0bYbijsBhBaXk+CJEALnVAHszQAAOUALH/AH95QAKV0Afr9AHIDQBfxUAB1MtsoAAVODKnJ6n6eZ9ndT1TgoGNQgjEZJzOzFhpcH0Q0Y08l0ifzIhlFu

9AxByJhdWPKBzAIdWcS16AI11PQclwYcmHm9BqjqRoWnabp+mGMYphmcNSBxYcCB5hS+cp2nGdZjnaVwIQoDYAAlcJJcZNkhAJzjbYACWxXFidjbRSUE4THyOiA+gAIQGItcGNAAtZJq/TroAE0YGr/BsFjihsAaPJCegpYEBWbywu3ZTUGcY5Lm0E5NK6S5rlufSdn2SYTLMm9LOs77QQdJ11NheEESRFE0VpVzs+4M5tBVsoh+4dqBFKgKeSCi

QBX2d/dUlaVOui5/YogAUeoRh6hFoaE0ZoKrZSqrlR++Ud7FRgbaD0EDKiVS2sIQMwZWrhkjC1UMzkXSdRTGmAovUIA5gLMWUs5ZKzVlrA2JsLZCIzQVnNI6i19rLRHKPOk+x0Gg12pjJaZQwj/UmPsU6UIRj7HREeXcp5ISXUgI9Tgz1XqoABH8CRpJ3hDl+sEOGvFvypxdCDBUgFMgQ1IVmESZRZKVCJghUSxdq6xzIgAVQaFzKgvUhK9Vsc4y

o7x6DoSMDUSYmhkzGnoMaPUQx1gNFaMoAAUsknCGI7F93WqQdkPj8LMOhiRMi/14gI1ovRFGDVIDoz2tjXGn5jEFyKCJcoLi3FQE8d4mSWS4q8yUjsNcU8ZHnGSJMZIZJfj7EXk8KExxtBdHvOvf4gIt6QDsg5Xg5JYQT00ScU61JT5Z3crwa+kBb5oHvq6WBMVBTvz4eFb+20bmqnIAlTU2oUoujSuAr0kCrQlSQQVDZSirlIPKqgqB/DMGzlDD

g5qsBWoEI6nKYhPVIIUIGtQ4adCxqMMmmAKGZQuysPthwoc3CxxdH4dOLBQjOEiMOq1ZE51TqvFkS6FRWt7pyJPKoi8jIgQT3GV9VZ5R9EIEMQDYxv5/wWOArkAohLIAw2KUdUpZJEYVNRmnVitTOI405HjaV25HESEAPnKgBpzTZoAO2NACdDqgAAFIAPXTmaADAlQAwHoAEpOYUEDjnCAlqbX2udW6r1IsxYSylk6S5oscjy0VvgZWhM4Im01p

UHW75+QPSYIbdwqazaK2IMQDYtIrZRFtqQe2Jdy6VxrnXBuzdW7t07t3XUfI/YcADrzc1Vq7WOpdUzD13rI7RzjgnKNaBk4mLKCxBAmc3I5ySPnUofjSgtLEhAXYUAACOepknoTrCWLmRY4A1HoLgGAyRknJiaEIbpiwJCOAVpwaBLpR7j3fgkU49Fkg6UYuysoBkeCTBGAka8Fllk2W3OsoqsZbhX0OQuplcRTl0jWD5AF7pnnoDfh/B5kV/zYY

AZoTQIDOxgPBRaSFmG7TwN4DR5BvyIX/O3AGWqtLYxwqjAi/BlyiHdWsS6DFVChq0NGgwiaBSiWzVJVjR8FL1rHGpTtDjZKDolKBLsZEPAZEgs5ZCKZPKbpqOlr+1ETk7y6MfOKyV+MZWgzlVYtAYE129XmD02CQcMmBIkJMAA8pgDgkh9jECaPisAq7wJudaUEkJYSIlRJiXEhJSTUnpOi/Y7JuTwuRdc1mHz6BjT5j6LmKA+BiB1lFvEIw7xlD

oSGDwbdXRSCpG81BB96AtTZd8VJ5VRTJXquouU5G2rmLDgxqgNTzEDX/Xxk0vLiFi7+cC8F0L965J9JHjsFEcRf23m0rpADDwdjUX2CkP4SyrIrNsnR6EiHz5oGOJc85TIGNEdw/c4GEUf6Kj/g415iUPmgPSigqjLG/KwKBbBkFeVKM+mo6xjB7GYWce3E1bjMY2oJhRQJ5zZDhODRoSNeh40mFzCVfqGT7C5NcNWjw3AuxlOCMmzThl/1Tjv3i

FCUV+m7p6fkXyl6jIvpfGA/+vRr4DGzeNaY2V4MQKKqIv1kpZSkYMSqRAGpdK6mGoafxad8xu3oGZoAQisAA6HBADR6j6v1lRTfW9ljkSNpnHdQHjUrVcyaFL5vTclPW2bSC5uNhrAtJBi3DxdGWm2QZK3Fy3bu/dh6eDHtPeey917b1tt9v4LtQcJD291FHGO8dWATtQFOtGGcjmLrzscebxQi6VEkKXQguZLgAFl07YEwDATAkhNC4jYDABZRZ

1v90HuhiPWwBnJFAy8TSB3xfbgMlp0y4HfiQd2BrmDrVTr3eOZfVDL3Ll5Xe3cz+32nl/dftgbAZHUoUdB/D8HIjId0Zh7AuHWUX+QDY8zjX6OeCsYSKkA/GJCeO6K/UImROOKEmZOU0nYVOeqtOa0nWkwTOqmrOAgjKoY6k501EX0AeN0BmRBT0/K8MG+PAHwXQIKT4kuEq0u+u9m5i8uCqEBUW+W7WG2Xm0WG64wRYeokgzAeouYeScwuWDenB

G6RWJWZWFWVWNWdWDWTWLWTiXBY4OSbAYhK6GSASMWj6pcZW+A8QRYjckg+YjcQwcAMSjc6cuY+w6E7eahmWnWmh2hEWvWEAKqA2quWqGuWuLOwi1SM2Rq+u9e66xc/BghwhohY+qom2b622IwakGkP6f6eky+AycyCy5kG+V2QI2+dGnw++OcpIkck+d8b21+OG5++GP2RG8UgOyUwOPymUaCDGUOToDGX+7RiONU/+XGQBWO24YBaKQmUBhO2K

4mpO+KFOxKPY1OQR5QCmnW7wGBKOU2bOaqlIVk94IGlyvOqA3KHKAuZ45Bjk3wT2pw5mEuf0oRQMsuDmrBkMSusMKuGqw26uleuq2u+q9SRiTBJqRuEAgA+0aAAQKoAMt+gAOeY27AngnQnhpO6JzRqu7u6Jqe5Ane4h6+66xZo8pB74A+4SCFrh6WxizR52zFzN6t4d5d49594D6SBD4j5Z4dq57+rwkwkjrF7jpJykApzfFzrV6tS17hGN4SCa

CNx+bBb5ilyNzGjKCYD6D7AwCTBNANAABWzAySfQcR6AQQRAcgU+kA76Nwkw2gK8bUs8Vw5wC8mRaABwU88Q8IcYaI948Q7wRkN2hUToEiFpJIs8iIxkx8iIJRvpRIAZ0iR8qIoZXkFRFyVRyowUoUYodRV+SZLy6o7yzR5GIOTGYOOUEOgK7+3RT+3+hZZQf+HGABuCPGwBfGOO4BGY+OExWKYmJOeKnh8xbCyBiEKxdIIw6x6Ymx2BJS4yxIBw

HwPOpxiipBgu6iK81kXO7woqdBdxeuDxZQZiYMliCu7BC2qsHmjibWG66cDOmgJExA26OWuhvBxcxAhhRoJhZhFhVhNhdhDhThbWLhdIbhOWnh3h7xQ2aulS3xE2I5muIRG5CAYpy0xcZ5uwF5Ra15vcHWnmny0+KkNE8yuwkws8ZI+x7w500ysYpwteMizpd4XOJwNE3pGykiecIG8IRFIGOkHwVmLoZ8xyT25RDIlRiCWG1RACIUqZX2jyhGQl

jR2ZOouZrRfyFZD8xZPpCCRZZUZZvRLoVZKONZ8KmOIBEAoxgmZQBObZxOuKkm5OiBJKix9Kok/ZuA+YQ5vZo5aq50uFrwyQ94c5XK/OvKZxQukInlQIGkNBtxUu9xBuEA25jme5GYFOgFrlHxIFo2M642zlkF/xUqgJqscJYJgAEqZQmAB3umgIAATWIJFuYJsJee6A4JBVxVqAZVlVruzuKJqUcsCsHuj2XuUARJ2sfueJJxgeRshJ2JxJYeJa

24UeFaVakp0p+wsp8pipypqp6pWpOprJOe+AtuEgtVRVpV4Jheo6JeyJk6/JkVs686D2ucy6EWhccFsWoS4SkS0SsS8S+AiSKSaSepv52W/STwlS8yqRFwNpNwIKBkhw7wa+uRG8120GdGMI0IRkB8yNzFqGXFOcaIecXwKNONvFGGAlT8GZOGIlxpUVl+ElRN0AAO0lGFkA3yPRCOqltGyl9GBNjGbRjNlZSOAxaOtZelDZSYuOzZkBlCkx7Z5l

8BBKVlCx6VK0qBdIpcTlaAti6hjk9wuWroOBsYhILwi5Wm3lV4kw3lJmrUOmiIp0wGlya54V0FzBO58qLx24CV8MSV9EGqYF6VXEuuAJm5kAcAbAw4bBwtcwLmpQ98YdvUSqYAodYAzgMIpypQWydeWYUdMdcdpknlONyNtE3mmNTks+WdB8kdnh+AoQUA3I+gisMgy4PQAdOovxEOWoUALeCow4yg3AKtGQ8qVaZcFcVctc9cTcLcbcHcXcPcZC

os2AQg6Y6dXwHwpSaI5ki9K5JwGSBluAcArU6ttI2QD5K0bdHdZCXdyUVa8ee6B6R6J6Z6F6V6N6d6a9k909OwMIc96kEiuwd4Omc9TkVSoBG9q4U8uFy8mk5wlIF0uw29MCTd+YbhWIuANlO9CoMDuScDxcXWWhuoQQf4FAjBAkOhzS4phWxWpW5WlWbA1WtW9WjWzWrWJqHm6DVAf1Y8AN8+5w1p88YNOwiQcya8EG+RW+dFsG8dYZaACyCQhd

KNeNjIJ+1yQlQoKZpNX8BGoMDR1NSUMlD+eZHNP+oK7onRKlr+YK6lnNv+3N1ZgxdZwxhCjZYxxlrZomZlcBsx0tPZDdfZdOY4rQStqAKtLhPAkDfkWtpw0I4yK5vlxBaARFxt5xsYcYBFqIq5NmuDkV0VzxiuTtyuiVwFbt1EHtbjwRmVdm24/tgdtj0dbm4dYAyQkdGSadwjbm5INTvUdTCGbmYjzpEjRdKdJdZdFdVd5Etdgd6VrITdLdjgaw

h9kEx9OQp9O659SeKe196ed9CE+oOMT9TwL9wG5IUIwG94UIK5Vka9yg/9oYKQextpWm5IK5gIATZQu9YzB9ytR9rBVa1Jbekwne3eve/eg+w++wo+D96z6Ycyex8+tEhIxkJIJwCAzgHFhCpzqAcy14XwqLaLrw8QdzD80DsDIQCD24u9yDWhqDGhv1BL+A2DyTsFi2lQD5Rhz55hlh1hxoth9hjh31DDuo76AN6kM8c8tpnDTwHpUNl2m8XOgj

/FnFwpaAK84jnT8IUjkrhjgllN8jIUF+4lKjklajQOslDNOjeU+jrNTN7N8lr6XN/R5jvNuliKAtXUTZod5C9jMB0xnZllLCMt+TyxHj60z43jvjPS/j4hi4/0PwKI945I05flht0TAVoYpIJIE5pwYVDBEVdtMVQdodehP5x5d56az4PQxw+A7i7eg5PW7rhSbxWTmqWk7tOq4FWBGV3tWVvtEAJTsVodMdlT1TKdtTFTudMI5wTTWYXb3msrHT

8rxdFbM6vTBg/TNdddOcEFIzgejzEzzzUzrzVJLeHzXz9JvzTJ/zgLE9wLAyU8oy4LZIzpW+OmxwsL8LHUiLmLwbBLCoa77dG7Lo0zUAM1UpMpcpCpSpKpapmp2pupQLU96YcQBwLwcIVBgZX0F097xziLyL6L6HGkz7+DjdgeRLFAJLXrhLuL8DpLGDO9FLWhVL2HkhNLEg2ABbRbJbg5qF3BtNEAppW+2gvLbD/LNwhmLoMYoG/wyRIV7pnpUT

cNLN1Ely6NToR+8Zr2bN72JNGryjv8lNUl6jbH9NxjBrb+UnpZ+Zz+ClEAWl9UFj/N2OgtDrLZotplsBMxXZSBXrct9O4w3jy7Wt+dMiZIRkBxM5fOsb6itwCys85ItBSTabwMcuu5mb8VmTLt2TI2/haVXrXtyT3VlQgAB6aACrNoAKrGgAksY2qAAhboAE9mDMgA0bGAD9SlVf6rl4VyV+V9V4ieLCdbwDGu1QmkmpiT1aNX1bifrDmsNb1RAC

SRNZHuSdNfeY+cYaYYy2+Syx+eyz7GyVtcCfV0V9amV5VzV9yWOqXnyQKTqkKUhmczdZFhEZUAMNgOMMaMcJyBQHALsBQFABwEYH5tRAgBoO5yx+PqsHxUw84GAykAcIxJ8AvTcJ5SRYZCMChskWMqcKUkRSCjvqI18Fx6i+SF0JSMkAci5NK6gFc60y6MfomS/MTQo6p/Udq1mVpy0fqyZ4ayWWzQz+a6Y5a9pRZ7a1Z/a2U+x8+F0H5jUFzHAE

YMcA0O3jwLmBwE0CdJIMku5059ZbLfZQ0B5426ImqtRAcJcPCP59Gw6eE2QXG9recGb6Fym7ZjLluTFw7UZTR4eWhbm1IcXD0KQO8A0EWB0DUGW/ktO31lW4l5qoCB6ahgERBelxFdSwVhAG7x717z799c75hWPIkC/aFwCJuE5IjdD4iOaRPKUsKkjwSBK3zl0NsqjXsqDWjQT2UXGXxQmUp3Iyp2mRTeT1TbT7q5o3Jcxoz/p8CoZ9oyZ2Z9gt

axjtzyMTY/b/z4L8L6L+L5L9L7L68PL4r/75Tsry5/ZWFtVDShsRr1555cvHRB6QbWgNIkFwKkfz8GMqf9ZvQVb9lTb08bF47ZW6qkH+UiHznfW57VBT7ZFVNToByYgAGLlQ4A6dmI6kAB6noAHm/QAIr+gAHb9h0rGbmMCVAHgChYbMaAfAKQEtcWqFxVEh1XRJdUeuI3DNP7nxLDc+u5sTeqWkm4x4q013W7vdwQCPdnur3d7p92+4bV/Ya3aq

hAHQH0wIBWAh1LAMQHICSeR1XktwArzHdLqxyJdMnVuoEN7qEgEYE0G6D7AOg26dvO4iEB6hnAzAfMPh2cBNBvgbPVWugGWD/dxuKfZwNIhB57JweeFSHvx0AxLxcKV8eHkX3Ogl9JOwKPeHCEPjBlUQR2KQAT0PyKtG+JrM/HhjEpqdfsqrIBPfi+SP4jO5ZCwUzwM4s9dOw/MxpzzH5DF9KhlfciaQF5C8ReYvCXlLxl5y8FezjD1q40CK2VvW

8tXAOnHV5LFNe3AA4MvA9JWQwhhxD+pf3hjkgqKGRH6A/wy7RcX+dvUodmyPIJFIIG6HgHqGriNweAjcHoEgF8S3kXelQJoE0HeB9AagAwZgM4EmDPhcAuAUwfgH2AsBiwuAZwvQz/LlsECGTQPpRFdrf8w+qXZoTrio7KCDy+hdAKsPWGbDthSfJYSaR2DQgUgCyRiMA1ni/BZ8oqcGjcCnjY8EeWI5HqX02Tl9aIlfRiNXxEYnIohinGIXI1qL

xDqeGnHVjmW76s9rQ/faHIPzNZQpkc5nQoZY2KFT9ShM/CofP2qFL86ha/d4V8mc7/D5MPrTrMmE6EtDuhDpbHocHOiEEjMCic/m4OUSnETaoYDpishJARcphUXR4iwVf7pN3+Phb4YkB/5jYfiUo6bIU2t6G5+BgaPtCGkHRhp/QqA10b2mDQDoh0eAtrjLDapxoiB3XHKliVNg4lM0g3IanmmoFjdSaU1BgcXHUGaDtBug/QYYOMGSBTB5gngZ

2j4H+o3R/o0NBIJvhSCDuMgs6oKXkE15zud1WjugCLDGEOAMAY4BQGSRDAW4AwDUgzkbjVw/MuYa4d9SfT6AX0XLE7JDRXpXtcesPZcmqIE4nYxkKQbwYj18Eo86MQIFDKSJ3HE8b4CnGRkgliGfZTE5NLVqqxIwpCiUaQofpkJZFdEch6QjSha2hRciXQgBHkXa1RTT9MA5QuflUMX61CV+9QpXp6wdHuM2hySeUSGyOhUE4QgIC6Gf0J4Psdwf

lXUagHEQf0P64yK2pF1tozCzRcw4OgeUyRO9oRzYiAO3j1BFg6g1cGAE4V2H+I82EgQ4ccNOHnDLh1w24fcOYCPDnhaFTlsxJsSsT0AdYIQJnD1B6hY4xIIXu3nGDKAhANQZQI3GFBUpvyLw7rH73FFlBnaXw7Jj8LyaQSCmzbObNR0u4SAaJdE/AAxK/J0MKJPBRIo5DiBUFKQ50aRKMhXKnQwhQGLnFxy0zYji+W4lmndnx6nckWz2I8WT3/gf

Yqe6Zdvppy76pCtG7IjoszxNZMjqo740fp+L5oT9rG1nPnv+Nn6VCF+NQ5fvEFX4NCJRm/Eya0Ppy5hYJ6meCUfyezAMUJF/dUfOQFTz0ucK8LSJb2mGmj7aTmOKq8Q/4GTg+No34faIj7/8W2gA4EmIItwIDaulQMQWtOarBiOuYYrrhiUjG9doxdHfqnGIJIjckxZJa2FN0qCtj4g7Yzsd2N7H9jdgg44caOJW6bVtq6ATaYdR5LVjTqR3O0Sd

yuqKDo+II0zu3m3SNxcARgAYI3DYCYAmgyYaGenE0AwABgNQJoIzl+4SBrBt8QHnMkSA0U4wVkQkIiCXHuCngpINcYXw3FEUOmIU+invnClXVIh9ffGpSNVbUjzxmrdTu3wFB35sA9PXIQ+KUoD9nx94jkTzTyk2teMPPX8fyJKmCigJFU0UTVOkx1SIKrnMcOhGamBN/oyIleDplCrdStYHwEYZE0pBkhZ4K5IaSaOf7ESxpWbDLIsKcnLDIiuA

WOLsHcRNBiAVKESRwQ9mVAJJUkmSXJJqAKSlJKktSQgA0muyhJrw/CHsODkSBfAxaegLmEbh1gLhkgWOH0GlKfBGJuFQSTBGEk6SpaHwyabGF8JGTf+aXBaeZKBEO8IZ4wL2T7L9nxzHerHKcfGyngrkzgPwPYt5OTb2lU+Z2D6EFN8GMy8RxRVmccjr4k9opTfbmXEN5kJDVGnfBkSlJ74FkxZejDKcq05BZS+iOU2FNyMs6T8ipf4gCWVOFEgS

qpYE9ft2VkxLEdZ60eyZpX/DM5POJSFGIPLRB2yzZ3ALqYNWMwxNzoE8H4Lj3tmESRpGbN/npIS5TSv+M04yfNKdFP8XRHJSElyRQG+pcqCJbaWXhDFfJOunVJFt1TIGnSA850xMeNWTH0DKSSwKGTDLhkIykZKM2wujMxnYzCx7JSoJyT+n7c2usg4GfWJFKNiVBVE54FAH0CSBjgdQeIJoFzAwATANQaiPgEwA1BvYDkmCAaUIBGle5qfc0lvh

JnzjyZCTEiq6Qx4XQRgKMC6CSHB54icee4l4OSOPEqsBZLfGkQlP/hJTt5t41Kb333nM0JZmU0WdLKtayzx+8sq+bzxvmlShRwEyqdVPAlNDtZ9lY9l/IESYEuhWtD0icCIq3Aje+4B0qhk5SYT1UrwQkG5VgUAD02aTeYQnJ7knli4+ACgF0CEAB0mgktZuWRJj7pz6gWcnOZMDzkFzJARc9vCXM0mJztJ4hACsgprnWjQ+6CxtpH2grgyN0HSr

pT0onB6KHE0I9jqbX7nqQNI5IK4MBnAbWLZ4XHOMICABB68rIFs/wbBjnlSsIpPFDmdIxinJl1WrfS8YlPpEaMd5J8k1kaw/xGMXxJjUzvkI/GNR8pcSwqQkqVm3zklas0CWKMrm1SIJmSmUXSD8z6ytiV4HTE4pgXALz+gwnUTEw9IrldedENCdbVTZwLHZo0jtvF0+HLLDJaC+ufVI2UNKeugi/KlCQtyABfTXWk7VhV4q4hS7lDFu5wxB0uxC

mmoHkCBqV0IbgmOOnoBLpdA66amMqCyL5Fii4wiorUXMANFPALRTov4XFihVBVaVV5CrGiLaxcggnmDIsmEMIA7Ek4WcIuFXCbhTQO4Q8Mrgcs/yTDMkP3OZRDyvJN7NEUvGdK0yp5uI15XfEBqkgtEfwUkGcGdJhDZOojcvneFOChNilVy0VKTxXneLKeAK/mf4uBXac7xaUtmhCrZEhKolBQmJUUJ/FC1HWyswCeVJFGYqNZdNSUXiraHpYcle

/dMAGw6xBtqOLlEBQcFx6ekqCKEpyKUv8rqIdaFM85UaPXICr4FTS8aVXKtHcqaCs0htksX5WLTaQ7bTNs037YVNh2IdBprCBoKF97F94efK8FhYTCswjTXtvevywwgXg6awtWSG/Q5rc6Ba6eMWuNluUp2uk6pLO0rpqABmi7YZlEFXb7112PjF5rFyrTpiFkmYvQQYKMEmCzB7wNnnTVPaPZ5kwTCeIiC0yeUVykwH9asj/qb1QwWLDAG+2w0f

tcNm7fDcXDukPSuxPY6uH2IHFDiRxTwiDhsyZApBSQMHXCskS0xmQUqj7TjcdHmTnRZ879aTneBODcaV2PVYjviy/ZIMzNpHRhuS0pZR8PVqg8SZJIQDSTZJ8QeSYpOUmqT1Joasls5KRZxB3gnpIyBImR4r0oMy4oVrj1fW/pSkhwKgu/FtHbwWawG4yMkTPW4TXgW+IBR8quqytAFjEZ0qkWy1hDy1XMytf8t8Vt9a1W8kFUEt3nGdQlcCbIRE

uhU6MR+58jtd+IVndqyEvau+SkvVnpLX5LQ9+Z1iGD+syEfjYzYf3EQXRaIyEile1zQmVKaVQWsNv0N3U2191rKhBfbwWGOTaaVEzQB0FFgtxCAbEN4diqQWcrBs00s9WssvWNznRbbRdmU1HaPqANI7NzOnVnhWRKQSmp7Flv6EDt5kmiRINx2y1/An1pQFpn9vS2A6vgBRHLVmHy0ohCtkOj0l0AQ3XakNrIPpqhoXZDMvWJm99pMy/ZbtbpbY

jsWJuelSb3psmk9pB2fopAsd8IHSISA9Kw8UYKHLTVh36U8a96rdHDZ3Up14zWFsM+GYjORmoyeFWMnGUzvk3h116Wm0Fhh3RaJBjNmG0zSgzxbpUiOuukjlljI62bKO9m5uZZPQAnazt1cC7VCPdkwiZkpirnHOLJmLi41Twc6K+rpk4jiQCrFNW9Bk6195ODfCkUfK5DN8q1VWwFTVreR089WkS9KS1vD1gq3xnI3KQirln1ketNnSCP1vRUDr

H5WKuYiOsbZja6QscIlfOsiZm0cJYyFCcRTNlVKaImdSkHf0mF7rr1REtlXFwmknrppqy3lRgrMkvagBEAQAKDKgAahUB0gAAftAA3LbCxvRBC/gVPtn0L6I4cq/Ae10IH7SSBh06hQN1oVUCtVo3BhVdPLT6qJAoclzeHPc2RzPNMcnzZ9N4HfSJ90+5mPPsX2Or/pzqoGalSDASKzuSgi7p6qGWZzs5uc/OYXOSDFz0CuM1wn5pT4RrGNg8zyW

5R8kkUdMgNOxQ4sJAt7vgeIoPRFPHbyt/dS80PZ4sJoVbQo8U6rf9lq31rgle85keLNZGSzG1OSs+aji62XzkVis0iWUKSWqzC9aS5+aXrfn2VjQk2yCNNpfYGyjoULU6H8BW0Bdltlswnh0wOCAgwhTKx/q21SbmjmlnBHNpRJj56hEQmAVJNuncT/l1++krlfdsXn/6L1LQq9UUxdC3r3tD6oDTDvKZAbQdN1RDX4azCVMSDpBnHRTlLr4652h

O4gIM3rr1TSdfG8nfczF1WCJd7C6XVwrRkYz5dqzR+umGV0nM+d3Gh5skc/apGhNBqroHIoUVKLTV6izRdot0WQQCjSrTTQA0WTq6kd/OjWiZrw4Ed6pBu4lnruN02aKddmzZQ5qonmH3glh5JNYft1sdTSzu8xW7opke6jikND+jga0h4HnFAezZEQaupfLyDnM8Pcpyj3rzaRQKhgyLLa199WDT41rVLOynp7Otme2JdnviX8Ge1aK4Qw/NEOI

aN+uKsvfZS5hV7Na7OGRDsYJBRsImWElQxhIgUHAaITkU2R3u21d6D1hho9ZaKAoD6kt1SP4cPuGlKr+BgAX4DAA+JqAAzbUAAGcoADc9cEo1wtw7cJV6Aak/SaZNglGubJmVa1TIV7SKFqGNWCqpoWUDNVaaMakWlsGQAUxzCtOUIAzkjLIDEyqZTMrRzZ4X9wJTk4yeZNbcmuu3b/SIrLxiL/9IMhQaKWmPYLKgZuUgBblQCOnUArIX5KgGfAA

wwEyAQBCfDyhem7k1APKM4BgzIAeAcAbYPyYIFyq0SEY8k0dKlPoBggeoNjgbGP3xmaBuoDdH5iMDKA787ebAGsXgPoVjFcLPCqZCK0nBf0aIeHrn0SD+ktMkyZIvYpXWHGUQ5pUpMZCIo19PlUUig78tfg+Lrjfi+g3HuSn1av8kgKUBoECAsGD5LNDXLDkT2nz3j3Bz452t34qZ9++SsRIWqNpLbSk6h2iL+j2MAg+9BJ4bNCAEb39O9Zp0Any

LxO7bD1+QSIySfWXPasFEAeU7HnSrtovpwJO0w6adMunggbpj00aC9N6gfTsCP0+/ADOwIgzDoEM2GfZMQB/zHAJ086YgQgXvk4FyC0gmgtTJAzwZ0M+GddURTFBe3Y6qaZdUoF6cqQQSOAChh0g4AcAU0MUnJ3QAsQWQSoBrDxAggGAhAVgaXAvE1rBQ0ksSzeI/MiAPkyYd8PoFNAnjI9lWsoFPRyQn1ZLQlvmYkNuMjnAlkl1SzM1ksNAG1ra

viypekuyX5Ls58JXKaktqXMgll4+YueUu2WDLmQWOHCoz16XzLmQPzIiu+POX9LP7Qy+QuIGUKigXluy/oAaARodpplly0FcyB+oRuiZjChFdctyXtdAx0Y5BLSsJX9A4wSzYbrQZhrwrZlyK3hy5geZtomwV0NgHZBGg6wV4OMCkE+BUgUQsPB5XxeYB1WeQ+ARuKuHaa+dCt2fexbubKBGA2ABgFIwwAIApxU1Q7ajrlZktuXv5HGWFaDBquyg

SAW+0hbee2vvhVdfFra8QHbxsBVoBV3AJoGCAOyDKvsLS7FBaSlweQxcUgMoElAOpal1AXgPue+tG0FNxwCsZAHjhKTS6/8V6+9a/pfXIbvALyv9e9T15crDlhAL5cNicBhyrOEEwgHjgjhfYIu19pdeus1i/9cpw0kTciqdouLgMyKsIDKy2wybCNuwBqQHi5BjQnaOAKdfOudorrZJukCsEICMAuYk1/ANNZ/JhBggfN/cKWmnoxx9AlVjrKSY

dnDqDAxoDIBLa1juGZ2rIfMHzYFtC29o9FoEfqENDhAO6BEPCEAA
```
%%