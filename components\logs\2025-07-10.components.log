2025-07-10 07:40:49.805 [info] script extension loaded 0 functions from components/script  [object Map] 
2025-07-10 07:40:49.805 [info] components database created cost 0 ms   
2025-07-10 07:40:49.806 [info] components index initializing...   
2025-07-10 07:40:50.007 [info] start to batch put pages: 5   
2025-07-10 07:40:50.022 [info] batch persist cost 5  15 
2025-07-10 07:40:50.055 [info] components index initialized, 963 files cost 250 ms   
2025-07-10 07:40:50.055 [info] refresh page data from init listeners 0 963   
2025-07-10 07:40:51.273 [info] indexing created file components/logs/2025-07-10.components.log  [object Object] 
2025-07-10 07:40:51.278 [info] refresh page data from created listeners 0 964   
2025-07-10 07:40:51.450 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-10 07:40:51.750 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-10 07:40:52.056 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-10 07:40:52.067 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-10 07:40:52.070 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-10 07:40:52.073 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-10 07:41:07.068 [info] components database created cost 11 ms   
2025-07-10 07:41:07.069 [info] components index initializing...   
2025-07-10 07:41:07.287 [info] start to batch put pages: 6   
2025-07-10 07:41:07.290 [info] batch persist cost 6  3 
2025-07-10 07:41:07.317 [info] components index initialized, 964 files cost 260 ms   
2025-07-10 07:41:07.317 [info] refresh page data from init listeners 0 964   
2025-07-10 07:41:15.649 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-10 07:41:15.663 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-10 07:41:15.729 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-07-10 07:41:15.730 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-07-10 07:41:15.730 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-07-10 07:41:15.868 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-10 07:41:17.367 [info] components database created cost 1 ms   
2025-07-10 07:41:17.367 [info] components index initializing...   
2025-07-10 07:41:17.569 [info] start to batch put pages: 5   
2025-07-10 07:41:17.631 [info] batch persist cost 5  62 
2025-07-10 07:41:17.632 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-10 07:41:17.637 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-10 07:41:17.667 [info] components index initialized, 964 files cost 301 ms   
2025-07-10 07:41:17.667 [info] refresh page data from init listeners 0 964   
2025-07-10 07:41:17.668 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-10 07:41:18.237 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-10 07:41:18.345 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-10 07:41:18.370 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-10 07:41:18.530 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-10 07:41:18.939 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-10 07:41:18.951 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-10 07:41:18.955 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-10 07:41:18.962 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-10 07:41:18.965 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-10 07:41:24 [info] indexing created file components/logs/2025-07-10.components.log  [object Object] 
2025-07-10 07:41:24 [info] refresh page data from created listeners 0 970   
2025-07-10 07:42:24 [info] ignore file modify evnet Home/components/view/remember.components   
2025-07-10 07:54:37 [info] ignore file modify evnet 学习库/Deep learning/概念库/语义分割/语义分割.md   
2025-07-10 07:54:37 [info] trigger 学习库/Deep learning/概念库/语义分割/语义分割.md resolve  [object Object] 
2025-07-10 07:54:37 [info] index finished after resolve  [object Object] 
2025-07-10 07:54:37 [info] refresh page data from resolve listeners 0 970   
2025-07-10 08:04:22 [info] ignore file modify evnet 学习库/Deep learning/概念库/语义分割/语义分割.md   
2025-07-10 08:04:22 [info] trigger 学习库/Deep learning/概念库/语义分割/语义分割.md resolve  [object Object] 
2025-07-10 08:04:22 [info] index finished after resolve  [object Object] 
2025-07-10 08:04:22 [info] refresh page data from resolve listeners 0 970   
2025-07-10 11:24:22 [info] ignore file modify evnet 学习库/Deep learning/概念库/评价指标.md   
2025-07-10 11:24:22 [info] trigger 学习库/Deep learning/概念库/评价指标.md resolve  [object Object] 
2025-07-10 11:24:22 [info] index finished after resolve  [object Object] 
2025-07-10 11:24:22 [info] refresh page data from resolve listeners 0 970   
2025-07-10 21:54:10 [info] refresh page data from delete listeners 0 969   
2025-07-10 21:54:10 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-07-10 21:54:10 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-07-10 21:54:10 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-07-10 21:54:10 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-07-10 21:54:10 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-07-10 21:54:10 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-10 21:54:11 [info] ignore file modify evnet 学习库/Anki/Deep learning/概念.md   
2025-07-10 21:54:11 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-07-10 21:54:11 [info] index finished after resolve  [object Object] 
2025-07-10 21:54:11 [info] refresh page data from resolve listeners 0 969   
2025-07-10 21:54:11 [info] ignore file modify evnet 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md   
2025-07-10 21:54:11 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-07-10 21:54:11 [info] index finished after resolve  [object Object] 
2025-07-10 21:54:11 [info] refresh page data from resolve listeners 0 969   
2025-07-10 21:54:11 [info] ignore file modify evnet 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md   
2025-07-10 21:54:11 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-07-10 21:54:11 [info] index finished after resolve  [object Object] 
2025-07-10 21:54:11 [info] refresh page data from resolve listeners 0 969   
2025-07-10 21:54:11 [info] ignore file modify evnet 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md   
2025-07-10 21:54:11 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-07-10 21:54:11 [info] index finished after resolve  [object Object] 
2025-07-10 21:54:11 [info] refresh page data from resolve listeners 0 969   
2025-07-10 21:54:11 [info] ignore file modify evnet 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md   
2025-07-10 21:54:11 [info] refresh page data from delete listeners 0 968   
2025-07-10 21:54:11 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-07-10 21:54:11 [info] trigger 学习库/Deep learning/训练实践/mmsegmentation/分割模型对比从入门到入土.md resolve  [object Object] 
2025-07-10 21:54:11 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-07-10 21:54:11 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-07-10 21:54:11 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-07-10 21:54:11 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-07-10 21:54:11 [info] index finished after resolve  [object Object] 
2025-07-10 21:54:11 [info] refresh page data from resolve listeners 0 968   
2025-07-10 21:54:11 [info] refresh page data from delete listeners 0 967   
2025-07-10 21:54:11 [info] refresh page data from delete listeners 0 966   
2025-07-10 21:54:11 [info] refresh page data from delete listeners 0 965   
2025-07-10 21:54:11 [info] refresh page data from delete listeners 0 964   
2025-07-10 21:54:11 [info] refresh page data from delete listeners 0 963   
2025-07-10 21:54:11 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-07-10 21:54:11 [info] trigger 学习库/Deep learning/概念库/评价指标.md resolve  [object Object] 
2025-07-10 21:54:11 [info] refresh page data from delete listeners 0 962   
2025-07-10 21:54:11 [info] refresh page data from delete listeners 0 961   
2025-07-10 21:54:11 [info] refresh page data from delete listeners 0 960   
2025-07-10 21:54:11 [info] refresh page data from delete listeners 0 959   
2025-07-10 21:54:11 [info] refresh page data from delete listeners 0 958   
2025-07-10 21:54:11 [info] refresh page data from delete listeners 0 957   
2025-07-10 21:54:11 [info] refresh page data from delete listeners 0 956   
2025-07-10 21:54:11 [info] refresh page data from delete listeners 0 955   
2025-07-10 21:54:11 [info] refresh page data from delete listeners 0 954   
2025-07-10 21:54:11 [info] refresh page data from delete listeners 0 953   
2025-07-10 21:54:12 [info] indexing created file 学习库/Deep learning/概念库/attachments/语义分割/Attachments/Pasted image 20240729165304.png  [object Object] 
2025-07-10 21:54:12 [info] refresh page data from created listeners 0 954   
2025-07-10 21:54:12 [info] indexing created file 学习库/Deep learning/概念库/attachments/语义分割/Attachments/Drawing 2024-07-30 09.05.48.excalidraw.md  [object Object] 
2025-07-10 21:54:12 [info] indexing created ignore file 学习库/Deep learning/概念库/attachments/语义分割/Attachments/Drawing 2024-07-30 09.05.48.excalidraw.md   
2025-07-10 21:54:12 [info] trigger 学习库/Deep learning/概念库/attachments/语义分割/Attachments/Drawing 2024-07-30 09.05.48.excalidraw.md resolve  [object Object] 
2025-07-10 21:54:12 [info] index finished after resolve  [object Object] 
2025-07-10 21:54:12 [info] refresh page data from resolve listeners 0 955   
2025-07-10 21:54:12 [info] indexing created file 学习库/Deep learning/概念库/attachments/语义分割/Attachments/Pasted image 20240729162504.png  [object Object] 
2025-07-10 21:54:12 [info] refresh page data from created listeners 0 956   
2025-07-10 21:54:12 [info] indexing created file 学习库/Deep learning/概念库/attachments/语义分割/Attachments/Pasted image 20240806155210.png  [object Object] 
2025-07-10 21:54:12 [info] refresh page data from created listeners 0 957   
2025-07-10 21:54:12 [info] indexing created file 学习库/Deep learning/概念库/attachments/语义分割/Attachments/Pasted image 20240729155300.png  [object Object] 
2025-07-10 21:54:12 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-07-10 21:54:12 [info] trigger 学习库/Deep learning/概念库/评价指标.md resolve  [object Object] 
2025-07-10 21:54:12 [info] refresh page data from created listeners 0 958   
2025-07-10 21:54:12 [info] indexing created file 学习库/Deep learning/概念库/attachments/语义分割/Attachments/Pasted image 20240729153712.png  [object Object] 
2025-07-10 21:54:12 [info] refresh page data from created listeners 0 959   
2025-07-10 21:54:12 [info] indexing created file 学习库/Deep learning/概念库/attachments/语义分割/Attachments/Quicker_20240729_102754.png  [object Object] 
2025-07-10 21:54:12 [info] refresh page data from created listeners 0 960   
2025-07-10 21:54:13 [info] indexing created file 学习库/Deep learning/概念库/attachments/语义分割/Attachments/Pasted image 20240730002523.png  [object Object] 
2025-07-10 21:54:13 [info] refresh page data from created listeners 0 961   
2025-07-10 21:54:13 [info] trigger 学习库/Deep learning/概念库/attachments/语义分割/Attachments/Drawing 2024-07-30 09.05.48.excalidraw.md resolve  [object Object] 
2025-07-10 21:54:13 [info] indexing created file 学习库/Deep learning/概念库/attachments/语义分割/Attachments/Pasted image 20240729170234.png  [object Object] 
2025-07-10 21:54:13 [info] refresh page data from created listeners 0 962   
2025-07-10 21:54:13 [info] indexing created file 学习库/Deep learning/概念库/attachments/语义分割/Attachments/Pasted image 20240730092903.png  [object Object] 
2025-07-10 21:54:13 [info] refresh page data from created listeners 0 963   
2025-07-10 21:54:13 [info] indexing created file 学习库/Deep learning/概念库/attachments/语义分割/Attachments/Pasted image 20240729095613.png  [object Object] 
2025-07-10 21:54:13 [info] refresh page data from created listeners 0 964   
2025-07-10 21:54:13 [info] indexing created file 学习库/Deep learning/概念库/attachments/语义分割/Attachments/Pasted image 20240729095710.png  [object Object] 
2025-07-10 21:54:13 [info] refresh page data from created listeners 0 965   
2025-07-10 21:54:14 [info] indexing created file 学习库/Deep learning/概念库/attachments/语义分割/Attachments/语义分割-2025-03-16-08-42-15.png  [object Object] 
2025-07-10 21:54:14 [info] refresh page data from created listeners 0 966   
2025-07-10 21:54:14 [info] indexing created file 学习库/Deep learning/概念库/attachments/语义分割/Attachments/Pasted image 20240911090035.png  [object Object] 
2025-07-10 21:54:14 [info] refresh page data from created listeners 0 967   
2025-07-10 21:54:14 [info] indexing created file 学习库/Deep learning/概念库/attachments/语义分割/Attachments/Quicker_20240729_101434.png  [object Object] 
2025-07-10 21:54:14 [info] refresh page data from created listeners 0 968   
2025-07-10 21:54:16 [info] indexing created file 学习库/Deep learning/概念库/语义分割.md  [object Object] 
2025-07-10 21:54:16 [info] indexing created ignore file 学习库/Deep learning/概念库/语义分割.md   
2025-07-10 21:54:16 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-07-10 21:54:16 [info] trigger 学习库/Deep learning/训练实践/mmsegmentation/分割模型对比从入门到入土.md resolve  [object Object] 
2025-07-10 21:54:16 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-07-10 21:54:16 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-07-10 21:54:16 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-07-10 21:54:16 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-07-10 21:54:16 [info] trigger 学习库/Deep learning/概念库/语义分割.md resolve  [object Object] 
2025-07-10 21:54:16 [info] index finished after resolve  [object Object] 
2025-07-10 21:54:16 [info] refresh page data from resolve listeners 0 969   
2025-07-10 21:54:24 [info] ignore file modify evnet 学习库/obsidian 插件使用说明/永久记忆.md   
2025-07-10 21:54:24 [info] trigger 学习库/obsidian 插件使用说明/永久记忆.md resolve  [object Object] 
2025-07-10 21:54:24 [info] index finished after resolve  [object Object] 
2025-07-10 21:54:24 [info] refresh page data from resolve listeners 0 969   
2025-07-10 21:54:55 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积和转置卷积.md   
2025-07-10 21:54:55 [info] trigger 学习库/Deep learning/概念库/卷积和转置卷积.md resolve  [object Object] 
2025-07-10 21:54:55 [info] index finished after resolve  [object Object] 
2025-07-10 21:54:55 [info] refresh page data from resolve listeners 0 969   
2025-07-10 21:56:53 [info] indexing created file 学习库/Anki/Artificial Intelligence/未命名.md  [object Object] 
2025-07-10 21:56:53 [info] indexing created ignore file 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-10 21:56:53 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-10 21:56:53 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-10 21:56:53 [info] index finished after resolve  [object Object] 
2025-07-10 21:56:53 [info] refresh page data from resolve listeners 0 970   
