---
tags:
  - 学习
  - deep_learning
---
# Netron 功能是什么
Netron，主要功能是：帮助用户快速理解和调试深度学习模型的结构。Netron 提供了一个图形界面，展示模型的各层、各节点，以及它们之间的连接关系。

# 模型结构解释
```ad-col2
title: 模型结构
color:178,22,164
- 0 表示输入层
> 输入形状：`?x3x448x448`，其中 ? 表示批次大小（batch size），通道数为3，高度和宽度分别为448像素

- 第一层：卷积层(conv2d)
> 卷积核的数量为32（输出通道数），每个卷积核的大小为`3*3*3`，即 `3*3`的空间维度和3个输入通道，输出形状为`?x32x224x224`

- 第二层：Batch Normalization（批量归一化层）
> 批量归一化层能够加速训练并稳定模型。参数：
`Bias`（偏置）：大小为 32（与通道数一致）。
`Mean`（均值）：大小为 32。
`Scale`（缩放）：大小为 32。
`Variance`（方差）：大小为 32。

- 第三层：激活层（ReLU）
> Rectified Linear Unit（ReLU）激活函数，主要用于引入非线性。
输出形状：`?x32x224x224`
输出形状与输入形状一致，因为激活函数不会改变数据的维度。
![[Netron 可视化-2025-05-04-09-22-21.png]]
```

