{"version": 3, "homepages": {"Main Homepage": {"value": "Home/Home", "kind": "File", "openOnStartup": true, "openMode": "Replace all open notes", "manualOpenMode": "Keep open notes", "view": "Reading view", "revertView": true, "openWhenEmpty": true, "refreshDataview": false, "autoCreate": true, "autoScroll": false, "pin": false, "commands": [], "alwaysApply": false, "hideReleaseNotes": false}, "Mobile Homepage": {"value": "Home/Home", "kind": "File", "openOnStartup": true, "openMode": "Replace all open notes", "manualOpenMode": "Keep open notes", "view": "Reading view", "revertView": true, "openWhenEmpty": true, "refreshDataview": false, "autoCreate": true, "autoScroll": false, "pin": false, "commands": [], "alwaysApply": false, "hideReleaseNotes": false}}, "separateMobile": false}