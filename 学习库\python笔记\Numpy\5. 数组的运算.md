---
tags:
  - 学习
  - python
  - Numpy
---

# 数组与系数之间的运算

数组与系数之间的运算，可以直接使用`+`、`-`、`*`、`/`符号进行运算，Numpy会自动将系数与数组中的每个元素进行运算，返回一个新的数组。
但是要注意`/`符号的运算结果一定是浮点数。


```python
import numpy as np
# 创建二维数组-矩阵
arr1 = np.arange(1, 17).reshape(4, 4)
print(arr1)  # 输出结果为[[ 1  2  3  4] [ 5  6  7  8] [ 9 10 11 12] [13 14 15 16]]

# 数组与系数之间的运算
print(arr1 + 1)  # 输出结果为[[ 2  3  4  5] [ 6  7  8  9] [10 11 12 13] [14 15 16 17]]，表示将数组中的每个元素都加1
print(arr1 - 1)  # 输出结果为[[ 0  1  2  3] [ 4  5  6  7] [ 8  9 10 11] [12 13 14 15]]，表示将数组中的每个元素都减1
print(arr1 * 2)  # 输出结果为[[ 2  4  6  8] [10 12 14 16] [18 20 22 24] [26 28 30 32]]，表示将数组中的每个元素都乘2
print(arr1 / 2)  # 输出结果为[[0.5 1.  1.5 2. ] [2.5 3.  3.5 4. ] [4.5 5.  5.5 6. ] [6.5 7.  7.5 8. ]],表示将数组中的每个元素都除2
``` 

# 数组与数组之间的运算

数组与数组之间的运算，可以直接使用`+`、`-`、`*`、`/`符号进行运算，Numpy会自动将两个数组中的对应元素进行运算，返回一个新的数组。

```python
import numpy as np
# 创建二维数组-矩阵1
arr1 = np.arange(1, 17).reshape(4, 4)
print(arr1)  # 输出结果为[[ 1  2  3  4] [ 5  6  7  8] [ 9 10 11 12] [13 14 15 16]]
# 创建二维数组-矩阵2
arr2 = np.arange(17, 33).reshape(4, 4)
print(arr2)  # 输出结果为[[17 18 19 20] [21 22 23 24] [25 26 27 28] [29 30 31 32]]，表示一个4行4列的矩阵

# 数组与数组之间的运算
print(arr1 + arr2)  # 输出结果为[[18 20 22 24] [26 28 30 32] [34 36 38 40] [42 44 46 48]]，表示将两个数组中的对应元素相加
print(arr1 - arr2)  # 输出结果为[[-16 -16 -16 -16] [-16 -16 -16 -16] [-16 -16 -16 -16] [-16 -16 -16 -16]]，表示将两个数组中的对应元素相减
print(arr1 * arr2)  # 输出结果为[[ 17  36  57  80] [105 132 161 192] [225 260 297 336] [365 396 429 464]]，表示将两个数组中的对应元素相乘
print(arr1 / arr2)  # 输出结果为[[0.05882353 0.11111111 0.15789474 0.2       ] [0.23809524 0.27272727 0.30434783 0.33333333] [0.36       0.38461538 0.40740741 0.42857143] [0.4516129  0.46666667 0.48387097 0.5       ]]，表示将两个数组中的对应元素相除
``` 

# 广播

- 如果是向量和矩阵之间的运算，Numpy会自动将向量扩展为矩阵，然后进行运算
- 如果某矩阵是行矩阵或列矩阵，则其被广播，以适配另一个矩阵的形状

## 向量被广播

当一个形状为`(m, n)`的矩阵与一个向量做运算的时候，要求该向量的形状为`(n,)`，即该向量的长度与矩阵的列数相同。运算时，Numpy会自动将该向量扩展为`(m, n)`的矩阵，然后进行运算。

```python
import numpy as np
# 创建二维数组-矩阵
arr1 = np.arange(1, 17).reshape(4, 4)
print(arr1)  # 输出结果为[[ 1  2  3  4] [ 5  6  7  8] [ 9 10 11 12] [13 14 15 16]]
# 创建一维数组-向量
arr2 = np.array([1, 2, 3, 4])
print(arr2)  # 输出结果为[1 2 3 4]，表示一个向量

# 向量被广播
print(arr1 + arr2)  # 输出结果为[[ 2  4  6  8] [ 6  8 10 12] [10 12 14 16] [14 16 18 20]]，表示将向量扩展为矩阵，然后进行运算
```

## 列矩阵被广播

当一个形状为`(m, n)`的矩阵与一个列矩阵做运算的时候，要求该列矩阵的形状为`(m, 1)`，即该列矩阵的行数与矩阵的行数相同。运算时，Numpy会自动将该列矩阵扩展为`(m, n)`的矩阵，然后进行运算。

```python
import numpy as np
# 创建二维数组-矩阵
arr1 = np.arange(1, 17).reshape(4, 4)
print(arr1)  # 输出结果为[[ 1  2  3  4] [ 5  6  7  8] [ 9 10 11 12] [13 14 15 16]]
# 创建二维数组-列矩阵
arr2 = np.arange(4).reshape(4, 1)
print(arr2)  # 输出结果为[[0] [1] [2] [3]]，表示一个列矩阵

# 列矩阵被广播
print(arr1 + arr2)  # 输出结果为[[ 1  2  3  4] [ 6  7  8  9] [11 12 13 14] [16 17 18 19]]，表示将列矩阵扩展为矩阵，然后进行运算
```

## 行矩阵和列矩阵同时被广播

当一个形状为`(1,n)`的行矩阵与一个形状为`(m,1)`的列矩阵做运算的时候，Numpy会自动将该行矩阵和列矩阵扩展为`(m, n)`的矩阵，然后进行运算。

```python
import numpy as np
# 创建二维数组-行矩阵
arr1 = np.arange(1, 5).reshape(1, 4)
print(arr1)  # 输出结果为[[1 2 3 4]]，表示一个行矩阵
# 创建二维数组-列矩阵
arr2 = np.arange(4).reshape(4, 1)
print(arr2)  # 输出结果为[[0] [1] [2] [3]]，表示一个列矩阵

# 行矩阵和列矩阵同时被广播
print(arr1 + arr2)  # 输出结果为[[ 1  2  3  4] [ 2  3  4  5] [ 3  4  5  6] [ 4  5  6  7]]，表示将行矩阵和列矩阵扩展为矩阵，然后进行运算
```