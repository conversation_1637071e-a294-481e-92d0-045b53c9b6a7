import numpy as np
import matplotlib.pyplot as plt
from matplotlib.font_manager import FontProperties

# --- 准备工作 ---
# 为了在图中正确显示中文
# 请确保你的环境中有一个支持中文的字体文件（例如 simhei.ttf, msyh.ttc 等）
# 如果你没有字体文件，可以先运行不带 fontproperties 的版本，标题会显示乱码，但图形是正确的
try:
    # 尝试使用黑体
    font = FontProperties(fname=r"c:\windows\fonts\simhei.ttf", size=14)
except IOError:
    # 如果找不到，使用默认字体（中文可能会显示为方框）
    font = FontProperties(size=14)

# --- 1. 生成非线性数据 ---
# 我们创建一些看起来像二次函数 y = ax^2 + b 的数据，并加入一些随机“噪声”
np.random.seed(0)  # 固定随机种子以保证每次结果一样
X = np.linspace(-3, 3, 100) # 生成100个从-3到3的x值
# 真实函数是 y = 1.5 * x^2 + 1，并加上一些上下波动的噪声
y = 1.5 * (X**2) + 1 + np.random.normal(0, 1.5, X.shape[0])

# --- 2. 创建画布，准备绘制两个子图进行对比 ---
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5)) # 1行2列的图，大小为12x5英寸

# --- 3. 左图：尝试用线性函数拟合 ---
ax1.scatter(X, y, alpha=0.6, label='原始数据点') # 绘制原始数据散点图
# 使用numpy进行1次多项式拟合（即线性拟合）
coef_linear = np.polyfit(X, y, 1)
poly1d_fn_linear = np.poly1d(coef_linear)
ax1.plot(X, poly1d_fn_linear(X), color='red', linewidth=2, label='线性拟合')

ax1.set_title('场景一：使用线性函数拟合', fontproperties=font)
ax1.set_xlabel('X')
ax1.set_ylabel('y')
ax1.legend(prop=font)
ax1.grid(True, linestyle='--', alpha=0.5)

# --- 4. 右图：尝试用非线性函数拟合 ---
ax2.scatter(X, y, alpha=0.6, label='原始数据点') # 同样绘制原始数据散点图
# 使用numpy进行2次多项式拟合（即二次函数，非线性）
coef_nonlinear = np.polyfit(X, y, 2)
poly1d_fn_nonlinear = np.poly1d(coef_nonlinear)
ax2.plot(X, poly1d_fn_nonlinear(X), color='green', linewidth=2, label='非线性拟合')

ax2.set_title('场景二：使用非线性函数拟合', fontproperties=font)
ax2.set_xlabel('X')
ax2.set_ylabel('y')
ax2.legend(prop=font)
ax2.grid(True, linestyle='--', alpha=0.5)

# --- 5. 显示图像 ---
plt.tight_layout() # 自动调整子图参数，使之填充整个图像区域
plt.show()