---
tags:
  - 学习
  - 软件
  - 使用心得
---
##  使用软换行来提升视觉体验

```ad-note
title: 硬换行和软换行
1. **软换行 (Soft Wrap)**: 这是您的**编辑器**提供的功能。当一行代码超出屏幕宽度时，编辑器会自动将这行代码的“视觉呈现”折叠到下一行，但它**并没有**在您的源文件中真正插入一个换行符。这整段文字实际上看来**仍然是一行**。
    
2. **硬换行 (Hard Wrap)**: 这是指您在源文件中**手动按下回车键 (Enter)** 所创建的真正的换行。
```

```ad-done
title: 临时启用和永久启用软换行
1. **临时启用**:
    
    - 在 VS Code 的菜单栏，选择 视图 (View) -> 自动换行 (Word Wrap)。
        
    - 或者使用快捷键：
        
        - Windows / Linux: Alt + Z
            
        - macOS: Option + Z
            
    - 按下后，您会看到所有超出屏幕的长行都会被自动折叠，而您的源代码一行都没有改变。再次按下快捷键可以取消。
        
2. **永久启用（推荐）**:
    
    - 打开 VS Code 的设置 (Settings)。快捷键：Ctrl + , (Windows/Linux) 或 Cmd + , (macOS)。
        
    - 在搜索框中输入 word wrap。
        
    - 找到 Editor: Word Wrap 选项，将其值从 off 修改为 on。
        
    - 这样，VS Code 就会为您所有的文件默认启用软换行了。
```
