import torch
import torch.nn.functional as F

# 你的代码是完全正确的！
Y_pred1 = torch.Tensor([[0.1, 0.2, 0.9], # 第一个样本对各类别的预测分数（logits）
                        [1.1, 0.1, 0.2], # 第二个样本对各类别的预测分数（logits）
                        [0.2, 2.1, 0.1]]) # 第三个样本对各类别的预测分数（logits）

print("=== 详细解释每个样本的预测 ===")
print("形状:", Y_pred1.shape)  # [3, 3] = [样本数, 类别数]

class_names = ["类别0", "类别1", "类别2"]

for i, sample_logits in enumerate(Y_pred1):
    print(f"\n第{i+1}个样本的预测分数（logits）:")
    for j, score in enumerate(sample_logits):
        print(f"  {class_names[j]}: {score:.1f}")
    
    # 找出预测的类别（分数最高的）
    predicted_class = torch.argmax(sample_logits).item()
    print(f"  → 模型预测: {class_names[predicted_class]} (分数最高)")

print("\n=== 从logits到概率的转换 ===")
# 通过softmax将logits转换为概率
probabilities = F.softmax(Y_pred1, dim=1)

for i, (logits, probs) in enumerate(zip(Y_pred1, probabilities)):
    print(f"\n第{i+1}个样本:")
    print("  原始分数(logits) → 概率(softmax)")
    for j, (logit, prob) in enumerate(zip(logits, probs)):
        print(f"  {class_names[j]}: {logit:.1f} → {prob:.3f} ({prob*100:.1f}%)")

print("\n=== CrossEntropyLoss 详细计算过程 ===")

# 真实标签
Y = torch.LongTensor([2, 0, 1])  # 第1个样本是类别2，第2个是类别0，第3个是类别1
criterion = torch.nn.CrossEntropyLoss()

print("真实标签:", Y.tolist())
print()

# 手动计算每个样本的损失
print("手动计算每个样本的损失:")
total_loss = 0

for i in range(3):
    # 获取第i个样本的logits和真实标签
    sample_logits = Y_pred1[i]  # 形状: [3]
    true_class = Y[i].item()

    # 计算softmax概率
    sample_probs = F.softmax(sample_logits, dim=0)

    # 获取真实类别的概率
    true_class_prob = sample_probs[true_class]

    # 计算负对数似然（该样本的损失）
    sample_loss = -torch.log(true_class_prob)
    total_loss += sample_loss

    print(f"样本{i+1}:")
    print(f"  logits: {sample_logits.tolist()}")
    print(f"  真实类别: {true_class}")
    print(f"  softmax概率: {sample_probs.tolist()}")
    print(f"  真实类别{true_class}的概率: {true_class_prob:.4f}")
    print(f"  该样本损失: -log({true_class_prob:.4f}) = {sample_loss:.4f}")
    print()

# 计算平均损失
average_loss = total_loss / 3
print(f"总损失: {total_loss:.4f}")
print(f"平均损失: {average_loss:.4f}")

# 使用PyTorch的CrossEntropyLoss验证
pytorch_loss = criterion(Y_pred1, Y)
print(f"PyTorch计算的损失: {pytorch_loss:.4f}")
print(f"手动计算是否正确: {abs(average_loss - pytorch_loss) < 1e-6}")

print("\n=== 总结 ===")
print("CrossEntropyLoss的计算步骤:")
print("1. 对每个样本的logits应用softmax得到概率分布")
print("2. 取出真实类别对应的概率")
print("3. 计算该概率的负对数似然作为该样本的损失")
print("4. 将所有样本的损失求平均")
print()
print("公式: Loss = (1/N) * Σ(-log(P(真实类别_i)))")
print("其中 N 是样本数量，P(真实类别_i) 是第i个样本真实类别的预测概率")
