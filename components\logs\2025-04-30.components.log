2025-04-30 08:43:54 [info] components database created cost 2 ms   
2025-04-30 08:43:54 [info] components index initializing...   
2025-04-30 08:43:54 [info] start to batch put pages: 5   
2025-04-30 08:43:55 [info] batch persist cost 5  1207 
2025-04-30 08:43:55 [info] components index initialized, 859 files cost 1414 ms   
2025-04-30 08:43:55 [info] refresh page data from init listeners 0 859   
2025-04-30 08:43:57 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-30 08:43:57 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-30 08:43:57 [info] indexing created file components/logs/2025-04-30.components.log  [object Object] 
2025-04-30 08:43:57 [info] refresh page data from created listeners 0 860   
2025-04-30 08:43:57 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-30 08:43:57 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-30 08:43:57 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-30 08:43:57 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-30 08:43:59 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-30 08:43:59 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-30 08:43:59 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-30 08:58:20 [info] indexing created file 学习库/Deep learning/pytorch/attachments/5. 逻辑回归（Logisitic Regression)-2025-04-30-08-58-20.png  [object Object] 
2025-04-30 08:58:20 [info] refresh page data from created listeners 0 861   
2025-04-30 08:58:21 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 08:58:21 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 08:58:21 [info] index finished after resolve  [object Object] 
2025-04-30 08:58:21 [info] refresh page data from resolve listeners 0 861   
2025-04-30 08:59:18 [info] trigger 学习库/Deep learning/pytorch/attachments/5. 逻辑回归（Logisitic Regression)-2025-04-30-08-58-20.png resolve  [object Object] 
2025-04-30 08:59:19 [info] index finished after resolve  [object Object] 
2025-04-30 08:59:19 [info] refresh page data from modify listeners 0 861   
2025-04-30 09:00:08 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:00:08 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:00:08 [info] index finished after resolve  [object Object] 
2025-04-30 09:00:08 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:00:28 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:00:28 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:00:28 [info] index finished after resolve  [object Object] 
2025-04-30 09:00:28 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:00:34 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:00:34 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:00:34 [info] index finished after resolve  [object Object] 
2025-04-30 09:00:34 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:00:40 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:00:41 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:00:41 [info] index finished after resolve  [object Object] 
2025-04-30 09:00:41 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:01:25 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-30 09:01:25 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-30 09:01:25 [info] index finished after resolve  [object Object] 
2025-04-30 09:01:25 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:01:27 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-30 09:01:27 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-30 09:01:27 [info] index finished after resolve  [object Object] 
2025-04-30 09:01:27 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:05:53 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:05:53 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:05:53 [info] index finished after resolve  [object Object] 
2025-04-30 09:05:53 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:07:13 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:07:13 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:07:13 [info] index finished after resolve  [object Object] 
2025-04-30 09:07:13 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:07:16 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:07:17 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:07:17 [info] index finished after resolve  [object Object] 
2025-04-30 09:07:17 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:07:23 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:07:23 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:07:23 [info] index finished after resolve  [object Object] 
2025-04-30 09:07:23 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:07:26 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:07:26 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:07:26 [info] index finished after resolve  [object Object] 
2025-04-30 09:07:26 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:07:31 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:07:31 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:07:31 [info] index finished after resolve  [object Object] 
2025-04-30 09:07:31 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:07:37 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:07:37 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:07:37 [info] index finished after resolve  [object Object] 
2025-04-30 09:07:37 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:07:41 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:07:41 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:07:41 [info] index finished after resolve  [object Object] 
2025-04-30 09:07:41 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:07:45 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:07:45 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:07:45 [info] index finished after resolve  [object Object] 
2025-04-30 09:07:45 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:09:02 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:09:03 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:09:03 [info] index finished after resolve  [object Object] 
2025-04-30 09:09:03 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:13:44 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:13:44 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:13:44 [info] index finished after resolve  [object Object] 
2025-04-30 09:13:44 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:32:39 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:32:39 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:32:39 [info] index finished after resolve  [object Object] 
2025-04-30 09:32:39 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:32:53 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:32:53 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:32:53 [info] index finished after resolve  [object Object] 
2025-04-30 09:32:53 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:33:00 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:33:00 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:33:00 [info] index finished after resolve  [object Object] 
2025-04-30 09:33:00 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:33:13 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:33:13 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:33:13 [info] index finished after resolve  [object Object] 
2025-04-30 09:33:13 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:33:19 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:33:19 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:33:19 [info] index finished after resolve  [object Object] 
2025-04-30 09:33:19 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:33:22 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:33:22 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:33:22 [info] index finished after resolve  [object Object] 
2025-04-30 09:33:22 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:33:27 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:33:27 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:33:27 [info] index finished after resolve  [object Object] 
2025-04-30 09:33:27 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:33:35 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:33:35 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:33:35 [info] index finished after resolve  [object Object] 
2025-04-30 09:33:35 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:33:39 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:33:39 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:33:39 [info] index finished after resolve  [object Object] 
2025-04-30 09:33:39 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:33:53 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:33:53 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:33:53 [info] index finished after resolve  [object Object] 
2025-04-30 09:33:53 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:34:08 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:34:08 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:34:08 [info] index finished after resolve  [object Object] 
2025-04-30 09:34:08 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:34:20 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:34:20 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:34:20 [info] index finished after resolve  [object Object] 
2025-04-30 09:34:20 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:34:27 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:34:27 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:34:27 [info] index finished after resolve  [object Object] 
2025-04-30 09:34:27 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:34:31 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:34:31 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:34:31 [info] index finished after resolve  [object Object] 
2025-04-30 09:34:31 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:34:33 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:34:33 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:34:33 [info] index finished after resolve  [object Object] 
2025-04-30 09:34:33 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:34:36 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:34:36 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:34:36 [info] index finished after resolve  [object Object] 
2025-04-30 09:34:36 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:34:41 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:34:41 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:34:41 [info] index finished after resolve  [object Object] 
2025-04-30 09:34:41 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:34:50 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:34:50 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:34:50 [info] index finished after resolve  [object Object] 
2025-04-30 09:34:50 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:34:54 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:34:54 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:34:54 [info] index finished after resolve  [object Object] 
2025-04-30 09:34:54 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:34:57 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:34:57 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:34:57 [info] index finished after resolve  [object Object] 
2025-04-30 09:34:57 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:35:17 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:35:17 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:35:17 [info] index finished after resolve  [object Object] 
2025-04-30 09:35:17 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:35:20 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:35:20 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:35:20 [info] index finished after resolve  [object Object] 
2025-04-30 09:35:20 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:35:26 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:35:26 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:35:26 [info] index finished after resolve  [object Object] 
2025-04-30 09:35:26 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:35:35 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:35:35 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:35:35 [info] index finished after resolve  [object Object] 
2025-04-30 09:35:35 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:35:44 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:35:44 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:35:44 [info] index finished after resolve  [object Object] 
2025-04-30 09:35:44 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:35:46 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:35:46 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:35:46 [info] index finished after resolve  [object Object] 
2025-04-30 09:35:46 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:35:50 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:35:50 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:35:50 [info] index finished after resolve  [object Object] 
2025-04-30 09:35:50 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:36:04 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:36:04 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:36:04 [info] index finished after resolve  [object Object] 
2025-04-30 09:36:04 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:36:07 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:36:07 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:36:07 [info] index finished after resolve  [object Object] 
2025-04-30 09:36:07 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:36:23 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:36:23 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:36:23 [info] index finished after resolve  [object Object] 
2025-04-30 09:36:23 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:36:26 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:36:26 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:36:26 [info] index finished after resolve  [object Object] 
2025-04-30 09:36:26 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:36:29 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:36:29 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:36:29 [info] index finished after resolve  [object Object] 
2025-04-30 09:36:29 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:36:31 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:36:31 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:36:31 [info] index finished after resolve  [object Object] 
2025-04-30 09:36:31 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:36:39 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:36:39 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:36:39 [info] index finished after resolve  [object Object] 
2025-04-30 09:36:39 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:36:43 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:36:43 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:36:43 [info] index finished after resolve  [object Object] 
2025-04-30 09:36:43 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:36:46 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:36:46 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:36:46 [info] index finished after resolve  [object Object] 
2025-04-30 09:36:46 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:36:49 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:36:49 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:36:49 [info] index finished after resolve  [object Object] 
2025-04-30 09:36:49 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:36:51 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:36:51 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:36:51 [info] index finished after resolve  [object Object] 
2025-04-30 09:36:51 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:37:12 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:37:12 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:37:12 [info] index finished after resolve  [object Object] 
2025-04-30 09:37:12 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:37:15 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:37:15 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:37:15 [info] index finished after resolve  [object Object] 
2025-04-30 09:37:15 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:37:19 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:37:19 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:37:19 [info] index finished after resolve  [object Object] 
2025-04-30 09:37:19 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:37:21 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:37:21 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:37:21 [info] index finished after resolve  [object Object] 
2025-04-30 09:37:21 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:37:26 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:37:26 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:37:26 [info] index finished after resolve  [object Object] 
2025-04-30 09:37:26 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:37:34 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:37:34 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:37:34 [info] index finished after resolve  [object Object] 
2025-04-30 09:37:34 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:37:43 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:37:43 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:37:43 [info] index finished after resolve  [object Object] 
2025-04-30 09:37:43 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:37:46 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:37:46 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:37:46 [info] index finished after resolve  [object Object] 
2025-04-30 09:37:46 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:37:48 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:37:48 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:37:48 [info] index finished after resolve  [object Object] 
2025-04-30 09:37:48 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:37:51 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:37:51 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:37:51 [info] index finished after resolve  [object Object] 
2025-04-30 09:37:51 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:37:58 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:37:58 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:37:58 [info] index finished after resolve  [object Object] 
2025-04-30 09:37:58 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:38:03 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:38:03 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:38:03 [info] index finished after resolve  [object Object] 
2025-04-30 09:38:03 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:38:12 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:38:12 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:38:12 [info] index finished after resolve  [object Object] 
2025-04-30 09:38:12 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:38:15 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:38:15 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:38:15 [info] index finished after resolve  [object Object] 
2025-04-30 09:38:15 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:38:17 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:38:17 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:38:17 [info] index finished after resolve  [object Object] 
2025-04-30 09:38:17 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:38:23 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:38:23 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:38:23 [info] index finished after resolve  [object Object] 
2025-04-30 09:38:23 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:38:28 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:38:28 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:38:28 [info] index finished after resolve  [object Object] 
2025-04-30 09:38:28 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:38:31 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:38:31 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:38:31 [info] index finished after resolve  [object Object] 
2025-04-30 09:38:31 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:38:34 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:38:34 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:38:34 [info] index finished after resolve  [object Object] 
2025-04-30 09:38:34 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:38:37 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:38:37 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:38:37 [info] index finished after resolve  [object Object] 
2025-04-30 09:38:37 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:38:39 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:38:39 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:38:39 [info] index finished after resolve  [object Object] 
2025-04-30 09:38:39 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:38:42 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:38:42 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:38:42 [info] index finished after resolve  [object Object] 
2025-04-30 09:38:42 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:38:46 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:38:46 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:38:46 [info] index finished after resolve  [object Object] 
2025-04-30 09:38:46 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:38:50 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:38:50 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:38:50 [info] index finished after resolve  [object Object] 
2025-04-30 09:38:50 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:39:32 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:39:32 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:39:32 [info] index finished after resolve  [object Object] 
2025-04-30 09:39:32 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:41:02 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:41:02 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:41:02 [info] index finished after resolve  [object Object] 
2025-04-30 09:41:02 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:42:45 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:42:46 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:42:46 [info] index finished after resolve  [object Object] 
2025-04-30 09:42:46 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:43:57 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:43:57 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:43:57 [info] index finished after resolve  [object Object] 
2025-04-30 09:43:57 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:45:28 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:45:28 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:45:28 [info] index finished after resolve  [object Object] 
2025-04-30 09:45:28 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:46:06 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:46:06 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:46:06 [info] index finished after resolve  [object Object] 
2025-04-30 09:46:06 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:46:10 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:46:10 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:46:10 [info] index finished after resolve  [object Object] 
2025-04-30 09:46:10 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:46:31 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:46:31 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:46:31 [info] index finished after resolve  [object Object] 
2025-04-30 09:46:31 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:47:24 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:47:24 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:47:24 [info] index finished after resolve  [object Object] 
2025-04-30 09:47:24 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:47:33 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:47:33 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:47:33 [info] index finished after resolve  [object Object] 
2025-04-30 09:47:33 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:48:09 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:48:09 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:48:09 [info] index finished after resolve  [object Object] 
2025-04-30 09:48:09 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:48:47 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:48:47 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:48:47 [info] index finished after resolve  [object Object] 
2025-04-30 09:48:47 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:48:59 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:48:59 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:48:59 [info] index finished after resolve  [object Object] 
2025-04-30 09:48:59 [info] refresh page data from resolve listeners 0 861   
2025-04-30 09:49:50 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 09:49:50 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 09:49:50 [info] index finished after resolve  [object Object] 
2025-04-30 09:49:50 [info] refresh page data from resolve listeners 0 861   
2025-04-30 10:04:48 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:04:48 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:04:48 [info] index finished after resolve  [object Object] 
2025-04-30 10:04:48 [info] refresh page data from resolve listeners 0 861   
2025-04-30 10:09:59 [info] indexing created file 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md)  [object Object] 
2025-04-30 10:09:59 [info] refresh page data from created listeners 0 862   
2025-04-30 10:10:17 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md) resolve  [object Object] 
2025-04-30 10:10:17 [info] index finished after resolve  [object Object] 
2025-04-30 10:10:17 [info] refresh page data from modify listeners 0 862   
2025-04-30 10:10:36 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md) resolve  [object Object] 
2025-04-30 10:10:36 [info] index finished after resolve  [object Object] 
2025-04-30 10:10:36 [info] refresh page data from modify listeners 0 862   
2025-04-30 10:10:51 [info] indexing created file 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md  [object Object] 
2025-04-30 10:10:51 [info] indexing created ignore file 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-04-30 10:10:51 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-04-30 10:10:51 [info] index finished after resolve  [object Object] 
2025-04-30 10:10:51 [info] refresh page data from resolve listeners 0 863   
2025-04-30 10:10:52 [info] refresh page data from delete listeners 0 862   
2025-04-30 10:30:32 [info] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-04-30 10:30:32 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-04-30 10:30:32 [info] index finished after resolve  [object Object] 
2025-04-30 10:30:32 [info] refresh page data from resolve listeners 0 862   
2025-04-30 10:30:40 [info] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-04-30 10:30:40 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-04-30 10:30:40 [info] index finished after resolve  [object Object] 
2025-04-30 10:30:40 [info] refresh page data from resolve listeners 0 862   
2025-04-30 10:30:52 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:30:52 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:30:52 [info] index finished after resolve  [object Object] 
2025-04-30 10:30:52 [info] refresh page data from resolve listeners 0 862   
2025-04-30 10:32:24 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:32:24 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:32:24 [info] index finished after resolve  [object Object] 
2025-04-30 10:32:24 [info] refresh page data from resolve listeners 0 862   
2025-04-30 10:33:11 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:33:11 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:33:11 [info] index finished after resolve  [object Object] 
2025-04-30 10:33:11 [info] refresh page data from resolve listeners 0 862   
2025-04-30 10:33:13 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:33:13 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:33:13 [info] index finished after resolve  [object Object] 
2025-04-30 10:33:13 [info] refresh page data from resolve listeners 0 862   
2025-04-30 10:33:16 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:33:16 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:33:16 [info] index finished after resolve  [object Object] 
2025-04-30 10:33:16 [info] refresh page data from resolve listeners 0 862   
2025-04-30 10:33:33 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:33:33 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:33:33 [info] index finished after resolve  [object Object] 
2025-04-30 10:33:33 [info] refresh page data from resolve listeners 0 862   
2025-04-30 10:33:37 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:33:37 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:33:37 [info] index finished after resolve  [object Object] 
2025-04-30 10:33:37 [info] refresh page data from resolve listeners 0 862   
2025-04-30 10:33:39 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:33:39 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:33:39 [info] index finished after resolve  [object Object] 
2025-04-30 10:33:39 [info] refresh page data from resolve listeners 0 862   
2025-04-30 10:33:42 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:33:42 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:33:42 [info] index finished after resolve  [object Object] 
2025-04-30 10:33:42 [info] refresh page data from resolve listeners 0 862   
2025-04-30 10:33:46 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:33:46 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:33:46 [info] index finished after resolve  [object Object] 
2025-04-30 10:33:46 [info] refresh page data from resolve listeners 0 862   
2025-04-30 10:34:15 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:34:15 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:34:15 [info] index finished after resolve  [object Object] 
2025-04-30 10:34:15 [info] refresh page data from resolve listeners 0 862   
2025-04-30 10:34:28 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:34:28 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:34:28 [info] index finished after resolve  [object Object] 
2025-04-30 10:34:28 [info] refresh page data from resolve listeners 0 862   
2025-04-30 10:37:32 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:37:33 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:37:33 [info] index finished after resolve  [object Object] 
2025-04-30 10:37:33 [info] refresh page data from resolve listeners 0 862   
2025-04-30 10:37:43 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:37:43 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:37:43 [info] index finished after resolve  [object Object] 
2025-04-30 10:37:43 [info] refresh page data from resolve listeners 0 862   
2025-04-30 10:37:46 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:37:46 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:37:46 [info] index finished after resolve  [object Object] 
2025-04-30 10:37:46 [info] refresh page data from resolve listeners 0 862   
2025-04-30 10:37:50 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:37:50 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:37:50 [info] index finished after resolve  [object Object] 
2025-04-30 10:37:50 [info] refresh page data from resolve listeners 0 862   
2025-04-30 10:44:03 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:44:03 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:44:03 [info] index finished after resolve  [object Object] 
2025-04-30 10:44:03 [info] refresh page data from resolve listeners 0 862   
2025-04-30 10:45:04 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:45:04 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:45:04 [info] index finished after resolve  [object Object] 
2025-04-30 10:45:04 [info] refresh page data from resolve listeners 0 862   
2025-04-30 10:45:21 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:45:21 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:45:21 [info] index finished after resolve  [object Object] 
2025-04-30 10:45:21 [info] refresh page data from resolve listeners 0 862   
2025-04-30 10:45:27 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:45:27 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:45:27 [info] index finished after resolve  [object Object] 
2025-04-30 10:45:27 [info] refresh page data from resolve listeners 0 862   
2025-04-30 10:45:34 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:45:34 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:45:34 [info] index finished after resolve  [object Object] 
2025-04-30 10:45:34 [info] refresh page data from resolve listeners 0 862   
2025-04-30 10:45:37 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:45:37 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:45:37 [info] index finished after resolve  [object Object] 
2025-04-30 10:45:37 [info] refresh page data from resolve listeners 0 862   
2025-04-30 10:46:29 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:46:29 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:46:29 [info] index finished after resolve  [object Object] 
2025-04-30 10:46:29 [info] refresh page data from resolve listeners 0 862   
2025-04-30 10:49:26 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:49:26 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:49:26 [info] index finished after resolve  [object Object] 
2025-04-30 10:49:26 [info] refresh page data from resolve listeners 0 862   
2025-04-30 10:49:40 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:49:40 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:49:40 [info] index finished after resolve  [object Object] 
2025-04-30 10:49:40 [info] refresh page data from resolve listeners 0 862   
2025-04-30 10:49:50 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:49:50 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:49:50 [info] index finished after resolve  [object Object] 
2025-04-30 10:49:50 [info] refresh page data from resolve listeners 0 862   
2025-04-30 10:49:52 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:49:52 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:49:52 [info] index finished after resolve  [object Object] 
2025-04-30 10:49:52 [info] refresh page data from resolve listeners 0 862   
2025-04-30 10:49:55 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:49:55 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:49:55 [info] index finished after resolve  [object Object] 
2025-04-30 10:49:55 [info] refresh page data from resolve listeners 0 862   
2025-04-30 10:49:58 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:49:58 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:49:58 [info] index finished after resolve  [object Object] 
2025-04-30 10:49:58 [info] refresh page data from resolve listeners 0 862   
2025-04-30 10:50:00 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:50:00 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:50:00 [info] index finished after resolve  [object Object] 
2025-04-30 10:50:00 [info] refresh page data from resolve listeners 0 862   
2025-04-30 10:50:04 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:50:04 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:50:04 [info] index finished after resolve  [object Object] 
2025-04-30 10:50:04 [info] refresh page data from resolve listeners 0 862   
2025-04-30 10:50:37 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:50:37 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:50:37 [info] index finished after resolve  [object Object] 
2025-04-30 10:50:37 [info] refresh page data from resolve listeners 0 862   
2025-04-30 10:51:00 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:51:00 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:51:00 [info] index finished after resolve  [object Object] 
2025-04-30 10:51:00 [info] refresh page data from resolve listeners 0 862   
2025-04-30 10:51:18 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:51:18 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:51:18 [info] index finished after resolve  [object Object] 
2025-04-30 10:51:18 [info] refresh page data from resolve listeners 0 862   
2025-04-30 10:54:24 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:54:24 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:54:24 [info] index finished after resolve  [object Object] 
2025-04-30 10:54:24 [info] refresh page data from resolve listeners 0 862   
2025-04-30 10:54:27 [info] indexing created file 学习库/Deep learning/pytorch/attachments/5. 逻辑回归（Logisitic Regression)-2025-04-30-10-54-27.png  [object Object] 
2025-04-30 10:54:27 [info] refresh page data from created listeners 0 863   
2025-04-30 10:54:29 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:54:29 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:54:29 [info] index finished after resolve  [object Object] 
2025-04-30 10:54:29 [info] refresh page data from resolve listeners 0 863   
2025-04-30 10:56:08 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 10:56:08 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 10:56:08 [info] index finished after resolve  [object Object] 
2025-04-30 10:56:08 [info] refresh page data from resolve listeners 0 863   
2025-04-30 11:01:44 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-30 11:01:44 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-30 11:01:44 [info] index finished after resolve  [object Object] 
2025-04-30 11:01:44 [info] refresh page data from resolve listeners 0 863   
2025-04-30 11:02:04 [info] trigger 学习库/Deep learning/pytorch/PDF/test.py resolve  [object Object] 
2025-04-30 11:02:04 [info] index finished after resolve  [object Object] 
2025-04-30 11:02:04 [info] refresh page data from modify listeners 0 863   
2025-04-30 11:02:07 [info] trigger 学习库/Deep learning/pytorch/PDF/test.py resolve  [object Object] 
2025-04-30 11:02:07 [info] index finished after resolve  [object Object] 
2025-04-30 11:02:07 [info] refresh page data from modify listeners 0 863   
