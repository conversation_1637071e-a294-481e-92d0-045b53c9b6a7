{"path": "文献库/文献库/舌诊/attachments/BQJPRVIF.png", "text": "Table 2 mloU comparison of different approaches with input images under various conditions, bold numbers indicate the highest values Segmentation model Original Rotation Brightening Darkening Gaussian Vertical Color blur flip correction GrabCut 40.5+£232 266+20.1 40.8+252 40.8+21.1 40.7+20.6 38.7+22.6 40.7+233 DeepMask 848+119 779+£20.0 72.1+£262 747+254 781+£220 78.0£19.7 855+95 Resnet50+FPN 90.1+£10.1 88.7+12.0 755+31.1 87.6+163 8444183 857+155 89.6+11.6 Resnet1014+-FPN 917+79 906+99 8814122 89.4+122 848+169 9I14+58 923148 Resnet1014+FPN (without 912+10.6 883+142 892+160 903+93 856+17.6 8894138 91.3+10.8 augmentation) Table 3 Dice score comparison of different approaches with input images under various conditions, bold numbers indicate the highest values Segmentation model Original Rotation Brightening Darkening Gaussian Vertical Color blur flip correction GrabCut 539+234 3834236 532+27.1 547+£219 5484236 5204237 54.0+23.6 DeepMask 91.7+82 86.7+17.1 80.8+24.1 81.3+26.1 86.1+17.8 848+11.9 91.8+£82 Resnet50+FPN 939+9.0 939+69 792+325 92.6+144 903+153 91.74+9.8 940+£89 Resnet101+FPN 958+t41 944+71 91.1+£196 932+£140 882+214 950+52 959441 Resnet1014+FPN (without 95075 93.0+104 93.0+t152 951+75 908+158 934+10.1 950+7.7 augmentation)", "libVersion": "0.3.2", "langs": "eng"}