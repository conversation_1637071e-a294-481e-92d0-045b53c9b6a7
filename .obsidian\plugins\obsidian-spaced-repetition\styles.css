/* MARK: utils */

.sr-bg-blue,
.sr-bg-green,
.sr-bg-red {
    color: #ffffff !important;
}

.sr-bg-green {
    background-color: #4caf50 !important;
}

.sr-bg-blue {
    background-color: #2094f3 !important;
}

.sr-bg-red {
    background-color: #ff7043 !important;
}

.sr-deck-list .sr-tree-item-row:hover .sr-bg-green,
.sr-response-button.sr-bg-green:hover {
    background-color: hsl(122, 39%, 44%) !important;
}

.sr-deck-list .sr-tree-item-row:hover .sr-bg-blue,
.sr-response-button.sr-bg-blue:hover {
    background-color: hsl(207, 90%, 49%) !important;
}

.sr-deck-list .sr-tree-item-row:hover .sr-bg-red,
.sr-response-button.sr-bg-red:hover {
    background-color: hsl(14, 100%, 58%) !important;
}

.sr-is-hidden {
    display: none !important;
}

.sr-centered {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.is-tablet .sr-button {
    padding: var(--size-4-1) var(--size-4-3) !important;
}

.sr-button {
    box-shadow: none !important;
    cursor: pointer;
    width: var(--side-button-size);
    height: var(--side-button-size);
}

.sr-back-button:hover,
.sr-button:hover {
    background-color: var(--background-modifier-hover);
}

/* MARK: Mobile landscape mode */

@media only screen and (orientation: landscape) {
    .is-mobile:not(.is-tablet) .sr-tab-view {
        padding: 0 8px;
    }

    .is-mobile:not(.is-tablet) #sr-modal .sr-modal-content {
        padding: 8px;
    }

    .is-mobile:not(.is-tablet) #sr-modal,
    .is-mobile:not(.is-tablet) .sr-tab-view {
        --side-button-clearance: calc(calc(var(--side-button-size)) + 8px);
    }

    .is-mobile:not(.is-tablet) .sr-deck-list,
    .is-mobile:not(.is-tablet) .sr-flashcard {
        gap: 8px;
    }

    .is-mobile:not(.is-tablet) .sr-deck-list hr {
        margin-bottom: 8px;
    }

    .is-mobile:not(.is-tablet) .sr-flashcard .sr-controls {
        width: unset !important;
        flex-direction: column;
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        padding-left: 8px;
    }

    .is-mobile:not(.is-tablet) .sr-flashcard .sr-chosen-deck-info > *,
    .is-mobile:not(.is-tablet) .sr-flashcard .sr-current-deck-info > * {
        font-size: medium;
    }

    .is-mobile:not(.is-tablet) .sr-tab-view .sr-flashcard .sr-info-section {
        margin: 0 0 0 calc(var(--side-button-clearance));
    }

    .is-mobile:not(.is-tablet) #sr-modal .sr-flashcard .sr-info-section {
        margin: 0 calc(var(--side-button-clearance));
    }

    .is-mobile:not(.is-tablet) .sr-flashcard .sr-content {
        margin: 0 0 0 calc(var(--side-button-clearance));
    }

    .is-mobile:not(.is-tablet) .sr-flashcard .sr-content hr {
        margin: 2px 0;
    }

    .is-mobile:not(.is-tablet) .sr-response-button {
        height: 48px !important;
    }
}

@media only screen and (orientation: landscape) and (max-height: 460px) {
    #sr-modal,
    .sr-tab-view {
        --side-button-size: 35px !important;
    }

    .is-mobile:not(.is-tablet) .sr-response {
        margin: 0 0 0 calc(var(--side-button-clearance));
    }

    .is-mobile:not(.is-tablet) .sr-response-button {
        height: 32px !important;
    }

    .is-mobile:not(.is-tablet) .sr-flashcard .sr-content p {
        margin-block-start: 0.5rem;
        margin-block-end: 0.5rem;
    }
}

@media only screen and (orientation: landscape) and (max-height: 400px) {
    .is-mobile:not(.is-tablet) .sr-tab-view .sr-flashcard .sr-info-section {
        --side-button-clearance: calc(var(--side-button-size) * 3) !important;
    }

    .is-mobile:not(.is-tablet) .sr-tab-view .sr-back-button {
        left: calc(var(--side-button-size) * 2) !important;
        top: 0px !important;
    }

    .is-mobile:not(.is-tablet) .sr-flashcard {
        gap: 4px;
    }
}

/* MARK: Mobile portrait mode */

@media only screen and (orientation: portrait) {
    .is-mobile:not(.is-tablet) .sr-flashcard,
    .is-mobile:not(.is-tablet) .sr-deck-list {
        gap: 12px;
    }

    .is-mobile:not(.is-tablet) .sr-tab-view {
        padding: 0 8px;
    }
}

@media only screen and (orientation: portrait) and (max-width: 650px) {
    .is-mobile:not(.is-tablet) .sr-chosen-deck-name,
    .is-mobile:not(.is-tablet) .sr-current-deck-name {
        max-width: 15ch;
    }
}

@media only screen and (orientation: portrait) and (max-width: 550px) {
    .is-mobile .sr-tab-view {
        padding: 0 8px;
    }

    .is-mobile:not(.is-tablet) .sr-chosen-deck-name,
    .is-mobile:not(.is-tablet) .sr-current-deck-name {
        max-width: 10ch;
    }
}

@media only screen and (orientation: portrait) and (max-width: 500px) {
    .is-mobile .sr-tab-view {
        padding: 0 8px;
    }
}

@media only screen and (orientation: portrait) and (max-width: 440px) {
    .is-mobile:not(.is-tablet) .sr-chosen-deck-name,
    .is-mobile:not(.is-tablet) .sr-current-deck-name {
        max-width: 8ch;
    }
}

@media only screen and (orientation: portrait) and (max-width: 410px) {
    .is-mobile:not(.is-tablet) .sr-chosen-deck-name,
    .is-mobile:not(.is-tablet) .sr-current-deck-name {
        max-width: 6ch;
    }
}

@media only screen and (orientation: portrait) and (max-width: 340px) {
    .is-mobile .sr-response-button {
        height: 32px !important;
    }

    #sr-modal,
    .sr-tab-view {
        --side-button-size: 35px !important;
    }

    .is-mobile .sr-tab-view {
        padding: 0 4px;
    }
}

/* MARK: Modal */

#sr-modal {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

#sr-modal,
.sr-tab-view {
    --side-button-size: 44px;
    --side-button-clearance: calc(var(--side-button-size));
    overflow: hidden !important;
}

#sr-modal .modal-header {
    display: none;
}

#sr-modal .modal-close-button {
    z-index: 21;
}

body:not(.native-scrollbars) #sr-modal .modal-close-button {
    top: 12px;
}

.sr-back-button {
    z-index: 21;
    cursor: var(--cursor);
    position: absolute;
    top: 12px;
    left: 12px;
    font-size: 26px;
    line-height: 22px;
    height: 26px;
    width: 26px;
    padding: 0 var(--size-2-2);
    border-radius: var(--radius-s);
    color: var(--text-muted);
    display: flex;
    justify-content: center;
    align-items: center;
}

/* MARK: Tab view */

.sr-tab-view {
    display: flex;
    flex-direction: column;
}

.sr-tab-view-content {
    flex-grow: 1;
    margin: auto;
}

.sr-modal-content,
.sr-tab-view {
    position: relative;
    overflow: hidden;
}

/* MARK: Views*/

.sr-deck-list,
.sr-flashcard {
    gap: 16px;
}

.sr-deck-list,
.sr-flashcard,
.sr-edit-view {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
}

.sr-header {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.sr-tab-view .sr-deck-list .sr-header {
    margin: 0;
}

.sr-title {
    font-size: var(--font-ui-large);
    font-weight: var(--font-semibold);
    text-align: center;
    line-height: var(--line-height-tight);
}

.sr-content {
    overflow-y: auto;
}

/* MARK: DeckListView */

.sr-deck-list > hr {
    margin: 2px 0 14px 0;
}

.sr-deck-list .sr-header {
    gap: 8px;
}

.sr-deck-list .sr-header-stats-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
    align-items: center;
}

.sr-deck-list .sr-header-stats-count {
    display: flex;
    color: #ffffff;
    padding: 4px;
    gap: 4px;
}

.sr-deck-list .sr-header-stats-count > *:first-child {
    min-width: 10ch;
}

.sr-deck-list .sr-header-stats-count > *:last-child {
    min-width: 3ch;
    text-align: right;
}

.sr-deck-list .sr-tree-item-row {
    padding-top: 2px;
    padding-bottom: 2px;
    margin-bottom: 0;
}

.sr-deck-list .sr-tree-stats-container {
    display: flex;
    gap: 4px;
}

.sr-deck-list .sr-tree-stats-count {
    min-width: 3ch;
    padding: 4px;
    box-sizing: content-box;
    text-align: center;
    color: #ffffff !important;
}

/* MARK: FlashcardReviewView */

.sr-flashcard .sr-button:disabled {
    cursor: not-allowed;
}

.sr-flashcard .sr-controls {
    display: flex;
    width: 100%;
    gap: var(--size-4-4);
    justify-content: center;
    align-items: center;
}

/* 跳转到原笔记按钮样式 */
.sr-goto-note-button {
    background-color: var(--interactive-accent);
    color: var(--text-on-accent);
    border-radius: var(--radius-s);
    transition: background-color 0.2s ease;
}

.sr-goto-note-button:hover {
    background-color: var(--interactive-accent-hover);
}

.sr-flashcard .sr-info-section {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.sr-flashcard .sr-deck-progress-info {
    display: flex;
    flex-wrap: wrap;
    gap: 4px 24px;
}

.sr-flashcard .sr-chosen-deck-info,
.sr-flashcard .sr-current-deck-info {
    display: flex;
    gap: 12px;
    flex-wrap: nowrap;

    color: var(--text-muted);
    text-wrap: nowrap;
    font-weight: bold;
}

.sr-flashcard .sr-chosen-deck-name,
.sr-flashcard .sr-current-deck-name {
    max-width: 20ch;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sr-flashcard .sr-chosen-deck-counter-wrapper,
.sr-flashcard .sr-current-deck-counter-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12px;
}

.sr-flashcard .sr-chosen-deck-counter-divider,
.sr-flashcard .sr-current-deck-counter-divider {
    display: flex;
    border: 1px solid var(--text-faint);
    padding: 1ch 0;
}

.sr-flashcard .sr-chosen-deck-card-counter-wrapper,
.sr-flashcard .sr-current-deck-card-counter-wrapper,
.sr-flashcard .sr-chosen-deck-subdeck-counter-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5ch;
}

.sr-flashcard .sr-chosen-deck-card-counter-icon,
.sr-flashcard .sr-chosen-deck-subdeck-counter-icon,
.sr-flashcard .sr-current-deck-card-counter-icon {
    display: flex;
    justify-content: center;
    align-items: center;
}

.sr-flashcard .sr-chosen-deck-card-counter-icon svg,
.sr-flashcard .sr-chosen-deck-subdeck-counter-icon svg,
.sr-flashcard .sr-current-deck-card-counter-icon svg {
    --icon-size: 16px;
    --icon-stroke: 2.5px;
}

.sr-flashcard .sr-context {
    font-style: italic;
    color: var(--text-faint);
    display: block;
}

.sr-flashcard .sr-content {
    font-size: var(--font-text-size);
    overflow-y: auto;
    user-select: text;
    padding-inline: 8px;
    flex-grow: 1;
}

.sr-flashcard .sr-content,
.sr-flashcard .sr-context {
    text-wrap: wrap;
    word-break: break-word;
}

.sr-flashcard .sr-content hr {
    backdrop-filter: invert(40%);
    border-top-style: dashed;
}

.sr-response {
    display: flex;
    gap: var(--size-4-4);
}

.sr-response-button {
    height: 48px;
    flex-grow: 1;
    margin: auto;
    line-height: 48px;
    text-align: center;
    cursor: pointer;
    border-radius: 4px;
    user-select: text;
}

/* MARK: EditModal */

.sr-edit-modal {
    height: 80%;
}

.sr-edit-view {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: var(--size-4-4);
}

.sr-edit-view .sr-input {
    flex-grow: 1;
    width: 100%;
    resize: none;
}

.sr-edit-view .sr-response {
    display: grid;
    grid-template-columns: auto auto auto;
    width: 100%;
    margin-top: 0;
}

.sr-edit-view .sr-response-button {
    width: 100%;
}

.sr-edit-view .sr-response-button.sr-spacer {
    opacity: 0;
    cursor: default;
}

/*  MARK: Statistics */

#sr-chart-period {
    appearance: menulist;
    border-right: 8px solid transparent;
}

/* MARK: Tab elements */

/*
 * Tab elements
 * This CSS is copied from https://github.com/Taitava/obsidian-shellcommands
 * Jarkko Linnanvirta https://github.com/Taitava comments below...
 *  - Renamed classes
 *
 * This CSS is copied 2021-10-21 from https://www.w3schools.com/howto/howto_js_tabs.asp
 * Modifications:
 *  - Renamed classes
 *  - Added tab icons.
 *  - Changed colors.
 *  - Changed/removed borders.
 *  - Removed button transition.
 *  - Changed button border-radiuses
 *  - Added margin-right rule to .sr-tab-header-button .
 */

/* Style the tab */
.sr-tab-header {
    border-bottom: 6px solid var(--background-modifier-border);
}

/* Style the buttons that are used to open the tab content */
button.sr-tab-header-button {
    background-color: unset;
    border: none;
    box-shadow: none;
    /* Remove a "border" that came via Obsidian 0.16.0. */
    outline: none;
    cursor: pointer;
    padding: 14px 16px;
    margin-right: 6px;
    /* Reduced margin. Obsidian's default margin-right for button is 12px (0 for other margins). */
    border-radius: 10px 10px 0 0;
    /* 0 0 = No border-radius at bottom */
}

/* Create an active/current tablink class */
button.sr-tab-header-button.sr-tab-active,
button.sr-tab-header-button:hover {
    background-color: var(--background-modifier-border);
}

.sr-tab-header-button svg {
    vertical-align: middle;
    /* Not middle but close enough. */
}

/* Style the tab content */
.sr-tab-content {
    display: none;
    padding: 6px 12px;
}

.sr-tab-content.sr-tab-active {
    display: block;
}

/* MARK: gridjs */

/*
 * gridjs
 * put everything SR plugin specific above this block
 */
.gridjs-footer button,
.gridjs-head button {
    background-color: transparent;
    background-image: none;
    border: none;
    cursor: pointer;
    margin: 0;
    outline: none;
    padding: 0;
}

.gridjs-temp {
    position: relative;
}

.gridjs-head {
    margin-bottom: 5px;
    padding: 5px 1px;
    width: 100%;
}

.gridjs-head:after {
    clear: both;
    content: "";
    display: block;
}

.gridjs-head:empty {
    border: none;
    padding: 0;
}

.gridjs-container {
    color: #000;
    display: inline-block;
    overflow: hidden;
    padding: 2px;
    position: relative;
    z-index: 0;
}

.gridjs-footer {
    background-color: #fff;
    border-bottom-width: 1px;
    border-color: #e5e7eb;
    border-radius: 0 0 8px 8px;
    border-top: 1px solid #e5e7eb;
    box-shadow:
        0 1px 3px 0 rgba(0, 0, 0, 0.1),
        0 1px 2px 0 rgba(0, 0, 0, 0.26);
    display: block;
    padding: 12px 24px;
    position: relative;
    width: 100%;
    z-index: 5;
}

.gridjs-footer:empty {
    border: none;
    padding: 0;
}

input.gridjs-input {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: #fff;
    border: 1px solid #d2d6dc;
    border-radius: 5px;
    font-size: 14px;
    line-height: 1.45;
    outline: none;
    padding: 10px 13px;
}

input.gridjs-input:focus {
    border-color: #9bc2f7;
    box-shadow: 0 0 0 3px rgba(149, 189, 243, 0.5);
}

.gridjs-pagination {
    color: #3d4044;
}

.gridjs-pagination:after {
    clear: both;
    content: "";
    display: block;
}

.gridjs-pagination .gridjs-summary {
    float: left;
    margin-top: 5px;
}

.gridjs-pagination .gridjs-pages {
    float: right;
}

.gridjs-pagination .gridjs-pages button {
    background-color: #fff;
    border: 1px solid #d2d6dc;
    border-right: none;
    outline: none;
    padding: 5px 14px;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}

.gridjs-pagination .gridjs-pages button:focus {
    border-right: 1px solid #d2d6dc;
    box-shadow: 0 0 0 2px rgba(149, 189, 243, 0.5);
    margin-right: -1px;
    position: relative;
}

.gridjs-pagination .gridjs-pages button:hover {
    background-color: #f7f7f7;
    color: #3c4257;
    outline: none;
}

.gridjs-pagination .gridjs-pages button:disabled,
.gridjs-pagination .gridjs-pages button:hover:disabled,
.gridjs-pagination .gridjs-pages button[disabled] {
    background-color: #fff;
    color: #6b7280;
    cursor: default;
}

.gridjs-pagination .gridjs-pages button.gridjs-spread {
    background-color: #fff;
    box-shadow: none;
    cursor: default;
}

.gridjs-pagination .gridjs-pages button.gridjs-currentPage {
    background-color: #f7f7f7;
    font-weight: 700;
}

.gridjs-pagination .gridjs-pages button:last-child {
    border-bottom-right-radius: 6px;
    border-right: 1px solid #d2d6dc;
    border-top-right-radius: 6px;
}

.gridjs-pagination .gridjs-pages button:first-child {
    border-bottom-left-radius: 6px;
    border-top-left-radius: 6px;
}

.gridjs-pagination .gridjs-pages button:last-child:focus {
    margin-right: 0;
}

button.gridjs-sort {
    background-color: transparent;
    background-position-x: center;
    background-repeat: no-repeat;
    background-size: contain;
    border: none;
    cursor: pointer;
    float: right;
    height: 24px;
    margin: 0;
    outline: none;
    padding: 0;
    width: 13px;
}

button.gridjs-sort-neutral {
    background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI0MDEuOTk4IiBoZWlnaHQ9IjQwMS45OTgiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDQwMS45OTggNDAxLjk5OCIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+PHBhdGggZD0iTTczLjA5MiAxNjQuNDUyaDI1NS44MTNjNC45NDkgMCA5LjIzMy0xLjgwNyAxMi44NDgtNS40MjQgMy42MTMtMy42MTYgNS40MjctNy44OTggNS40MjctMTIuODQ3cy0xLjgxMy05LjIyOS01LjQyNy0xMi44NUwyMTMuODQ2IDUuNDI0QzIxMC4yMzIgMS44MTIgMjA1Ljk1MSAwIDIwMC45OTkgMHMtOS4yMzMgMS44MTItMTIuODUgNS40MjRMNjAuMjQyIDEzMy4zMzFjLTMuNjE3IDMuNjE3LTUuNDI0IDcuOTAxLTUuNDI0IDEyLjg1IDAgNC45NDggMS44MDcgOS4yMzEgNS40MjQgMTIuODQ3IDMuNjIxIDMuNjE3IDcuOTAyIDUuNDI0IDEyLjg1IDUuNDI0ek0zMjguOTA1IDIzNy41NDlINzMuMDkyYy00Ljk1MiAwLTkuMjMzIDEuODA4LTEyLjg1IDUuNDIxLTMuNjE3IDMuNjE3LTUuNDI0IDcuODk4LTUuNDI0IDEyLjg0N3MxLjgwNyA5LjIzMyA1LjQyNCAxMi44NDhMMTg4LjE0OSAzOTYuNTdjMy42MjEgMy42MTcgNy45MDIgNS40MjggMTIuODUgNS40MjhzOS4yMzMtMS44MTEgMTIuODQ3LTUuNDI4bDEyNy45MDctMTI3LjkwNmMzLjYxMy0zLjYxNCA1LjQyNy03Ljg5OCA1LjQyNy0xMi44NDggMC00Ljk0OC0xLjgxMy05LjIyOS01LjQyNy0xMi44NDctMy42MTQtMy42MTYtNy44OTktNS40Mi0xMi44NDgtNS40MnoiLz48L3N2Zz4=");
    background-position-y: center;
    opacity: 0.3;
}

button.gridjs-sort-asc {
    background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyOTIuMzYyIiBoZWlnaHQ9IjI5Mi4zNjEiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDI5Mi4zNjIgMjkyLjM2MSIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+PHBhdGggZD0iTTI4Ni45MzUgMTk3LjI4NyAxNTkuMDI4IDY5LjM4MWMtMy42MTMtMy42MTctNy44OTUtNS40MjQtMTIuODQ3LTUuNDI0cy05LjIzMyAxLjgwNy0xMi44NSA1LjQyNEw1LjQyNCAxOTcuMjg3QzEuODA3IDIwMC45MDQgMCAyMDUuMTg2IDAgMjEwLjEzNHMxLjgwNyA5LjIzMyA1LjQyNCAxMi44NDdjMy42MjEgMy42MTcgNy45MDIgNS40MjUgMTIuODUgNS40MjVoMjU1LjgxM2M0Ljk0OSAwIDkuMjMzLTEuODA4IDEyLjg0OC01LjQyNSAzLjYxMy0zLjYxMyA1LjQyNy03Ljg5OCA1LjQyNy0xMi44NDdzLTEuODE0LTkuMjMtNS40MjctMTIuODQ3eiIvPjwvc3ZnPg==");
    background-position-y: 35%;
    background-size: 10px;
}

button.gridjs-sort-desc {
    background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyOTIuMzYyIiBoZWlnaHQ9IjI5Mi4zNjIiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDI5Mi4zNjIgMjkyLjM2MiIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+PHBhdGggZD0iTTI4Ni45MzUgNjkuMzc3Yy0zLjYxNC0zLjYxNy03Ljg5OC01LjQyNC0xMi44NDgtNS40MjRIMTguMjc0Yy00Ljk1MiAwLTkuMjMzIDEuODA3LTEyLjg1IDUuNDI0QzEuODA3IDcyLjk5OCAwIDc3LjI3OSAwIDgyLjIyOGMwIDQuOTQ4IDEuODA3IDkuMjI5IDUuNDI0IDEyLjg0N2wxMjcuOTA3IDEyNy45MDdjMy42MjEgMy42MTcgNy45MDIgNS40MjggMTIuODUgNS40MjhzOS4yMzMtMS44MTEgMTIuODQ3LTUuNDI4TDI4Ni45MzUgOTUuMDc0YzMuNjEzLTMuNjE3IDUuNDI3LTcuODk4IDUuNDI3LTEyLjg0NyAwLTQuOTQ4LTEuODE0LTkuMjI5LTUuNDI3LTEyLjg1eiIvPjwvc3ZnPg==");
    background-position-y: 65%;
    background-size: 10px;
}

button.gridjs-sort:focus {
    outline: none;
}

table.gridjs-table {
    border-collapse: collapse;
    display: table;
    margin: 0;
    max-width: 100%;
    overflow: auto;
    padding: 0;
    table-layout: fixed;
    text-align: left;
    width: 100%;
}

.gridjs-tbody,
td.gridjs-td {
    background-color: #fff;
}

td.gridjs-td {
    border: 1px solid #e5e7eb;
    box-sizing: content-box;
    padding: 12px 24px;
}

td.gridjs-td:first-child {
    border-left: none;
}

td.gridjs-td:last-child {
    border-right: none;
}

td.gridjs-message {
    text-align: center;
}

th.gridjs-th {
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-top: none;
    box-sizing: border-box;
    color: #6b7280;
    outline: none;
    padding: 14px 24px;
    position: relative;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    vertical-align: middle;
    white-space: nowrap;
}

th.gridjs-th .gridjs-th-content {
    float: left;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
}

th.gridjs-th-sort {
    cursor: pointer;
}

th.gridjs-th-sort .gridjs-th-content {
    width: calc(100% - 15px);
}

th.gridjs-th-sort:focus,
th.gridjs-th-sort:hover {
    background-color: #e5e7eb;
}

th.gridjs-th-fixed {
    box-shadow: 0 1px 0 0 #e5e7eb;
    position: sticky;
}

@supports (-moz-appearance: none) {
    th.gridjs-th-fixed {
        box-shadow: 0 0 0 1px #e5e7eb;
    }
}

th.gridjs-th:first-child {
    border-left: none;
}

th.gridjs-th:last-child {
    border-right: none;
}

.gridjs-tr {
    border: none;
}

.gridjs-tr-selected td {
    background-color: #ebf5ff;
}

.gridjs-tr:last-child td {
    border-bottom: 0;
}

.gridjs *,
.gridjs :after,
.gridjs :before {
    box-sizing: border-box;
}

.gridjs-wrapper {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    border-color: #e5e7eb;
    border-radius: 8px 8px 0 0;
    border-top-width: 1px;
    box-shadow:
        0 1px 3px 0 rgba(0, 0, 0, 0.1),
        0 1px 2px 0 rgba(0, 0, 0, 0.26);
    display: block;
    overflow: auto;
    position: relative;
    width: 100%;
    z-index: 1;
}

.gridjs-wrapper:nth-last-of-type(2) {
    border-bottom-width: 1px;
    border-radius: 8px;
}

.gridjs-search {
    float: left;
}

.gridjs-search-input {
    width: 250px;
}

.gridjs-loading-bar {
    background-color: #fff;
    opacity: 0.5;
    z-index: 10;
}

.gridjs-loading-bar,
.gridjs-loading-bar:after {
    bottom: 0;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
}

.gridjs-loading-bar:after {
    animation: shimmer 2s infinite;
    background-image: linear-gradient(
        90deg,
        hsla(0, 0%, 80%, 0),
        hsla(0, 0%, 80%, 0.2) 20%,
        hsla(0, 0%, 80%, 0.5) 60%,
        hsla(0, 0%, 80%, 0)
    );
    content: "";
    transform: translateX(-100%);
}

@keyframes shimmer {
    to {
        transform: translateX(100%);
    }
}

.gridjs-td .gridjs-checkbox {
    cursor: pointer;
    display: block;
    margin: auto;
}

.gridjs-resizable {
    bottom: 0;
    position: absolute;
    right: 0;
    top: 0;
    width: 5px;
}

.gridjs-resizable:hover {
    background-color: #9bc2f7;
    cursor: ew-resize;
}
