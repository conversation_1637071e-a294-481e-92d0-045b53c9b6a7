{"userAdmonitions": {"flex": {"type": "flex", "color": "#7d7d7d", "icon": {"name": "ad", "type": "font-awesome"}, "command": false, "injectColor": true, "noTitle": true}, "col": {"type": "col", "color": "#7d7d7d", "icon": {"name": "ad", "type": "font-awesome"}, "command": false, "injectColor": true, "noTitle": true}, "col2": {"type": "col2", "color": "#7d7d7d", "icon": {"name": "address-book", "type": "font-awesome"}, "command": false, "injectColor": true, "noTitle": true}, "col3": {"type": "col3", "color": "#7d7d7d", "icon": {"name": "address-book", "type": "font-awesome"}, "command": false, "injectColor": true, "noTitle": true}, "col4": {"type": "col4", "color": "#7d7d7d", "icon": {"name": "address-book", "type": "font-awesome"}, "command": false, "injectColor": true, "noTitle": true}, "icon": {"type": "icon", "color": "#7d7d7d", "icon": {"name": "ad", "type": "font-awesome"}, "command": false, "injectColor": true, "noTitle": true}, "hibox": {"type": "hibox", "color": "#7d7d7d", "icon": {"name": "border-style", "type": "font-awesome"}, "command": false, "title": null, "injectColor": true, "noTitle": true, "copy": false}, "kanban": {"type": "kanban", "color": "#7d7d7d", "icon": {"name": "ad", "type": "font-awesome"}, "command": false, "injectColor": true, "noTitle": false}, "thm": {"type": "thm", "color": "#7d7d7d", "icon": {"name": "ad", "type": "font-awesome"}, "command": false, "title": "math", "injectColor": true, "noTitle": false}}, "syntaxHighlight": false, "copyButton": false, "version": "10.3.2", "autoCollapse": false, "defaultCollapseType": "open", "injectColor": true, "parseTitles": true, "dropShadow": true, "hideEmpty": false, "open": {"admonitions": true, "icons": true, "other": true, "advanced": false}, "icons": [], "useFontAwesome": true, "rpgDownloadedOnce": false, "msDocConverted": false, "useSnippet": true, "snippetPath": "custom-admonitions.a412a9"}