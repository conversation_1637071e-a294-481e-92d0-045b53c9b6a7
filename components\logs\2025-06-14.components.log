2025-06-14 08:44:28.313 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-06-14 08:44:28.315 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-06-14 08:44:28.332 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-06-14 08:44:28.334 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-06-14 08:44:30.103 [info] script extension loaded 0 functions from components/script  [object Map] 
2025-06-14 08:44:30.104 [info] components database created cost 2 ms   
2025-06-14 08:44:30.104 [info] components index initializing...   
2025-06-14 08:44:30.663 [info] start to batch put pages: 7   
2025-06-14 08:44:30.666 [info] batch persist cost 7  3 
2025-06-14 08:44:30.671 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-06-14 08:44:30.676 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-06-14 08:44:30.681 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-06-14 08:44:30.711 [info] components index initialized, 905 files cost 609 ms   
2025-06-14 08:44:30.711 [info] refresh page data from init listeners 0 905   
2025-06-14 08:44:30.718 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-06-14 08:44:31.300 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-06-14 08:44:31.303 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-06-14 08:44:31.306 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-06-14 08:44:31.310 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-06-14 08:44:31.469 [info] indexing created file components/logs/2025-06-14.components.log  [object Object] 
2025-06-14 08:44:31.481 [info] refresh page data from created listeners 0 906   
2025-06-14 08:44:31.607 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-06-14 08:44:31.616 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-06-14 08:44:31.628 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-06-14 08:44:31.631 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-06-14 08:44:47.174 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-06-14 08:44:47.181 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-06-14 11:11:54.774 [debug] ignore file modify evnet 工作库/项目/舌诊/机械臂.md   
2025-06-14 11:11:54.855 [info] trigger 工作库/项目/舌诊/机械臂.md resolve  [object Object] 
2025-06-14 11:11:54.859 [info] index finished after resolve  [object Object] 
2025-06-14 11:11:54.863 [info] refresh page data from resolve listeners 0 906   
2025-06-14 11:11:56.054 [info] indexing created file 学习库/ROS/机器人学/机器人运动学/未命名.md  [object Object] 
2025-06-14 11:11:56.054 [info] indexing created ignore file 学习库/ROS/机器人学/机器人运动学/未命名.md   
2025-06-14 11:11:56.058 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-06-14 11:11:56.059 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-06-14 11:11:56.059 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-06-14 11:11:56.059 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-06-14 11:11:56.060 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-06-14 11:11:56.218 [info] trigger 学习库/ROS/机器人学/机器人运动学/未命名.md resolve  [object Object] 
2025-06-14 11:11:56.221 [info] index finished after resolve  [object Object] 
2025-06-14 11:11:56.224 [info] refresh page data from resolve listeners 0 907   
