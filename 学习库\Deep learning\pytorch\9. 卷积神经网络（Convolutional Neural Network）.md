---
tags:
  - 学习
  - deep_learning
  - pytorch
---

# 卷积神经网络

[[8. 多分类问题 (Softmax Classifier)#神经网络架构|全连接神经网络]] 中每一个神经元都与上一层的所有神经元相连，这种结构在处理高维数据时会导致大量的参数和计算量。为了解决这个问题，卷积神经网络应运而生。

与全连接直接拼接成一长串不同，卷积神经网络可以保留图片的空间信息，如下图所示，其包含了以下步骤：
输入 → 卷积 → 下采样 → 卷积 → 下采样 → 全连接层 → 输出层

- **特征提取**：所有的卷积和下采样的过程
- **分类**：全连接层和输出层的过程

![[9. 卷积神经网络-2025-07-07-09-21-37.png]]

- 输入层 (Input Layer)
  - 输入数据: 1×28×28的灰度图像
- 卷积层 (Convolutional Layer)
  - 卷积核数量: 4个
  - 卷积核大小: 5×5
  - 步长: 1
  - 填充: 0
  - 输出: 4×24×24的特征图
- 下采样 (Subsampling)
  - 窗口尺寸: 2×2
  - 步长: 2
  - 输出: 4×12×12的特征图
- 第二个卷积层 (Convolutional Layer)
  - 卷积核数量: 8个
  - 卷积核大小: 5×5
  - 步长: 1
  - 填充: 0
  - 输出: 8×8×8的特征图
- 下采样 (Subsampling)
  - 窗口尺寸: 2×2
  - 步长: 2
  - 输出: 8×4×4的特征图
- 全连接层 (Fully Connected Layer)
  - 输入: 将8×4×4的特征图展平为128维向量
  - 输出: n1个神经元（如120个）
- 输出层 (Output Layer)
  - 输入: n1个神经元
  - 输出: n2个神经元（如10个，表示数字0-9的分类）

```python
import torch
import torch.nn as nn
from torch.nn import functional as F


class Net(nn.Module):
    def __init__(self):
        super(Net, self).__init__()
        self.conv1 = nn.Conv2d(in_channels=1, out_channels=10, kernel_size=5)
        self.conv2 = nn.Conv2d(in_channels=10, out_channels=20, kernel_size=5)
        self.pooling = nn.MaxPool2d(2)
        self.fc = nn.Linear(20 * 4 * 4, 10)
        self.relu = F.relu
    
    
    def forward(self, x):
        x = self.conv1(x)
        x = self.pooling(x)
        x = self.relu(x)  
        x = self.conv2(x)
        x = self.pooling(x)
        x = self.relu(x)
        x = x.view(x.size(0), -1)
        x = self.fc(x)
        return x
model = Net()
device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")  # 如果有独显，则选择使用独显，否则使用cpu来进行计算
model.to(device)
batch = 64
input = torch.randn(batch, 1, 28, 28).to(device)
output = model(input)
print(output.shape)  # 输出形状应为 (64, 10) 

```

## 卷积 (Convolution)

```ad-col2
title: 栅格图像
color:178,22,164
对于一个图像，都可以分为一个一个的格子 (栅格图像)，每个格子都有其对应的颜色值(R, G, B)，这些颜色值可以看作是一个三维数组。
![[9. 卷积神经网络-2025-07-07-09-41-38.png|128]]

```


如下图所示，卷积操作就是就是在一张图像上的每一个格子(patch)上应用一个卷积核 (kernel)，每个卷积核都是一个小的矩阵。卷积核在图像上滑动，并与图像的每个patch进行点积运算，生成新的特征图 (feature map)。这个过程可以提取出图像中的局部特征。
![[9. 卷积神经网络-2025-07-07-09-46-01.png]]

### 卷积操作

![[卷积和转置卷积#二维单通道单卷积核]]
![[卷积和转置卷积#二维多通道单卷积核]]
![[卷积和转置卷积#二维单通道多卷积核]]

### 卷积层 (Convolutional Layer)

如下图所示，从 `n*w*h` 的输入特征，经过卷积操作后，输出 `m*w*h` 的特征图，需要一个四维的卷积层，其中 ，`m` 是输出通道数，`n` 是输入通道数，`w` 和 `h` 是特征图的宽和高。

![[9. 卷积神经网络-2025-07-07-10-09-21.png]]

```python
import torch

# 模拟输入特征图，形状为 (batch_size, in_channels, height, width)
input_tensor = torch.randn(1, 1, 28, 28) # 在pytorch中都是小批量的，所以要加上batch_size维度

# 定义卷积层，输入通道数为1，输出通道数为4，卷积核大小为5x5
conv_layer = torch.nn.Conv2d(in_channels=1, out_channels=4, kernel_size=5)

# 前向传播
output_tensor = conv_layer(input_tensor)

print(input_tensor.shape) # (1, 1, 28, 28)
print(output_tensor.shape) # (1, 4, 24, 24)，图像的长宽的计算过程是 (28-5+1, 28-5+1) = (24, 24)
print(conv_layer.weight.shape) # (4, 1, 5, 5)
```

### padding

![[卷积和转置卷积#padding]]

### stride

![[卷积和转置卷积#步长（stride）与特征图（feature map）的关系]]

## 下采样

![[上采样；下采样#下采样]]

## 不同类型的CNN

### GoogleNet

GoogleNet 是一种深度卷积神经网络架构，具有以下特点：

- **Inception 模块**：通过不同大小的卷积核并行处理输入
- **全局平均池化**：在最后一层使用全局平均池化代替全连接层
- **深度可分离卷积**：将标准卷积分解为深度卷积和逐点卷积
- **辅助分类器**：在中间层添加辅助分类器以提高梯度流

![[9. 卷积神经网络（Convolutional Neural Network）-2025-07-07-15-16-36.png]]

#### Inception 模块

```ad-col2
title: Inception
color:178,22,164

在卷积神经网络中的选择合适尺寸的卷积是很困难的，GoogleNet的出发点就是在一个Inception模块中同时使用不同尺寸的卷积核，如果哪个效果好，它的权重自然会高

![[9. 卷积神经网络（Convolutional Neural Network）-2025-07-07-15-21-40.png]]
```

- **Concatenate**: 将不同尺寸卷积核的输出在通道维度上拼接起来
- **Average Pooling**: 均值池化，通过设置池化窗口的大小和步长来控制输出特征图的尺寸
- **1x1 卷积**: 
  - 信息融合：对不同通道的同一位置的像素进行信息融合，可以理解为对不同通道信息的加权求和，通俗的例子就像考试中如何公平的计算总成绩，就是对不同的科目赋予不同的权重，然后进行相加得到总成绩
  - 改变通道数量：1x1 卷积的主要作用是改变张量的通道数量
  - 降低计算量：在 `Inception` 模块中，通常会在 `3x3` 或 `5x5` 卷积之前使用 `1x1` 卷积来减少通道数，从而显著降低后续大尺寸卷积的计算开销。例如，通过引入一个 `1x1` 卷积将 192 个输入通道先降到 16 个，再进行 5x5 卷积，可以将计算量从 120 M 降到 12 M，大大节省训练时间![[9. 卷积神经网络（Convolutional Neural Network）-2025-07-07-16-48-37.png]]

```python
import pytorch
import pytorch.nn as nn
class InceptionA(nn.Module):
  def __init__(self, in_channels):
    super(InceptionA, self).__init__()
    self.branch1x1 = nn.Conv2d(in_channels, 16, kernel_size=1)  # 1x1 分支

    self.branch5x5_1 = nn.Conv2d(in_channels, 16, kernel_size=1)  # 5x5 分支的第一个 1x1 卷积
    self.branch5x5_2 = nn.Conv2d(16, 24, kernel_size=5, padding=2)  # 5x5 分支的第二个 5x5 卷积

    self.branch3x3_1 = nn.Conv2d(in_channels, 16, kernel_size=1)  # 3x3 分支的第一个 1x1 卷积
    self.branch3x3_2 = nn.Conv2d(16, 24, kernel_size=3, padding=1)  # 3x3 分支的第二个 3x3 卷积
    self.branch3x3_3 = nn.Conv2d(24, 24, kernel_size=3, padding=1)  # 3x3 分支的第三个 3x3 卷积

    self.branch_pool = nn.AvgPool2d(kernel_size=3, stride=1, padding=1)  # 池化分支

  def forward(self, x):
    branch1x1 = self.branch1x1(x)

    branch5x5 = self.branch5x5_1(x)
    branch5x5 = self.branch5x5_2(branch5x5)

    branch3x3 = self.branch3x3_1(x)
    branch3x3 = self.branch3x3_2(branch3x3)
    branch3x3 = self.branch3x3_3(branch3x3)

    branch_pool = self.branch_pool(x)

    outputs = [branch1x1, branch5x5, branch3x3, branch_pool]
    return torch.cat(outputs, 1)  # 沿着通道维度拼接
```

### ResNet

```ad-col2
title: SkipConnection
color:178,22,164
ResNet 通过引入残差连接（Skip Connection）来解决深度神经网络训练中的梯度消失问题。残差连接允许梯度直接通过跳过某些层进行传播，从而使得网络更容易训练。
![[9. 卷积神经网络（Convolutional Neural Network）-2025-07-07-17-18-19.png]]
```
