---
tags:
  - 毕设
  - 项目
  - 舌诊
---
# 项目地址

```ad-note
title: TensorRT-YOLO
https://github.com/laugh12321/TensorRT-YOLO
```

# 使用训练好的YOLOv8 加速推理

安装 TensorRT-YOLO 的包
```python
pip install -U tensorrt_yolo
```

导出使用 Ultralytics 训练好的模型使用 trtyolo 导出为带 EfficientNMS 的 onnx 模型
```python
# -w best.pt 为同目录下的model文件夹中使用 ultralytics 训练好的名为best.pt的权重文件
# -o output 将转换好的模型输出到同目录下的 output文件夹中的best.onnx
# -v ultralytics 指定训练的文件是使用ultralytics训练的
# -b 指定batch_size = 1
# 剩余的参数可以通过 trtyolo export --help 查看
trtyolo export -w model/best.pt -v ultralytics -o output --max_boxes 100 --iou_thres 0.45 --conf_thres 0.25 -b -1
```

>==注：使用 ultralytics 训练的模型必须通过 trtyolo 进行导出，使用 ultralytics 或者 pytorch 的导出都不行

将导出的 onnx 模型通过 TensorRT 转换为 TensorRT 引擎
```python
#  --onnx=output/best.onnx 指定onnx模型的路径为 output/best.onnx
#  --saveEngine=models/best.engine 保存模型的路径
#  --fp16 
#  --shapes=images:1x3x640x640 指定输入形状
trtexec --onnx=output/best.onnx --saveEngine=models/best.engine --fp16
trtexec --onnx=/output/best.onnx --saveEngine=engine/best.engine --fp16 --shapes=images:1x3x640x640
```

使用 python 进行推理加速
```python
import cv2
from tensorrt_yolo.infer import InferOption, DetectModel, generate_labels, visualize

def main():
    # -------------------- 初始化配置 --------------------
    # 配置推理设置
    option = InferOption()
    option.enable_swap_rb()  # 将OpenCV默认的BGR格式转为RGB格式
    # 特殊模型配置示例（如PP-YOLOE系列需取消下方注释）
    # option.set_normalize_params([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])

    # -------------------- 模型初始化 --------------------
    # 加载TensorRT引擎文件（注意检查文件路径）
    # 提示：首次加载引擎可能需要较长时间进行优化
    model = DetectModel(engine_file="engine/best.engine",
                      option=option)

    # -------------------- 数据预处理 --------------------
    # 加载测试图片（建议添加文件存在性检查）
    input_img = cv2.imread("test_image.jpg")
    if input_img is None:
        raise FileNotFoundError("测试图片加载失败，请检查文件路径")

    # -------------------- 执行推理 --------------------
    # 执行目标检测（返回结果包含边界框、置信度、类别信息）
    detection_result = model.predict(input_img)
    print(f"==> detection_result: {detection_result}")

    # -------------------- 结果可视化 --------------------
    # 加载类别标签（需确保labels.txt与模型匹配）
    class_labels = generate_labels(labels_file="labels.txt")
    # 生成可视化结果
    visualized_img = visualize(
        image=input_img,
        result=detection_result,
        labels=class_labels,
    )
    cv2.imwrite("vis_image.jpg", visualized_img)

    # -------------------- 模型克隆演示 --------------------
    # 克隆模型实例（适用于多线程场景）
    cloned_model = model.clone()  # 创建独立副本，避免资源竞争
    # 验证克隆模型推理一致性
    cloned_result = cloned_model.predict(input_img)
    print(f"==> cloned_result: {cloned_result}")

if __name__ == "__main__":
    main()
```


# 推理加速
好的，结合你给出的源码（warpaffine. cu 和 warpaffine. hpp），我为你详细梳理一下在 @laugh 12321/TensorRT-YOLO 项目中，仿射变换（Affine Transform）对一张图片的具体处理流程：

---

## 1. 仿射变换的目的

在深度学习目标检测（比如 YOLO）中，输入图片往往需要经过缩放、中心裁剪、通道转换、归一化等预处理，使其适配网络输入，且保证目标比例不失真。这些都可以通过仿射变换一次性完成，并且 warpaffine. cu 实现了高效的 CUDA 并行加速。

---

## 2. 核心流程说明

### Step 1: 构造仿射变换矩阵

- 通过 AffineTransform:: updateMatrix ()，根据原图片和目标图片的尺寸，动态计算用于缩放、居中、裁剪的 2×3 仿射变换矩阵（float 3 matrix[2]）。
- 这个矩阵决定了输入图像的每个像素如何映射到输出图像的坐标系上。
- 支持保持原图比例填充（避免拉伸变形）。

### Step 2: CUDA 核心并行处理

- warp_affine_bilinear () 是仿射变换处理的 CUDA 设备函数。
- 对于输出图像的每个像素 (element_x, element_y)，通过仿射矩阵计算其在原图中的浮点坐标 (src_x, src_y)。
- 采用双线性插值，从原图四邻域像素采样，获得目标像素值，支持边界填充。
- 支持 BGR/RGB 通道互换，以及归一化（乘 alpha，加 beta）。

### Step 3: 启动 CUDA kernel 执行

- 通过 cudaWarpAffine () 启动 gpuBilinearWarpAffine CUDA kernel，遍历目标图像所有像素，批量完成上述变换和赋值操作，全部在 GPU 上高效进行。

---

## 3. 对一张图片的具体操作流程

以一张输入图片为例，仿射变换的完整处理包括：

1. **输入原图数据（src）与目标尺寸（dst_cols, dst_rows）**
2. **计算仿射矩阵**（updateMatrix）：  
   - 包括缩放比例、偏移量（保持内容居中或对齐）。
3. **为每个目标像素 (x, y)：**
   - 用仿射矩阵反推回原图的浮点坐标 (src_x, src_y)
   - 判断 (src_x, src_y) 是否在原图范围内
   - 对于超出边界的点填充默认值（如 0 或均值）
   - 四点双线性插值采样，计算该点的实际像素值
   - 如果需要，做通道顺序转换（RGB/BGR）、归一化（*alpha + beta）
1. **输出到目标内存 dst**，格式通常是 float 32，按通道顺序排列，适配后续神经网络输入。

---

## 4. 伪代码示例

```cpp
// 1. 更新仿射变换矩阵
AffineTransform affine;
affine.updateMatrix(src_width, src_height, dst_width, dst_height);

// 2. CUDA 调用
cudaWarpAffine(src, src_width, src_height, src_pitch, 
               dst, dst_width, dst_height, 
               affine.matrix, config, stream);
```

---

## 5. 总结

在 @laugh 12321/TensorRT-YOLO 这个项目里，
- 仿射变换负责将任意尺寸和比例的原始图片，快速、批量地裁剪、缩放、标准化到 YOLO 网络需要的输入格式（如 640 x 640, RGB float 32），且在 GPU 上极快完成；
- 同时支持批量（多图）处理，适用于推理前的高吞吐数据预处理。

如果你还想了解 CUDA 内具体某一步的细节、插值方法，或参数配置的具体作用，可以再问我！