---

excalidraw-plugin: parsed
tags: [excalidraw]

---
==⚠  Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠== You can decompress Drawing data with the command palette: 'Decompress current Excalidraw file'. For more info check in plugin settings under 'Saving'


# Excalidraw Data

## Text Elements
%%
## Drawing
```compressed-json
N4KAkARALgngDgUwgLgAQQQDwMYEMA2AlgCYBOuA7hADTgQBuCpAzoQPYB2KqATLZMzYBXUtiRoIACyhQ4zZAHoFAc0JRJQgEYA6bGwC2CgF7N6hbEcK4OCtptbErHALRY8RMpWdx8Q1TdIEfARcZgRmBShcZQUebQBObQBGGjoghH0EDihmbgBtcDBQMBKIEm4IAA4KAGFCADYAawBJABkATVwAMRg2UPjmAA04XABBVJLIWEQKgDNAhE8qflLM

bh4AFiTtDYAGfd34pIBWY54AdgBmDY2VyBhuJPr67XPz4/2j04vr28LICgkdSPOLneLgypveI8eL1SHHO5SBCEZTSbiXbbgiEbc5bHhJQ6XSqI6zKYLcXaI5hQUhsRoIGpsfBsUgVADESQQnM5E1Kmlw2EaylpQg4xEZzNZEhp1mYI0C2V5kFmhHw+AAyrByRJBB4lRBqbT6QB1IGSdZUml0hCamDa9C68qIkWojjhXJoJKItgjbBqB6e/aI4XCO

DNYge1B5AC6iPmuEy4e4HCEasRhDFWAquF2+pFYrdzEjKbT/wNCEW3Eqx0quw2Fx4CLLjBY7C4aEuiJbrE4ADlOGJ1kkiZUbhd08wACLpKCVtCzAhhRGaYRigCiwUy2WLqfwiKEcGIuFnxEeuPqGNhGyJu2JZaIHEayd3iOZgrnqAX+CXZbgbAzOT5P8YAFJMJSUsBYC7MBsbAaBYFJKCWKQmCMJwu8dwlBiCTITieIEvERIwf8sGTBAcCBEWIjh

PkpGlKw+ipseCAAAqUcw1HcDSQgIK+oRQIy+j6GoJ4sf+ipoPBJSIa8yFQmh8KYdhWLxKOuKIQRRGTNGhQAL4rMUpTlBImgABJdEkFDHPE+C7AAqvUADiLGXLsmDHAAaqMjkAPr6tM4joPMFZLPqaxoI2GzJCp9TxBslSbDwfBlgGqBJKpry4uCRJXqORKIoCxDAmguzaAcXplpIyKolAFJlfsFVkaS9oQWRhrWhKLLstyXJIMuApCvm4pMl10rk

Bwcq4AqtVxqqGpaoFBpMk6ZbtSaZoWqtVr0ra9pLXqzrCK67qPN6vr+o8QZliGB7hpGMZxuQianmgJZ7mWGbEFmEi4Ckh2isQhY7qWbUhY8qn1nW9RvF2TA9u2qCds2cNtv2HCDhFdbxccSSVPEE7TsEJ7cF+P5kSuAMbhkWSAa9L5lgeR7E5656XvUGzHO8fxkQ+T50yDpRvvSL2fouvG/uJtNRnBkGtWB0FgXRJRSeB9W7I18tq0kxHaYiFHut

R91KwahCMfgzFsQbgRcaQPF8dSgnCTIixiQBQGa+VmGleVJF6eApEQLgcBwJqzHcIZ0BVZkFREDVvIMIQCAUAAQgNN1ip1UroGysy53n8fYCI03NLO+iakaDIjVnEAcr1PIrBAhekMXpep4K6fDZKFQyhN8o0wXRc0yXGRdHNu2LY6p4N03LcZOX1qmkV5oRdPg/ZMPZfbTaC0VJPA/N0PpcAEpHZIQOnYUjdr1AG8APLnbAl1y1fB/r6XXScFAX

QJqqqVNqUM9D4j0/uqQgRhAo8GfoAt+GQAAqWAoCjFjgjCAwRZgzUvtAm+pdQ6kEQc3NgFAqq4BFm9Ver9sEZDXGKUYBCiEhBFoHOh+9Z76FobSCgsD4CLSGvHZg2BaRqkGBSBu/DBH4HaCIy+Rg2AGHDs2AgPFHh6XIawk+ANz4SF4Q3YUJBQHgPWM/XRxBNQIDgNwf+kBjEAFk2BfWobgTQwQRak3FqUYxmd5FkWTkyRhpBlD8gABT4nONQXgS

RQnhMiaVY4ABKfUR8EDKCYtXfxQSeCuTCRkykvBMmoBifElRmDr7z3pPfKAbZgbvVKPGTIiTMykAzMoLxpQsiOOcTbO2ZZsBEHMWgbibjSgcATIFAZ3ohBQAfKM22CAimlDsAAKwQNgHI6phlwFsfY4ZTiPyuIbgKCpjBYGyPwC0qY3C5gLFCoiQu1IDBcJmPzapgs2DvhcWLe2iCVmECOSc58ao/ZgH0nQFUwRIzAF0iAXSQA==
```
%%