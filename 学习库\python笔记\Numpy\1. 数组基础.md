---
tags:
  - 学习
  - python
  - Numpy
---
# 数据类型

## 整数型数组与浮点型数组

为克服列表的缺点，一个 Numpy 数组只能容纳一种数据类型，以节约内存，可分为整数型数组与浮点型数组
创建整数型数组与浮点型数组

```python
import numpy as np

# 创建整数型数组
arr1 = np.array([1, 2, 3])  # 元素若都为整数，则为整数型数组
print(arr1)  # 输出结果为[1 2 3]，中间是没有逗号的

# 创建浮点型数组
arr2 = np.array([1.0, 2, 3])  # 内含浮点数，则为浮点型数组
print(arr1)  # 输出结果为[1. 2. 3.]，中间是没有逗号的
```

>使用print输出Numpy数组后，元素之间没有逗号，一是好处是可以与python列表区分，二是可以避免逗号和小数点之间的混淆

## 同化定理

- 往整数型数组里插入浮点数，该浮点数会自动转换为整数
- 往浮点型数组里插入整数，该整数会自动转换为浮点数

```python
import numpy as np
# 整数型数组
arr1 = np.array([1, 2, 3])
arr1[0] = 1.5  # 将整数型数组的第一个元素改为浮点数，会被自动转换为整数
print(arr1)  # 输出结果为[1 2 3]，第一个元素仍为整数

# 浮点型数组
arr2 = np.array([1.0, 2, 3])
arr2[0] = 1  # 将浮点型数组的第一个元素改为整数，会被自动转换为浮点数
print(arr2)  # 输出结果为[1. 2. 3.]，第一个元素仍为浮点数
```

## 共同改变定理

Numpy的整数型数组和浮点数型数组之间的界限十分严格，必须要将内部的数据类型全部转换为相同的类型才能进行运算
转换方式是使用`.astype()`方法
> 注：方法是类里面的函数，在使用方法时需要加上类的实例化对象；函数是类外面的函数，在使用函数时不需要加上类的实例化对象 [[类，方法，和对象#方法与函数的区别|方法与函数的区别]]

```python
import numpy as np
# 整数型数组
arr1 = np.array([1, 2, 3])
print(arr1.dtype)  # 输出结果为int64，表示该数组为整数型数组

# 整数型数组转换为浮点型数组
arr1 = arr1.astype(np.float64)  # 将整数型数组转换为浮点型数组
print(arr1.dtype)  # 输出结果为float64，表示该数组为浮点型数组

# 浮点型数组转换为整数型数组
arr2 = arr1.astype(np.int64)  # 将浮点型数组转换为整数型数组
print(arr2.dtype)  # 输出结果为int64，表示该数组为整数型数组
```

在运算的过程中，Numpy会自动将整数型数组转换为浮点型数组

```python
import numpy as np
# 整数型数组
arr1 = np.array([1, 2, 3])

# 整数型数组与浮点数做运算
arr2 = arr1 + 1.5  # 将整数型数组与浮点数相加
print(arr2)  # 输出结果为[2.5 3.5 4.5]，表示该数组为浮点型数组

# 整数型数组遇到除法（即使是除整数）
arr3 = arr1 / 2  # 将整数型数组与整数相除
print(arr3)  # 输出结果为[0.5 1.  1.5]，表示该数组为浮点型数组

# 整数型数组与浮点型数组做运算
arr4 = arr1 + arr2  # 将整数型数组与浮点型数组相加
print(arr4)  # 输出结果为[3.5 5.  6.5]，表示该数组为浮点型数组
```

# 数组的维度

Numpy的数组是多维数组，Numpy的数组可以是1维、2维、3维、4维、5维等，甚至可以是n维
在实际应用中，需要留意数组的维度，因为不同维度的数组之间有些函数是不能直接进行运算的

## 一维数组与二维数组

- 不同维度的数组之间，从外形上的本质区别是：
  - 一维数组：使用一层中括号表示
  - 二维数组：使用两层中括号表示
  - 三维数组：使用三层中括号表示
- 有些函数需要传入数组的形状参数，不同维度数组的形状参数为
  - 一维数组：`(n,)`，n为元素个数
  - 二维数组：`(m, n)`，m为行数，n为列数
  - 三维数组：`(l, m, n)`，l为层数，m为行数，n为列数
- 举例
  - 一维数组：`np.array([1, 2, 3])`，形状为`(3,)`
  - 二维数组：`np.array([[1, 2], [3, 4]])`，形状为`(2, 2)`
  - 三维数组：`np.array([[[1, 2], [3, 4]], [[5, 6], [7, 8]]])`，形状为`(2, 2, 2)`

````python
import numpy as np
arr1 = np.ones(3)  # 传入形状3
print(arr1)  # 输出结果为[1. 1. 1.]，表示该数组为一维数组
print(arr1.shape)  # 输出结果为(3,)，表示该数组的形状为(3,)

arr2 = np.ones((3, 4))  # 传入形状(3, 4)
print(arr2)  # 输出结果为[[1. 1. 1. 1.]
print(arr2.shape)  # 输出结果为(3, 4)，表示该数组的形状为(3, 4)

arr3 = np.ones((2, 3, 4))  # 传入形状(2, 3, 4)
print(arr3)  # 输出结果为[[[1. 1. 1. 1.]
print(arr3.shape)  # 输出结果为(2, 3, 4)，表示该数组的形状为(2, 3, 4)
````

## 不同维度数组之间的转换

数组之间的转换是通过`reshape()`方法实现的，该方法需要传入重塑后的形状（shape）参数

- 一维数组转换为二维数组：`reshape(1, n)`，n为元素个数
- 二维数组转换为一维数组：`reshape(n,)`，n为元素个数
- 二维数组转换为三维数组：`reshape(l, m, n)`，l为层数，m为行数，n为列数

`reshape`就是对原数组中的元素进行重新排列组织，但要注意以下几点：

1. 重新排列组织后的数组的元素个数必须与原数组的元素个数相同
2. 重排是按照原数组的顺序进行的

```python
import numpy as np
# 一维数组 
arr1 = np.range(12)  # 传入形状12
print(arr1)  # 输出结果为[0 1 2 3 4 5 6 7 8 9 10 11]，表示该数组为一维数组

# 一维数组转换为二维数组
arr2 = arr1.reshape(3, 4)  # 将一维数组转换为二维数组，传入形状(3, 4)
print(arr2)  # 输出结果为[[0 1 2 3]
#                        [4 5 6 7]
#                        [8 9 10 11]]，表示该数组为二维数组
```

后面将一维数组称为向量，二维数组称为矩阵，三维数组称为张量


