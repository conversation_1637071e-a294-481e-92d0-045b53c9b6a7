2025-04-29 07:28:45 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:28:45 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:28:45 [info] index finished after resolve  [object Object] 
2025-04-29 07:28:45 [info] refresh page data from resolve listeners 0 851   
2025-04-29 07:28:45 [info] indexing created file components/logs/2025-04-29.components.log  [object Object] 
2025-04-29 07:28:45 [info] refresh page data from created listeners 0 852   
2025-04-29 07:28:49 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:28:49 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:28:49 [info] index finished after resolve  [object Object] 
2025-04-29 07:28:49 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:34:46 [info] trigger 学习库/Deep learning/pytorch/PDF/Lecture_04_Back_Propagation.pdf resolve  [object Object] 
2025-04-29 07:34:46 [info] index finished after resolve  [object Object] 
2025-04-29 07:34:46 [info] refresh page data from modify listeners 0 852   
2025-04-29 07:36:07 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:36:07 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:36:07 [info] index finished after resolve  [object Object] 
2025-04-29 07:36:07 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:36:12 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:36:12 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:36:12 [info] index finished after resolve  [object Object] 
2025-04-29 07:36:12 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:36:17 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:36:17 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:36:17 [info] index finished after resolve  [object Object] 
2025-04-29 07:36:17 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:38:02 [info] trigger 学习库/Deep learning/pytorch/PDF/Lecture_04_Back_Propagation.pdf resolve  [object Object] 
2025-04-29 07:38:02 [info] index finished after resolve  [object Object] 
2025-04-29 07:38:02 [info] refresh page data from modify listeners 0 852   
2025-04-29 07:38:23 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:38:23 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:38:23 [info] index finished after resolve  [object Object] 
2025-04-29 07:38:23 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:38:29 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:38:29 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:38:29 [info] index finished after resolve  [object Object] 
2025-04-29 07:38:29 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:39:23 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:39:23 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:39:23 [info] index finished after resolve  [object Object] 
2025-04-29 07:39:23 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:39:25 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:39:25 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:39:25 [info] index finished after resolve  [object Object] 
2025-04-29 07:39:25 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:39:28 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:39:28 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:39:28 [info] index finished after resolve  [object Object] 
2025-04-29 07:39:28 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:39:33 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:39:33 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:39:33 [info] index finished after resolve  [object Object] 
2025-04-29 07:39:33 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:39:35 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:39:35 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:39:35 [info] index finished after resolve  [object Object] 
2025-04-29 07:39:35 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:39:43 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:39:43 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:39:43 [info] index finished after resolve  [object Object] 
2025-04-29 07:39:43 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:39:48 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:39:48 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:39:48 [info] index finished after resolve  [object Object] 
2025-04-29 07:39:48 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:39:50 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:39:50 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:39:50 [info] index finished after resolve  [object Object] 
2025-04-29 07:39:50 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:39:52 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:39:52 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:39:52 [info] index finished after resolve  [object Object] 
2025-04-29 07:39:52 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:39:55 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:39:55 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:39:55 [info] index finished after resolve  [object Object] 
2025-04-29 07:39:55 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:39:57 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:39:57 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:39:57 [info] index finished after resolve  [object Object] 
2025-04-29 07:39:57 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:40:00 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:40:00 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:40:00 [info] index finished after resolve  [object Object] 
2025-04-29 07:40:00 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:43:34 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:43:34 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:43:34 [info] index finished after resolve  [object Object] 
2025-04-29 07:43:34 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:43:39 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:43:39 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:43:39 [info] index finished after resolve  [object Object] 
2025-04-29 07:43:39 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:43:41 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:43:41 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:43:41 [info] index finished after resolve  [object Object] 
2025-04-29 07:43:41 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:43:43 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:43:43 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:43:43 [info] index finished after resolve  [object Object] 
2025-04-29 07:43:43 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:43:45 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:43:45 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:43:45 [info] index finished after resolve  [object Object] 
2025-04-29 07:43:45 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:43:48 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:43:48 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:43:48 [info] index finished after resolve  [object Object] 
2025-04-29 07:43:48 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:43:50 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:43:50 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:43:50 [info] index finished after resolve  [object Object] 
2025-04-29 07:43:50 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:43:54 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:43:54 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:43:54 [info] index finished after resolve  [object Object] 
2025-04-29 07:43:54 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:44:00 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:44:00 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:44:00 [info] index finished after resolve  [object Object] 
2025-04-29 07:44:00 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:44:31 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:44:31 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:44:31 [info] index finished after resolve  [object Object] 
2025-04-29 07:44:31 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:46:09 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:46:09 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:46:09 [info] index finished after resolve  [object Object] 
2025-04-29 07:46:09 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:46:11 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:46:11 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:46:11 [info] index finished after resolve  [object Object] 
2025-04-29 07:46:11 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:46:14 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:46:14 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:46:14 [info] index finished after resolve  [object Object] 
2025-04-29 07:46:14 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:46:16 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:46:16 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:46:16 [info] index finished after resolve  [object Object] 
2025-04-29 07:46:16 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:46:22 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:46:22 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:46:22 [info] index finished after resolve  [object Object] 
2025-04-29 07:46:22 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:46:25 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:46:25 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:46:25 [info] index finished after resolve  [object Object] 
2025-04-29 07:46:25 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:46:27 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:46:27 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:46:27 [info] index finished after resolve  [object Object] 
2025-04-29 07:46:27 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:46:29 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:46:29 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:46:29 [info] index finished after resolve  [object Object] 
2025-04-29 07:46:29 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:46:40 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:46:40 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:46:40 [info] index finished after resolve  [object Object] 
2025-04-29 07:46:40 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:46:45 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:46:45 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:46:45 [info] index finished after resolve  [object Object] 
2025-04-29 07:46:45 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:46:48 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:46:48 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:46:48 [info] index finished after resolve  [object Object] 
2025-04-29 07:46:48 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:46:58 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:46:58 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:46:58 [info] index finished after resolve  [object Object] 
2025-04-29 07:46:58 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:47:03 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:47:03 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:47:03 [info] index finished after resolve  [object Object] 
2025-04-29 07:47:03 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:47:05 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:47:05 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:47:05 [info] index finished after resolve  [object Object] 
2025-04-29 07:47:05 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:47:31 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:47:31 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:47:31 [info] index finished after resolve  [object Object] 
2025-04-29 07:47:31 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:47:33 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:47:33 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:47:33 [info] index finished after resolve  [object Object] 
2025-04-29 07:47:33 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:47:35 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:47:35 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:47:35 [info] index finished after resolve  [object Object] 
2025-04-29 07:47:35 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:48:07 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:48:07 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:48:07 [info] index finished after resolve  [object Object] 
2025-04-29 07:48:07 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:48:11 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:48:11 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:48:11 [info] index finished after resolve  [object Object] 
2025-04-29 07:48:11 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:48:13 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:48:13 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:48:13 [info] index finished after resolve  [object Object] 
2025-04-29 07:48:13 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:48:25 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:48:25 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:48:25 [info] index finished after resolve  [object Object] 
2025-04-29 07:48:25 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:48:40 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:48:40 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:48:40 [info] index finished after resolve  [object Object] 
2025-04-29 07:48:40 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:49:14 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:49:14 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:49:14 [info] index finished after resolve  [object Object] 
2025-04-29 07:49:14 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:49:19 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:49:19 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:49:19 [info] index finished after resolve  [object Object] 
2025-04-29 07:49:19 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:49:35 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:49:35 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:49:35 [info] index finished after resolve  [object Object] 
2025-04-29 07:49:35 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:54:01 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:54:01 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:54:01 [info] index finished after resolve  [object Object] 
2025-04-29 07:54:01 [info] refresh page data from resolve listeners 0 852   
2025-04-29 07:54:04 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 07:54:04 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 07:54:04 [info] index finished after resolve  [object Object] 
2025-04-29 07:54:04 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:00:27 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:00:27 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:00:27 [info] index finished after resolve  [object Object] 
2025-04-29 08:00:27 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:04:32 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:04:32 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:04:32 [info] index finished after resolve  [object Object] 
2025-04-29 08:04:32 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:06:59 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:06:59 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:06:59 [info] index finished after resolve  [object Object] 
2025-04-29 08:06:59 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:07:10 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:07:10 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:07:10 [info] index finished after resolve  [object Object] 
2025-04-29 08:07:10 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:07:40 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:07:40 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:07:40 [info] index finished after resolve  [object Object] 
2025-04-29 08:07:40 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:10:29 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:10:30 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:10:30 [info] index finished after resolve  [object Object] 
2025-04-29 08:10:30 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:11:32 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:11:32 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:11:32 [info] index finished after resolve  [object Object] 
2025-04-29 08:11:32 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:11:48 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:11:48 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:11:48 [info] index finished after resolve  [object Object] 
2025-04-29 08:11:48 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:11:50 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:11:50 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:11:50 [info] index finished after resolve  [object Object] 
2025-04-29 08:11:50 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:11:53 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:11:53 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:11:53 [info] index finished after resolve  [object Object] 
2025-04-29 08:11:53 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:12:16 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:12:16 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:12:16 [info] index finished after resolve  [object Object] 
2025-04-29 08:12:16 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:12:18 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:12:18 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:12:18 [info] index finished after resolve  [object Object] 
2025-04-29 08:12:18 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:12:21 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:12:21 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:12:21 [info] index finished after resolve  [object Object] 
2025-04-29 08:12:21 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:12:26 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:12:26 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:12:26 [info] index finished after resolve  [object Object] 
2025-04-29 08:12:26 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:12:28 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:12:28 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:12:28 [info] index finished after resolve  [object Object] 
2025-04-29 08:12:28 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:12:30 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:12:30 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:12:30 [info] index finished after resolve  [object Object] 
2025-04-29 08:12:30 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:12:34 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:12:34 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:12:34 [info] index finished after resolve  [object Object] 
2025-04-29 08:12:34 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:12:39 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:12:39 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:12:39 [info] index finished after resolve  [object Object] 
2025-04-29 08:12:39 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:12:42 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:12:42 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:12:42 [info] index finished after resolve  [object Object] 
2025-04-29 08:12:42 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:12:47 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:12:47 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:12:47 [info] index finished after resolve  [object Object] 
2025-04-29 08:12:47 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:12:49 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:12:49 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:12:49 [info] index finished after resolve  [object Object] 
2025-04-29 08:12:49 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:12:51 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:12:51 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:12:51 [info] index finished after resolve  [object Object] 
2025-04-29 08:12:51 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:12:53 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:12:53 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:12:53 [info] index finished after resolve  [object Object] 
2025-04-29 08:12:53 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:12:58 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:12:58 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:12:58 [info] index finished after resolve  [object Object] 
2025-04-29 08:12:58 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:13:01 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:13:01 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:13:01 [info] index finished after resolve  [object Object] 
2025-04-29 08:13:01 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:13:03 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:13:03 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:13:03 [info] index finished after resolve  [object Object] 
2025-04-29 08:13:03 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:13:07 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:13:07 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:13:07 [info] index finished after resolve  [object Object] 
2025-04-29 08:13:07 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:13:14 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:13:14 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:13:14 [info] index finished after resolve  [object Object] 
2025-04-29 08:13:14 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:13:18 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:13:18 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:13:18 [info] index finished after resolve  [object Object] 
2025-04-29 08:13:18 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:13:26 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:13:26 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:13:26 [info] index finished after resolve  [object Object] 
2025-04-29 08:13:26 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:13:29 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:13:29 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:13:29 [info] index finished after resolve  [object Object] 
2025-04-29 08:13:29 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:13:31 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:13:31 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:13:31 [info] index finished after resolve  [object Object] 
2025-04-29 08:13:31 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:13:34 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:13:34 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:13:34 [info] index finished after resolve  [object Object] 
2025-04-29 08:13:34 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:13:43 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:13:43 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:13:43 [info] index finished after resolve  [object Object] 
2025-04-29 08:13:43 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:13:46 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:13:46 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:13:46 [info] index finished after resolve  [object Object] 
2025-04-29 08:13:46 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:13:48 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:13:48 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:13:48 [info] index finished after resolve  [object Object] 
2025-04-29 08:13:48 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:13:57 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:13:57 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:13:57 [info] index finished after resolve  [object Object] 
2025-04-29 08:13:57 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:13:59 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:13:59 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:13:59 [info] index finished after resolve  [object Object] 
2025-04-29 08:13:59 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:14:02 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:14:02 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:14:02 [info] index finished after resolve  [object Object] 
2025-04-29 08:14:02 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:14:05 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:14:05 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:14:05 [info] index finished after resolve  [object Object] 
2025-04-29 08:14:05 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:14:17 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:14:17 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:14:17 [info] index finished after resolve  [object Object] 
2025-04-29 08:14:17 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:14:21 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:14:21 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:14:21 [info] index finished after resolve  [object Object] 
2025-04-29 08:14:21 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:14:24 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:14:24 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:14:24 [info] index finished after resolve  [object Object] 
2025-04-29 08:14:24 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:14:27 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:14:27 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:14:27 [info] index finished after resolve  [object Object] 
2025-04-29 08:14:27 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:14:33 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:14:33 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:14:33 [info] index finished after resolve  [object Object] 
2025-04-29 08:14:33 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:14:36 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:14:36 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:14:36 [info] index finished after resolve  [object Object] 
2025-04-29 08:14:36 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:14:43 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:14:43 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:14:43 [info] index finished after resolve  [object Object] 
2025-04-29 08:14:43 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:15:01 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:15:01 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:15:01 [info] index finished after resolve  [object Object] 
2025-04-29 08:15:01 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:15:06 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:15:06 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:15:06 [info] index finished after resolve  [object Object] 
2025-04-29 08:15:06 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:15:27 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:15:27 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:15:27 [info] index finished after resolve  [object Object] 
2025-04-29 08:15:27 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:15:31 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:15:31 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:15:31 [info] index finished after resolve  [object Object] 
2025-04-29 08:15:31 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:15:37 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:15:37 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:15:37 [info] index finished after resolve  [object Object] 
2025-04-29 08:15:37 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:15:41 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:15:41 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:15:41 [info] index finished after resolve  [object Object] 
2025-04-29 08:15:41 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:15:47 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:15:47 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:15:47 [info] index finished after resolve  [object Object] 
2025-04-29 08:15:47 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:15:50 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:15:50 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:15:50 [info] index finished after resolve  [object Object] 
2025-04-29 08:15:50 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:16:00 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:16:00 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:16:00 [info] index finished after resolve  [object Object] 
2025-04-29 08:16:00 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:16:05 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:16:05 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:16:05 [info] index finished after resolve  [object Object] 
2025-04-29 08:16:05 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:16:07 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:16:07 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:16:07 [info] index finished after resolve  [object Object] 
2025-04-29 08:16:07 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:16:25 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:16:25 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:16:25 [info] index finished after resolve  [object Object] 
2025-04-29 08:16:25 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:16:35 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:16:35 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:16:35 [info] index finished after resolve  [object Object] 
2025-04-29 08:16:35 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:16:45 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:16:45 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:16:45 [info] index finished after resolve  [object Object] 
2025-04-29 08:16:45 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:17:25 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:17:25 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:17:25 [info] index finished after resolve  [object Object] 
2025-04-29 08:17:25 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:17:27 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:17:27 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:17:27 [info] index finished after resolve  [object Object] 
2025-04-29 08:17:27 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:17:29 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:17:29 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:17:29 [info] index finished after resolve  [object Object] 
2025-04-29 08:17:29 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:17:37 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:17:37 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:17:37 [info] index finished after resolve  [object Object] 
2025-04-29 08:17:37 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:17:41 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:17:41 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:17:41 [info] index finished after resolve  [object Object] 
2025-04-29 08:17:41 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:17:43 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:17:43 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:17:43 [info] index finished after resolve  [object Object] 
2025-04-29 08:17:43 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:17:47 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:17:47 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:17:47 [info] index finished after resolve  [object Object] 
2025-04-29 08:17:47 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:18:00 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:18:00 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:18:00 [info] index finished after resolve  [object Object] 
2025-04-29 08:18:00 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:18:02 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:18:02 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:18:02 [info] index finished after resolve  [object Object] 
2025-04-29 08:18:02 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:18:11 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:18:11 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:18:11 [info] index finished after resolve  [object Object] 
2025-04-29 08:18:11 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:18:13 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:18:13 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:18:13 [info] index finished after resolve  [object Object] 
2025-04-29 08:18:13 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:18:16 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:18:16 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:18:16 [info] index finished after resolve  [object Object] 
2025-04-29 08:18:16 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:18:19 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:18:19 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:18:19 [info] index finished after resolve  [object Object] 
2025-04-29 08:18:19 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:18:23 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:18:23 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:18:23 [info] index finished after resolve  [object Object] 
2025-04-29 08:18:23 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:18:27 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:18:27 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:18:27 [info] index finished after resolve  [object Object] 
2025-04-29 08:18:27 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:18:37 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:18:37 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:18:37 [info] index finished after resolve  [object Object] 
2025-04-29 08:18:37 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:18:40 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:18:40 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:18:40 [info] index finished after resolve  [object Object] 
2025-04-29 08:18:40 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:18:45 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:18:45 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:18:45 [info] index finished after resolve  [object Object] 
2025-04-29 08:18:45 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:18:49 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:18:49 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:18:49 [info] index finished after resolve  [object Object] 
2025-04-29 08:18:49 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:18:51 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:18:51 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:18:51 [info] index finished after resolve  [object Object] 
2025-04-29 08:18:51 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:18:53 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:18:53 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:18:53 [info] index finished after resolve  [object Object] 
2025-04-29 08:18:53 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:18:56 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:18:56 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:18:56 [info] index finished after resolve  [object Object] 
2025-04-29 08:18:56 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:19:03 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:19:03 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:19:03 [info] index finished after resolve  [object Object] 
2025-04-29 08:19:03 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:19:15 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:19:15 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:19:15 [info] index finished after resolve  [object Object] 
2025-04-29 08:19:15 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:19:17 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:19:17 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:19:17 [info] index finished after resolve  [object Object] 
2025-04-29 08:19:17 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:19:20 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:19:20 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:19:20 [info] index finished after resolve  [object Object] 
2025-04-29 08:19:20 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:19:35 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:19:35 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:19:35 [info] index finished after resolve  [object Object] 
2025-04-29 08:19:35 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:19:37 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:19:37 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:19:37 [info] index finished after resolve  [object Object] 
2025-04-29 08:19:37 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:19:45 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:19:45 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:19:45 [info] index finished after resolve  [object Object] 
2025-04-29 08:19:45 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:19:47 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:19:47 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:19:47 [info] index finished after resolve  [object Object] 
2025-04-29 08:19:47 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:19:49 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:19:49 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:19:49 [info] index finished after resolve  [object Object] 
2025-04-29 08:19:49 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:20:08 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:20:08 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:20:08 [info] index finished after resolve  [object Object] 
2025-04-29 08:20:08 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:20:14 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:20:14 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:20:14 [info] index finished after resolve  [object Object] 
2025-04-29 08:20:14 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:20:24 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:20:24 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:20:24 [info] index finished after resolve  [object Object] 
2025-04-29 08:20:24 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:20:26 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:20:26 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:20:26 [info] index finished after resolve  [object Object] 
2025-04-29 08:20:26 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:20:36 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:20:36 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:20:36 [info] index finished after resolve  [object Object] 
2025-04-29 08:20:36 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:20:39 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:20:39 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:20:39 [info] index finished after resolve  [object Object] 
2025-04-29 08:20:39 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:20:41 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:20:41 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:20:41 [info] index finished after resolve  [object Object] 
2025-04-29 08:20:41 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:20:43 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:20:43 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:20:43 [info] index finished after resolve  [object Object] 
2025-04-29 08:20:43 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:20:48 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:20:48 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:20:48 [info] index finished after resolve  [object Object] 
2025-04-29 08:20:48 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:20:54 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:20:54 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:20:54 [info] index finished after resolve  [object Object] 
2025-04-29 08:20:54 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:20:59 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:20:59 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:20:59 [info] index finished after resolve  [object Object] 
2025-04-29 08:20:59 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:21:03 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:21:03 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:21:03 [info] index finished after resolve  [object Object] 
2025-04-29 08:21:03 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:21:07 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:21:07 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:21:07 [info] index finished after resolve  [object Object] 
2025-04-29 08:21:07 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:21:10 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:21:10 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:21:10 [info] index finished after resolve  [object Object] 
2025-04-29 08:21:10 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:21:15 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:21:15 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:21:15 [info] index finished after resolve  [object Object] 
2025-04-29 08:21:15 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:21:17 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:21:17 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:21:17 [info] index finished after resolve  [object Object] 
2025-04-29 08:21:17 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:21:20 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:21:20 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:21:20 [info] index finished after resolve  [object Object] 
2025-04-29 08:21:20 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:21:30 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:21:30 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:21:30 [info] index finished after resolve  [object Object] 
2025-04-29 08:21:30 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:21:32 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:21:32 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:21:32 [info] index finished after resolve  [object Object] 
2025-04-29 08:21:32 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:21:48 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:21:48 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:21:48 [info] index finished after resolve  [object Object] 
2025-04-29 08:21:48 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:21:50 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:21:50 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:21:50 [info] index finished after resolve  [object Object] 
2025-04-29 08:21:50 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:22:00 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:22:00 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:22:00 [info] index finished after resolve  [object Object] 
2025-04-29 08:22:00 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:22:03 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:22:03 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:22:03 [info] index finished after resolve  [object Object] 
2025-04-29 08:22:03 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:22:07 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:22:07 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:22:07 [info] index finished after resolve  [object Object] 
2025-04-29 08:22:07 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:22:09 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:22:09 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:22:09 [info] index finished after resolve  [object Object] 
2025-04-29 08:22:09 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:22:13 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:22:13 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:22:13 [info] index finished after resolve  [object Object] 
2025-04-29 08:22:13 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:22:24 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:22:24 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:22:24 [info] index finished after resolve  [object Object] 
2025-04-29 08:22:24 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:22:26 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:22:26 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:22:26 [info] index finished after resolve  [object Object] 
2025-04-29 08:22:26 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:22:29 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:22:29 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:22:29 [info] index finished after resolve  [object Object] 
2025-04-29 08:22:29 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:22:32 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:22:32 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:22:32 [info] index finished after resolve  [object Object] 
2025-04-29 08:22:32 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:22:35 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:22:35 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:22:35 [info] index finished after resolve  [object Object] 
2025-04-29 08:22:35 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:23:02 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:23:03 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:23:03 [info] index finished after resolve  [object Object] 
2025-04-29 08:23:03 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:23:06 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:23:06 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:23:06 [info] index finished after resolve  [object Object] 
2025-04-29 08:23:06 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:23:20 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:23:21 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:23:21 [info] index finished after resolve  [object Object] 
2025-04-29 08:23:21 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:23:30 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:23:30 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:23:30 [info] index finished after resolve  [object Object] 
2025-04-29 08:23:30 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:23:56 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:23:56 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:23:56 [info] index finished after resolve  [object Object] 
2025-04-29 08:23:56 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:24:01 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:24:01 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:24:01 [info] index finished after resolve  [object Object] 
2025-04-29 08:24:01 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:24:10 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:24:10 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:24:10 [info] index finished after resolve  [object Object] 
2025-04-29 08:24:10 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:24:15 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:24:15 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:24:15 [info] index finished after resolve  [object Object] 
2025-04-29 08:24:15 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:24:20 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:24:20 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:24:20 [info] index finished after resolve  [object Object] 
2025-04-29 08:24:20 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:24:28 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:24:28 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:24:28 [info] index finished after resolve  [object Object] 
2025-04-29 08:24:28 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:24:30 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:24:30 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:24:30 [info] index finished after resolve  [object Object] 
2025-04-29 08:24:30 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:24:34 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:24:34 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:24:34 [info] index finished after resolve  [object Object] 
2025-04-29 08:24:34 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:24:43 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:24:43 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:24:43 [info] index finished after resolve  [object Object] 
2025-04-29 08:24:43 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:24:55 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:24:55 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:24:55 [info] index finished after resolve  [object Object] 
2025-04-29 08:24:55 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:24:58 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:24:58 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:24:58 [info] index finished after resolve  [object Object] 
2025-04-29 08:24:58 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:25:02 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:25:02 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:25:02 [info] index finished after resolve  [object Object] 
2025-04-29 08:25:02 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:25:11 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:25:11 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:25:11 [info] index finished after resolve  [object Object] 
2025-04-29 08:25:11 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:25:18 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:25:18 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:25:18 [info] index finished after resolve  [object Object] 
2025-04-29 08:25:18 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:25:24 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:25:24 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:25:24 [info] index finished after resolve  [object Object] 
2025-04-29 08:25:24 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:25:37 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:25:37 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:25:37 [info] index finished after resolve  [object Object] 
2025-04-29 08:25:37 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:25:40 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:25:40 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:25:40 [info] index finished after resolve  [object Object] 
2025-04-29 08:25:40 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:25:47 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:25:47 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:25:47 [info] index finished after resolve  [object Object] 
2025-04-29 08:25:47 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:26:07 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:26:07 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:26:07 [info] index finished after resolve  [object Object] 
2025-04-29 08:26:07 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:26:17 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:26:17 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:26:17 [info] index finished after resolve  [object Object] 
2025-04-29 08:26:17 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:26:27 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:26:27 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:26:27 [info] index finished after resolve  [object Object] 
2025-04-29 08:26:27 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:26:30 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:26:30 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:26:30 [info] index finished after resolve  [object Object] 
2025-04-29 08:26:30 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:26:52 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:26:53 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:26:53 [info] index finished after resolve  [object Object] 
2025-04-29 08:26:53 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:26:55 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:26:55 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:26:55 [info] index finished after resolve  [object Object] 
2025-04-29 08:26:55 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:27:07 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:27:07 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:27:07 [info] index finished after resolve  [object Object] 
2025-04-29 08:27:07 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:27:10 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:27:10 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:27:10 [info] index finished after resolve  [object Object] 
2025-04-29 08:27:10 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:27:12 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:27:12 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:27:12 [info] index finished after resolve  [object Object] 
2025-04-29 08:27:12 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:27:19 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:27:19 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:27:19 [info] index finished after resolve  [object Object] 
2025-04-29 08:27:19 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:27:21 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:27:21 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:27:21 [info] index finished after resolve  [object Object] 
2025-04-29 08:27:21 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:27:24 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:27:24 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:27:24 [info] index finished after resolve  [object Object] 
2025-04-29 08:27:24 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:27:26 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:27:27 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:27:27 [info] index finished after resolve  [object Object] 
2025-04-29 08:27:27 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:28:04 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:28:04 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:28:04 [info] index finished after resolve  [object Object] 
2025-04-29 08:28:04 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:28:10 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:28:10 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:28:10 [info] index finished after resolve  [object Object] 
2025-04-29 08:28:10 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:28:16 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:28:16 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:28:16 [info] index finished after resolve  [object Object] 
2025-04-29 08:28:16 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:31:31 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:31:31 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:31:31 [info] index finished after resolve  [object Object] 
2025-04-29 08:31:31 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:31:33 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:31:33 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:31:33 [info] index finished after resolve  [object Object] 
2025-04-29 08:31:33 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:31:39 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:31:39 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:31:39 [info] index finished after resolve  [object Object] 
2025-04-29 08:31:39 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:31:43 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:31:43 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:31:43 [info] index finished after resolve  [object Object] 
2025-04-29 08:31:43 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:31:45 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:31:45 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:31:45 [info] index finished after resolve  [object Object] 
2025-04-29 08:31:45 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:31:49 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:31:49 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:31:49 [info] index finished after resolve  [object Object] 
2025-04-29 08:31:49 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:31:51 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:31:51 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:31:51 [info] index finished after resolve  [object Object] 
2025-04-29 08:31:51 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:32:17 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:32:17 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:32:17 [info] index finished after resolve  [object Object] 
2025-04-29 08:32:17 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:32:45 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-29 08:32:45 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-29 08:32:45 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-29 08:32:45 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-29 08:32:45 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-29 08:33:12 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-29 08:33:12 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-29 08:33:12 [info] index finished after resolve  [object Object] 
2025-04-29 08:33:12 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:33:28 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-29 08:33:28 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-29 08:33:28 [info] index finished after resolve  [object Object] 
2025-04-29 08:33:28 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:35:13 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:35:13 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:35:13 [info] index finished after resolve  [object Object] 
2025-04-29 08:35:13 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:35:35 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:35:35 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:35:35 [info] index finished after resolve  [object Object] 
2025-04-29 08:35:35 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:38:56 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:38:56 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:38:56 [info] index finished after resolve  [object Object] 
2025-04-29 08:38:56 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:40:00 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:40:00 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:40:00 [info] index finished after resolve  [object Object] 
2025-04-29 08:40:00 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:40:16 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:40:16 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:40:16 [info] index finished after resolve  [object Object] 
2025-04-29 08:40:16 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:40:38 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:40:38 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:40:38 [info] index finished after resolve  [object Object] 
2025-04-29 08:40:38 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:41:06 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:41:06 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:41:06 [info] index finished after resolve  [object Object] 
2025-04-29 08:41:06 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:41:11 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:41:11 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:41:11 [info] index finished after resolve  [object Object] 
2025-04-29 08:41:11 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:41:13 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:41:13 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:41:13 [info] index finished after resolve  [object Object] 
2025-04-29 08:41:13 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:41:18 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:41:18 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:41:18 [info] index finished after resolve  [object Object] 
2025-04-29 08:41:18 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:41:20 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:41:20 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:41:20 [info] index finished after resolve  [object Object] 
2025-04-29 08:41:20 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:41:22 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:41:22 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:41:22 [info] index finished after resolve  [object Object] 
2025-04-29 08:41:22 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:41:27 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:41:27 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:41:27 [info] index finished after resolve  [object Object] 
2025-04-29 08:41:27 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:41:30 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:41:30 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:41:30 [info] index finished after resolve  [object Object] 
2025-04-29 08:41:30 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:41:32 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:41:32 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:41:32 [info] index finished after resolve  [object Object] 
2025-04-29 08:41:32 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:41:36 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:41:36 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:41:36 [info] index finished after resolve  [object Object] 
2025-04-29 08:41:36 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:43:03 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:43:03 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:43:03 [info] index finished after resolve  [object Object] 
2025-04-29 08:43:03 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:43:12 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:43:12 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:43:12 [info] index finished after resolve  [object Object] 
2025-04-29 08:43:12 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:43:22 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:43:22 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:43:22 [info] index finished after resolve  [object Object] 
2025-04-29 08:43:22 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:43:25 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:43:25 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:43:25 [info] index finished after resolve  [object Object] 
2025-04-29 08:43:25 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:43:43 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:43:43 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:43:43 [info] index finished after resolve  [object Object] 
2025-04-29 08:43:43 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:43:46 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:43:46 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:43:46 [info] index finished after resolve  [object Object] 
2025-04-29 08:43:46 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:43:48 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:43:48 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:43:48 [info] index finished after resolve  [object Object] 
2025-04-29 08:43:48 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:43:51 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:43:51 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:43:51 [info] index finished after resolve  [object Object] 
2025-04-29 08:43:51 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:43:53 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:43:53 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:43:53 [info] index finished after resolve  [object Object] 
2025-04-29 08:43:53 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:43:55 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:43:55 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:43:55 [info] index finished after resolve  [object Object] 
2025-04-29 08:43:55 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:43:57 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:43:57 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:43:57 [info] index finished after resolve  [object Object] 
2025-04-29 08:43:57 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:43:59 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:43:59 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:43:59 [info] index finished after resolve  [object Object] 
2025-04-29 08:43:59 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:44:32 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:44:32 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:44:32 [info] index finished after resolve  [object Object] 
2025-04-29 08:44:32 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:44:35 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:44:35 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:44:35 [info] index finished after resolve  [object Object] 
2025-04-29 08:44:35 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:44:37 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:44:37 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:44:37 [info] index finished after resolve  [object Object] 
2025-04-29 08:44:37 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:44:39 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:44:39 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:44:39 [info] index finished after resolve  [object Object] 
2025-04-29 08:44:39 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:44:41 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:44:41 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:44:41 [info] index finished after resolve  [object Object] 
2025-04-29 08:44:41 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:44:45 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:44:45 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:44:45 [info] index finished after resolve  [object Object] 
2025-04-29 08:44:45 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:45:00 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:45:00 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:45:00 [info] index finished after resolve  [object Object] 
2025-04-29 08:45:00 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:45:04 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:45:04 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:45:04 [info] index finished after resolve  [object Object] 
2025-04-29 08:45:04 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:45:13 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:45:13 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:45:13 [info] index finished after resolve  [object Object] 
2025-04-29 08:45:13 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:45:20 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:45:20 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:45:20 [info] index finished after resolve  [object Object] 
2025-04-29 08:45:20 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:45:23 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:45:24 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:45:24 [info] index finished after resolve  [object Object] 
2025-04-29 08:45:24 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:45:49 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:45:49 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:45:49 [info] index finished after resolve  [object Object] 
2025-04-29 08:45:49 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:45:52 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:45:52 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:45:52 [info] index finished after resolve  [object Object] 
2025-04-29 08:45:52 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:45:54 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:45:54 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:45:54 [info] index finished after resolve  [object Object] 
2025-04-29 08:45:54 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:46:14 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:46:14 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:46:14 [info] index finished after resolve  [object Object] 
2025-04-29 08:46:14 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:46:17 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:46:17 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:46:17 [info] index finished after resolve  [object Object] 
2025-04-29 08:46:17 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:46:19 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:46:19 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:46:19 [info] index finished after resolve  [object Object] 
2025-04-29 08:46:19 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:46:21 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:46:21 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:46:21 [info] index finished after resolve  [object Object] 
2025-04-29 08:46:21 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:46:50 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:46:50 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:46:50 [info] index finished after resolve  [object Object] 
2025-04-29 08:46:50 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:46:54 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:46:54 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:46:54 [info] index finished after resolve  [object Object] 
2025-04-29 08:46:54 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:46:56 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:46:56 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:46:56 [info] index finished after resolve  [object Object] 
2025-04-29 08:46:56 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:47:06 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:47:06 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:47:06 [info] index finished after resolve  [object Object] 
2025-04-29 08:47:06 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:51:08 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:51:08 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:51:08 [info] index finished after resolve  [object Object] 
2025-04-29 08:51:08 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:55:12 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:55:12 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:55:12 [info] index finished after resolve  [object Object] 
2025-04-29 08:55:12 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:56:06 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:56:06 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:56:06 [info] index finished after resolve  [object Object] 
2025-04-29 08:56:06 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:56:33 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:56:33 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:56:33 [info] index finished after resolve  [object Object] 
2025-04-29 08:56:33 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:56:45 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:56:45 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:56:45 [info] index finished after resolve  [object Object] 
2025-04-29 08:56:45 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:56:47 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:56:47 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:56:47 [info] index finished after resolve  [object Object] 
2025-04-29 08:56:47 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:57:17 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:57:17 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:57:17 [info] index finished after resolve  [object Object] 
2025-04-29 08:57:17 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:57:20 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:57:20 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:57:20 [info] index finished after resolve  [object Object] 
2025-04-29 08:57:20 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:58:18 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:58:18 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:58:18 [info] index finished after resolve  [object Object] 
2025-04-29 08:58:18 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:58:25 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:58:25 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:58:25 [info] index finished after resolve  [object Object] 
2025-04-29 08:58:25 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:58:46 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:58:46 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:58:46 [info] index finished after resolve  [object Object] 
2025-04-29 08:58:46 [info] refresh page data from resolve listeners 0 852   
2025-04-29 08:58:56 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 08:58:56 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 08:58:56 [info] index finished after resolve  [object Object] 
2025-04-29 08:58:56 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:10:21 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-29 09:10:21 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-29 09:10:21 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-29 09:10:21 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-29 09:10:21 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-29 09:11:04 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-29 09:11:04 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-29 09:11:04 [info] index finished after resolve  [object Object] 
2025-04-29 09:11:04 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:11:21 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-29 09:11:21 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-29 09:11:21 [info] index finished after resolve  [object Object] 
2025-04-29 09:11:21 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:12:02 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-29 09:12:02 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-29 09:12:02 [info] index finished after resolve  [object Object] 
2025-04-29 09:12:02 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:12:18 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-29 09:12:18 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-29 09:12:18 [info] index finished after resolve  [object Object] 
2025-04-29 09:12:18 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:12:34 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-29 09:12:34 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-29 09:12:34 [info] index finished after resolve  [object Object] 
2025-04-29 09:12:34 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:12:59 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-29 09:12:59 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-29 09:12:59 [info] index finished after resolve  [object Object] 
2025-04-29 09:12:59 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:13:15 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-29 09:13:15 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-29 09:13:15 [info] index finished after resolve  [object Object] 
2025-04-29 09:13:15 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:14:47 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-29 09:14:47 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-29 09:14:47 [info] index finished after resolve  [object Object] 
2025-04-29 09:14:47 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:14:53 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-29 09:14:53 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-29 09:14:53 [info] index finished after resolve  [object Object] 
2025-04-29 09:14:53 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:15:16 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-29 09:15:16 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-29 09:15:16 [info] index finished after resolve  [object Object] 
2025-04-29 09:15:16 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:15:34 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-29 09:15:34 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-29 09:15:34 [info] index finished after resolve  [object Object] 
2025-04-29 09:15:34 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:15:56 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-29 09:15:56 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-29 09:15:56 [info] index finished after resolve  [object Object] 
2025-04-29 09:15:56 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:16:16 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-29 09:16:16 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-29 09:16:16 [info] index finished after resolve  [object Object] 
2025-04-29 09:16:16 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:16:35 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-29 09:16:35 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-29 09:16:35 [info] index finished after resolve  [object Object] 
2025-04-29 09:16:35 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:16:51 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-29 09:16:51 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-29 09:16:51 [info] index finished after resolve  [object Object] 
2025-04-29 09:16:51 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:17:28 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-29 09:17:28 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-29 09:17:28 [info] index finished after resolve  [object Object] 
2025-04-29 09:17:28 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:20:56 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-29 09:20:56 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-29 09:20:56 [info] index finished after resolve  [object Object] 
2025-04-29 09:20:56 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:21:12 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-29 09:21:12 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-29 09:21:12 [info] index finished after resolve  [object Object] 
2025-04-29 09:21:12 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:21:28 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-29 09:21:28 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-29 09:21:28 [info] index finished after resolve  [object Object] 
2025-04-29 09:21:28 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:21:45 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-29 09:21:45 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-29 09:21:45 [info] index finished after resolve  [object Object] 
2025-04-29 09:21:45 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:22:13 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-29 09:22:13 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-29 09:22:13 [info] index finished after resolve  [object Object] 
2025-04-29 09:22:13 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:22:28 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-29 09:22:28 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-29 09:22:28 [info] index finished after resolve  [object Object] 
2025-04-29 09:22:28 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:22:50 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-29 09:22:50 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-29 09:22:50 [info] index finished after resolve  [object Object] 
2025-04-29 09:22:50 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:23:11 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-29 09:23:11 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-29 09:23:11 [info] index finished after resolve  [object Object] 
2025-04-29 09:23:11 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:27:40 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-29 09:27:40 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-29 09:27:40 [info] index finished after resolve  [object Object] 
2025-04-29 09:27:40 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:28:57 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-29 09:28:57 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-29 09:28:57 [info] index finished after resolve  [object Object] 
2025-04-29 09:28:57 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:29:20 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-29 09:29:20 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-29 09:29:20 [info] index finished after resolve  [object Object] 
2025-04-29 09:29:20 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:29:37 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-29 09:29:37 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-29 09:29:37 [info] index finished after resolve  [object Object] 
2025-04-29 09:29:37 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:30:15 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-29 09:30:15 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-29 09:30:15 [info] index finished after resolve  [object Object] 
2025-04-29 09:30:15 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:30:33 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-29 09:30:33 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-29 09:30:33 [info] index finished after resolve  [object Object] 
2025-04-29 09:30:33 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:31:04 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-29 09:31:04 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-29 09:31:04 [info] index finished after resolve  [object Object] 
2025-04-29 09:31:04 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:31:21 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-29 09:31:21 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-29 09:31:21 [info] index finished after resolve  [object Object] 
2025-04-29 09:31:21 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:31:38 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-29 09:31:38 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-29 09:31:38 [info] index finished after resolve  [object Object] 
2025-04-29 09:31:38 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:31:56 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-29 09:31:56 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-29 09:31:56 [info] index finished after resolve  [object Object] 
2025-04-29 09:31:56 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:32:25 [info] ignore file modify evnet 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md   
2025-04-29 09:32:25 [info] trigger 学习库/Deep learning/pytorch/Drawing 2025-04-27 16.50.59.excalidraw.md resolve  [object Object] 
2025-04-29 09:32:25 [info] index finished after resolve  [object Object] 
2025-04-29 09:32:25 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:36:34 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 09:36:34 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 09:36:34 [info] index finished after resolve  [object Object] 
2025-04-29 09:36:34 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:39:44 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 09:39:44 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 09:39:44 [info] index finished after resolve  [object Object] 
2025-04-29 09:39:44 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:40:41 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 09:40:41 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 09:40:41 [info] index finished after resolve  [object Object] 
2025-04-29 09:40:41 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:40:52 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 09:40:52 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 09:40:52 [info] index finished after resolve  [object Object] 
2025-04-29 09:40:52 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:41:16 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 09:41:16 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 09:41:16 [info] index finished after resolve  [object Object] 
2025-04-29 09:41:16 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:41:46 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 09:41:46 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 09:41:46 [info] index finished after resolve  [object Object] 
2025-04-29 09:41:46 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:42:35 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 09:42:35 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 09:42:35 [info] index finished after resolve  [object Object] 
2025-04-29 09:42:35 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:42:56 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 09:42:56 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 09:42:56 [info] index finished after resolve  [object Object] 
2025-04-29 09:42:56 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:43:05 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 09:43:05 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 09:43:05 [info] index finished after resolve  [object Object] 
2025-04-29 09:43:05 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:43:15 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 09:43:15 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 09:43:15 [info] index finished after resolve  [object Object] 
2025-04-29 09:43:15 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:44:43 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 09:44:43 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 09:44:43 [info] index finished after resolve  [object Object] 
2025-04-29 09:44:43 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:45:11 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 09:45:11 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 09:45:11 [info] index finished after resolve  [object Object] 
2025-04-29 09:45:11 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:45:26 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 09:45:27 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 09:45:27 [info] index finished after resolve  [object Object] 
2025-04-29 09:45:27 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:46:34 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 09:46:34 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 09:46:34 [info] index finished after resolve  [object Object] 
2025-04-29 09:46:34 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:48:35 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 09:48:35 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 09:48:35 [info] index finished after resolve  [object Object] 
2025-04-29 09:48:35 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:48:47 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 09:48:47 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 09:48:47 [info] index finished after resolve  [object Object] 
2025-04-29 09:48:47 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:48:52 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 09:48:52 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 09:48:52 [info] index finished after resolve  [object Object] 
2025-04-29 09:48:52 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:48:56 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 09:48:56 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 09:48:56 [info] index finished after resolve  [object Object] 
2025-04-29 09:48:56 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:48:59 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 09:48:59 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 09:48:59 [info] index finished after resolve  [object Object] 
2025-04-29 09:48:59 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:49:18 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 09:49:18 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 09:49:18 [info] index finished after resolve  [object Object] 
2025-04-29 09:49:18 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:49:39 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 09:49:39 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 09:49:39 [info] index finished after resolve  [object Object] 
2025-04-29 09:49:39 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:50:32 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 09:50:32 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 09:50:32 [info] index finished after resolve  [object Object] 
2025-04-29 09:50:32 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:52:43 [info] ignore file modify evnet 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md   
2025-04-29 09:52:43 [info] trigger 学习库/Deep learning/pytorch/2. 梯度下降算法（Gradient Descent Algorithm）.md resolve  [object Object] 
2025-04-29 09:52:43 [info] index finished after resolve  [object Object] 
2025-04-29 09:52:43 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:52:48 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 09:52:48 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 09:52:48 [info] index finished after resolve  [object Object] 
2025-04-29 09:52:48 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:52:53 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 09:52:53 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 09:52:53 [info] index finished after resolve  [object Object] 
2025-04-29 09:52:53 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:52:55 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 09:52:55 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 09:52:55 [info] index finished after resolve  [object Object] 
2025-04-29 09:52:55 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:53:00 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 09:53:00 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 09:53:00 [info] index finished after resolve  [object Object] 
2025-04-29 09:53:00 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:53:02 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 09:53:02 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 09:53:02 [info] index finished after resolve  [object Object] 
2025-04-29 09:53:02 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:53:09 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 09:53:09 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 09:53:09 [info] index finished after resolve  [object Object] 
2025-04-29 09:53:09 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:53:12 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 09:53:12 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 09:53:12 [info] index finished after resolve  [object Object] 
2025-04-29 09:53:12 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:53:14 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 09:53:14 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 09:53:14 [info] index finished after resolve  [object Object] 
2025-04-29 09:53:14 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:53:16 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 09:53:16 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 09:53:16 [info] index finished after resolve  [object Object] 
2025-04-29 09:53:16 [info] refresh page data from resolve listeners 0 852   
2025-04-29 09:53:21 [info] ignore file modify evnet 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md   
2025-04-29 09:53:21 [info] trigger 学习库/Deep learning/pytorch/3. 反向传播（Back Propagation）.md resolve  [object Object] 
2025-04-29 09:53:21 [info] index finished after resolve  [object Object] 
2025-04-29 09:53:21 [info] refresh page data from resolve listeners 0 852   
2025-04-29 19:47:50 [info] indexing created file 学习库/Deep learning/pytorch/未命名.md  [object Object] 
2025-04-29 19:47:50 [info] indexing created ignore file 学习库/Deep learning/pytorch/未命名.md   
2025-04-29 19:47:50 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-04-29 19:47:50 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-04-29 19:47:50 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-04-29 19:47:50 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-04-29 19:47:50 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-04-29 19:47:50 [info] trigger 学习库/Deep learning/pytorch/未命名.md resolve  [object Object] 
2025-04-29 19:47:50 [info] index finished after resolve  [object Object] 
2025-04-29 19:47:50 [info] refresh page data from resolve listeners 0 853   
2025-04-29 19:48:32 [info] ignore file modify evnet 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md   
2025-04-29 19:48:32 [info] trigger 学习库/Deep learning/pytorch/1. 线性模型（Linear Model）.md resolve  [object Object] 
2025-04-29 19:48:32 [info] index finished after resolve  [object Object] 
2025-04-29 19:48:32 [info] refresh page data from resolve listeners 0 853   
2025-04-29 19:49:05 [info] refresh page data from rename listeners 0 853   
2025-04-29 19:49:05 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-04-29 19:49:05 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-04-29 19:49:05 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-04-29 19:49:05 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-04-29 19:49:05 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-04-29 19:49:17 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 19:49:17 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 19:49:17 [info] index finished after resolve  [object Object] 
2025-04-29 19:49:17 [info] refresh page data from resolve listeners 0 853   
2025-04-29 19:51:23 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 19:51:23 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 19:51:23 [info] index finished after resolve  [object Object] 
2025-04-29 19:51:23 [info] refresh page data from resolve listeners 0 853   
2025-04-29 19:52:07 [info] indexing created file 学习库/Anki/Deep learning/未命名.md  [object Object] 
2025-04-29 19:52:07 [info] indexing created ignore file 学习库/Anki/Deep learning/未命名.md   
2025-04-29 19:52:07 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-04-29 19:52:07 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-04-29 19:52:07 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-04-29 19:52:07 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-04-29 19:52:07 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-04-29 19:52:07 [info] trigger 学习库/Anki/Deep learning/未命名.md resolve  [object Object] 
2025-04-29 19:52:07 [info] index finished after resolve  [object Object] 
2025-04-29 19:52:07 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:52:36 [info] ignore file modify evnet 学习库/Anki/Deep learning/未命名.md   
2025-04-29 19:52:36 [info] trigger 学习库/Anki/Deep learning/未命名.md resolve  [object Object] 
2025-04-29 19:52:36 [info] index finished after resolve  [object Object] 
2025-04-29 19:52:36 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:52:46 [info] ignore file modify evnet 学习库/Anki/Deep learning/未命名.md   
2025-04-29 19:52:46 [info] trigger 学习库/Anki/Deep learning/未命名.md resolve  [object Object] 
2025-04-29 19:52:46 [info] index finished after resolve  [object Object] 
2025-04-29 19:52:46 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:52:55 [info] refresh page data from rename listeners 0 854   
2025-04-29 19:52:55 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-04-29 19:52:55 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-04-29 19:52:55 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-04-29 19:52:55 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-04-29 19:52:55 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-04-29 19:53:31 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:53:31 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:53:31 [info] index finished after resolve  [object Object] 
2025-04-29 19:53:31 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:53:33 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:53:33 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:53:33 [info] index finished after resolve  [object Object] 
2025-04-29 19:53:33 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:53:35 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:53:35 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:53:35 [info] index finished after resolve  [object Object] 
2025-04-29 19:53:35 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:53:38 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:53:38 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:53:38 [info] index finished after resolve  [object Object] 
2025-04-29 19:53:38 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:53:40 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:53:41 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:53:41 [info] index finished after resolve  [object Object] 
2025-04-29 19:53:41 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:53:43 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:53:43 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:53:43 [info] index finished after resolve  [object Object] 
2025-04-29 19:53:43 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:53:45 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:53:45 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:53:45 [info] index finished after resolve  [object Object] 
2025-04-29 19:53:45 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:53:47 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:53:47 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:53:47 [info] index finished after resolve  [object Object] 
2025-04-29 19:53:47 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:53:49 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:53:49 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:53:49 [info] index finished after resolve  [object Object] 
2025-04-29 19:53:49 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:53:51 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:53:51 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:53:51 [info] index finished after resolve  [object Object] 
2025-04-29 19:53:51 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:53:53 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:53:53 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:53:53 [info] index finished after resolve  [object Object] 
2025-04-29 19:53:53 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:53:56 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:53:56 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:53:56 [info] index finished after resolve  [object Object] 
2025-04-29 19:53:56 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:53:58 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:53:58 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:53:58 [info] index finished after resolve  [object Object] 
2025-04-29 19:53:58 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:54:00 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:54:00 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:54:00 [info] index finished after resolve  [object Object] 
2025-04-29 19:54:00 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:54:02 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:54:02 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:54:02 [info] index finished after resolve  [object Object] 
2025-04-29 19:54:02 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:54:05 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:54:05 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:54:05 [info] index finished after resolve  [object Object] 
2025-04-29 19:54:05 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:54:07 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:54:07 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:54:07 [info] index finished after resolve  [object Object] 
2025-04-29 19:54:07 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:54:09 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:54:09 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:54:09 [info] index finished after resolve  [object Object] 
2025-04-29 19:54:09 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:54:11 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:54:11 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:54:11 [info] index finished after resolve  [object Object] 
2025-04-29 19:54:11 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:54:13 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:54:13 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:54:13 [info] index finished after resolve  [object Object] 
2025-04-29 19:54:13 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:54:15 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:54:15 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:54:15 [info] index finished after resolve  [object Object] 
2025-04-29 19:54:15 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:54:17 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:54:17 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:54:17 [info] index finished after resolve  [object Object] 
2025-04-29 19:54:17 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:54:19 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:54:19 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:54:19 [info] index finished after resolve  [object Object] 
2025-04-29 19:54:19 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:54:25 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:54:25 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:54:25 [info] index finished after resolve  [object Object] 
2025-04-29 19:54:25 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:54:32 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:54:32 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:54:32 [info] index finished after resolve  [object Object] 
2025-04-29 19:54:32 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:54:35 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:54:35 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:54:35 [info] index finished after resolve  [object Object] 
2025-04-29 19:54:35 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:54:37 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:54:37 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:54:37 [info] index finished after resolve  [object Object] 
2025-04-29 19:54:37 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:54:39 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:54:39 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:54:39 [info] index finished after resolve  [object Object] 
2025-04-29 19:54:39 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:54:41 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:54:41 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:54:41 [info] index finished after resolve  [object Object] 
2025-04-29 19:54:41 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:54:43 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:54:43 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:54:43 [info] index finished after resolve  [object Object] 
2025-04-29 19:54:43 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:54:46 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:54:46 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:54:46 [info] index finished after resolve  [object Object] 
2025-04-29 19:54:46 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:54:48 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:54:48 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:54:48 [info] index finished after resolve  [object Object] 
2025-04-29 19:54:48 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:54:51 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:54:51 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:54:51 [info] index finished after resolve  [object Object] 
2025-04-29 19:54:51 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:54:53 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:54:53 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:54:53 [info] index finished after resolve  [object Object] 
2025-04-29 19:54:53 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:54:55 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:54:55 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:54:55 [info] index finished after resolve  [object Object] 
2025-04-29 19:54:55 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:54:58 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:54:58 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:54:58 [info] index finished after resolve  [object Object] 
2025-04-29 19:54:58 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:55:00 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:55:00 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:55:00 [info] index finished after resolve  [object Object] 
2025-04-29 19:55:00 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:55:03 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:55:03 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:55:03 [info] index finished after resolve  [object Object] 
2025-04-29 19:55:03 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:55:05 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:55:05 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:55:05 [info] index finished after resolve  [object Object] 
2025-04-29 19:55:05 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:55:14 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:55:14 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:55:14 [info] index finished after resolve  [object Object] 
2025-04-29 19:55:14 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:55:57 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:55:57 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:55:57 [info] index finished after resolve  [object Object] 
2025-04-29 19:55:57 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:56:00 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:56:00 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:56:00 [info] index finished after resolve  [object Object] 
2025-04-29 19:56:00 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:56:03 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:56:03 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:56:03 [info] index finished after resolve  [object Object] 
2025-04-29 19:56:03 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:56:34 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:56:34 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:56:34 [info] index finished after resolve  [object Object] 
2025-04-29 19:56:34 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:56:37 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:56:37 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:56:37 [info] index finished after resolve  [object Object] 
2025-04-29 19:56:37 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:56:42 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:56:42 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:56:42 [info] index finished after resolve  [object Object] 
2025-04-29 19:56:42 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:56:50 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:56:50 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:56:50 [info] index finished after resolve  [object Object] 
2025-04-29 19:56:50 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:56:53 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:56:53 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:56:53 [info] index finished after resolve  [object Object] 
2025-04-29 19:56:53 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:56:56 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 19:56:56 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 19:56:56 [info] index finished after resolve  [object Object] 
2025-04-29 19:56:56 [info] refresh page data from resolve listeners 0 854   
2025-04-29 19:59:48 [info] components database created cost 1 ms   
2025-04-29 19:59:48 [info] components index initializing...   
2025-04-29 19:59:48 [info] start to batch put pages: 6   
2025-04-29 19:59:49 [info] batch persist cost 6  1160 
2025-04-29 19:59:49 [info] components index initialized, 854 files cost 1437 ms   
2025-04-29 19:59:49 [info] refresh page data from init listeners 0 854   
2025-04-29 19:59:51 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-29 19:59:53 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-29 19:59:53 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-29 19:59:53 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-29 19:59:53 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-29 19:59:53 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-29 19:59:53 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-29 19:59:54 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-29 19:59:54 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-29 19:59:54 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-29 19:59:54 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-29 20:19:12 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-29 20:19:12 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-04-29 20:19:12 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-04-29 20:19:12 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-04-29 20:19:12 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-04-29 20:19:12 [error] Error: Minified React error #409; visit https://reactjs.org/docs/error-decoder.html?invariant=409 for the full message or use the non-minified dev environment for full errors and additional helpful warnings.   
2025-04-29 20:19:12 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-29 20:19:12 [info] components database created cost 18 ms   
2025-04-29 20:19:12 [info] components index initializing...   
2025-04-29 20:19:12 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-29 20:19:12 [info] start to batch put pages: 5   
2025-04-29 20:19:14 [info] batch persist cost 5  1411 
2025-04-29 20:19:14 [info] components index initialized, 854 files cost 1574 ms   
2025-04-29 20:19:14 [info] refresh page data from init listeners 0 854   
2025-04-29 20:19:15 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-29 20:19:15 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-29 20:19:15 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-29 20:19:15 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-29 20:19:15 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-29 20:19:15 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-29 20:19:15 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-29 20:19:15 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-29 20:19:15 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-04-29 20:19:16 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-04-29 20:19:16 [info] ignore file modify evnet Home/components/view/remember.components   
2025-04-29 20:19:16 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-04-29 20:19:16 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-04-29 20:25:37 [info] indexing created file 学习库/Deep learning/pytorch/PDF/test.py  [object Object] 
2025-04-29 20:25:37 [info] refresh page data from created listeners 0 855   
2025-04-29 20:29:10 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 20:29:10 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 20:29:10 [info] index finished after resolve  [object Object] 
2025-04-29 20:29:10 [info] refresh page data from resolve listeners 0 855   
2025-04-29 20:29:35 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 20:29:35 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 20:29:35 [info] index finished after resolve  [object Object] 
2025-04-29 20:29:35 [info] refresh page data from resolve listeners 0 855   
2025-04-29 20:30:14 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 20:30:14 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 20:30:14 [info] index finished after resolve  [object Object] 
2025-04-29 20:30:14 [info] refresh page data from resolve listeners 0 855   
2025-04-29 20:30:17 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 20:30:17 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 20:30:17 [info] index finished after resolve  [object Object] 
2025-04-29 20:30:17 [info] refresh page data from resolve listeners 0 855   
2025-04-29 20:30:18 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 20:30:18 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 20:30:18 [info] index finished after resolve  [object Object] 
2025-04-29 20:30:18 [info] refresh page data from resolve listeners 0 855   
2025-04-29 20:30:31 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 20:30:31 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 20:30:31 [info] index finished after resolve  [object Object] 
2025-04-29 20:30:31 [info] refresh page data from resolve listeners 0 855   
2025-04-29 20:30:34 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 20:30:34 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 20:30:34 [info] index finished after resolve  [object Object] 
2025-04-29 20:30:34 [info] refresh page data from resolve listeners 0 855   
2025-04-29 20:30:37 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 20:30:37 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 20:30:37 [info] index finished after resolve  [object Object] 
2025-04-29 20:30:37 [info] refresh page data from resolve listeners 0 855   
2025-04-29 20:30:44 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 20:30:44 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 20:30:44 [info] index finished after resolve  [object Object] 
2025-04-29 20:30:44 [info] refresh page data from resolve listeners 0 855   
2025-04-29 20:31:55 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 20:31:55 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 20:31:55 [info] index finished after resolve  [object Object] 
2025-04-29 20:31:55 [info] refresh page data from resolve listeners 0 855   
2025-04-29 20:31:57 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 20:31:57 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 20:31:57 [info] index finished after resolve  [object Object] 
2025-04-29 20:31:57 [info] refresh page data from resolve listeners 0 855   
2025-04-29 20:31:59 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 20:31:59 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 20:31:59 [info] index finished after resolve  [object Object] 
2025-04-29 20:31:59 [info] refresh page data from resolve listeners 0 855   
2025-04-29 20:32:46 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 20:32:46 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 20:32:46 [info] index finished after resolve  [object Object] 
2025-04-29 20:32:46 [info] refresh page data from resolve listeners 0 855   
2025-04-29 20:32:48 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 20:32:49 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 20:32:49 [info] index finished after resolve  [object Object] 
2025-04-29 20:32:49 [info] refresh page data from resolve listeners 0 855   
2025-04-29 20:32:55 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 20:32:55 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 20:32:55 [info] index finished after resolve  [object Object] 
2025-04-29 20:32:55 [info] refresh page data from resolve listeners 0 855   
2025-04-29 20:33:02 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 20:33:02 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 20:33:02 [info] index finished after resolve  [object Object] 
2025-04-29 20:33:02 [info] refresh page data from resolve listeners 0 855   
2025-04-29 20:33:05 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 20:33:05 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 20:33:05 [info] index finished after resolve  [object Object] 
2025-04-29 20:33:05 [info] refresh page data from resolve listeners 0 855   
2025-04-29 20:46:37 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 20:46:37 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 20:46:37 [info] index finished after resolve  [object Object] 
2025-04-29 20:46:37 [info] refresh page data from resolve listeners 0 855   
2025-04-29 20:46:41 [info] indexing created file 学习库/Deep learning/pytorch/attachments/4. 使用pytorch实现线性模型-2025-04-29-20-46-41.png  [object Object] 
2025-04-29 20:46:41 [info] refresh page data from created listeners 0 856   
2025-04-29 20:46:43 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 20:46:43 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 20:46:43 [info] index finished after resolve  [object Object] 
2025-04-29 20:46:43 [info] refresh page data from resolve listeners 0 856   
2025-04-29 20:52:00 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 20:52:00 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 20:52:00 [info] index finished after resolve  [object Object] 
2025-04-29 20:52:00 [info] refresh page data from resolve listeners 0 856   
2025-04-29 20:52:04 [info] indexing created file 学习库/Deep learning/pytorch/attachments/4. 使用pytorch实现线性模型-2025-04-29-20-52-04.png  [object Object] 
2025-04-29 20:52:04 [info] refresh page data from created listeners 0 857   
2025-04-29 20:52:05 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 20:52:05 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 20:52:05 [info] index finished after resolve  [object Object] 
2025-04-29 20:52:05 [info] refresh page data from resolve listeners 0 857   
2025-04-29 20:52:10 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 20:52:10 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 20:52:10 [info] index finished after resolve  [object Object] 
2025-04-29 20:52:10 [info] refresh page data from resolve listeners 0 857   
2025-04-29 20:52:13 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 20:52:13 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 20:52:13 [info] index finished after resolve  [object Object] 
2025-04-29 20:52:13 [info] refresh page data from resolve listeners 0 857   
2025-04-29 20:54:09 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 20:54:10 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 20:54:10 [info] index finished after resolve  [object Object] 
2025-04-29 20:54:10 [info] refresh page data from resolve listeners 0 857   
2025-04-29 20:54:54 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 20:54:54 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 20:54:54 [info] index finished after resolve  [object Object] 
2025-04-29 20:54:54 [info] refresh page data from resolve listeners 0 857   
2025-04-29 21:02:39 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 21:02:39 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 21:02:39 [info] index finished after resolve  [object Object] 
2025-04-29 21:02:39 [info] refresh page data from resolve listeners 0 857   
2025-04-29 21:03:42 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 21:03:42 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 21:03:42 [info] index finished after resolve  [object Object] 
2025-04-29 21:03:42 [info] refresh page data from resolve listeners 0 857   
2025-04-29 21:12:06 [info] ignore file modify evnet 学习库/python笔记/python函数/函数.md   
2025-04-29 21:12:06 [info] trigger 学习库/python笔记/python函数/函数.md resolve  [object Object] 
2025-04-29 21:12:06 [info] index finished after resolve  [object Object] 
2025-04-29 21:12:06 [info] refresh page data from resolve listeners 0 857   
2025-04-29 21:15:56 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 21:15:56 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 21:15:56 [info] index finished after resolve  [object Object] 
2025-04-29 21:15:56 [info] refresh page data from resolve listeners 0 857   
2025-04-29 21:29:39 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 21:29:39 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 21:29:39 [info] index finished after resolve  [object Object] 
2025-04-29 21:29:39 [info] refresh page data from resolve listeners 0 857   
2025-04-29 21:36:25 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 21:36:25 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 21:36:25 [info] index finished after resolve  [object Object] 
2025-04-29 21:36:25 [info] refresh page data from resolve listeners 0 857   
2025-04-29 21:36:44 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 21:36:44 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 21:36:44 [info] index finished after resolve  [object Object] 
2025-04-29 21:36:44 [info] refresh page data from resolve listeners 0 857   
2025-04-29 21:36:59 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 21:36:59 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 21:36:59 [info] index finished after resolve  [object Object] 
2025-04-29 21:36:59 [info] refresh page data from resolve listeners 0 857   
2025-04-29 21:37:16 [info] indexing created file 学习库/Deep learning/pytorch/attachments/4. 使用pytorch实现线性模型-2025-04-29-21-37-16.png  [object Object] 
2025-04-29 21:37:16 [info] refresh page data from created listeners 0 858   
2025-04-29 21:37:18 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 21:37:18 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 21:37:18 [info] index finished after resolve  [object Object] 
2025-04-29 21:37:18 [info] refresh page data from resolve listeners 0 858   
2025-04-29 21:37:22 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 21:37:22 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 21:37:22 [info] index finished after resolve  [object Object] 
2025-04-29 21:37:22 [info] refresh page data from resolve listeners 0 858   
2025-04-29 21:37:50 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 21:37:50 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 21:37:50 [info] index finished after resolve  [object Object] 
2025-04-29 21:37:50 [info] refresh page data from resolve listeners 0 858   
2025-04-29 21:37:52 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 21:37:52 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 21:37:52 [info] index finished after resolve  [object Object] 
2025-04-29 21:37:52 [info] refresh page data from resolve listeners 0 858   
2025-04-29 21:37:54 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 21:37:54 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 21:37:54 [info] index finished after resolve  [object Object] 
2025-04-29 21:37:54 [info] refresh page data from resolve listeners 0 858   
2025-04-29 21:37:56 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 21:37:56 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 21:37:56 [info] index finished after resolve  [object Object] 
2025-04-29 21:37:56 [info] refresh page data from resolve listeners 0 858   
2025-04-29 21:37:58 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 21:37:59 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 21:37:59 [info] index finished after resolve  [object Object] 
2025-04-29 21:37:59 [info] refresh page data from resolve listeners 0 858   
2025-04-29 21:38:01 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 21:38:01 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 21:38:01 [info] index finished after resolve  [object Object] 
2025-04-29 21:38:01 [info] refresh page data from resolve listeners 0 858   
2025-04-29 21:38:03 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 21:38:03 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 21:38:03 [info] index finished after resolve  [object Object] 
2025-04-29 21:38:03 [info] refresh page data from resolve listeners 0 858   
2025-04-29 21:38:06 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 21:38:06 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 21:38:06 [info] index finished after resolve  [object Object] 
2025-04-29 21:38:06 [info] refresh page data from resolve listeners 0 858   
2025-04-29 21:38:16 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 21:38:16 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 21:38:16 [info] index finished after resolve  [object Object] 
2025-04-29 21:38:16 [info] refresh page data from resolve listeners 0 858   
2025-04-29 21:38:20 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 21:38:20 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 21:38:20 [info] index finished after resolve  [object Object] 
2025-04-29 21:38:20 [info] refresh page data from resolve listeners 0 858   
2025-04-29 21:38:25 [info] ignore file modify evnet 学习库/Anki/Deep learning/pytorch.md   
2025-04-29 21:38:25 [info] trigger 学习库/Anki/Deep learning/pytorch.md resolve  [object Object] 
2025-04-29 21:38:25 [info] index finished after resolve  [object Object] 
2025-04-29 21:38:25 [info] refresh page data from resolve listeners 0 858   
2025-04-29 21:38:48 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 21:38:48 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 21:38:48 [info] index finished after resolve  [object Object] 
2025-04-29 21:38:48 [info] refresh page data from resolve listeners 0 858   
2025-04-29 21:38:51 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 21:38:51 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 21:38:51 [info] index finished after resolve  [object Object] 
2025-04-29 21:38:51 [info] refresh page data from resolve listeners 0 858   
2025-04-29 21:38:53 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 21:38:53 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 21:38:53 [info] index finished after resolve  [object Object] 
2025-04-29 21:38:53 [info] refresh page data from resolve listeners 0 858   
2025-04-29 21:39:46 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 21:39:46 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 21:39:46 [info] index finished after resolve  [object Object] 
2025-04-29 21:39:46 [info] refresh page data from resolve listeners 0 858   
2025-04-29 21:41:41 [info] ignore file modify evnet 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md   
2025-04-29 21:41:41 [info] trigger 学习库/Deep learning/pytorch/4. 使用pytorch实现线性模型.md resolve  [object Object] 
2025-04-29 21:41:41 [info] index finished after resolve  [object Object] 
2025-04-29 21:41:41 [info] refresh page data from resolve listeners 0 858   
2025-04-29 21:42:08 [info] trigger 学习库/Deep learning/pytorch/PDF/test.py resolve  [object Object] 
2025-04-29 21:42:08 [info] index finished after resolve  [object Object] 
2025-04-29 21:42:08 [info] refresh page data from modify listeners 0 858   
2025-04-29 21:42:24 [info] trigger 学习库/Deep learning/pytorch/PDF/test.py resolve  [object Object] 
2025-04-29 21:42:24 [info] index finished after resolve  [object Object] 
2025-04-29 21:42:24 [info] refresh page data from modify listeners 0 858   
2025-04-29 21:44:30 [info] trigger 学习库/Deep learning/pytorch/PDF/test.py resolve  [object Object] 
2025-04-29 21:44:30 [info] index finished after resolve  [object Object] 
2025-04-29 21:44:30 [info] refresh page data from modify listeners 0 858   
2025-04-29 21:45:53 [info] indexing created file 学习库/Deep learning/pytorch/未命名.md  [object Object] 
2025-04-29 21:45:53 [info] indexing created ignore file 学习库/Deep learning/pytorch/未命名.md   
2025-04-29 21:45:53 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-04-29 21:45:53 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-04-29 21:45:53 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-04-29 21:45:53 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-04-29 21:45:53 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-04-29 21:45:53 [info] trigger 学习库/Deep learning/pytorch/未命名.md resolve  [object Object] 
2025-04-29 21:45:53 [info] index finished after resolve  [object Object] 
2025-04-29 21:45:53 [info] refresh page data from resolve listeners 0 859   
2025-04-29 21:46:31 [info] refresh page data from rename listeners 0 859   
2025-04-29 21:46:31 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-04-29 21:46:31 [info] trigger 文献库/文献库/舌诊/Automatic Classification Framework of Tongue Feature Based on Convolutional Neural Networks.md resolve  [object Object] 
2025-04-29 21:46:31 [info] trigger 文献库/文献库/舌诊/Multi-Task Joint Learning Model for Segmenting and Classifying Tongue Images Using a Deep Neural Network.md resolve  [object Object] 
2025-04-29 21:46:31 [info] trigger 文献库/文献库/舌诊/TongueMobile- automated tongue segmentation and diagnosis on smartphones.md resolve  [object Object] 
2025-04-29 21:46:31 [info] trigger 文献库/文献库/舌诊/综述/Research and application of tongue and face diagnosis based on deep learning.md resolve  [object Object] 
2025-04-29 21:53:27 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-29 21:53:27 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-29 21:53:27 [info] index finished after resolve  [object Object] 
2025-04-29 21:53:27 [info] refresh page data from resolve listeners 0 859   
2025-04-29 21:53:47 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-29 21:53:47 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-29 21:53:47 [info] index finished after resolve  [object Object] 
2025-04-29 21:53:47 [info] refresh page data from resolve listeners 0 859   
2025-04-29 21:53:52 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-29 21:53:52 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-29 21:53:52 [info] index finished after resolve  [object Object] 
2025-04-29 21:53:52 [info] refresh page data from resolve listeners 0 859   
2025-04-29 21:54:32 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-29 21:54:32 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-29 21:54:32 [info] index finished after resolve  [object Object] 
2025-04-29 21:54:32 [info] refresh page data from resolve listeners 0 859   
2025-04-29 21:54:36 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-29 21:54:36 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-29 21:54:36 [info] index finished after resolve  [object Object] 
2025-04-29 21:54:36 [info] refresh page data from resolve listeners 0 859   
2025-04-29 21:54:40 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-29 21:54:40 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-29 21:54:40 [info] index finished after resolve  [object Object] 
2025-04-29 21:54:40 [info] refresh page data from resolve listeners 0 859   
2025-04-29 21:57:30 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-29 21:57:30 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-29 21:57:30 [info] index finished after resolve  [object Object] 
2025-04-29 21:57:30 [info] refresh page data from resolve listeners 0 859   
2025-04-29 21:57:39 [info] ignore file modify evnet 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md   
2025-04-29 21:57:39 [info] trigger 学习库/Deep learning/pytorch/5. 逻辑回归（Logisitic Regression).md resolve  [object Object] 
2025-04-29 21:57:39 [info] index finished after resolve  [object Object] 
2025-04-29 21:57:39 [info] refresh page data from resolve listeners 0 859   
