---
tags:
  - 学习
  - deep_learning
  - pytorch
---
# 多分类的情况

```ad-col2
title: sigmoid 处理多分类问题
color:178,22,164
如图所示，使用 [[激活函数#Sigmoid|Sigmoid 函数]] 处理多分类（图中为10分类）问题时，虽然也能输出概率，但每个类别的输出都是独立的。每个输出值表示该样本属于对应类别的概率。由于每个输出都是独立计算的，因此它们之间没有相互影响。
比如：对于一个样本，可能会输出 `0.8` 的概率属于类别 1，`0.7` 的概率属于类别 2，而这两个概率的和可能大于 1。这种情况下，模型**无法保证所有类别的概率和为 1**。
![[8. 多分类问题 (Softmax Classifier)-2025-06-18-09-35-32.png]]
```

```ad-col2
title: softmax 处理多分类问题
color:178,22,164
如图所示，使用 [[激活函数#softmax|Softmax 函数]] 函数处理多分类问题时，所有类别的输出是相互关联的。每个输出值表示该样本属于对应类别的概率，并且**所有输出值的和为 1**。这意味着如果一个类别的概率增加，其他类别的概率就会相应减少。
![[8. 多分类问题 (Softmax Classifier)-2025-06-18-10-12-17.png]]
```

## 计算多分类问题的概率分布

如下图所示，假设最后一个线性层输出的三个值分别为 $0.2，0.1，-0.1$，[[激活函数#softmax|Softmax 函数]] 会首先计算每个输出的指数值，分别为 $exp(0.2)=1.22$, $exp(0.1)=1.11$, $exp(-0.1)=0.90$，然后将这些指数值归一化（计算它们的和，并用它们分别除以和），使它们的和为 1。这样就得到了每个类别的概率分布。
![[8. 多分类问题 (Softmax Classifier)-2025-06-18-10-31-59.png]]


### 使用NumPy 计算交叉熵损失

因为这是一个多分类问题，所以使用 [[5. 逻辑回归（Logisitic Regression)#交叉熵损失函数（Cross-Entropy Loss）|多类分类交叉熵损失函数（Categorical Cross-Entropy Loss]] 来计算损失。
下图中，经过独热编码后，真实标签为 $[1, 0, 0]$，表示样本属于类别 1。模型预测的概率分布为 $[0.38, 0.34, 0.28]$。使用交叉熵损失函数计算损失时，只考虑真实标签对应的类别的概率，即 $-\log(0.38)$，得到损失值为 $0.966$。

![[8. 多分类问题 (Softmax Classifier)-2025-06-18-11-22-04.png]]

```python
# 使用 NumPy 计算交叉熵损失，这里的 y_pred 是 softmax 输出的概率分布
import numpy as np
y = np.array([1, 0, 0])  # 真实标签
y_pred = np.array([0.38, 0.34, 0.28])  # 模型预测的概率分布
loss = -np.sum(y * np.log(y_pred + 1e-9))  # 计算交叉熵损失，加上一个小常数以避免对数为零的情况
print(loss)  # 输出: 0.966
```

### 使用PyTorch 计算交叉熵损失

在 PyTorch 中，计算多分类问题的交叉熵损失时，通常使用 `CrossEntropyLoss` 函数。这个函数会自动应用 softmax 函数，因此我们需要传入模型的原始输出（logits），而不是经过 softmax 处理的概率分布。
如下图所示，模型输出的 logits 为 $[0.2, 0.1, -0.1]$，真实标签为类别 1（索引为 0）。使用 `CrossEntropyLoss` 函数计算损失时，PyTorch 会自动应用 softmax 函数，并计算交叉熵损失。得到的损失

![[8. 多分类问题 (Softmax Classifier)-2025-06-18-15-20-52.png]]

```python
# 使用 PyTorch 计算交叉熵损失，要使用原始的 logits 而不是 softmax 输出，pytorch 会自动应用 softmax
# 这里的 logits 是模型输出的未归一化的分数
import torch
logits = torch.tensor([[0.2, 0.1, -0.1]])  # 模型输出的 logits
labels = torch.tensor([0])  # 真实标签，索引0表示类别 1
criterion = torch.nn.CrossEntropyLoss()  # 定义交叉熵损失函数
loss = criterion(logits, labels)  # 计算损失
print(loss.item())  # 输出: 0.973
```

```python
import torch
import torch.nn.functional as F

# 设置数据
criterion = torch.nn.CrossEntropyLoss()
Y = torch.LongTensor([2, 0, 1]) #真实标签，类别索引从0开始，样本1属于类别2，样本2属于类别0，样本3属于类别1
# 模型预测的 logits（未经过 softmax 处理）
# 注意：这里的 logits 是模型输出的未归一化的分数

# 下面可以理解为batch_size=3的情况
Y_pred1 = torch.Tensor([[0.1, 0.2, 0.9], # 模型对第一个输入样本对各类别的预测分数（logits），类别0的分数为0.1，类别1的分数为0.2，类别2的分数为0.9
                        [1.1, 0.1, 0.2], # 模型对第二个样本对各类别的预测分数（logits），类别0的分数为1.1，类别1的分数为0.1，类别2的分数为0.2
                        [0.2, 2.1, 0.1]]) # 模型对第三个样本对各类别的预测分数（logits），类别0的分数为0.2，类别1的分数为2.1，类别2的分数为0.1
Y_pred2 = torch.Tensor([[0.8, 0.2, 0.3], # 模型对第一个样本对各类别的预测分数（logits），类别0的分数为0.8，类别1的分数为0.2，类别2的分数为0.3
                        [0.2, 0.3, 0.5], # 模型对第二个样本对各类别的预测分数（logits ），类别0的分数为0.2，类别1的分数为0.3，类别2的分数为0.5
                        [0.2, 0.2, 0.5]]) # 模型对第三个样本对各类别的预测分数（logits），类别0的分数为0.2，类别1的分数为0.2，类别2的分数为0.5

# 计算损失
"""
对于 Y_pred1:
- 样本1：真实类别是2，经过softmax得到的预测概率分布为 [0.26, 0.25, 0.51]，真实类别的概率为 0.51，损失为 -log(0.51) = 0.6657
- 样本2：真实类别是0，经过softmax得到的预测概率分布为 [0.56, 0.20, 0.24]，真实类别的概率为 0.56，损失为 -log(0.56) = 0.5735
- 样本3：真实类别是1，经过softmax得到的预测概率分布为 [0.20, 0.77, 0.10]，真实类别的概率为 0.77，损失为 -log(0.77) = 0.2507
cross_entropy_loss = (0.6657 + 0.5735 + 0.2507) / 3 = 0.4966
"""
l1 = criterion(Y_pred1, Y) 

l2 = criterion(Y_pred2, Y)

print("=== 损失比较 ===")
print(f"Y_pred1 损失: {l1.item():.4f}") # 0.4966
print(f"Y_pred2 损失: {l2.item():.4f}") # 1.2389
```

### `NLLLoss` 和 `CrossEntropyLoss` 的区别

CrossEntropyLoss：
- 输入：原始logits（神经网络直接输出）
- 自动处理softmax转换

NLLLoss：
- 输入：log概率（需要先手动计算log_softmax）
- 直接计算负对数似然

它们之间的数学关系: `CrossEntropyLoss(logits, Y) = NLLLoss(log_softmax(logits), Y)`


```python
import torch
import torch.nn as nn
import torch.nn.functional as F

Y = torch.LongTensor([2, 0, 1])  # 真实标签
logits = torch.Tensor([[0.1, 0.2, 0.9],
                       [1.1, 0.1, 0.2], 
                       [0.2, 2.1, 0.1]])

# CrossEntropyLoss (一步到位)
loss1 = nn.CrossEntropyLoss()(logits, Y)  # 自动应用softmax并计算交叉熵损失
print("CrossEntropyLoss 结果:", loss1.item()) # 0.4966

# NLLLoss (需要两步)
log_probs = F.log_softmax(logits, dim=1)  # 第1步：手动log_softmax，dim=1表示对每一行进行softmax
loss2 = nn.NLLLoss()(log_probs, Y)   # 第2步：计算NLL
print("NLLLoss 结果:", loss2.item()) # 0.4966
```

## Minist 数据集的多分类问题

### 图像的变换

```ad-col2
title: 图像的转换
color:178,22,164
一张图片，有单通道（灰度图像）和三通道（RGB图像）两种情况。使用pil或者opencv读取到的图像的格式为`H * W * C`，其中`H`是图像的高度，`W`是图像的宽度，`C`是通道数（单通道为1，三通道为3）。在处理图像数据时，通常需要将其转换为PyTorch张量，并进行归一化处理，也就是将图像的格式变更为`C * H * W`，并将其像素值从0-255范围转换为0-1范围。
![[8. 多分类问题 (Softmax Classifier)-2025-06-18-17-12-30.png]]
```

```ad-col2
title: 输入图像的映射
color:178,22,164
如图所示，==输入模型的图像==的每个像素点都可以看作一个特征。对于 MNIST 数据集中的手写数字图像，每个图像是一个 28x28 的灰度图像，因此每个图像有 784 个特征（28*28=784）。每个像素的值在0到1之间，表示灰度值。
![[8. 多分类问题 (Softmax Classifier)-2025-06-18-16-47-41.png]]
```

#### 张量形状变换

如下图所示
输入：形状为 (N, 1, 28, 28) 的一批图像数据。
view 操作（展平）：对每张图片进行操作，将其 1x28x28 的结构“拉直”成一个 784 维的向量。
输出：得到一个形状为 (N, 784) 的张量。这个输出的每一行都是一个样本（一张图片），可以直接作为输入，送入一个输入神经元数量为 784 的全连接层中，进行下一步的分类计算。
简单来说，view 操作就像是把一张 28x28 的方格纸，从第一行开始，一行一行地首尾相连，拼接成一条包含全部 784 个格子的长纸条。这个操作是连接计算机视觉中的特征提取部分和传统神经网络分类部分的关键桥梁。

![[8. 多分类问题 (Softmax Classifier)-2025-06-18-17-33-09.png]]



```python
import torch

print("=== 张量形状变换演示 ===")

# 模拟MNIST数据：批次大小为3的28x28灰度图像
N = 3  # 批次大小
batch_images = torch.randn(N, 1, 28, 28) # 随机生成的图像数据，形状为 (N, C, H, W)

print(f"原始形状: {batch_images.shape}") # (3, 1, 28, 28)
print(f"含义: ({N}个样本, 1个通道, 28高度, 28宽度)")

# 计算总的像素数
total_pixels = 1 * 28 * 28
print(f"\n每张图像的像素总数: 1 × 28 × 28 = {total_pixels}") # 784

# 使用view(-1, 784)进行形状变换
reshaped = batch_images.view(-1, 784)
print(f"\n变换后形状: {reshaped.shape}") # (3, 784)
print(f"含义: ({N}个样本, 每个样本{total_pixels}个特征)")

print("\n=== view(-1, 784) 详解 ===")
print("参数说明:")
print("- 第一个参数 -1: 让PyTorch自动计算这个维度的大小")
print("- 第二个参数 784: 指定第二个维度为784")
print()
print("计算过程:")
print(f"- 原始总元素数: {N} × 1 × 28 × 28 = {N * 1 * 28 * 28}")
print(f"- 新形状总元素数: ? × 784")
print(f"- 自动计算: ? = {N * 1 * 28 * 28} ÷ 784 = {N}")
print(f"- 所以最终形状: ({N}, 784)") # 可以自动计算出最终的形状为 (3, 784)
```

#### 神经网络架构

 🔍 **数据流转过程**：
 
1. **输入阶段** (N,1,28,28)
    - 输入是28×28的灰度图像
    - N表示批次大小
2. **数据预处理** (N,784)
    - 使用`x.view(-1, 784)`将2D图像展平为1D向量
    - 784 = 28×28个像素值
3. **特征提取层次**
    - **第1层**: 784 → 512 + ReLU (提取基础特征)
    - **第2层**: 512 → 256 + ReLU (特征组合)
    - **第3层**: 256 → 128 + ReLU (高级特征)
    - **第4层**: 128 → 64 + ReLU (抽象特征)
    - **输出层**: 64 → 10 (分类分数)
4. **输出处理**
    - 得到10个logits(原始分数)
    - 通过Softmax转换为概率分布
    - 使用ArgMax得到最终预测类别

🧠 **关键设计理念**：

1. **逐层降维**: 784→512→256→128→64→10
    - 逐步压缩和抽象特征
    - 从像素级特征到语义级特征
2. **ReLU激活函数**
    - 增加非线性表达能力
    - 防止梯度消失
    - 计算效率高

![[8. 多分类问题 (Softmax Classifier)-2025-06-18-17-46-45.png]]


### 使用 PyTorch 实现 MNIST 数据集的多分类问题的完整流程

```python
import torch
import torch.nn as nn
# 用于数据的加载
from torchvision import datasets, transforms
from torch.utils.data import DataLoader
# 使用激活函数
import torch.nn.functional as F 
# 优化器
import torch.optim as optim

# 1. 数据准备
batch_size = 64

transform = transforms.Compose([
    # compose方法将多个变换组合在一起
    transforms.ToTensor(),  # 将PIL图像转变为pytorch张量
    transforms.Normalize((0.1307,), (0.3081,))  # 归一化处理，像素值归一化到[0, 1]，均值为0.1307，标准差为0.3081
])
## 下载 MNIST 数据集
train_dataset = datasets.MNIST(root='./data', train=True, download=True, transform=transform)
train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)

test_dataset = datasets.MNIST(root='./data', train=False, download=True, transform=transform)
test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

# 2. 网络定义
class Net(nn.Module):
    def __init__(self):
        super(Net, self).__init__()
        self.fc1 = nn.Linear(784, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, 64)
        self.fc5 = nn.Linear(64, 10)

    def forward(self, x):
        x = x.view(-1, 784)  # 展平
        x = F.relu(self.fc1(x))
        x = F.relu(self.fc2(x))
        x = F.relu(self.fc3(x))
        x = F.relu(self.fc4(x))
        x = self.fc5(x) # 最后一层不做激活
        return x

model = Net()

# 3. 设置损失函数和优化器
criterion = nn.CrossEntropyLoss()
optimizer = optim.Adam(model.parameters(), lr=0.001)

# 4. 训练和验证
def train(epoch):
    running_loss = 0.0
    for batch_index, data in enumerate(train_loader):
        # 从train_loader中获取数据
        inputs, target = data
        optimizer.zero_grad()

        # forward + backward + optimize
        outputs = model(inputs)
        loss = criterion(outputs, target)
        loss.backward()
        optimizer.step()

        running_loss += loss.item()
        if batch_index % 100 == 99:  # 每100个batch输出一次
            print(f"Batch [{batch_index+1}], Loss: {running_loss/100:.4f}")
            running_loss = 0.0

def val():
    correct = 0
    total = 0
    with torch.no_grad():
        for data in test_loader:
            # 从test_loader中获取数据
            inputs, target = data
            outputs = model(inputs)
            _, predicted = torch.max(outputs.data, dim=1) # 获取预测结果，其中会返回最大值和对应的索引，这里我们只需要索引，而最大值不需要所以留空
            total += target.size(0) # 统计总样本数
            correct += (predicted == target).sum().item() # 张量之间的比较，统计正确预测的样本数
    print(f"Validation Accuracy: {100 * correct / total:.2f}%")

if __name__ == "__main__":
    for epoch in range(10):
        train(epoch)
        val()
