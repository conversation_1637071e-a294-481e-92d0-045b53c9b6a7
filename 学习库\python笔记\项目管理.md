---
tags:
  - python
  - 学习
---

# 使用`UV`管理Python项目

`UV`是一个轻量级的Python项目管理工具，集成了从安装python到创建虚拟环境、管理依赖、安装工具、以及最终打包发布的所有流程。以下是如何使用`UV`来管理Python项目的基本步骤。

---

## 安装`UV`

可以根据官方的提示来安装`UV`。通常可以通过以下命令来安装：

> https://docs.astral.sh/uv/getting-started/installation/

```bash
# 使用 powershell 安装
# 如果你在Windows上，可以使用以下命令安装UV：
powershell -ExecutionPolicy ByPass -c "irm https://astral.sh/uv/install.ps1 | iex"

# 使用 pip 安装
pip install uv
```

## 创建新项目

创建新项目，使用`uv init <project dir>`命令，创建之后会生成一个基本的项目结构。

```bash
PS G:\> uv init uv_test
Initialized project `uv-test` at `G:\uv_test`

PS G:\uv_test> ls


    目录: G:\uv_test


Mode                 LastWriteTime         Length Name
----                 -------------         ------ ----
-a----         2025/7/14     15:07            109 .gitignore
-a----         2025/7/14     15:07              5 .python-version
-a----         2025/7/14     15:07             85 main.py
-a----         2025/7/14     15:07            153 pyproject.toml
-a----         2025/7/14     15:07              0 README.md
```

可以看出 `uv` 默认使用 `git` 来管理项目，会自动生成一些文件：

- `pyproject.toml`：这是Python项目的配置文件，包含了项目的依赖、版本等信息。
- `.gitignore`：这是Git忽略文件的配置，指定了哪些文件或目录不应该被Git跟踪。
- `.python-version`：这是Python版本管理文件，指定了项目使用的Python版本。
- `main.py`：这是项目的主文件，通常是入口点。
- `README.md`：这是项目的说明文件，用于描述项目的功能和使用方法。

## 操作环境

创建完项目以后，进入项目根目录的第一件事就是同步项目依赖

```bash
PS G:\uv_test> uv sync
Using CPython 3.11.0 interpreter at: C:\Users\<USER>\.conda\envs\yolov8\python.exe
Creating virtual environment at: .venv
Resolved 1 package in 10ms
Audited in 0.02ms
PS G:\uv_test> ls


    目录: G:\uv_test


Mode                 LastWriteTime         Length Name
----                 -------------         ------ ----
d-----         2025/7/14     15:16                .venv
-a----         2025/7/14     15:07            109 .gitignore
-a----         2025/7/14     15:07              5 .python-version
-a----         2025/7/14     15:07             85 main.py
-a----         2025/7/14     15:07            153 pyproject.toml
-a----         2025/7/14     15:07              0 README.md
-a----         2025/7/14     15:16            127 uv.lock

PS G:\uv_test> cat .\uv.lock
version = 1
revision = 2
requires-python = ">=3.11"

[[package]]
name = "uv-test"
version = "0.1.0"
source = { virtual = "." }

``` 

这将会创建一个虚拟环境，并安装项目所需的依赖。`uv sync`命令会自动读取`pyproject.toml`文件中的依赖信息，构建完整的依赖列表并写入 `uv.lock` 文件中。

同步之后，可以使用`uv`命令来运行代码，比如：`uv run main.py`，这将会在虚拟环境中运行`main.py`文件。

## 管理依赖

可以使用`uv add`命令来添加依赖，比如：

```bash
uv add flask

# 这将会添加`flask`依赖到项目中，并更新`pyproject.toml`和`uv.lock`文件。

# 在 main.py 中使用 flask
PS G:\uv_test> cat main.py
import flask

app = flask.Flask(__name__)

@app.route('/')
def home():
    return "Welcome to the Flask App!"

if __name__ == "__main__":
    app.run(debug=True)

    PS G:\uv_test> uv run main.py
 * Serving Flask app 'main'
 * Debug mode: on
WARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.
 * Running on http://127.0.0.1:5000
Press CTRL+C to quit
 * Restarting with stat
 * Debugger is active!
 * Debugger PIN: 862-491-324
 
```