---

excalidraw-plugin: parsed
tags: [excalidraw]

---
==⚠  Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠== You can decompress Drawing data with the command palette: 'Decompress current Excalidraw file'. For more info check in plugin settings under 'Saving'


# Excalidraw Data

## Text Elements
%%
## Drawing
```compressed-json
N4KAkARALgngDgUwgLgAQQQDwMYEMA2AlgCYBOuA7hADTgQBuCpAzoQPYB2KqATLZMzYBXUtiRoIACyhQ4zZAHoFAc0JRJQgEYA6bGwC2CgF7N6hbEcK4OCtptbErHALRY8RMpWdx8Q1TdIEfARcZgRmBShcZQUebQBObQBGGjoghH0EDihmbgBtcDBQMBKIEm4IAHUASQBFAFUAOQBHAA0DUmaAdgBRBB6jAGFWgAZlIVSSyFhECoAzQIRPKn5S

zG4eeK7tAGZ4/fidgA54gDYAFiPz89XIGG4ko4BWBK6nkZHTpJGdp6Sn058QqQCgkdTcRKnD4fL4/P4AnhPW5SBCEZTSbgvKHQ2G/f6ApHAiDWZTBbgjZHMKCkNgAawQgzY+DYpAqAGIkghOZzJqVNLhsLTlDShBxiIzmayJNTrMw4LhAtleZA5oR8PgAMqwMkSQQeZUQKk0+mVMGSDaU6l0hBamA69B68rIkXojjhXJoJLItjy7Bqe6ej7I4XCO

DVYge1B5AC6yIWuEy4e4HCE6uRhDFWAquBGBpFYrdzEjKbTRLCS24XR2SR4IyOp32yMYLHYXDQOybTFYnEanDED2+iPiTyrHaJhGYABF0lAK2g5gQwsjNMIxT1gplssXU/hkUI4MRcLPiA8uudTn8ul1TkceA3kUQOLTkzuH2xBXPUAv8EuiXA2BmOT5MCYAFFMJQUiBYAjCBsYgWB4GQtCnzfHiCKEuBYBYshuLwgSsHAnBUwQHAgRFiI4T5ERp

SsPoqZHggAAKZHMBR3DUkICAPqEUCMvo+hqMejEAUqaAISUSE4qheGIrcWHaNiMLSfiiKEYUAC+qzFKU5QSEYCBGEIkhzJIACCXTVEIjGMQA4gAKs0hAAGLGRMyIzOI6ALAgSzkCsRLrO2IxdMiAaoEk8RJNoI7vDWtZdD8NYYaUoLEOCaBQto9anDeexPPsULxDcRKSKi6JQBCLwJQSOwjJeI5HF6RIkvakHEUa1oSiy7LclySDLgKQr5uKTLdd

K5AcHKCpZBVcZqpq2qeYaTJOmWVommaFprcaNqLRUjons6wiuu6Dzer6/oPEGRIhvu4aRjGcbkImJ5oCWu7jpmgXoLgKRHaKxCFtupbtT5r28KcXQ1tWpxjsRzbdm2qBw6UCOtr2HD9p65x3iMmw7CjkATtOwTHtw36/sRK4A+uGQzcDH3Efuh5k56Z4XkkV5Qz8pwPhmz5va+RLMh+4MU1xf4iUBYnwVBbXgTB4HUSU4klJl2W5cOBUjEVclgM4

w7aNViK1fVXSNQRUzKyRLEUQ91u0fRs7Me6bFoBxEvEfgPF8QJMhLMJgHAeB6s5Zr+VnDrxXgQbVWfCbdWcw1SRqSUmmFNpRPg4aUDYH4yj7AAVpzSTMDAAAyXStAASoQACabAGh58yLMsBrfTskNZWHRx5drutEmFhv1kcbyRU8Tw8FW1zIql6WoJPuwE9WmxHLejWySVZUYmgPA7EvBPnL8dXPIcbzIi1nnywI60MqNUroByfU8gNgq3WKXUP9

AE1TYqs1EqqdUtp7TLX1JaHapo0rml3uA60wCloHTzMdSQQMzpEh9AKS6gZr4QFumGCMVEnoJgQEmQWIMdJfWzDwJBANUFkMZjRMG3ALiQ0hk8c4IUiRo04NwQmDAuzoz7J5DhOUjg605umKcM5Pzi2XKuYgtNNzS1QO9PcB4GLg05ueS8V58R8MfALFRQsvbvnpGLRcntSj/kAg9WWmFr4QVgnJVWWE4jLxXvENePAN7JSmHvA+Owj7vCeKfHYb

xLYlGtqRV2ZFCFlkIHRb2ztbaBHYqQTi3EqS+0EgHKWwcpiL3cTWTx68kpyX8e4oJJ98phKeKnMA6cSiZzKNnAA+tgAA0k5KAIw66Fw1HASQpB2gAFkRgAClez2CbvAJa3lfKUHbtwHGIxkgHDOEVW8OMgTETCkkCK2haw8EHDlfZOxvFNWInPaB4V96VOPiEmp58t5oh3uFXmzUOCkivrA+kn8erP36kSfkb9hr/PGrKeUf8DSAIWnaBBK1DrbW

tJA+eOyaK33gftRFNCTpFjQcRDBfpYBXRwXg+6cTiLxhei+chRNKESFwDsGhBZTr0MpEwtAhxHjDgSpw+GAieHtk7C2HsQiNgHDvIcSRJMECsy/BYuRNMNz01pQwyAzMNGnm0ZzK8tZfh8yfGqt8otyaKsljY/JKs5Z60VlbZxUEkh3OXlUx5Z9fElCSKcCJYAokpMomJB2CSnZMX9WkjJwsfYGD9kJPJMtwJOoCa60JzyE3eqthpLS45s4ADUWj

OGrqccutJC6nDspIJyAB5ScMAbJJFqM4SoMzZgSHmW3ZE30j77yeC6kY5x2HfHxKFB4vxDl4xOV8J13jZ6bTQFiMOAJzhFSSOceOviUSvIqu2KKYcLwXGXaugkF8vmtV+XfSUALeoGhBUNeR4L0AykmlCmaML5pYt1Dis9qKbnopvjtd9DpP1EhdCgtl4VzqYJJdg4MIp8H2yITS9ln1iBZkZecFlgMwOqLLJy8Ke98q9x4FPEViNeEkcEZjTytY

PieP2FWGV0jzE/ksZAama4VVbmNUSTV8qtEcy5kR6shrDHYZMaa+c5riLWM4/GqYLiHHQScXYmO86cqLoPWuvW1YFILv3ecFda6fV+piQGqMQbEkMRduRVJ7t0ksYgN7LJ0acnEEDqJKMIFnCqb3Uu/Th7N7gW07u9TfnDMZrTlm4iul0C0g4F0fA9QRhzEaLUKA1cRnlw4I0AZbBKinDmM2uZrc/JLLQDjbYGte5a0jgPXZDwcrd1Hvlf4k9p7R

xSrO8Kqz7nBJTeu0qm7KyrN3RcaGPa/gfOIpfckZ770QCfle1+t6AZzcfb/F9c0gF7Q/WA5FG0oFbXapi7bgHdvERA3Q8D6CLpQa62S2DFLA0IZIeDUTFCUPfWJE8DDl23sCFw16r13KvV8O4UjUHgqsviuxlWCKlw6oMdJjIyTfJ5GKNVUhpm6jePsx0VWCe/LSgGK42JsxZrmPImk8o1W8nbVKfAi45w3wk0PL63rZwCUdNh30/hvE6bwuRMp/

6+D8SLPJJM+G+zjneLOf9q5uNHmY7M569U91ckOfDd3Tz34fP6mNKKNmio5dNCDCcqZQYcwNQUEaCMAAEoMDgtQYDOEIIXHg+hCstzBiVjtyzObRV7f2/TIwh2DwhKcMdtZhyJSrOvGdB3d47t075gzR6XnlQ2OcbQyFgqAkOEVHtx7vkzb2+esaj9eov2BYNd+I0L0QqfdNJUm24UgMQV+zrv6c7/pO6A1a53kGXcuaUIlWC7swdDI9szz3SFGL

pWUBlP1Tg/aw8Yxhn5axBP2ZO8jQrka76h5Rq6jwQ99sL+OKRSOmOU1R8qumMm5/qogDxz8fG8f6YSsJknRPTHI4pxah/GnG1OWenOTKCY5LnHzDTNPTCHgLPHPa8fGAvHYIzIXEzEXdqYNJJUNCXWzCNL2KNfiFzNzLdRXPxJPNTPTVPALPxeA5CRA/PftFAgXfXZpaLCAHNHNeoWkLoRoGAZgauDUSQLocgeIOyEZWkRiOYXIdyWZL3BZfyYiT

tPYNZA4BsS4OAojYdT0WsSPCdM5C5ePeeR4FnXrJ5frbeUg/xYLKg/zddabNAHBDqP5e+S9Pqa9GvMFVwhvdbZvABN9XvdvUvb9Q7DFHveFbFM7UoC7MDYfSAUfW7b4e7SfAhJ7ABZ6F7b/elD7bMLoFffFTHdfTRbxMRKGftOI/hUVcHA/DGLGd5EPDhH4GgomS/OVP/G/VjNHDjZRP7Z/bHV/XHXVdmZ4L/QoyAEWMnCTf/KTKWWxBnYA+xUA6

1TCEwlXN1WpPWaw5PaA1SJWQiNA6zUzR6UXENKzViGzVAD2TJGXIguXEgq1MAVYl1Vncw8pfeGwlPOwvXSLHSbOJ4QYW3WkVzCgTQUyWkGAGAcZUgEZOuTQcUQYegT3VtYrRZX3MrEcAPQ+M/YPUPOrbGfeEeMeFrKeQJdrEETrLPNYtndPN5bYXdKGN4TYTYG8VNUoBw1AJw2+ObBbdwpbWvVbH+Z9PwqlAIiInbfvMIlFTvM9ADPvJFAfPwUDA

oq7QlG7PZa6YiclVI6fdI4hWfXojMHIxlI4fIhmDlV/R4LoAvY5CosHMjLhSHWozyJIhks4e8C/WVeVWRYFLo+/HotfDVfozRQYrmS4d0r2fmLIhzX/a/ezKnOYsA+xOnJWB1TCSk54swtXKCOksOBkyeeIZkprVAv8YXSlGiLAyzMNPAqXQgmNXJIOWTEodMw+F4rM8CHMnKPMpks4Islgn4rOCoZoU0XAJyEZJ4Lg4gcuAAISOAAC1ag5hzghA

oBTJ8AkSvIUTFC1hlkvhu5RE+4asySIA9k/gA92Fap4g6x9UawjCblvMQtqCLDBtd5utl5vE4C+VEou92TOSdpuTK8gUqZPC71vCH1BSm9/4RStsxTTsJS/0pSE9eAZTAigMFS8VIwKiEj1Tki7ptTjiqUMj9SAyF8jSfp4hTSozyxiij4Ip/h/gD97SBUqinSHgCz8ZO4RiPTGNycOiIA2MFFuizTuMgztV+Mrxgl9FIyxjozxMFVpirFZiHjac

QCUzlMClICHy7DNjXyCZ3zGiEoV5iypNSy0jMCxccDDjJdrjsk7iFdVZ7zbDNMQJdD3E9LPzDK+yM5DcJBMBMAjBag7JThlAnJaQRgEsbITAEBLyjgoB1hZCW0NzvdUSAodztge0sSg9B0ARtDwooZGsiSJ4SSZ4iRrkrpkQBsM9PQKifzZtQL5sAKPDQUQL68wLIUILX1oK29ULJT9s0VkKYK5TcUlSMKINiVsKJ9cKMDShqVMjpLDTUMfpTIKL

pKqLKxTYiNNh4gGLhUHTmLodcqqwug4C89Ec2jYylV2M/ShKscWYBidUuZJ44DRjH8TVJi5LeL4ylKFiFYljQIoImcFN9kjKrETKdSzLTiqzLi7NrLZdY0GyyCSgAa5IgbPKmlvL0BnAegNQ2BbJsBC5XNzgNRsBJxiBTh6gKBmAng4qiRm5kSkqtzIBO0MT0rAlsSsrJtSg9lbx8rmtCq2tbyR1TDVcNiaTSDnUWzMyRapsT0flS9/zAVGrlsP4

6q1shTILprRSuqoj4Leqf1+qta4KIAYjlTMK1TSUJq4MyyVRCLXtiL5rPtcApzlqXqcNX8TgdZY8/htr99drEYWK0AxFe4I5miyhWivSUdOi78lFrrSgX9gz7rxKwku9idpKJj2i4zFLGy/qkyVL7U1LEbE0qTXioJxbAlWzalgbIBolDipqBAKzxdLLqyYbbi4b3NVYmdS7k1i7Ashb1jwlUaDcots44AR79g2BahTIxDKhaQjhyBsAnghCoT1y

IA20fcUqytrhVCCpNlNCu8uaI8jl9Cp0jgBadDyrLDlkAk3Lgovz7CZaS8js/y6qeSq8gKmqVsVbwLoUW9ZSgjH6EK+rS9f7urIBjaRrrtINxqboHs8LrYZqiL597bsxBhnbejVrPRapRsrxR5vaIc9qj850iNV1jgvhTrw75LI7Lro6oy47RK8cJ5YZnrei07zqADqd87oJky875jMImdf0myr6tl3KkpK6bZ0CrbDR66LLzjPIrjI0nMW76y27

PMbyQJmzzkhGb6V46kB62Ds4khSBGhKgbJsBGJaRBhxlnBBgjhGJTJmAdgKBJwAJl7V7kqlDllzkt6NkNDtkcqIpVlxsj4dYryvzT7wos8PidinzKrkZICuyCyezWTIAaq5bn6Gq+SvCWrv42rv7/DOqEVtbu8AG9agGULCmwGCUR8zboNoGUja6V6baoykHGVJxUHiL0H3ldEe48HSMdqmK/b9qXSwl2YKjiZuKpjeL+L0cH9ejaG2YE7rw6wjy

U6XbSd07KdM6Ebs6FYuHBcOGmcIntjQsYCe76Sk5uyWSdGBdfUDiZH6nHZsCzi3Yob8CidaziC7KVHDnKDPinLTnczzmEnLnvivKh6KgOkRgc1SA5gjhlBxlWhqhmgOkeB0tvZbd+lWn4qit6bStUAP9MTWbMqQ9sqw9PQio9C8ZTlj6wmgdz7nzUBhwi9T1UmsmX7AK+RgKP6snVb2qf6ynDbnCEAQiYFSmBq/7ojB9YjRqx8kiLap98LprGm5r

F9iQeg2n58Onvg/gPgDMenWxGLUZHTBnJ59le5bwtquKr8eL7MpnBKaGRL5mxLsHiNhYpLVmf9ZLvSZjLUs7lLFjVKeGldwyY5hxRHq67mJGHnKzcCXmayFG6z5d4b27aXPNQ3dH0aIAnhGh6Bq5TInIip9Bq4YBJxq5aRSBJxlBaRq5TcXHNzcW7xtgIo1Cd7fHSXUBe4ebx5WtSSwnNcfmom6WYmTgmXZb/6XDWX0nq937lbuWv6Ns8nW8CmBX

b5hWkLRWDb5SJXFSh9pXEiNTSgtT6n4HbbEGVWRz1Wn8OnawN4V18NcGaj9qNqV5bxRmw71mfSo6Md3XAzbr46xL6x9lk63XmGYzrWNmfWtm/WfqA3EyY4+2oDjnmiSgTgw3QaFW67zKnmLi5GCD42Pmk3PN4PNK/mpgUP02wWJAhApzlANQOlBgdY4AKAJ6jBZzNBGJMAehzh6gZCaa5C6aFD62GsWbV0iXcTObTw4hCTebu3iqrlOsdKNGPytG

ylRady4nAXmSLXpbi9HDaqJ2FaMnmry9snG9cmoLF3Ijl2IFpT12l3N3QHJWTbd2oHNSYGj2lXv2SKFriQbIL3zTNF9lms2F73faKM6jHhvEQng2WjPT32qZfTqHpK5ncqE76x2F10VmQPPWI6SJNmgCc7/XuHYOpgFPr6DKVO0yI8znGSgX4hUPxHTLyzMPIacO3m8PbKCOFZBGlPyukPzx1OavNOQW0aKP0A1RqhTh8BGI64c0JlGJ3gYBaxgq

hAnhWQsX5D2116IYI8NYWSQ8S4tPxPE9EhAmLyQmV4wmcYBv8zNPB23kjhuv9LQnPkdOOS9PjO2XFb+TP6cn53zPgHCnBXV2u9BWAfDaKnPRnPzbanJqJHj2mmz3bc/PXbwYOZIpLzIYQv+mwvnTHgrhjkz8yG4vb8qGv3ZmHWUv/3IZPEmHiKWGwO2GEzlidnc69nA2pgrvqubuNk9YHvXLNHev6ua7I2pGsPZHob5GbiE37is7OeAXBueeQI+e

3yBevzhvB7fiKhuCnJJxGhBhK0EBzhZzWg5gKBzhNBGhcAc0oAeAjBa2cW0TkYdYCWROB1iWOa7gNgp5O3iT+aSrOt8oNLHKTnIAKq3l1Gyvb6R2H6eqy8v5PvDOuXjOeWzONb8nLP7OindbQidbdoxWQGjbHPwHVTIHofXO6m4ePODSz3qhkfQY3a15jhDhgosfDX8G6japngQ8tg+ExmrWJmbWEuyfiLku39dV6x9hJKjVU7QOB/wPACOGFM7U

2fiuShA/InEOPV9YI/VftGheI3GuMOIaY3Wvxj3mOvlHwJ1+jnHz1cd+evb71e9GKh6AOly494hBqgOANRBgNQeh9AOAmgPGqZA6QdJNA9vATo707iJAm229HxloTbaCYzyQTaKteS7ylVPQVXHuAlH2RQwmsd3UgvB3iaFkkmxIe+rpxZYfdJ2b9JWnXmT5zthSafCzuKUz5A8bOY7PPhuyGo7sIGY1Mvgezc6V89SJ7J/M0x+jjI6+RREdD2RX

QGpQue+PVmKgIa8B8oxyEJKQ0tZnUGe8XT9jMxH4U8x+2DBkh7wczAc6es/d6hnQg75cWehXFfszymBepICo8fbngKSaI0iBGnRJlc0iT7ESyDXMGk12P6N1Y2zdaXp8zTTOCcBJcfAYR2u4XNeyvgiLKC014SBaQOacuB0lMg9Akg1jZQLOQQBTly4MgZgB0mYBORq4EAzbu43bDPA9y4cfuEeTCgbUfefNHtv70QpOCikq8UpCHTD6kFIQN/LS

i92ZYcD5ai2KdrQIFK/dGBKoTWnZwNBsDEKIPY7Pn3KZF9Km8RapuPhh6W1D+DTYQQj1IrEgOkkg/7J+ABBXlrgR8VvpADBz+0UuY8c2P2iJ6sMdBpPPQfPlH4hlzYVYTHq62n6ed6ec/Rnl9QK7QciuDgz1BHm6ElILkSHfWIMP7ab99+dsEXs1xP4S9cOUvfDpf0cEwjl4xSLxD4nVxIiEOj5J/hm1nJGARgNvLoOXEqCTg4AZaWoIMHwA2R6g

zQXADAEYhVC16NQvFnjC8bqEtkCAvErwETSH1KWk6DRmE2iZvIXgRdNsmyXIFvdKB8fagRy2nZ0Cv4KfP7kwLB6sCV27A2PoaO4FSteBMrfdpAEPZCDEMnnMQcSHLhnDDQAOD4GhEuCKDqi8gw/B30eDnl9MvfN9m8JJ4CUrq9rX9nQ3H5vAOEtPefMCMsHz92G7PCCLsxuYcMt+XmXun1lRGxJ9hUbBujIysqS8bKrdUgvZXVyKiMywtfukkIaT

9kWkFQSoK0h2A9Adg1cNgK0CECnBs2dka4JWinL7hag2APkW423JlZgowoltmKKO68BJxUnLtkVSPIYDwoFBPbrgMtLyirCa44jiHzIGvdfynUNJgZ0mHfdZ2Mw9WnMPT4sDFhxo5YfrQWH/R0KmwiAFhX4E2jBB+w+Hsq2OG4ARkLojpiOERDQgXW2PBQQ+2UERQRwXwAspxSizBjtBoY6Zv6S+EGCfho8a8Id3GJmD4xFgr1gpWsGL80xysbZo

4LXEuCNxsQ3hhAQ34Ui9ikIsRsL3zGi8WuWItrjiIv7liQI+yKIa4M3Hq4aJQwsLHWNYIZtzgpkRwPQGID4BJwTwZwLOXwD0AoAmgfIT2NICVD1u/HaoeOLxYmFKsB5QqE0IeD1gKWd4GUdOg6HzxtgsI4kRV1KD9CNgnghXjeBPojDR2sfcYbyVPGZN6BF4jqswNgpGjrO942zhn3NFOdLRe7HCnsMCHW1DhP47zrgEaAATcMamMeEOFuGVEBmk

EwDpFDPxBjYuIYyhmGMS6edvhqXK0jeDjFP4Ex+EqunlyIms90xKYsANZMJE9D4RmY3QlzwSFHBcxRxczMEKLFN0SxsNJRlxPbIBIiRvQj1M4G6ny9ueLkykaNwgBO5q4TwfQI0CsgagbINkTQJtKnLZZrek4XAKOIZoQBO0d4KcfAL3r1YXgp3YJreGe5ydEKCnLusqND4X1PQd9A8e9w1EniaBZ43yaZ31FXiApg1DvCFI4Fminxw1F8W+Jqbl

9YeX4qvnbTPaVoUpn4C4ARl7ixifRBrO4Ua2UGqCrSQdQnDF3GaJiP2HwlCU/nKn/tGoJLCMoCKy5vU6puXQiS1KX6/V5M2Y7ukrh8G+o/BxlAIeh0kYYiQhp/BzOfzLEPE3p5ddwUzh8HRhM0KQgchIBtCEAc0RgVpM4CnIjBWkNcEYOXFIDEBiA9QVoDZEz601EqkArbjjAjzCc2a7vPxu8GQFncnpF3SyT+jJG7i+hX0heEeRSZjDjxEwwGT5

N1EMDLxK9eYWFMhmANoZ/LTPhDxVJVNS+iMgQRXxRnxSHRZ7Xkf9FZTKk0Gbo98pDE9GZT7h+1c5I9S+AXhXhiE4qchJjo/stUjrHRGvAbCuTmZImcwdlwoYcyF+XM4iamRjibAg+vzEPojXYT9T7mLEzEa8zP7tdZZWdOab7OD5IcvM5wZaakPQCMRTgoocICMAoBsAa0TwDgBQB4A9BKg5cHoJoHOk2yGmDvLbnsB27dNqshknKmcHdmPS0BYT

OAlFBskzSCBI6NcaNl5wTZo+FAkOfpzDlaiphP3EGbMJjnXjApt44KQnNNFJzwpxfNOXwIzkfis5sUg4faOr6/jagmM4ovsDxjHAfg3tY5BBPC6jwVIV4euSCPeElTh+qEyMW3N1QFlzYOCTLr3LZk5dPqvrb6iVx5ngELJ1ExIO4l+CjwWElwa4JItgK1ReJlE9wfrERDxCgWa8FReBD3huJ2pcIkkf9TgL6K/EBMdRTEM0VzSeAFikoPhimkdT

TFvDA5D1N0V9SYOUIsAHvHJkF0sBnZLwUtO8WkTHFKhQBZ1PZyJoPFhZLxZCLCW+LV0zikxXZKmB8MHFSSgBcYtskIiAaOiuJZkrgLwcKJNizMcrncQhJrw54JRVvNCUuI94ZI0pW4PKUKd5F1Si4FcDqUJKGlKhXdM0v4n/UyRxAnsvEvsGJLGl7OWRZUoUU1KulRSsJNYpaVTKAk7SxRfMvqXgFR0kS1xUrjaVVL1lyizZaosJgF1sly8A5XMq

OU9KtlCmDugUtGVFKrp/S6Icsv+qcxMl0ygmNNKiWOohR/PB/tWBnnoihpzzKWdLlLHjSHiXyjxLko9SDhHuwjbeerPQDVtsAU5W3IQFtxOR6AU5cZFAEYgIBGg1QIwJoGqCaA1ymk22dpMZrLJPg100UbdLJYPcFxvvdoS9PngAgllgy4iA5LQBWk+ZhwLuSqN+nqi3Cr9OBUDMjl+S+WawqzsUxz5Z9OBj44DBsMh6RSXOmc5GUQu/G5zfxGk1

VbQlXwatcM8inWKPDxlgTvRVqh4SulgmRQOE5M0OoVIbl8Uh+nwumWhITqHBzkJgwRbhL7kfUGpQ8pqSRJcRcqXlfEqie2RhXvTjgwK5iRLOGmhDRpijRNniLX6BL1xZSuSAKqVHxryOO8iACMmrjKBKgdcKALbmeAdJhCk4MlRwHLjNBnA9QQuGdNxbnhJOb8iOB/MQHmxTJVLWUd7PJDZ4dWwCxwiOpDyQK1R0CqgQDMlURyKgeopBbChhnBET

RufVdWhThnqqS+eCnYUjJilizdVpCxKRqAoUPBcYgIb4L3ArlEzwuHwK8BcDqhYTnVlM9mba3DFJcvVYlIqIiCA4syhFxPeqZzNX6cNQ1I8krhOriIQQoNCaohQWOkZgq2Ji8jicvK2arJoQ0GjDTqz1zgAiIxIEelqAYjcBtI0AUqJkAqBEAM8qwBgIQAQAUApynLGdh9zmCsa2NvICAHnFIB/xqgs4fQFqCfowKvJpQLjTxr42MbtR0wxBerU4

0iAxNGQJyLHJvE0bRNM0XjRkAE0KqRWImuTWpr42ab6Qm6yAKpuyDqbC2aq1ObJu416aMglabYbK0KBWb5N+gJyJwCgBOQEwaoMKMlCc02aXNbmjUIQH0iOSVNum0zXxrshYAVyVGpGA5gQBzAZNJmqAGZqI2kAVy3GtgBQFKi4ARBoW6zeFoyA9AxQpkDLVlpCDZwFQNILcr5oK36AStVWuyHx3QDDQONzAbADSHVCtBuAzgfrg2GbbwCIoNGtr

R1vwB1xutVXHxIuOnhS1IARgNgAYBI1cICAnEDYKrJ035bktfGjsUauVKF8AYHG4UCQEC3BaXyNGw7cQC1AIA4AmIM7aQBIAjI2AKGIrbgApXE9cEd2nUYtuIhTkmQ2cUgMoH5AAAKY5CFAlGg6Qd1ADktFAACUBoauAgHGDewv4/2oHXvApC8BaokOtHZDoCaw61txmsLVAAM0IA7NUAVsDHWIUIB4dmYO7V8i+2lAsgL24IODClnYAiA12kacR

A4DEJixhKZco+HF6cR8dr4zQIXAQDYAcgGobnXAAe1Pbudr2kMcSAl2EBGAdkebWuTQDNIH5rjc6XnCpAGBGtCVVmUBullUhTIyu1XerrVQaRwAjSZBaZmADqQQA6kIAA===
```
%%