2025-07-13 19:22:07.228 [info] components database created cost 4 ms   
2025-07-13 19:22:07.241 [info] components index initializing...   
2025-07-13 19:22:09.392 [info] indexing created file components/logs/2025-07-13.components.log  [object Object] 
2025-07-13 19:22:09.615 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-13 19:22:09.638 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-13 19:22:09.653 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-13 19:22:09.663 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-13 19:22:10.030 [info] start to batch put pages: 5   
2025-07-13 19:22:10.036 [info] batch persist cost 5  6 
2025-07-13 19:22:10.059 [info] components index initialized, 973 files cost 2835 ms   
2025-07-13 19:22:10.059 [info] refresh page data from init listeners 0 973   
2025-07-13 19:22:10.169 [info] refresh page data from delete listeners 0 972   
2025-07-13 19:22:13.733 [info] indexing created file copilot-custom-prompts/Summarize.md  [object Object] 
2025-07-13 19:22:13.734 [info] indexing created ignore file copilot-custom-prompts/Summarize.md   
2025-07-13 19:22:13.782 [info] trigger copilot-custom-prompts/Summarize.md resolve  [object Object] 
2025-07-13 19:22:13.787 [info] index finished after resolve  [object Object] 
2025-07-13 19:22:13.790 [info] refresh page data from resolve listeners 0 973   
2025-07-13 19:22:14.204 [info] indexing created file copilot-custom-prompts/Remove URLs.md  [object Object] 
2025-07-13 19:22:14.204 [info] indexing created ignore file copilot-custom-prompts/Remove URLs.md   
2025-07-13 19:22:14.218 [info] trigger copilot-custom-prompts/Remove URLs.md resolve  [object Object] 
2025-07-13 19:22:14.219 [info] index finished after resolve  [object Object] 
2025-07-13 19:22:14.221 [info] refresh page data from resolve listeners 0 974   
2025-07-13 19:22:14.700 [info] indexing created file copilot-custom-prompts/Fix grammar and spelling.md  [object Object] 
2025-07-13 19:22:14.700 [info] indexing created ignore file copilot-custom-prompts/Fix grammar and spelling.md   
2025-07-13 19:22:14.712 [info] trigger copilot-custom-prompts/Fix grammar and spelling.md resolve  [object Object] 
2025-07-13 19:22:14.715 [info] index finished after resolve  [object Object] 
2025-07-13 19:22:14.717 [info] refresh page data from resolve listeners 0 975   
2025-07-13 19:22:15.162 [info] indexing created file copilot-custom-prompts/Generate glossary.md  [object Object] 
2025-07-13 19:22:15.162 [info] indexing created ignore file copilot-custom-prompts/Generate glossary.md   
2025-07-13 19:22:15.177 [info] trigger copilot-custom-prompts/Generate glossary.md resolve  [object Object] 
2025-07-13 19:22:15.179 [info] index finished after resolve  [object Object] 
2025-07-13 19:22:15.181 [info] refresh page data from resolve listeners 0 976   
2025-07-13 19:22:15.714 [info] indexing created file copilot-custom-prompts/Generate table of contents.md  [object Object] 
2025-07-13 19:22:15.714 [info] indexing created ignore file copilot-custom-prompts/Generate table of contents.md   
2025-07-13 19:22:15.726 [info] trigger copilot-custom-prompts/Generate table of contents.md resolve  [object Object] 
2025-07-13 19:22:15.728 [info] index finished after resolve  [object Object] 
2025-07-13 19:22:15.729 [info] refresh page data from resolve listeners 0 977   
2025-07-13 19:22:16.240 [info] indexing created file copilot-custom-prompts/Translate to Chinese.md  [object Object] 
2025-07-13 19:22:16.240 [info] indexing created ignore file copilot-custom-prompts/Translate to Chinese.md   
2025-07-13 19:22:16.269 [info] trigger copilot-custom-prompts/Translate to Chinese.md resolve  [object Object] 
2025-07-13 19:22:16.272 [info] index finished after resolve  [object Object] 
2025-07-13 19:22:16.273 [info] refresh page data from resolve listeners 0 978   
2025-07-13 19:22:16.856 [info] indexing created file copilot-custom-prompts/Simplify.md  [object Object] 
2025-07-13 19:22:16.857 [info] indexing created ignore file copilot-custom-prompts/Simplify.md   
2025-07-13 19:22:16.873 [info] trigger copilot-custom-prompts/Simplify.md resolve  [object Object] 
2025-07-13 19:22:16.876 [info] index finished after resolve  [object Object] 
2025-07-13 19:22:16.878 [info] refresh page data from resolve listeners 0 979   
2025-07-13 19:22:17.343 [info] indexing created file copilot-custom-prompts/Make shorter.md  [object Object] 
2025-07-13 19:22:17.343 [info] indexing created ignore file copilot-custom-prompts/Make shorter.md   
2025-07-13 19:22:17.354 [info] trigger copilot-custom-prompts/Make shorter.md resolve  [object Object] 
2025-07-13 19:22:17.355 [info] index finished after resolve  [object Object] 
2025-07-13 19:22:17.356 [info] refresh page data from resolve listeners 0 980   
2025-07-13 19:22:17.865 [info] indexing created file copilot-custom-prompts/Rewrite as tweet.md  [object Object] 
2025-07-13 19:22:17.865 [info] indexing created ignore file copilot-custom-prompts/Rewrite as tweet.md   
2025-07-13 19:22:17.880 [info] trigger copilot-custom-prompts/Rewrite as tweet.md resolve  [object Object] 
2025-07-13 19:22:17.882 [info] index finished after resolve  [object Object] 
2025-07-13 19:22:17.884 [info] refresh page data from resolve listeners 0 981   
2025-07-13 19:22:18.382 [info] indexing created file copilot-custom-prompts/Make longer.md  [object Object] 
2025-07-13 19:22:18.382 [info] indexing created ignore file copilot-custom-prompts/Make longer.md   
2025-07-13 19:22:18.396 [info] trigger copilot-custom-prompts/Make longer.md resolve  [object Object] 
2025-07-13 19:22:18.397 [info] index finished after resolve  [object Object] 
2025-07-13 19:22:18.399 [info] refresh page data from resolve listeners 0 982   
2025-07-13 19:22:18.894 [info] indexing created file copilot-custom-prompts/Explain like I am 5.md  [object Object] 
2025-07-13 19:22:18.894 [info] indexing created ignore file copilot-custom-prompts/Explain like I am 5.md   
2025-07-13 19:22:18.906 [info] trigger copilot-custom-prompts/Explain like I am 5.md resolve  [object Object] 
2025-07-13 19:22:18.907 [info] index finished after resolve  [object Object] 
2025-07-13 19:22:18.909 [info] refresh page data from resolve listeners 0 983   
2025-07-13 19:22:19.348 [info] indexing created file copilot-custom-prompts/Rewrite as press release.md  [object Object] 
2025-07-13 19:22:19.348 [info] indexing created ignore file copilot-custom-prompts/Rewrite as press release.md   
2025-07-13 19:22:19.372 [info] trigger copilot-custom-prompts/Rewrite as press release.md resolve  [object Object] 
2025-07-13 19:22:19.373 [info] index finished after resolve  [object Object] 
2025-07-13 19:22:19.377 [info] refresh page data from resolve listeners 0 984   
2025-07-13 19:22:19.831 [info] indexing created file copilot-custom-prompts/Emojify.md  [object Object] 
2025-07-13 19:22:19.831 [info] indexing created ignore file copilot-custom-prompts/Emojify.md   
2025-07-13 19:22:19.854 [info] trigger copilot-custom-prompts/Emojify.md resolve  [object Object] 
2025-07-13 19:22:19.857 [info] index finished after resolve  [object Object] 
2025-07-13 19:22:19.858 [info] refresh page data from resolve listeners 0 985   
2025-07-13 19:22:20.329 [info] indexing created file copilot-custom-prompts/Rewrite as tweet thread.md  [object Object] 
2025-07-13 19:22:20.329 [info] indexing created ignore file copilot-custom-prompts/Rewrite as tweet thread.md   
2025-07-13 19:22:20.342 [info] trigger copilot-custom-prompts/Rewrite as tweet thread.md resolve  [object Object] 
2025-07-13 19:22:20.343 [info] index finished after resolve  [object Object] 
2025-07-13 19:22:20.345 [info] refresh page data from resolve listeners 0 986   
2025-07-13 19:22:30.266 [info] indexing created file 学习库/stm32/attachments/2 GPIO-2025-07-13-08-53-44.png  [object Object] 
2025-07-13 19:22:30.273 [info] refresh page data from created listeners 0 987   
2025-07-13 19:22:31.142 [debug] ignore file modify evnet 学习库/stm32/2 GPIO.md   
2025-07-13 19:22:31.173 [info] trigger 学习库/stm32/2 GPIO.md resolve  [object Object] 
2025-07-13 19:22:31.176 [info] index finished after resolve  [object Object] 
2025-07-13 19:22:31.178 [info] refresh page data from resolve listeners 0 987   
2025-07-13 19:22:32.070 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-13 19:22:32.105 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-13 19:22:32.107 [info] index finished after resolve  [object Object] 
2025-07-13 19:22:32.108 [info] refresh page data from resolve listeners 0 987   
2025-07-13 19:24:13.197 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-13 19:24:13.206 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
