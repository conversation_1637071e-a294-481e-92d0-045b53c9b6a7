# YOLOv5 6.0网络结构图
![[Pasted image 20240123163728.png]]
![[157381276-6e8429f3-c759-4aef-aea8-034438919457.png]]
# 参数部分
```
# Parameters
nc: 80  # number of classes
depth_multiple: 1.0  # model depth multiple
width_multiple: 1.0  # layer channel multiple
anchors:
  - [10,13, 16,30, 33,23]  # P3/8
  - [30,61, 62,45, 59,119]  # P4/16
  - [116,90, 156,198, 373,326]  # P5/32
```
- nc代表类别
- depth_multiple用于控制模型的深度，用来控制模型的复杂度和大小，数字越大模型越深；数字越小模型越浅。例：1.0表示使用原始模型的深度，0.5表示使用原始模型一半的深度
	- 深度是指模型的层数，也就是模型由多少个卷积层，池化层和全连接层等组成
- width_multiple用于控制模型的宽度，用来控制模型的通道数和参数量，数字越大模型越宽；数字越小模型越窄。例：1.0表示使用原始模型的宽度，0.5表示减少一般的宽度
	- 宽度是指模型的通道数，也就是每一层的输出特征图的数量
- anchors是用于预测物体的边界框尺寸，每一行表示一组预测框，比如第一行分别由有10\*13；16\*13；33\*23的边界框
	- p3/8的含义：p3代表特征图的层级，/8则表示输出是原始尺寸的1/8，假设输入尺寸是640\*640，则输出为80\*80。p4/8也是同理。

# backbone部分
```yaml
# YOLOv5 v6.0 backbone
backbone:
  # [from, number, module, args]
  [[-1, 1, Conv, [64, 6, 2, 2]],  # 0-P1/2
   [-1, 1, Conv, [128, 3, 2]],  # 1-P2/4
   [-1, 3, C3, [128]],
   [-1, 1, Conv, [256, 3, 2]],  # 3-P3/8
   [-1, 6, C3, [256]],
   [-1, 1, Conv, [512, 3, 2]],  # 5-P4/16
   [-1, 9, C3, [512]],
   [-1, 1, Conv, [1024, 3, 2]],  # 7-P5/32
   [-1, 3, C3, [1024]],
   [-1, 1, SPPF, [1024, 5]],  # 9
  ]
```
- 每一行的分别由四个参数组成，from(该层的输入)；number(该模块的数量或者该模块内部子模块的数量)；module(模块的名称)；args(类的初始化参数)
	- args：channel(输出通道数)；kernel_size(卷积核大小)；stride(步长)；padding(填充)
	- from：表示当前模块的输入来自那一层的输出，-1表示来自上一层的输出，-2表示上两层，\[-1, 4]表示取自上一层和第4层，依次类推
	- number：表示该层模块堆叠的次数，对于C3、BottleneckCSP等模块，表示其子模块的堆叠，具体细节可以查看源代码，当然最终的次数还要乘上depth_multiple系数。
	- module：表示该层模块的名称，这些模块写在common.py中，进行模块化的搭建网络。
- 各层代码注释
	第0层是一个卷积层，输出通道数为 64，卷积核大小为 6x6，步长为 2，填充为 2，输出特征图大小为输入的1/2。
	第1层是一个卷积层，输出通道数为 128，卷积核大小为 3x3，步长为 2，输出特征图大小为输入的1/2。
	第2层是一个 C3 模块，包含 3 个卷积层，每个卷积层的输出通道数为 128，卷积核大小分别为 1x1、3x3、1x1，不改变特征图大小。
	第3层是一个卷积层，输出通道数为 256，卷积核大小为 3x3，步长为 2，输出特征图大小为输入的1/2。
	第4层是一个 C3 模块，包含 6 个卷积层，每个卷积层的输出通道数为 256，卷积核大小分别为 1x1、3x3、1x1，不改变特征图大小。
	第5层是一个卷积层，输出通道数为 512，卷积核大小为 3x3，步长为 2，输出特征图大小为输入的1/2。
	第6层是一个 C3 模块，包含 9 个卷积层，每个卷积层的输出通道数为 512，卷积核大小分别为 1x1、3x3、1x1，不改变特征图大小。
	第7层是一个卷积层，输出通道数为 1024，卷积核大小为 3x3，步长为 2，输出特征图大小为输入的1/2。
	第8层是一个 C3 模块，包含 3 个卷积层，每个卷积层的输出通道数为 1024，卷积核大小分别为 1x1、3x3、1x1，不改变特征图大小。
	第9层是一个 SPPF 层，具有金字塔式空间池化（Spatial Pyramid Pooling），输出通道数为 1024，使用大小为 5x5 的金字塔空间池化。

# head部分
```
# YOLOv5 v6.0 head
head:
  [[-1, 1, Conv, [512, 1, 1]],
   [-1, 1, nn.Upsample, [None, 2, 'nearest']],
   [[-1, 6], 1, Concat, [1]],  # cat backbone P4
   [-1, 3, C3, [512, False]],  # 13
 
   [-1, 1, Conv, [256, 1, 1]],
   [-1, 1, nn.Upsample, [None, 2, 'nearest']],
   [[-1, 4], 1, Concat, [1]],  # cat backbone P3
   [-1, 3, C3, [256, False]],  # 17 (P3/8-small)
 
   [-1, 1, Conv, [256, 3, 2]],
   [[-1, 14], 1, Concat, [1]],  # cat head P4
   [-1, 3, C3, [512, False]],  # 20 (P4/16-medium)
 
   [-1, 1, Conv, [512, 3, 2]],
   [[-1, 10], 1, Concat, [1]],  # cat head P5
   [-1, 3, C3, [1024, False]],  # 23 (P5/32-large)
 
   [[17, 20, 23], 1, Detect, [nc, anchors]],  # Detect(P3, P4, P5)
  ]
```
YOLOv5的head是包括Neck和Detect两个部分的
- nu.Upsample 是上采样模块，将输入的特征图进行上采样获得更高分辨率的特征图，它只改变特征图的尺寸而不改变特征图的通道数
- Concat是指将多个特征图按照通道维度进行拼接，在YOLOv5中主要用于将不同尺度的特征图像进行融合[[concatenate和add]]
- 最后Detect部分有三个检测层，80\*80，40\*40，20\*20，分别对应小目标，中目标和大目标，划分网格越多可以检测的目标越小。                                                                                                              ![[Pasted image 20240123213540.png]]![[目标检测的尺度.png]]