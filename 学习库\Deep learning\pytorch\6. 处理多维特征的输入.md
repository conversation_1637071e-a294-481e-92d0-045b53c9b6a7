---
tags:
  - 学习
  - deep_learning
  - pytorch
---

## 多维度数据结构

当输入`x`不再是单一实数，而是包含多个特征的向量时，这种情况就是多维度输入的情况

多维度数据通常结构化:

- 每一行代表一个样本 （Sample）或者一条记录 （Record）
- 每一列代表一个特征 （Feature）或者字段 （Field）

![[6. 处理多维特征的输入-2025-05-08-20-28-47.png]]

## 多维度输入的模型构建

对于单一样本(i)，多维度输入的线性变换是所有输入特征乘以对应的权重，然后加上偏置项，最后通过激活函数得到输出
$$
\hat{y}_i = \sigma(\sum_{j=1}^m w_j x_{ij} + b)
$$  

- 线性部分通常表示为 $Z = \sum_{j=1}^m w_j x_{ij} + b$
- 激活函数通常表示为 $\sigma()$

## 多层神经网络

多层神经网络通过将多个层顺序连接构成，一个基本的神经网络层通常包含：

- 线性变换(Linear Transformation)：通过权重矩阵乘法将输入向量从一个维度空间映射到另一个维度空间，在pytorch中通常使用`torch.nn.Linear`来实现，`torch.nn.Linear(in_features, out_features)`，其中`in_features`是输入特征的维度，`out_features`是输出特征的维度。

- 激活函数(Activation Function)：对线性变换的结果==进行非线性变换==，通常使用非线性激活函数，如ReLU、Sigmoid、Tanh等，在pytorch中通常使用`torch.nn.ReLU`、`torch.nn.Sigmoid`、`torch.nn.Tanh`等来实现。

通过将多个层顺序连接构成，一个多层神经网络可以实现更复杂的非线性映射，从而拟合更复杂的数据。

```ad-col2
title: 多层神经网络
color:178,22,164

![[6. 处理多维特征的输入-2025-05-08-20-42-18.png]]
![[6. 处理多维特征的输入-2025-05-08-20-42-32.png]]
```

## Pytorch 模型实现

```python
import torch
import torch.nn as nn

class MultiLayerNN(nn.Module):
    def __init__(self, input_size, hidden_size, output_size):
        super(MultiLayerNN, self).__init__()
        self.fc1 = nn.Linear(input_size, hidden_size)  # 第一层线性变换
        self.relu = nn.ReLU()  # 激活函数
        self.fc2 = nn.Linear(hidden_size, output_size)  # 第二层线性变换

    def forward(self, x):
        out = self.fc1(x)  # 第一层线性变换
        out = self.relu(out)  # 激活函数
        out = self.fc2(out)  # 第二层线性变换
        return out
```
