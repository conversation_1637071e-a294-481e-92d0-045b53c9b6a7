"use strict";var Re=Object.create;var I=Object.defineProperty;var Ce=Object.getOwnPropertyDescriptor;var Ve=Object.getOwnPropertyNames;var _e=Object.getPrototypeOf,Ie=Object.prototype.hasOwnProperty;var We=(a,t)=>()=>(t||a((t={exports:{}}).exports,t),t.exports),Ye=(a,t)=>{for(var e in t)I(a,e,{get:t[e],enumerable:!0})},pe=(a,t,e,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of Ve(t))!Ie.call(a,i)&&i!==e&&I(a,i,{get:()=>t[i],enumerable:!(n=Ce(t,i))||n.enumerable});return a};var Ue=(a,t,e)=>(e=a!=null?Re(_e(a)):{},pe(t||!a||!a.__esModule?I(e,"default",{value:a,enumerable:!0}):e,a)),Be=a=>pe(I({},"__esModule",{value:!0}),a);var Oe=We(l=>{"use strict";Object.defineProperty(l,"__esModule",{value:!0});var g=require("obsidian"),X="YYYY-MM-DD",ee="gggg-[W]ww",fe="YYYY-MM",we="YYYY-[Q]Q",ye="YYYY";function S(a){let t=window.app.plugins.getPlugin("periodic-notes");return t&&t.settings?.[a]?.enabled}function E(){try{let{internalPlugins:a,plugins:t}=window.app;if(S("daily")){let{format:o,folder:s,template:p}=t.getPlugin("periodic-notes")?.settings?.daily||{};return{format:o||X,folder:s?.trim()||"",template:p?.trim()||""}}let{folder:e,format:n,template:i}=a.getPluginById("daily-notes")?.instance?.options||{};return{format:n||X,folder:e?.trim()||"",template:i?.trim()||""}}catch(a){console.info("No custom daily note settings found!",a)}}function L(){try{let a=window.app.plugins,t=a.getPlugin("calendar")?.options,e=a.getPlugin("periodic-notes")?.settings?.weekly;if(S("weekly"))return{format:e.format||ee,folder:e.folder?.trim()||"",template:e.template?.trim()||""};let n=t||{};return{format:n.weeklyNoteFormat||ee,folder:n.weeklyNoteFolder?.trim()||"",template:n.weeklyNoteTemplate?.trim()||""}}catch(a){console.info("No custom weekly note settings found!",a)}}function H(){let a=window.app.plugins;try{let t=S("monthly")&&a.getPlugin("periodic-notes")?.settings?.monthly||{};return{format:t.format||fe,folder:t.folder?.trim()||"",template:t.template?.trim()||""}}catch(t){console.info("No custom monthly note settings found!",t)}}function x(){let a=window.app.plugins;try{let t=S("quarterly")&&a.getPlugin("periodic-notes")?.settings?.quarterly||{};return{format:t.format||we,folder:t.folder?.trim()||"",template:t.template?.trim()||""}}catch(t){console.info("No custom quarterly note settings found!",t)}}function R(){let a=window.app.plugins;try{let t=S("yearly")&&a.getPlugin("periodic-notes")?.settings?.yearly||{};return{format:t.format||ye,folder:t.folder?.trim()||"",template:t.template?.trim()||""}}catch(t){console.info("No custom yearly note settings found!",t)}}function ve(...a){let t=[];for(let n=0,i=a.length;n<i;n++)t=t.concat(a[n].split("/"));let e=[];for(let n=0,i=t.length;n<i;n++){let o=t[n];!o||o==="."||e.push(o)}return t[0]===""&&e.unshift(""),e.join("/")}function qe(a){let t=a.substring(a.lastIndexOf("/")+1);return t.lastIndexOf(".")!=-1&&(t=t.substring(0,t.lastIndexOf("."))),t}async function Ke(a){let t=a.replace(/\\/g,"/").split("/");if(t.pop(),t.length){let e=ve(...t);window.app.vault.getAbstractFileByPath(e)||await window.app.vault.createFolder(e)}}async function C(a,t){t.endsWith(".md")||(t+=".md");let e=g.normalizePath(ve(a,t));return await Ke(e),e}async function A(a){let{metadataCache:t,vault:e}=window.app,n=g.normalizePath(a);if(n==="/")return Promise.resolve(["",null]);try{let i=t.getFirstLinkpathDest(n,""),o=await e.cachedRead(i),s=window.app.foldManager.load(i);return[o,s]}catch(i){return console.error(`Failed to read the daily note template '${n}'`,i),new g.Notice("Failed to read the daily note template"),["",null]}}function b(a,t="day"){let e=a.clone().startOf(t).format();return`${t}-${e}`}function Ne(a){return a.replace(/\[[^\]]*\]/g,"")}function je(a,t){if(t==="week"){let e=Ne(a);return/w{1,2}/i.test(e)&&(/M{1,4}/.test(e)||/D{1,4}/.test(e))}return!1}function O(a,t){return be(a.basename,t)}function $e(a,t){return be(qe(a),t)}function be(a,t){let n={day:E,week:L,month:H,quarter:x,year:R}[t]().format.split("/").pop(),i=window.moment(a,n,!0);if(!i.isValid())return null;if(je(n,t)&&t==="week"){let o=Ne(n);if(/w{1,2}/i.test(o))return window.moment(a,n.replace(/M{1,4}/g,"").replace(/D{1,4}/g,""),!1)}return i}var te=class extends Error{};async function ke(a){let t=window.app,{vault:e}=t,n=window.moment,{template:i,format:o,folder:s}=E(),[p,d]=await A(i),r=a.format(o),c=await C(s,r);try{let y=await e.create(c,p.replace(/{{\s*date\s*}}/gi,r).replace(/{{\s*time\s*}}/gi,n().format("HH:mm")).replace(/{{\s*title\s*}}/gi,r).replace(/{{\s*(date|time)\s*(([+-]\d+)([yqmwdhs]))?\s*(:.+?)?}}/gi,(k,P,T,v,f,m)=>{let G=n(),Q=a.clone().set({hour:G.get("hour"),minute:G.get("minute"),second:G.get("second")});return T&&Q.add(parseInt(v,10),f),m?Q.format(m.substring(1).trim()):Q.format(o)}).replace(/{{\s*yesterday\s*}}/gi,a.clone().subtract(1,"day").format(o)).replace(/{{\s*tomorrow\s*}}/gi,a.clone().add(1,"d").format(o)));return t.foldManager.save(y,d),y}catch(y){console.error(`Failed to create file: '${c}'`,y),new g.Notice("Unable to create new file.")}}function ze(a,t){return t[b(a,"day")]??null}function Ge(){let{vault:a}=window.app,{folder:t}=E(),e=a.getAbstractFileByPath(g.normalizePath(t));if(!e)throw new te("Failed to find daily notes folder");let n={};return g.Vault.recurseChildren(e,i=>{if(i instanceof g.TFile){let o=O(i,"day");if(o){let s=b(o,"day");n[s]=i}}}),n}var ae=class extends Error{};function Qe(){let{moment:a}=window,t=a.localeData()._week.dow,e=["sunday","monday","tuesday","wednesday","thursday","friday","saturday"];for(;t;)e.push(e.shift()),t--;return e}function Je(a){return Qe().indexOf(a.toLowerCase())}async function Pe(a){let{vault:t}=window.app,{template:e,format:n,folder:i}=L(),[o,s]=await A(e),p=a.format(n),d=await C(i,p);try{let r=await t.create(d,o.replace(/{{\s*(date|time)\s*(([+-]\d+)([yqmwdhs]))?\s*(:.+?)?}}/gi,(c,y,k,P,T,v)=>{let f=window.moment(),m=a.clone().set({hour:f.get("hour"),minute:f.get("minute"),second:f.get("second")});return k&&m.add(parseInt(P,10),T),v?m.format(v.substring(1).trim()):m.format(n)}).replace(/{{\s*title\s*}}/gi,p).replace(/{{\s*time\s*}}/gi,window.moment().format("HH:mm")).replace(/{{\s*(sunday|monday|tuesday|wednesday|thursday|friday|saturday)\s*:(.*?)}}/gi,(c,y,k)=>{let P=Je(y);return a.weekday(P).format(k.trim())}));return window.app.foldManager.save(r,s),r}catch(r){console.error(`Failed to create file: '${d}'`,r),new g.Notice("Unable to create new file.")}}function Ze(a,t){return t[b(a,"week")]??null}function Xe(){let a={};if(!De())return a;let{vault:t}=window.app,{folder:e}=L(),n=t.getAbstractFileByPath(g.normalizePath(e));if(!n)throw new ae("Failed to find weekly notes folder");return g.Vault.recurseChildren(n,i=>{if(i instanceof g.TFile){let o=O(i,"week");if(o){let s=b(o,"week");a[s]=i}}}),a}var ne=class extends Error{};async function Te(a){let{vault:t}=window.app,{template:e,format:n,folder:i}=H(),[o,s]=await A(e),p=a.format(n),d=await C(i,p);try{let r=await t.create(d,o.replace(/{{\s*(date|time)\s*(([+-]\d+)([yqmwdhs]))?\s*(:.+?)?}}/gi,(c,y,k,P,T,v)=>{let f=window.moment(),m=a.clone().set({hour:f.get("hour"),minute:f.get("minute"),second:f.get("second")});return k&&m.add(parseInt(P,10),T),v?m.format(v.substring(1).trim()):m.format(n)}).replace(/{{\s*date\s*}}/gi,p).replace(/{{\s*time\s*}}/gi,window.moment().format("HH:mm")).replace(/{{\s*title\s*}}/gi,p));return window.app.foldManager.save(r,s),r}catch(r){console.error(`Failed to create file: '${d}'`,r),new g.Notice("Unable to create new file.")}}function et(a,t){return t[b(a,"month")]??null}function tt(){let a={};if(!Me())return a;let{vault:t}=window.app,{folder:e}=H(),n=t.getAbstractFileByPath(g.normalizePath(e));if(!n)throw new ne("Failed to find monthly notes folder");return g.Vault.recurseChildren(n,i=>{if(i instanceof g.TFile){let o=O(i,"month");if(o){let s=b(o,"month");a[s]=i}}}),a}var ie=class extends Error{};async function at(a){let{vault:t}=window.app,{template:e,format:n,folder:i}=x(),[o,s]=await A(e),p=a.format(n),d=await C(i,p);try{let r=await t.create(d,o.replace(/{{\s*(date|time)\s*(([+-]\d+)([yqmwdhs]))?\s*(:.+?)?}}/gi,(c,y,k,P,T,v)=>{let f=window.moment(),m=a.clone().set({hour:f.get("hour"),minute:f.get("minute"),second:f.get("second")});return k&&m.add(parseInt(P,10),T),v?m.format(v.substring(1).trim()):m.format(n)}).replace(/{{\s*date\s*}}/gi,p).replace(/{{\s*time\s*}}/gi,window.moment().format("HH:mm")).replace(/{{\s*title\s*}}/gi,p));return window.app.foldManager.save(r,s),r}catch(r){console.error(`Failed to create file: '${d}'`,r),new g.Notice("Unable to create new file.")}}function nt(a,t){return t[b(a,"quarter")]??null}function it(){let a={};if(!Fe())return a;let{vault:t}=window.app,{folder:e}=x(),n=t.getAbstractFileByPath(g.normalizePath(e));if(!n)throw new ie("Failed to find quarterly notes folder");return g.Vault.recurseChildren(n,i=>{if(i instanceof g.TFile){let o=O(i,"quarter");if(o){let s=b(o,"quarter");a[s]=i}}}),a}var oe=class extends Error{};async function ot(a){let{vault:t}=window.app,{template:e,format:n,folder:i}=R(),[o,s]=await A(e),p=a.format(n),d=await C(i,p);try{let r=await t.create(d,o.replace(/{{\s*(date|time)\s*(([+-]\d+)([yqmwdhs]))?\s*(:.+?)?}}/gi,(c,y,k,P,T,v)=>{let f=window.moment(),m=a.clone().set({hour:f.get("hour"),minute:f.get("minute"),second:f.get("second")});return k&&m.add(parseInt(P,10),T),v?m.format(v.substring(1).trim()):m.format(n)}).replace(/{{\s*date\s*}}/gi,p).replace(/{{\s*time\s*}}/gi,window.moment().format("HH:mm")).replace(/{{\s*title\s*}}/gi,p));return window.app.foldManager.save(r,s),r}catch(r){console.error(`Failed to create file: '${d}'`,r),new g.Notice("Unable to create new file.")}}function st(a,t){return t[b(a,"year")]??null}function rt(){let a={};if(!Ae())return a;let{vault:t}=window.app,{folder:e}=R(),n=t.getAbstractFileByPath(g.normalizePath(e));if(!n)throw new oe("Failed to find yearly notes folder");return g.Vault.recurseChildren(n,i=>{if(i instanceof g.TFile){let o=O(i,"year");if(o){let s=b(o,"year");a[s]=i}}}),a}function lt(){let{app:a}=window,t=a.internalPlugins.plugins["daily-notes"];if(t&&t.enabled)return!0;let e=a.plugins.getPlugin("periodic-notes");return e&&e.settings?.daily?.enabled}function De(){let{app:a}=window;if(a.plugins.getPlugin("calendar"))return!0;let t=a.plugins.getPlugin("periodic-notes");return t&&t.settings?.weekly?.enabled}function Me(){let{app:a}=window,t=a.plugins.getPlugin("periodic-notes");return t&&t.settings?.monthly?.enabled}function Fe(){let{app:a}=window,t=a.plugins.getPlugin("periodic-notes");return t&&t.settings?.quarterly?.enabled}function Ae(){let{app:a}=window,t=a.plugins.getPlugin("periodic-notes");return t&&t.settings?.yearly?.enabled}function ct(a){let t={day:E,week:L,month:H,quarter:x,year:R}[a];return t()}function dt(a,t){return{day:ke,month:Te,week:Pe}[a](t)}l.DEFAULT_DAILY_NOTE_FORMAT=X;l.DEFAULT_MONTHLY_NOTE_FORMAT=fe;l.DEFAULT_QUARTERLY_NOTE_FORMAT=we;l.DEFAULT_WEEKLY_NOTE_FORMAT=ee;l.DEFAULT_YEARLY_NOTE_FORMAT=ye;l.appHasDailyNotesPluginLoaded=lt;l.appHasMonthlyNotesPluginLoaded=Me;l.appHasQuarterlyNotesPluginLoaded=Fe;l.appHasWeeklyNotesPluginLoaded=De;l.appHasYearlyNotesPluginLoaded=Ae;l.createDailyNote=ke;l.createMonthlyNote=Te;l.createPeriodicNote=dt;l.createQuarterlyNote=at;l.createWeeklyNote=Pe;l.createYearlyNote=ot;l.getAllDailyNotes=Ge;l.getAllMonthlyNotes=tt;l.getAllQuarterlyNotes=it;l.getAllWeeklyNotes=Xe;l.getAllYearlyNotes=rt;l.getDailyNote=ze;l.getDailyNoteSettings=E;l.getDateFromFile=O;l.getDateFromPath=$e;l.getDateUID=b;l.getMonthlyNote=et;l.getMonthlyNoteSettings=H;l.getPeriodicNoteSettings=ct;l.getQuarterlyNote=nt;l.getQuarterlyNoteSettings=x;l.getTemplateInfo=A;l.getWeeklyNote=Ze;l.getWeeklyNoteSettings=L;l.getYearlyNote=st;l.getYearlyNoteSettings=R});var gt={};Ye(gt,{default:()=>z});module.exports=Be(gt);var M=require("obsidian");var w=require("obsidian");var Se=require("obsidian");var ue=require("obsidian");function N(a){return a?a.extension=="md"?a.path.slice(0,-3):a.path:""}function ge(a){return a.split("/").slice(-1)[0].contains(".")?a:`${a}.md`}function me(a){let t=a.vault.getFiles().filter(e=>["md","canvas"].contains(e.extension));if(t.length){let e=Math.floor(Math.random()*t.length);return N(t[e])}}function he(a){return a.workspace.getActiveViewOfType(ue.View)?.getViewType()=="empty"}function J(a,t){return a.localeCompare(t,void 0,{sensitivity:"accent"})===0}function Z(a){return new Promise(t=>setTimeout(t,a))}var u=Ue(Oe()),se={["Daily Note"]:{noun:"day",adjective:"daily",create:u.createDailyNote,get:u.getDailyNote,getAll:u.getAllDailyNotes},["Weekly Note"]:{noun:"week",adjective:"weekly",create:u.createWeeklyNote,get:u.getWeeklyNote,getAll:u.getAllWeeklyNotes},["Monthly Note"]:{noun:"month",adjective:"monthly",create:u.createMonthlyNote,get:u.getMonthlyNote,getAll:u.getAllMonthlyNotes},["Yearly Note"]:{noun:"year",adjective:"yearly",create:u.createYearlyNote,get:u.getYearlyNote,getAll:u.getAllYearlyNotes}},W=["Daily Note","Weekly Note","Monthly Note","Yearly Note"];async function Ee(a,t){let e=t.communityPlugins["periodic-notes"],n=se[a],i=(0,Se.moment)().startOf(n.noun),o;if(He(e)){let s=n.getAll();Object.keys(s).length?o=n.get(i,s)||await n.create(i):o=await n.create(i)}else e.cache.initialize(),o=e.getPeriodicNote(n.noun,i)||await e.createPeriodicNote(n.noun,i);return N(o)}function Le(a,t){if(a=="Daily Note"&&t.internalPlugins["daily-notes"]?.enabled)return!0;let e=t.communityPlugins["periodic-notes"];if(!e)return!1;if(He(e)){let n=se[a].adjective;return t.communityPlugins["periodic-notes"].settings[n]?.enabled}else{let n=se[a].noun;return t.communityPlugins["periodic-notes"]?.calendarSetManager?.getActiveSet()[n]?.enabled}}function Y(a){let t=a.internalPlugins["daily-notes"];return t?.enabled&&t?.instance.options.autorun}function He(a){return(a?.manifest.version||"0").startsWith("0")}var xe=["markdown","canvas","kanban"],pt=[...xe,"audio","graph","image","pdf","video"],F="Main Homepage",U="Mobile Homepage",B=(n=>(n.ReplaceAll="Replace all open notes",n.ReplaceLast="Replace last note",n.Retain="Keep open notes",n))(B||{}),re=(i=>(i.Default="Default view",i.Reading="Reading view",i.Source="Editing view (Source)",i.LivePreview="Editing view (Live Preview)",i))(re||{}),V=(c=>(c.File="File",c.Workspace="Workspace",c.Random="Random file",c.Graph="Graph view",c.None="Nothing",c.DailyNote="Daily Note",c.WeeklyNote="Weekly Note",c.MonthlyNote="Monthly Note",c.YearlyNote="Yearly Note",c.MomentDate="Date-dependent file",c))(V||{}),le=["Random file","Graph view","Nothing",...W],_=class{constructor(t,e){this.lastView=void 0;this.openedViews=new WeakMap;this.name=t,this.plugin=e,this.app=e.app,this.data=e.settings.homepages[t]}async open(t=!1){if(this.plugin.hasRequiredPlugin(this.data.kind))this.data.kind==="Date-dependent file"&&new w.Notice("Moment-based homepages are deprecated, and will be removed in a future version. Please change your homepage to a Daily or Periodic Notes type in the Homepage settings pane.");else{new w.Notice("Homepage cannot be opened due to plugin unavailablity.");return}if(this.data.kind==="Workspace")await this.launchWorkspace();else if(this.data.kind!=="Nothing"){let e=this.plugin.loaded?this.data.manualOpenMode:this.data.openMode;t&&(e="Keep open notes"),await this.launchLeaf(e)}if(this.data.commands)for(let e of this.data.commands)this.app.commands.executeCommandById(e)}async launchWorkspace(){let t=this.plugin.internalPlugins.workspaces?.instance;if(!(this.data.value in t.workspaces)){new w.Notice(`Cannot find the workspace "${this.data.value}" to use as the homepage.`);return}t.loadWorkspace(this.data.value),await Z(100)}async launchLeaf(t){let e;if(this.computedValue=await this.computeValue(),this.plugin.executing=!0,!(Y(this.plugin)&&!this.plugin.loaded)){if(t!=="Replace all open notes"){let n=this.getOpened();if(n.length>0){this.app.workspace.setActiveLeaf(n[0]),await this.configure(n[0]);return}else t=="Keep open notes"&&he(this.app)&&(t="Replace last note")}t!=="Keep open notes"&&this.app.workspace.getActiveViewOfType(w.View)?.leaf.setPinned(!1),t==="Replace all open notes"&&(pt.forEach(n=>this.app.workspace.detachLeavesOfType(n)),await Z(0)),this.data.kind==="Graph view"?e=await this.launchGraph(t):e=await this.launchNote(t),e&&await this.configure(e)}}async launchGraph(t){if(t==="Keep open notes"){let e=this.app.workspace.getLeaf("tab");this.app.workspace.setActiveLeaf(e)}return this.app.commands.executeCommandById("graph:open"),this.app.workspace.getActiveViewOfType(w.View)?.leaf}async launchNote(t){let e=this.app.metadataCache.getFirstLinkpathDest(this.computedValue,"/");if(!e){if(!this.data.autoCreate){new w.Notice(`Homepage "${this.computedValue}" does not exist.`);return}e=await this.app.vault.create(ge(this.computedValue),"")}let n=await this.app.vault.cachedRead(e),i=this.app.workspace.getLeaf(t=="Keep open notes");return await i.openFile(e),this.app.workspace.setActiveLeaf(i),n!==await this.app.vault.read(e)&&await this.app.vault.modify(e,n),i}async configure(t){this.plugin.executing=!1;let e=t.view;if(!(e instanceof w.MarkdownView)){this.data.pin&&e.leaf.setPinned(!0);return}let n=e.getState();if(this.data.revertView&&(this.lastView=new WeakRef(e)),this.data.autoScroll){let i=e.editor.lineCount();n.mode=="preview"?e.previewMode.applyScroll(i-4):(e.editor.setCursor(i),e.editor.focus())}if(this.data.pin&&e.leaf.setPinned(!0),this.data.view!="Default view"){switch(this.data.view){case"Editing view (Live Preview)":case"Editing view (Source)":n.mode="source",n.source=this.data.view!="Editing view (Live Preview)";break;case"Reading view":n.mode="preview";break}await e.leaf.setViewState({type:"markdown",state:n}),this.plugin.loaded&&this.data.refreshDataview&&this.plugin.communityPlugins.dataview?.index.touch()}}getOpened(){return this.data.kind=="Graph view"?this.app.workspace.getLeavesOfType("graph"):xe.flatMap(e=>this.app.workspace.getLeavesOfType(e)).filter(e=>J(N(e.view.file),this.computedValue))}async computeValue(){let t=this.data.value;switch(this.data.kind){case"Date-dependent file":t=(0,w.moment)().format(this.data.value);break;case"Random file":let e=me(this.app);e&&(t=e);break;case"Daily Note":case"Weekly Note":case"Monthly Note":case"Yearly Note":t=await Ee(this.data.kind,this.plugin);break}return t}async save(){this.plugin.settings.homepages[this.name]=this.data,await this.plugin.saveSettings()}async setToActiveFile(){this.data.value=N(this.app.workspace.getActiveFile()),await this.save(),new w.Notice(`The homepage has been changed to "${this.data.value}".`)}canSetToFile(){return this.app.workspace.getActiveFile()!==null&&!le.includes(this.data.kind)}async revertView(){if(this.lastView==null||this.data.view=="Default view")return;let t=this.lastView.deref();if(!t||J(N(t.file),this.computedValue))return;let e=t.getState(),n=this.app.vault.config,i=n.defaultViewMode||"source",o=n.livePreview!==void 0?!n.livePreview:!1;t.leaf.getViewState().type=="markdown"&&(i!=e.mode||o!=e.source)&&(e.mode=i,e.source=o,await t.leaf.setViewState({type:"markdown",state:e,active:!0})),this.lastView=void 0}async openWhenEmpty(){if(!this.plugin.loaded||this.plugin.executing)return;let t=this.app.workspace.getActiveViewOfType(w.View)?.leaf;t?.getViewState().type!=="empty"||t.parentSplit.children.length!=1||await this.open(!0)}async apply(){let t=this.app.workspace.getActiveViewOfType(w.FileView);if(!t)return;let e=N(t.file);this.openedViews.get(t)!==e&&(this.openedViews.set(t,e),e===await this.computeValue()&&this.plugin.loaded&&!this.plugin.executing&&await this.configure(t.leaf))}};var h=require("obsidian");var D=require("obsidian");var q=class extends D.AbstractInputSuggest{getSuggestions(e){let n=this.app.vault.getAllLoadedFiles(),i=[],o=e.toLowerCase();return n.forEach(s=>{s instanceof D.TFile&&["md","canvas"].contains(s.extension)&&s.path.toLowerCase().contains(o)&&i.push(s)}),i}renderSuggestion(e,n){e.extension=="md"?n.setText(N(e)):(n.setText(e.path.slice(0,-7)),n.insertAdjacentHTML("beforeend",'<div class="nav-file-tag" style="display:inline-block;vertical-align:middle">canvas</div>'))}selectSuggestion(e){this.textInputEl.value=N(e),this.textInputEl.trigger("input"),this.close()}},K=class extends D.AbstractInputSuggest{getSuggestions(e){let n=Object.keys(this.app.internalPlugins.plugins.workspaces?.instance.workspaces),i=e.toLowerCase();return n.filter(o=>o.toLowerCase().contains(i))}renderSuggestion(e,n){n.setText(e)}selectSuggestion(e){this.textInputEl.value=e,this.textInputEl.trigger("input"),this.close()}},j=class extends D.FuzzySuggestModal{constructor(e){super(e.plugin.app);this.homepage=e.plugin.homepage,this.tab=e}getItems(){return Object.values(this.app.commands.commands)}getItemText(e){return e.name}onChooseItem(e){if(e.id==="homepage:open-homepage"){new D.Notice("Really?");return}else this.homepage.data.commands||(this.homepage.data.commands=[]);this.homepage.data.commands.push(e.id),this.homepage.save(),this.tab.updateCommandBox()}};var de={version:3,homepages:{[F]:{value:"Home",kind:"File",openOnStartup:!0,openMode:"Replace all open notes",manualOpenMode:"Keep open notes",view:"Default view",revertView:!0,openWhenEmpty:!1,refreshDataview:!1,autoCreate:!0,autoScroll:!1,pin:!1,commands:[],alwaysApply:!1,hideReleaseNotes:!1}},separateMobile:!1},ce=de.homepages[F],$=class extends h.PluginSettingTab{constructor(e,n){super(e,n);this.plugin=n,this.settings=n.settings}sanitiseNote(e){return e===null||e.match(/^\s*$/)!==null?null:(0,h.normalizePath)(e)}display(){let e=this.plugin.homepage.data.kind=="Workspace",n=this.plugin.homepage.data.kind,i=Y(this.plugin),o=!1,s=document.createElement("article"),p=e?K:q;this.containerEl.empty(),this.elements={},s.id="nv-desc";let d=new h.Setting(this.containerEl).setName("Homepage").addDropdown(async r=>{for(let c of Object.values(V)){if(!this.plugin.hasRequiredPlugin(c))if(c==this.plugin.homepage.data.kind)o=!0;else continue;let y=c;if(c=="Date-dependent file"){if(!this.enableMomentOption())continue;y="Moment (legacy)"}r.addOption(c,y)}r.setValue(this.plugin.homepage.data.kind),r.onChange(async c=>{this.plugin.homepage.data.kind=c,await this.plugin.homepage.save(),this.display()})});switch(d.settingEl.id="nv-main-setting",d.settingEl.append(s),n){case"File":s.innerHTML="Enter a note or canvas to use.";break;case"Workspace":s.innerHTML="Enter an Obsidian workspace to use.";break;case"Graph view":s.innerHTML="Your graph view will be used.";break;case"Nothing":s.innerHTML="Nothing will occur by default. Any commands added will still take effect.";break;case"Date-dependent file":s.innerHTML=`<span class="mod-warning">This type is deprecated and will eventually be removed. It is only available since you have previously chosen it. Use Daily/Weekly/Monthly/Yearly Note instead, which works natively with Daily and Periodic Notes.</span><br>
				Enter a note or canvas to use based on <a href="https://momentjs.com/docs/#/displaying/format/" target="_blank" rel="noopener">Moment date formatting</a>.`;break;case"Random file":s.innerHTML="A random note or canvas from your Obsidian folder will be selected.";break;case"Daily Note":s.innerHTML="Your Daily Note or Periodic Daily Note will be used.";break;case"Weekly Note":case"Monthly Note":case"Yearly Note":s.innerHTML=`Your Periodic ${this.plugin.homepage.data.kind} will be used.`;break}o&&s.createDiv({text:"The plugin required for this homepage type isn't available.",attr:{class:"mod-warning"}}),le.includes(n)?d.addText(r=>{r.setDisabled(!0)}):d.addText(r=>{new p(this.app,r.inputEl),r.setPlaceholder(ce.value),r.setValue(ce.value==this.plugin.homepage.data.value?"":this.plugin.homepage.data.value),r.onChange(async c=>{this.plugin.homepage.data.value=this.sanitiseNote(c)||ce.value,await this.plugin.homepage.save()})}),this.addToggle("Open on startup","When launching Obsidian, open the homepage.","openOnStartup",r=>this.display()),i&&(this.elements.openOnStartup.descEl.createDiv({text:`This setting has been disabled, as it isn't compatible with Daily Notes' "Open daily note on startup" functionality. To use it, disable the Daily Notes setting.`,attr:{class:"mod-warning"}}),this.disableSetting("openOnStartup")),this.addToggle("Open when empty","When there are no tabs open, open the homepage.","openWhenEmpty"),this.addToggle("Use when opening normally","Use homepage settings when opening it normally, such as from a link or the file browser.","alwaysApply"),new h.Setting(this.containerEl).setName("Separate mobile homepage").setDesc("For mobile devices, store the homepage and its settings separately.").addToggle(r=>r.setValue(this.plugin.settings.separateMobile).onChange(async c=>{this.plugin.settings.separateMobile=c,this.plugin.homepage=this.plugin.getHomepage(),await this.plugin.saveSettings(),this.display()})),this.addHeading("Commands","commandsHeading"),this.containerEl.createDiv({cls:"nv-command-desc setting-item-description",text:"Select commands that will be executed when opening the homepage."}),this.commandBox=this.containerEl.createDiv({cls:"nv-command-box"}),this.updateCommandBox(),this.addHeading("Vault environment","vaultHeading"),this.addDropdown("Opening method","Determine how extant tabs and views are affected on startup.","openMode",B),this.addDropdown("Manual opening method","Determine how extant tabs and views are affected when opening with commands or the ribbon button.","manualOpenMode",B),this.addToggle("Auto-create","If the homepage doesn't exist, create a note with the specified name.","autoCreate"),this.addToggle("Pin","Pin the homepage when opening.","pin"),this.addToggle("Hide release notes","Never display release notes when Obsidian updates.","hideReleaseNotes"),this.addHeading("Opened view","paneHeading"),this.addDropdown("Homepage view","Choose what view to open the homepage in.","view",re),this.addToggle("Revert view on close","When navigating away from the homepage, restore the default view.","revertView"),this.addToggle("Auto-scroll","When opening the homepage, scroll to the bottom and focus on the last line.","autoScroll"),"dataview"in this.plugin.communityPlugins&&(this.addToggle("Refresh Dataview","Always attempt to reload Dataview views when opening the homepage.","refreshDataview"),this.elements.refreshDataview.descEl.createDiv({text:"Requires Dataview auto-refresh to be enabled.",attr:{class:"mod-warning"}})),h.Platform.isMobile||new h.ButtonComponent(this.containerEl).setButtonText("Copy debug info").setClass("nv-debug-button").onClick(async()=>await this.copyDebugInfo()),(e||n==="Nothing")&&this.disableSettings("openWhenEmpty","alwaysApply","vaultHeading","openMode","manualOpenMode","autoCreate","pin"),(e||["Nothing","Graph view"].includes(n))&&this.disableSettings("paneHeading","view","revertView","autoScroll","refreshDataview"),(!this.plugin.homepage.data.openOnStartup||i)&&this.disableSetting("openMode"),W.includes(this.plugin.homepage.data.kind)&&this.disableSetting("autoCreate")}disableSetting(e){this.elements[e]?.settingEl.setAttribute("nv-greyed","")}disableSettings(...e){e.forEach(n=>this.disableSetting(n))}addHeading(e,n){let i=new h.Setting(this.containerEl).setHeading().setName(e);this.elements[n]=i}addDropdown(e,n,i,o,s){let p=new h.Setting(this.containerEl).setName(e).setDesc(n).addDropdown(async d=>{for(let r of Object.values(o))d.addOption(r,r);d.setValue(this.plugin.homepage.data[i]),d.onChange(async r=>{this.plugin.homepage.data[i]=r,await this.plugin.homepage.save(),s&&s(r)})});this.elements[i]=p}addToggle(e,n,i,o){let s=new h.Setting(this.containerEl).setName(e).setDesc(n).addToggle(p=>p.setValue(this.plugin.homepage.data[i]).onChange(async d=>{this.plugin.homepage.data[i]=d,await this.plugin.homepage.save(),o&&o(d)}));this.elements[i]=s}updateCommandBox(){this.commandBox.innerHTML="";for(let[e,n]of this.plugin.homepage.data.commands.entries()){let i=this.app.commands.findCommand(n);if(!i)continue;let o=this.commandBox.createDiv({cls:"nv-command-pill",text:i.name});new h.ButtonComponent(o).setIcon("trash-2").setClass("clickable-icon").onClick(()=>{this.plugin.homepage.data.commands.splice(e,1),this.plugin.homepage.save(),this.updateCommandBox()})}new h.ButtonComponent(this.commandBox).setClass("nv-command-add-button").setButtonText("Add...").onClick(()=>{new j(this).open()})}enableMomentOption(){return this.plugin.homepage.data.kind=="Date-dependent file"||window.homepageLegacyOptionsEnabled}async copyDebugInfo(){let e=this.app.vault.config,n={...this.settings,_defaultViewMode:e.defaultViewMode||"default",_livePreview:e.livePreview!==void 0?e.livePreview:"default",_focusNewTab:e.focusNewTab!==void 0?e.focusNewTab:"default",_plugins:Object.keys(this.plugin.communityPlugins),_internalPlugins:Object.values(this.plugin.internalPlugins).flatMap(i=>i.enabled?[i.instance.id]:[]),_obsidianVersion:window.electron.ipcRenderer.sendSync("version")};await navigator.clipboard.writeText(JSON.stringify(n)),new h.Notice("Copied homepage debug information to clipboard")}};var ut='<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" xml:space="preserve" style="fill-rule:evenodd;clip-rule:evenodd;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:1.5"><path d="M10.025 21H6v-7H3v-1.5L12 3l9 9.5V14h-3v7h-4v-7h-3.975v7Z" style="fill:none;stroke:currentColor;stroke-width:2px"/></svg>',z=class extends M.Plugin{constructor(){super(...arguments);this.newRelease=!1;this.loaded=!1;this.executing=!1;this.onLayoutChange=async()=>{this.homepage.data.revertView&&await this.homepage.revertView(),this.homepage.data.openWhenEmpty&&await this.homepage.openWhenEmpty(),this.homepage.data.alwaysApply&&await this.homepage.apply()}}async onload(){let e=document.body.querySelector(".progress-bar")!==null;this.patchReleaseNotes(),this.settings=await this.loadSettings(),this.internalPlugins=this.app.internalPlugins.plugins,this.communityPlugins=this.app.plugins.plugins,this.homepage=this.getHomepage(),this.app.workspace.onLayoutReady(async()=>{let n=this.homepage.data.openOnStartup&&e&&!await this.hasUrlParams();this.patchNewTabPage(),n&&await this.homepage.open(),this.loaded=!0,this.unpatchReleaseNotes()}),(0,M.addIcon)("homepage",ut),this.addRibbonIcon("homepage","Open homepage",n=>this.homepage.open(n.button==1||n.button==2||M.Keymap.isModifier(n,"Mod"))).setAttribute("id","nv-homepage-icon"),this.registerEvent(this.app.workspace.on("layout-change",this.onLayoutChange)),this.addSettingTab(new $(this.app,this)),this.addCommand({id:"open-homepage",name:"Open homepage",callback:()=>this.homepage.open()}),this.addCommand({id:"set-to-active-file",name:"Set to active file",checkCallback:n=>{if(n)return this.homepage.canSetToFile();this.homepage.setToActiveFile()}}),console.log(`Homepage: ${this.homepage.data.value} (method: ${this.homepage.data.openMode}, view: ${this.homepage.data.view}, kind: ${this.homepage.data.kind})`)}async onunload(){this.app.workspace.off("layout-change",this.onLayoutChange),this.unpatchNewTabPage()}getHomepage(){return this.settings.separateMobile&&M.Platform.isMobile?(U in this.settings.homepages||(this.settings.homepages[U]={...this.settings.homepages[F]}),new _(U,this)):new _(F,this)}async loadSettings(){let e=await this.loadData();if(!e||e.version!==2)return Object.assign({},de,e);{let n={version:3,homepages:{},separateMobile:!1},i=e;return e.workspaceEnabled?(i.value=i.workspace,i.kind="Workspace"):e.useMoment?(i.value=i.momentFormat,i.kind="Date-dependent file"):(i.value=i.defaultNote,i.kind="File"),i.commands=[],delete i.workspace,delete i.momentFormat,delete i.defaultNote,delete i.useMoment,delete i.workspaceEnabled,n.homepages[F]=i,n}}async saveSettings(){await this.saveData(this.settings)}async hasUrlParams(){let e,n;if(M.Platform.isMobile){let i=await window.Capacitor.Plugins.App.getLaunchUrl();if(!i)return!1;let o=new URL(i.url);n=Array.from(o.searchParams.keys()),e=o.hostname}else if(window.OBS_ACT)n=Object.keys(window.OBS_ACT),e=window.OBS_ACT.action;else return!1;return["open","advanced-uri"].includes(e)&&["file","filepath","workspace"].some(i=>n.includes(i))}hasRequiredPlugin(e){switch(e){case"Workspace":return this.internalPlugins.workspaces?.enabled;case"Graph view":return this.internalPlugins.graph?.enabled;case"Daily Note":case"Weekly Note":case"Monthly Note":case"Yearly Note":return Le(e,this);default:return!0}}patchNewTabPage(){let e=this.communityPlugins["new-tab-default-page"];e&&(e.nvOrig_checkForNewTab=e.checkForNewTab,e.checkForNewTab=async n=>{if(!(this&&this.executing))return await e.nvOrig_checkForNewTab(n)})}unpatchNewTabPage(){let e=this.communityPlugins["new-tab-default-page"];e&&(e.checkForNewTab=e._checkForNewTab)}patchReleaseNotes(){this.app.nvOrig_showReleaseNotes=this.app.showReleaseNotes,this.app.showReleaseNotes=()=>this.newRelease=!0}unpatchReleaseNotes(){this.newRelease&&!this.homepage.data.hideReleaseNotes&&this.app.nvOrig_showReleaseNotes(),this.app.showReleaseNotes=this.app.nvOrig_showReleaseNotes}};
