2025-05-01 16:25:18 [info] components database created cost 2 ms   
2025-05-01 16:25:18 [info] components index initializing...   
2025-05-01 16:25:18 [info] start to batch put pages: 5   
2025-05-01 16:25:19 [info] batch persist cost 5  1112 
2025-05-01 16:25:19 [info] components index initialized, 863 files cost 1338 ms   
2025-05-01 16:25:19 [info] refresh page data from init listeners 0 863   
2025-05-01 16:25:21 [info] indexing created file components/logs/2025-05-01.components.log  [object Object] 
2025-05-01 16:25:21 [info] refresh page data from created listeners 0 864   
2025-05-01 16:25:21 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-05-01 16:25:21 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-05-01 16:25:21 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-05-01 16:25:21 [info] ignore file modify evnet Home/components/view/remember.components   
2025-05-01 16:25:22 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-05-01 16:25:22 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-05-01 16:25:33 [error] check new version failed  Error: net::ERR_CONNECTION_CLOSED 
2025-05-01 16:26:08 [info] refresh page data from rename listeners 0 864   
2025-05-01 16:26:08 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-05-01 16:26:08 [info] refresh page data from rename listeners 0 864   
2025-05-01 16:26:08 [info] refresh page data from rename listeners 0 864   
2025-05-01 16:26:08 [info] refresh page data from rename listeners 0 864   
2025-05-01 16:26:08 [info] refresh page data from rename listeners 0 864   
2025-05-01 16:26:08 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-05-01 16:26:08 [info] refresh page data from rename listeners 0 864   
2025-05-01 16:26:08 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-05-01 16:26:08 [info] refresh page data from rename listeners 0 864   
2025-05-01 16:26:08 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-05-01 16:26:08 [info] refresh page data from rename listeners 0 864   
2025-05-01 16:26:08 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-05-01 16:26:08 [info] refresh page data from rename listeners 0 864   
2025-05-01 16:26:08 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-05-01 16:26:08 [info] refresh page data from rename listeners 0 864   
2025-05-01 16:26:08 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-05-01 16:26:09 [info] refresh page data from rename listeners 0 864   
2025-05-01 16:26:09 [info] trigger 学习库/Deep learning/概念库/卷积层/卷积和转置卷积.md resolve  [object Object] 
2025-05-01 16:26:16 [info] refresh page data from rename listeners 0 864   
2025-05-01 16:26:16 [info] trigger 学习库/Anki/Deep learning/概念.md resolve  [object Object] 
2025-05-01 16:26:16 [info] trigger 学习库/Deep learning/概念库/上采样；下采样/上采样；下采样.md resolve  [object Object] 
2025-05-01 16:26:16 [info] trigger 学习库/Deep learning/概念库/卷积和转置卷积.md resolve  [object Object] 
2025-05-01 16:26:16 [info] index finished after resolve  [object Object] 
2025-05-01 16:26:16 [info] refresh page data from resolve listeners 0 864   
2025-05-01 16:29:39 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积和转置卷积.md   
2025-05-01 16:29:40 [info] trigger 学习库/Deep learning/概念库/卷积和转置卷积.md resolve  [object Object] 
2025-05-01 16:29:40 [info] index finished after resolve  [object Object] 
2025-05-01 16:29:40 [info] refresh page data from resolve listeners 0 864   
2025-05-01 16:29:42 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积和转置卷积.md   
2025-05-01 16:29:42 [info] trigger 学习库/Deep learning/概念库/卷积和转置卷积.md resolve  [object Object] 
2025-05-01 16:29:42 [info] index finished after resolve  [object Object] 
2025-05-01 16:29:42 [info] refresh page data from resolve listeners 0 864   
2025-05-01 16:29:44 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积和转置卷积.md   
2025-05-01 16:29:44 [info] trigger 学习库/Deep learning/概念库/卷积和转置卷积.md resolve  [object Object] 
2025-05-01 16:29:44 [info] index finished after resolve  [object Object] 
2025-05-01 16:29:45 [info] refresh page data from resolve listeners 0 864   
2025-05-01 16:30:40 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积和转置卷积.md   
2025-05-01 16:30:40 [info] trigger 学习库/Deep learning/概念库/卷积和转置卷积.md resolve  [object Object] 
2025-05-01 16:30:40 [info] index finished after resolve  [object Object] 
2025-05-01 16:30:40 [info] refresh page data from resolve listeners 0 864   
2025-05-01 16:31:30 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积和转置卷积.md   
2025-05-01 16:31:30 [info] trigger 学习库/Deep learning/概念库/卷积和转置卷积.md resolve  [object Object] 
2025-05-01 16:31:30 [info] index finished after resolve  [object Object] 
2025-05-01 16:31:30 [info] refresh page data from resolve listeners 0 864   
2025-05-01 16:31:32 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积和转置卷积.md   
2025-05-01 16:31:32 [info] trigger 学习库/Deep learning/概念库/卷积和转置卷积.md resolve  [object Object] 
2025-05-01 16:31:32 [info] index finished after resolve  [object Object] 
2025-05-01 16:31:32 [info] refresh page data from resolve listeners 0 864   
2025-05-01 16:31:42 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积和转置卷积.md   
2025-05-01 16:31:42 [info] trigger 学习库/Deep learning/概念库/卷积和转置卷积.md resolve  [object Object] 
2025-05-01 16:31:42 [info] index finished after resolve  [object Object] 
2025-05-01 16:31:42 [info] refresh page data from resolve listeners 0 864   
2025-05-01 16:31:47 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积和转置卷积.md   
2025-05-01 16:31:47 [info] trigger 学习库/Deep learning/概念库/卷积和转置卷积.md resolve  [object Object] 
2025-05-01 16:31:47 [info] index finished after resolve  [object Object] 
2025-05-01 16:31:47 [info] refresh page data from resolve listeners 0 864   
2025-05-01 16:31:50 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积和转置卷积.md   
2025-05-01 16:31:50 [info] trigger 学习库/Deep learning/概念库/卷积和转置卷积.md resolve  [object Object] 
2025-05-01 16:31:50 [info] index finished after resolve  [object Object] 
2025-05-01 16:31:50 [info] refresh page data from resolve listeners 0 864   
2025-05-01 16:31:52 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积和转置卷积.md   
2025-05-01 16:31:52 [info] trigger 学习库/Deep learning/概念库/卷积和转置卷积.md resolve  [object Object] 
2025-05-01 16:31:52 [info] index finished after resolve  [object Object] 
2025-05-01 16:31:52 [info] refresh page data from resolve listeners 0 864   
2025-05-01 16:32:01 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积和转置卷积.md   
2025-05-01 16:32:02 [info] trigger 学习库/Deep learning/概念库/卷积和转置卷积.md resolve  [object Object] 
2025-05-01 16:32:02 [info] index finished after resolve  [object Object] 
2025-05-01 16:32:02 [info] refresh page data from resolve listeners 0 864   
2025-05-01 16:32:06 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积和转置卷积.md   
2025-05-01 16:32:06 [info] trigger 学习库/Deep learning/概念库/卷积和转置卷积.md resolve  [object Object] 
2025-05-01 16:32:06 [info] index finished after resolve  [object Object] 
2025-05-01 16:32:06 [info] refresh page data from resolve listeners 0 864   
2025-05-01 16:32:10 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积和转置卷积.md   
2025-05-01 16:32:10 [info] trigger 学习库/Deep learning/概念库/卷积和转置卷积.md resolve  [object Object] 
2025-05-01 16:32:10 [info] index finished after resolve  [object Object] 
2025-05-01 16:32:10 [info] refresh page data from resolve listeners 0 864   
2025-05-01 16:32:13 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积和转置卷积.md   
2025-05-01 16:32:13 [info] trigger 学习库/Deep learning/概念库/卷积和转置卷积.md resolve  [object Object] 
2025-05-01 16:32:13 [info] index finished after resolve  [object Object] 
2025-05-01 16:32:13 [info] refresh page data from resolve listeners 0 864   
2025-05-01 16:32:15 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积和转置卷积.md   
2025-05-01 16:32:15 [info] trigger 学习库/Deep learning/概念库/卷积和转置卷积.md resolve  [object Object] 
2025-05-01 16:32:15 [info] index finished after resolve  [object Object] 
2025-05-01 16:32:15 [info] refresh page data from resolve listeners 0 864   
2025-05-01 16:32:18 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积和转置卷积.md   
2025-05-01 16:32:19 [info] trigger 学习库/Deep learning/概念库/卷积和转置卷积.md resolve  [object Object] 
2025-05-01 16:32:19 [info] index finished after resolve  [object Object] 
2025-05-01 16:32:19 [info] refresh page data from resolve listeners 0 864   
2025-05-01 16:32:26 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积和转置卷积.md   
2025-05-01 16:32:26 [info] trigger 学习库/Deep learning/概念库/卷积和转置卷积.md resolve  [object Object] 
2025-05-01 16:32:26 [info] index finished after resolve  [object Object] 
2025-05-01 16:32:26 [info] refresh page data from resolve listeners 0 864   
2025-05-01 16:32:28 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积和转置卷积.md   
2025-05-01 16:32:28 [info] trigger 学习库/Deep learning/概念库/卷积和转置卷积.md resolve  [object Object] 
2025-05-01 16:32:28 [info] index finished after resolve  [object Object] 
2025-05-01 16:32:28 [info] refresh page data from resolve listeners 0 864   
2025-05-01 16:33:00 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积和转置卷积.md   
2025-05-01 16:33:00 [info] trigger 学习库/Deep learning/概念库/卷积和转置卷积.md resolve  [object Object] 
2025-05-01 16:33:00 [info] index finished after resolve  [object Object] 
2025-05-01 16:33:00 [info] refresh page data from resolve listeners 0 864   
2025-05-01 16:33:44 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积和转置卷积.md   
2025-05-01 16:33:44 [info] trigger 学习库/Deep learning/概念库/卷积和转置卷积.md resolve  [object Object] 
2025-05-01 16:33:44 [info] index finished after resolve  [object Object] 
2025-05-01 16:33:44 [info] refresh page data from resolve listeners 0 864   
2025-05-01 16:33:50 [info] ignore file modify evnet 学习库/Deep learning/概念库/卷积和转置卷积.md   
2025-05-01 16:33:50 [info] trigger 学习库/Deep learning/概念库/卷积和转置卷积.md resolve  [object Object] 
2025-05-01 16:33:50 [info] index finished after resolve  [object Object] 
2025-05-01 16:33:50 [info] refresh page data from resolve listeners 0 864   
2025-05-01 16:36:39 [info] refresh page data from rename listeners 0 864   
2025-05-01 16:36:39 [info] trigger 学习库/Deep learning/YOLOv5.md resolve  [object Object] 
2025-05-01 16:36:39 [info] trigger 学习库/Deep learning/概念库/空间金字塔池化/spp(spatial pyramid pooling).md resolve  [object Object] 
2025-05-01 16:37:21 [info] refresh page data from rename listeners 0 864   
2025-05-01 16:37:21 [info] trigger 学习库/Deep learning/概念库/上采样；下采样/上采样；下采样.md resolve  [object Object] 
2025-05-01 16:37:21 [info] refresh page data from rename listeners 0 864   
2025-05-01 16:37:21 [info] trigger 学习库/Deep learning/概念库/上采样；下采样/上采样；下采样.md resolve  [object Object] 
2025-05-01 16:37:21 [info] refresh page data from rename listeners 0 864   
2025-05-01 16:37:21 [info] trigger 学习库/Deep learning/概念库/上采样；下采样/上采样；下采样.md resolve  [object Object] 
2025-05-01 16:37:21 [info] refresh page data from rename listeners 0 864   
2025-05-01 16:37:21 [info] refresh page data from rename listeners 0 864   
2025-05-01 16:37:21 [info] trigger 学习库/Deep learning/概念库/上采样；下采样/上采样；下采样.md resolve  [object Object] 
2025-05-01 16:37:21 [info] refresh page data from rename listeners 0 864   
2025-05-01 16:37:21 [info] refresh page data from rename listeners 0 864   
2025-05-01 16:37:21 [info] trigger 学习库/Deep learning/概念库/上采样；下采样/上采样；下采样.md resolve  [object Object] 
2025-05-01 16:37:24 [info] refresh page data from rename listeners 0 864   
2025-05-01 16:37:24 [info] trigger 学习库/Deep learning/Unet.md resolve  [object Object] 
2025-05-01 16:37:24 [info] trigger 学习库/Deep learning/概念库/卷积和转置卷积.md resolve  [object Object] 
2025-05-01 16:37:37 [info] ignore file modify evnet 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md   
2025-05-01 16:37:37 [info] trigger 学习库/Deep learning/概念库/独热编码(One-Hot Encoding).md resolve  [object Object] 
2025-05-01 16:37:37 [info] index finished after resolve  [object Object] 
2025-05-01 16:37:37 [info] refresh page data from resolve listeners 0 864   
2025-05-01 17:25:01 [info] indexing created file 学习库/Deep learning/pytorch/6. 处理多维特征的输入  [object Object] 
2025-05-01 17:25:01 [info] refresh page data from created listeners 0 865   
