---
tags:
  - 学习
  - deep_learning
---
# 激活函数

**激活函数 (Activation Function)**  是人工神经网络中的一个核心组件。在一个神经元中，来自上一层节点的输入会先进行加权求和，然后这个总和会通过一个**非线性的数学函数**进行处理，这个函数就是“激活函数”。处理后的结果将作为该神经元的输出，并传递给下一层的神经元。

>可以把它想象成生物神经元中的一个开关机制。生物神经元接收到来自其他神经元的信号后，并不会立即将所有信号都传递出去，而是会累积这些信号。只有当累积的信号强度超过一个特定的“阈值”时，这个神经元才会被“激活”并向其他神经元发送信号。

---
## 激活函数的作用

激活函数最核心、最关键的作用是**为神经网络引入非线性**。
如果一个神经网络没有激活函数（或者说激活函数是线性函数 $f(x)=x$），那么这个网络无论有多少层，每一层的输出都只是是上一层的线性组合，这样的网络即使很深，但能力也和一个单层的线性模型没有本质的区别。
通过**引入非线性的激活函数**，每一层就可以在前一层的基础上进行线性变换，使得**网络能够拟合和表示复杂的函数关系**。

```ad-hint
title: 线性函数与非线性函数拟合数据的对比
![[激活函数-2025-07-12-10-08-54.png]]
- 线性函数（直线）无法捕捉数据的 "u" 型分布趋势，这是欠拟合的情况
- 非线性函数（曲线）可以穿过这些数据点，捕捉 "u" 型分布趋势，表示了非线性函数可以拟合负载的数据
```

除了引入非线性，激活函数还有一些其它的功能：
1. **控制输出范围**: 像 Sigmoid 和 Tanh 这样的激活函数可以将任意大小的输入值压缩到-个固定的范围内（例如 Sigmoid 的 (0, 1) 和 Tanh 的 (-1, 1))。这有助于在训练过程中保持数据和梯度的稳定。例如，Sigmoid 的输出可以被直接解释为概率。
    
2. **增强网络稀疏性**: 像 ReLU 这样的函数，当输入为负时输出为 0。这会导致网络中的一些神经元被“关闭”，从而形成一个稀疏的网络。这种稀疏性可以降低参数间的相互依赖，在一定程度上防止过拟合。
    
3. **影响训练动态**: 不同的激活函数有不同的导数形式，这会直接影响梯度在反向传播过程中的流动。选择合适的激活函数（如 ReLU）可以有效缓解梯度消失问题，从而让深度网络的训练变得更加容易和高效。


## ReLU（Rectified Linear Unit，线性整流函数）

```ad-col2
title: ReLU激活函数
color:178,22,164

$$
f(x) = max(0,x)
$$
ReLU函数虽然在==$X<0$ 的区间上是导数恒为 0==的线性函数， ==$X>=0$ 的区间上是导数恒为 1== 的线性函数。
但是，从整体来看，在定义域为 $−∞<X<+∞$ 区间上却是一个非线性函数，或者说是分段线性函数。
![[激活函数-2025-04-24-15-09-01.png|350]]

```

- 梯度计算快速，且在==正区间不会出现梯度消失==
- 对输入小于 0 的部分输出 0，从而使模型具有稀疏性。
- ReLU 及其变体是==隐藏层==的首选激活函数

## Sigmoid

```ad-col2
title: Sigmoid激活函数
color:178,22,164
$$f(x) = \frac{1}{1+e^{-x}}$$
可以将一个实数映射到 $(0,1)$ 的区间，用来做**二分类**。

![[激活函数-2025-04-24-15-18-54.png|350]]
```

- 将输入映射到 (0, 1) 范围。
- 常用于概率输出的场景。
- 缺点：容易导致梯度消失，尤其是在输入值绝对值很大时，梯度接近 0。
- **Sigmoid 主要用于输出层**，分别处理二元分类问题。

## Tanh（双正切函数）

```ad-col2
title: Tanh函数
color:178,22,164
$f(x) = tanh(x) = \frac{e^{x}-e^{-x}}{e^{x}+e^{-x}}$ 
值域为 (-1,1)

![[激活函数-2025-04-24-15-21-53.png|350]]
```

- 将输入映射到 (−1, 1) 范围。
- 当输入接近 0 时，梯度较大，收敛速度快于 Sigmoid。
- 缺点：大输入值时也可能导致梯度消失。

## softmax
```ad-col2
title: softmax函数
color:178,22,164
$$\sigma(z_{i}) =\frac{e^{z_{i}}}{ {\textstyle \sum_{j=1}^{n}}e^{z_{j}}}$$
Softmax函数是一种用于**多分类模型**的激活函数，其主要作用是将一个实数向量转换为概率分布。这个函数常用于神经网络的最后一层，用于将网络的输出转换为各个类别的概率
![[激活函数-2025-04-24-15-27-02.png|350]]
```

- 归一化：Softmax 函数将输入向量中的每个元素转换为一个非负值，这些值的总和为 1，因此可以看作是一个概率分布
- 应用场景：softmax 函数通常用于多分类问题的输出层。例如，在图像分类问题中，Softmax 函数将神经网络的输出层的结果转换为每个类别的概率，进而决定最终分类结果
-  **Softmax 则主要用于输出层**，处理多类别分类问题。

### 计算公式 
1. 先对每个输入元素 $z_{i}$ 进行指数运算 $e^{z_{i}}$
2. 然后再将所有的指数相加得到分母，从而归一化每个元素的值，使其范围再\[0,1\]内
3. 对于每个元素除分母就是它在所有元素中所占的概率。

假设输入是一个长度为 n 的实数向量 $$z=\begin{bmatrix}z_{1},&z_{2},&.....&,&z_{n}\end{bmatrix}$$ 经过 sofmax 函数计算可以得到长度为 n 的概率向量 $$z=\begin{bmatrix}\sigma (z_{1}) ,&\sigma (z_{2}),&.....&,&\sigma (z_{n})\end{bmatrix}$$ 
### 例题
假设有一个三维向量 $z=[2, 1, 0.1]$，计算其 Softmax 输出
```ad-hibox
```
```ad-hibox

```
```ad-hibox
对向量内各个元素值计算指数得：
$$
[e^{2}, e^{1}, e^{0.1}]=[7.39, 2.72, 1.11]
$$
将各个元素的指数值进行求和以计算分母：
$$
7.39+2.72+1.11=11.22
$$
每个元素的softmax值如下：
$$
[\sigma (z_{1}),\sigma (z_{2}),\sigma (z_{3})]=[\frac{7.39}{11.22},\frac{2.72}{11.22},\frac{1.11}{11.22}]=[0.66,0.24,0.1]
$$
```