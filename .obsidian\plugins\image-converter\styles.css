/* go beyond the line width */
/* .markdown-reading-view img,
.markdown-source-view img,
.internal-embed.image-embed {
    max-width: 100%;
    height: auto;
} */


/* Preserve image dimensions - with consideration for Image Gallery plugin*/
/* .markdown-reading-view img[style*="width"]:not(.grid-item img),
.markdown-source-view img[style*="width"]:not(.grid-item img) {
    height: auto !important;
} */



/* --- Image Converter Settings --- */
/* Remove X icon and get use all available space*/
.image-converter-settings-tab {
    padding: 20px;
    position: relative;
    /* Relative positioning for child elements */
    height: 100%;
    /* Occupy full height */
    overflow: auto;
    /* Enable scrolling if content overflows */
}

/* ------------------- Tabs ------------------------------ */
.image-converter-setting-tabs {
    display: flex;
    margin-bottom: 15px;
    border-bottom: 1px solid var(--background-modifier-border);
    justify-content: center;
    overflow-x: auto;
    /* Horizontal scrolling for many tabs */
}

.image-converter-setting-tabs::-webkit-scrollbar {
    display: none;
    /* Hide scrollbar */
}

.image-converter-tab {
    padding: 8px 12px;
    margin-right: 5px;
    margin-bottom: -1px;
    /* Overlap the bottom border slightly */
    border: 1px solid transparent;
    border-radius: 5px 5px 0 0;
    /* Rounded corners at the top */
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-adaptive-normal);
    transition: background-color 0.1s ease-in-out;
    /* Smooth hover effect */
    border-bottom: 3px solid transparent;
}

/* Remove left margin for the first tab */
.image-converter-tab:first-child {
    margin-left: 0;
}

/* Remove right margin for the last tab */
.image-converter-tab:last-child {
    margin-right: 0;
}

.image-converter-tab:hover {
    /* Remove background color on hover */
    /* background-color: var(--background-modifier-hover); */

    /* Add underline on hover */
    border-bottom-color: var(--interactive-accent);
    /* Use an accent color for the underline */
}

/* Active tab styling */
.image-converter-tab-active {
    /* Remove background color */
    /* border-color: var(--background-modifier-border); */
    /* border-bottom-color: var(--background-primary); */
    /* background-color: var(--background-primary);  */

    font-weight: 600;

    /* Add underline for active tab */
    border-bottom-color: var(--interactive-accent);
    /* Match the hover underline color */
}

.image-converter-tab>svg {
    width: 1.2em;
    height: 1.2em;
    margin-right: 6px;
    stroke-width: 1.5;
}

/* Tab label text - inherit font weight (normal or slightly bolder) */
.image-converter-tab-label {
    /* No specific styles needed here - inherits from parent */
}

/* ------------------- Preset Group --------------------- */
.image-converter-preset-group {
    margin-bottom: 20px;
    position: relative;
    /* For absolute positioning of child elements */
    z-index: 1;
    /* Ensure preset group is above other elements */
    transition: transform 0.3s ease, filter 0.3s ease, opacity 0.3s ease;
    transform-origin: top center;
    transform: scale(1);
    /* Initially no scaling */
    filter: brightness(1) blur(0);
    /* No initial filter */
    opacity: 1;
    /* Fully opaque */
}

.image-converter-preset-group-title {
    margin-bottom: 15px;
    font-size: 1.2em;
    font-weight: 600;
    border-bottom: 2px solid var(--background-modifier-border);
    padding-bottom: 5px;
    display: none;
    /* HIDDEN - Remove this to show titles */
}

.image-converter-preset-group-description {
    font-size: var(--font-ui-smaller);
    color: var(--text-muted);
    margin-bottom: 1em;
}

/* ---------------------- Preset Card ------------------------------- */
.image-converter-preset-card {
    border: 1px solid var(--background-modifier-border);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
    /* Subtle shadow */
    padding: 14px 20px;
    display: flex;
    flex-direction: column;
    background-color: var(--background-primary);
    height: auto;
    transition: opacity 0.3s ease;
    overflow: hidden;
    /* Prevent content from spilling out */
}

/* "Grabbing" cursor when dragging */
.image-converter-preset-card:active {
    cursor: grabbing;
}

/* Pointer cursor for non-add-new cards */
.image-converter-preset-card:not(.image-converter-add-new-preset) {
    cursor: pointer;
}

.image-converter-preset-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    margin-top: -20px;
    cursor: move;
    /* Indicate draggable */
}

.image-converter-preset-card-title {
    font-size: calc(var(--font-ui-small) + 1px);
    font-weight: 600;
    color: var(--text-normal);
    overflow: hidden;
    text-overflow: ellipsis;
    /* Add ellipsis (...) for long titles */
    white-space: nowrap;
}

/* --- Button Styling --- */
.image-converter-preset-card-actions {
    display: flex;
    gap: 2px;
    margin-right: -16px;
}

.image-converter-preset-card-actions .clickable-icon {
    color: var(--text-muted);
    transition: color 0.2s ease;
}

.image-converter-preset-card-actions .clickable-icon:hover {
    color: var(--text-accent);
}

/* --- Danger Button (e.g., DELETE) --- */
.image-converter-preset-card-actions .danger {
    color: var(--text-error);
}

/* --- SortableJS Ghost Styling --- */
.image-converter-sortable-ghost {
    opacity: 0.5;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

.image-converter-preset-card-body {
    color: var(--text-mute);
    font-size: 0.75em;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: wrap;
    margin-top: -22px;
}

.image-converter-preset-card-body p {
    margin-block-start: 0;
    margin-block-end: 0;
    line-height: 1.4;
}

/* --- Preset Cards Grid --- */
.image-converter-preset-cards {
    display: grid;
    gap: 15px;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    /* Responsive grid */
    cursor: grab;
    /* Indicate draggable */
}

/* Media Query for 3-column layout on larger screens */
@media (min-width: 1000px) {
    .image-converter-preset-cards {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* --- Active and Default Preset Styling --- */
.image-converter-active-preset {
    border-color: var(--interactive-accent);
    box-shadow: 0 0 5px var(--interactive-accent);
}

.image-converter-default-preset {
    background-color: var(--background-modifier-hover);
}

/* --- Custom Variables for Default Preset Background --- */
:root {
    --image-converter-default-bg: #f5f5f5;
    /* Light mode default */
}

html[data-theme='dark'] {
    --image-converter-default-bg: #2b2b2b;
    /* Dark mode default */
}

/* ----------------------------- Preset Form ------------ */
.image-converter-preset-form {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.image-converter-form-buttons {
    display: flex;
    justify-content: flex-end;
    /* Align buttons to the right */
    gap: 10px;
    margin-top: 10px;
}

/* ------------------- Add New Preset Card --------------- */
.image-converter-add-new-preset {
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px dashed var(--background-modifier-border);
    min-height: 100px;
    height: 100%;
    cursor: pointer;
    transition: background-color 0.2s ease;
    text-align: center;
}

.image-converter-add-new-preset:hover {
    background-color: var(--background-modifier-hover);
}

.image-converter-add-new-preset-text {
    font-weight: 500;
    color: var(--text-accent);
}

/* ----------- Modal Styling --------------------------*/
.image-converter-confirm-modal-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 0.5em;
    margin-top: 1em;
}

/* --------- GLOBAL PRESET ------------------------------ */
.image-converter-global-preset-container {
    display: flex;
    align-items: center;
    gap: 10px;
    /* margin-bottom: 20px; */
    cursor: pointer;
}

.image-converter-global-preset-container .image-converter-global-preset-title {
    width: 135px;
}

/* --- Dropdown --- */
.image-converter-global-preset-container .setting-item {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-grow: 1;
    width: 100%;
    border-top: none;
    min-width: 112px;
}

.image-converter-global-preset-container .settings-section-title {
    margin-left: 0.5em;
    margin-bottom: 1px;
    font-weight: 600;
    width: 130px;
}

.image-converter-global-preset-container .setting-item-name {
    flex: 0 0 auto;
    /* Prevent label from shrinking */
}

/* --- Clickable Toggle Area --- */
.image-converter-global-preset-toggle {
    display: flex;
    align-items: center;
    margin-right: 10px;
    font-weight: 600;
}

.image-converter-chevron-icon {
    margin-right: 5px;
    transition: transform 0.2s ease;
}

/* Rotate chevron when collapsed */
.image-converter-settings-tab:not(.global-presets-visible) .image-converter-chevron-icon {
    transform: rotate(-90deg);
}





/* --------------- IMAGE ALIGNMENT SETTINGS ------------------------------ */
/* ----------------------------------------------------------------------- */

/* --- Section Container --- */
.image-converter-settings-tab .image-alignment-settings-section {
    display: flex;
    /* align-items: center; */
    gap: 10px;
    /* margin-bottom: 10px; */
    cursor: pointer;
    flex-direction: column;
}


/* --- Section Header (Clickable Toggle Area) --- */
.image-converter-settings-tab .image-alignment-settings-section .settings-section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 8px 0;
}


.image-converter-settings-tab .image-alignment-settings-section .settings-section-title {
    /* flex-grow: 1; */
    margin-left: 0.5em;
    font-weight: 600;
    margin-bottom: 5px;
}

.image-converter-settings-tab .image-alignment-settings-section .settings-section-toggle-button {
    margin-left: auto;
    margin-right: 0;
}


/* --- Chevron Icon --- */
.image-converter-settings-tab .image-alignment-settings-section .settings-section-chevron-icon {
    margin-right: 5px;
    transition: transform 0.2s ease;
}


/* --- HIDE CHEVRON WHEN IMAGE ALIGNMENT IS DISABLED --- */
.image-converter-settings-tab .image-alignment-settings-section:not(.image-alignment-enabled) .settings-section-chevron-icon {
    /* Refined selector */
    display: none;
    /* Hide chevron when image alignment section is disabled */
}


/* --- Indentation for Settings within Section --- */
.image-converter-settings-tab .image-alignment-settings-section .setting-item {
    padding-left: 1.5em;
    border-top: none;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
}

/* --- Hide Settings when Section is Collapsed --- */
.image-converter-settings-tab .image-alignment-settings-section.settings-section-collapsed>*:not(.settings-section-header) {
    display: none;
}

.settings-section-clarification-text {
    margin-left: 19px;
    color: var(--text-muted);
    font-size: var(--font-ui-smaller);
    padding-top: var(--size-4-1);
    line-height: var(--line-height-tight);
}

/* ----------------------------------------------------------------------- */
/* ----------------------------------------------------------------------- */



/* --------- Image Drag & Resize Settings Section --------- */

/* --- Main Container --- */
.image-drag-resize-settings-section {
    display: flex;
    /* align-items: center; */
    gap: 10px;
    /* margin-bottom: 10px; */
    cursor: pointer;
    flex-direction: column;
}

/* --- Collapsible Section Header --- */
.image-drag-resize-settings-section .settings-section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 8px 0;
}

/* --- Section Title --- */
.image-drag-resize-settings-section .settings-section-title {
    /* flex-grow: 1; */
    margin-left: 0.5em;
    margin-bottom: 5px;
    font-weight: 600;
}

/* --- Toggle Switch --- */
.image-drag-resize-settings-section .settings-section-toggle-button {
    margin-left: auto;
    margin-right: 0;
}


/* --- Chevron Icon --- */
.image-drag-resize-settings-section .settings-section-chevron-icon {
    margin-right: 5px;
    transition: transform 0.2s ease;
}

/* --- HIDE CHEVRON WHEN IMAGE ALIGNMENT IS DISABLED --- */
.image-converter-settings-tab .image-drag-resize-settings-section:not(.image-drag-resize-enabled) .settings-section-chevron-icon {
    /* Refined selector */
    display: none;
    /* Hide chevron when image alignment section is disabled */
}

/* --- Indentation for Settings within Section --- */
.image-converter-settings-tab .image-drag-resize-settings-section .setting-item {
    padding-left: 1.5em;
    border-top: none;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
}

/* --- Hide Settings when Section is Collapsed --- */
.image-converter-settings-tab .image-drag-resize-settings-section.settings-section-collapsed>*:not(.settings-section-header) {
    display: none;
}

/* ----------------------------------------------------------------------- */
/* ----------------------------------------------------------------------- */



/* --- Hide Tabs and Content --- */
/* Hide tabs and preset groups when global presets are not visible */
.image-converter-settings-tab:not(.global-presets-visible) .image-converter-setting-tabs,
.image-converter-settings-tab:not(.global-presets-visible) .image-converter-tab-content-wrapper {
    display: none;
}

/* ------- Preset Summary in Modal ----------------------*/
.image-converter-preset-summary {
    margin-top: 10px;
}

.image-converter-preset-summary .summary-section {
    margin-bottom: 15px;
}

.image-converter-preset-summary .summary-section-title {
    font-weight: bold;
    font-size: 1.1em;
    margin-bottom: 5px;
}

.image-converter-preset-summary .summary-item {
    margin-left: 20px;
    margin-bottom: 3px;
}

.image-converter-preset-summary .summary-label {
    font-weight: 600;
}

.image-converter-preset-summary .summary-value {
    font-style: italic;
}

.image-converter-preset-summary .summary-value-bold {
    font-weight: bold;
}

.image-converter-summary-example {
    font-style: italic;
}

/* --------------------------- Form Container ----------- */
.image-converter-form-container {
    margin-top: 20px;
    padding: 20px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
    background-color: var(--background-primary);
    display: none;
    z-index: 2;
    /* Ensure form is above preset group */
    position: relative;
}

/* Class to make the form container visible */
.image-converter-form-container.visible {
    display: block;
    border: var(--interactive-accent);
    box-shadow: 0 0 5px var(--interactive-accent);
    animation: slideDown 0.3s ease forwards;
}


/* --- Responsive Design --- */
@media (max-width: 600px) {
    .image-converter-available-variables-modal {
        width: 95vw;
    }

    .image-converter-available-variables-modal th,
    .image-converter-available-variables-modal td {
        font-size: 0.9em;
        padding: 8px;
    }
}

/* --- Animation --- */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ------------- VARIABLES Modal -----------------------*/
.image-converter-available-variables-modal {
    width: 90vw;
    max-width: 800px;
}

.image-converter-available-variables-modal .modal-content {
    padding: 0px;
    /* Remove padding */
}

.image-converter-available-variables-modal .modal-title {
    margin-bottom: 20px;
    font-size: 1.4em;
    font-weight: 600;
}

.image-converter-available-variables-modal h4 {
    margin-top: 20px;
    margin-bottom: 10px;
    font-size: 1.2em;
    font-weight: 600;
    color: var(--text-normal);
}

.image-converter-available-variables-modal table {
    width: 100%;
    border-collapse: collapse;
}

.image-converter-available-variables-modal th,
.image-converter-available-variables-modal td {
    border: 1px solid var(--background-modifier-border);
    padding: 10px;
    text-align: left;
    font-size: 0.95em;
    vertical-align: top;
}

.image-converter-available-variables-modal th {
    background-color: var(--background-secondary);
    font-weight: 600;
}

.image-converter-available-variables-modal td:first-child {
    font-family: monospace;
    font-weight: 600;
    color: var(--text-accent);
    white-space: nowrap;
}

.image-converter-available-variables-modal td:nth-child(2) {
    /* width: 60%; */
    /* Adjust width ratio as needed */
}

.image-converter-available-variables-modal td:last-child {
    font-family: monospace;
    color: var(--text-muted);
}

/* -------------- Link FORMATS --------------------- */
.image-converter-format-examples-section {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 6px;
}

/* --- Summary (Clickable Part) --- */
.image-converter-format-examples-section summary {
    padding: 10px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: var(--text-muted);
    user-select: none;
    list-style: none;
    /* Remove default list-style */
}

.image-converter-format-examples-section summary::-webkit-details-marker {
    display: none;
    /* Remove default WebKit marker */
}

.image-converter-format-examples-section summary:hover {
    background-color: var(--background-secondary);
}

/* --- Arrow --- */
.image-converter-format-examples-section summary::after {
    content: '▼';
    /* Downward arrow */
    transition: transform 0.2s ease;
    transform-origin: center;
    font-size: 0.8em;
    margin-left: 5px;
    display: inline-block;
}

/* Rotate arrow when details is open */
.image-converter-format-examples-section[open] summary::after {
    transform: rotate(90deg);
}

/* --- Content --- */
.image-converter-format-examples-content {
    padding: 10px;
    border-top: 1px solid var(--background-modifier-border);
}

.image-converter-format-examples-table {
    width: 100%;
    margin-top: 10px;
    border-collapse: collapse;
}

.image-converter-format-label,
.image-converter-format-example,
.image-converter-format-description {
    border: 1px solid var(--background-modifier-border);
    padding: 8px;
    text-align: left;
    vertical-align: middle;
}

.image-converter-format-label {
    font-weight: bold;
    width: 15%;
}

.image-converter-format-description {
    width: 60%;
}

/* Styling for the <ul> list within the description */
.image-converter-format-description ul {
    margin: 0;
    padding-left: 20px;
    line-height: 1.5;
}

.image-converter-format-description li {
    margin-bottom: 0;
}

.image-converter-format-example {
    /* font-family: var(--font-monospace); */
    white-space: pre-wrap;
    width: 25%;
    font-size: 0.735em;
}

/* --- Practical Scenario --- */
.image-converter-format-scenario {
    margin-top: 15px;
    border: 1px solid var(--background-modifier-border);
    padding: 10px;
    border-radius: 4px;
}

.image-converter-format-paths {
    font-family: var(--font-monospace);
    font-size: 0.9em;
}

.image-converter-format-paths>div {
    display: flex;
    align-items: center;
    margin-bottom: 2px;
}

.image-converter-path-label {
    margin-top: 4px;
    margin-right: 5px;
}

.image-converter-path-value {
    padding-left: 5px;
}

.image-converter-format-result {
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid var(--background-modifier-border);
}

.image-converter-result-label {
    color: var(--text-muted);
}

.image-converter-result-value {
    font-family: var(--font-monospace);
    padding-left: 20px;
}

/* ---------- Preview Container ------------------------*/
.image-converter-preview-container {
    margin-top: -8px;
    padding: 0px;
    border: none;
    border-radius: 0px;
    background-color: transparent;
    display: flex;
    align-items: baseline;
    justify-content: flex-end;
}

.image-converter-preview-label {
    font-style: italic;
    font-weight: normal;
    font-size: smaller;
    color: #666;
    /* Lighter gray */
    margin-bottom: 0px;
    margin-right: 5px;
    margin-bottom: 5px;
    /* display: none; */
    /* Hide the label if desired */
}

.image-converter-preview-path {
    font-style: italic;
    font-family: var(--font-monospace);
    font-size: var(--font-ui-smaller);
    color: var(--text-muted);
    overflow-wrap: break-word;
    margin-top: 0px;
}

/* --- Input Field and Preview Wrapper --- */
.image-converter-custom-template-setting-wrapper,
.image-converter-subfolder-name-setting-wrapper {
    /* Consider removing border/padding if you want a more seamless look */
    /* border: 1px solid var(--background-modifier-border);
    padding: 10px; */
    border-radius: 6px;
}

.image-converter-subfolder-name-setting-wrapper input[type='text'],
.image-converter-custom-path-setting-wrapper input[type='text'],
.image-converter-custom-template-setting-wrapper input[type='text'] {
    width: 350px;
}

.image-converter-preset-form textarea {
    width: 239px;
}

/* --- Style for Units Dropdown --- */
.image-converter-resize-units-dropdown {
    max-width: 55px;
    min-width: 55px;
    width: auto;
}










/* --------------- CAPTIONS SETTINGS ------------------------------ */
/* ----------------------------------------------------------------------- */

/* --- Section Container --- */
.image-converter-settings-tab .image-caption-settings-section {
    display: flex;
    /* align-items: center; */
    gap: 10px;
    /* margin-bottom: 10px; */
    cursor: pointer;
    flex-direction: column;
}


/* --- Section Header (Clickable Toggle Area) --- */
.image-converter-settings-tab .image-caption-settings-section .settings-section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 8px 0;
}


.image-converter-settings-tab .image-caption-settings-section .settings-section-title {
    /* flex-grow: 1; */
    margin-left: 0.5em;
    font-weight: 600;
    margin-bottom: 5px;
}

.image-converter-settings-tab .image-caption-settings-section .settings-section-toggle-button {
    margin-left: auto;
    margin-right: 0;
}


/* --- Chevron Icon --- */
.image-converter-settings-tab .image-caption-settings-section .settings-section-chevron-icon {
    margin-right: 5px;
    transition: transform 0.2s ease;
}


/* --- HIDE CHEVRON WHEN IMAGE ALIGNMENT IS DISABLED --- */
.image-converter-settings-tab .image-caption-settings-section:not(.image-caption-enabled) .settings-section-chevron-icon {
    /* Refined selector */
    display: none;
    /* Hide chevron when image alignment section is disabled */
}


/* --- Indentation for Settings within Section --- */
.image-converter-settings-tab .image-caption-settings-section .setting-item {
    padding-left: 1.5em;
    border-top: none;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
}

/* --- Hide Settings when Section is Collapsed --- */
.image-converter-settings-tab .image-caption-settings-section.settings-section-collapsed>*:not(.settings-section-header) {
    display: none;
}

.settings-section-clarification-text {
    margin-left: 19px;
    color: var(--text-muted);
    font-size: var(--font-ui-smaller);
    padding-top: var(--size-4-1);
    line-height: var(--line-height-tight);
}

/* ----------------------------------------------------------------------- */
/* ----------------------------------------------------------------------- */


























/* MODAL WINDOW - each drop */
/* FILENAME--------------------------------------------------------------------------- */
.image-converter-preset-selection-modal {
    padding: 0;
}

.image-converter-main-container {
    display: flex;
    height: 100%;
    gap: 20px;
}

.image-converter-settings-column {
    flex: 3;
    padding: 0px;
    /* overflow-y: auto; */
    max-height: 80vh;
}

.image-converter-preview-column {
    flex: 2;
    background: var(--background-secondary);
    padding: 20px;
    border-left: 1px solid var(--background-modifier-border);
    position: sticky;
    top: 0;
    max-height: 80vh;
    overflow-y: auto;
}

.image-converter-section-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 7px;
}

.image-converter-section-header h3 {
    margin: 0;
}

.image-converter-help-button {
    color: var(--text-muted);
    padding: 4px;
    height: 24px;
    width: 24px;
}

.image-converter-preset-section {
    margin-bottom: 6px;
    padding: 15px;
    background: var(--background-primary);
    border-radius: 8px;
    border: 1px solid var(--background-modifier-border);
}

.image-converter-preset-section h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 1.1em;
    color: var(--text-normal);
}

.image-converter-input-group {
    margin-bottom: 5px;
    /* padding: 15px; */
    background: var(--background-primary);
    border-radius: 6px;
}

.image-converter-preview-header {
    margin-bottom: 5px;
    margin-top: 15px;
}

.image-converter-modal-preview-container {
    font-family: var(--font-monospace);
    font-size: 0.9em;
    padding: 15px;
    background: var(--background-primary);
    border-radius: 6px;
    border: 1px solid var(--background-modifier-border);
}

.image-converter-conversion-container {
    padding: 15px;
    background: var(--background-secondary);
    border-radius: 6px;
}

/* Action buttons */
.image-converter-action-section {
    margin-top: 32px;
    padding-top: 20px;
    border-top: 1px solid var(--background-modifier-border);
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

.image-converter-action-section .setting-item {
    padding: 0;
    margin: 0;
    border-top: none;
}

/* Enhance dropdown and input styling */
.image-converter-preset-selection-modal .dropdown {
    border-radius: 4px;
    min-width: 200px;
}

.image-converter-preset-selection-modal input {
    border-radius: 4px;
    width: 100%;
}

/* Preview components */
.image-converter-preview-path {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.image-converter-preview-component {
    padding: 8px;
    background: var(--background-secondary);
    border-radius: 4px;
}

.image-converter-preview-label {
    color: var(--text-muted);
    font-size: 0.9em;
    margin-bottom: 4px;
}

.image-converter-preview-value {
    word-break: break-all;
    color: var(--text-normal);
}

.image-converter-title-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.image-converter-title-group h3 {
    margin: 0;
}

/* Dropdown and input styling */
.image-converter-preset-selection-modal .setting-item {
    border: none;
    padding: 0 0 12px 0;
}

.image-converter-section-description {
    margin: 0 0 20px 0;
    color: var(--text-muted);
    font-size: 12px;
    line-height: 1.4;
}


/* ------------------------------- */

.image-converter-contextmenu-input-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.image-converter-contextmenu-icon-container {
    display: flex;
    align-items: center;
}

.image-converter-contextmenu-name-input,
.image-converter-contextmenu-path-input {
    flex-grow: 1;
    padding: 4px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    background-color: var(--background-secondary);
    color: var(--text-normal);
}

.image-converter-contextmenu-disabled {
    background-color: var(--background-modifier-hover);
    color: var(--text-muted);
    cursor: not-allowed;
}

.image-converter-contextmenu-buttons {
    display: flex;
    gap: 4px;
}

.image-converter-contextmenu-button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px;
    cursor: pointer;
    border-radius: 4px;
    /* margin-top: 8px; */
    grid-column: 2;
    grid-row: 1 / span 2;
    color: var(--text-normal);
}


.image-converter-contextmenu-button:hover {
    background-color: var(--background-modifier-hover);
}

.image-converter-contextmenu-info-container {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: var(--size-4-2);
    /* padding: var(--size-4-1) var(--size-4-2); */
    cursor: var(--cursor);
    font-size: var(--font-ui-small);
    border-radius: var(--radius-s);
    white-space: nowrap;
}

/* ------------------------------------------------------------------ */
/* -------------------IMAGE ANNOTATION ------------------------------ */
/* ------------------------------------------------------------------ */

/* Modal styling */
.image-converter-annotation-tool-image-annotation-modal {
    max-width: 95vw;
    max-height: 95vh;
    margin: auto;
    border-radius: var(--radius-l);
    box-shadow: var(--shadow-l);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.image-converter-annotation-tool-image-annotation-modal .image-converter-annotation-tool-modal-container {
    background-color: var(--background-primary);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    gap: 10px;
    padding: 10px;
    position: relative;
}

.image-converter-annotation-tool-image-annotation-modal .image-converter-annotation-tool-canvas-container {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: var(--background-secondary);
    border-radius: var(--radius-m);
    overflow: hidden;
    position: relative;
}

.image-converter-annotation-tool-image-annotation-modal .image-converter-annotation-tool-canvas-container canvas {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

/* Toolbar styling */
.image-converter-annotation-tool-image-annotation-modal .image-converter-annotation-tool-annotation-toolbar {
    position: relative;
    /* Changed from absolute */
    top: auto;
    left: auto;
    transform: none;
    display: flex;
    flex-wrap: wrap;
    /* Allow wrapping on smaller screens */
    justify-content: center;
    gap: 8px;
    padding: 8px;
    background-color: var(--background-secondary);
    border-radius: var(--radius-m);
}

.image-converter-annotation-tool-image-annotation-modal .image-converter-annotation-tool-annotation-toolbar button {
    background-color: var(--interactive-normal);
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: background-color 0.2s ease;
    color: var(--text-normal);
    font-size: var(--font-ui-small);
}

.image-converter-annotation-tool-image-annotation-modal .image-converter-annotation-tool-preset-buttons {
    margin-left: auto;
    margin-right: auto;
    gap: 8px;
    padding: 8px;
    border-top: 1px solid var(--background-modifier-border);
}

.image-converter-annotation-tool-image-annotation-modal .preset-button {
    display: flex;
    padding: 4px 8px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    cursor: pointer;
    background: var(--background-primary);
}

.image-converter-annotation-tool-image-annotation-modal .image-converter-annotation-tool-preset-color {
    width: 16px;
    height: 16px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 2px;
}

.image-converter-annotation-tool-image-annotation-modal .preset-button:hover {
    background-color: var(--background-modifier-hover);
}

.image-converter-annotation-tool-image-annotation-modal .preset-number {
    font-size: 0.8em;
    opacity: 0.8;
    margin-left: 4.5px;
}





.image-converter-annotation-tool-image-annotation-modal .control-label {
    font-size: 12px;
    color: var(--text-muted);
    margin-bottom: 2px;
    white-space: nowrap;
}

.image-converter-annotation-tool-image-annotation-modal .size-buttons-container {
    display: flex;
    gap: 27px;
    margin-top: 8px;
    margin-bottom: 2px;
}



.image-converter-annotation-tool-image-annotation-modal .opacity-buttons-container {
    display: flex;
    gap: 8px;
    margin-top: 8px;
    margin-bottom: 2px;
}

.image-converter-annotation-tool-image-annotation-modal .image-converter-annotation-tool-button-group {
    display: flex;
    gap: 4px;
    align-items: center;
}

.image-converter-annotation-tool-image-annotation-modal .image-converter-annotation-tool-button-group button {
    padding: 4px 8px;
    min-width: 36px;
    height: 24px;
    font-size: 12px;
}

.image-converter-annotation-tool-image-annotation-modal .image-converter-annotation-tool-button-group button.is-active {
    background-color: var(--interactive-accent);
    color: var(--text-on-accent);
}



.image-converter-annotation-tool-image-annotation-modal .blend-modes-container {
    display: flex;
    gap: 18px;
    margin-top: 8px;
    margin-bottom: 2px;
}

.image-converter-annotation-tool-image-annotation-modal .blend-modes-container .dropdown-container {
    min-width: 100px;
}

.image-converter-annotation-tool-image-annotation-modal .blend-modes-container .dropdown {
    width: 80%;
    height: 100%;
}



.image-converter-annotation-tool-image-annotation-modal .layer-controls {
    display: flex;
    gap: 20px;
    margin-top: 8px;
    margin-bottom: 2px;
}



.image-converter-annotation-tool-image-annotation-modal .size-buttons-container .control-label,
.image-converter-annotation-tool-image-annotation-modal .opacity-buttons-container .control-label,
.image-converter-annotation-tool-image-annotation-modal .blend-modes-container .control-label,
.image-converter-annotation-tool-image-annotation-modal .layer-controls .control-label {
    margin-top: auto;
    margin-bottom: auto;
}






.image-converter-annotation-tool-image-annotation-modal .image-converter-annotation-tool-color-swatches {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 8px;
}

.image-converter-annotation-tool-image-annotation-modal .image-converter-annotation-tool-color-row {
    display: flex;
    align-items: center;
    gap: 8px;
}

.image-converter-annotation-tool-image-annotation-modal .image-converter-annotation-tool-row-label {
    font-size: 12px;
    min-width: 70px;
}

.image-converter-annotation-tool-image-annotation-modal .image-converter-annotation-tool-swatches-container {
    display: flex;
    gap: 8px;
}

/* .image-converter-annotation-tool-image-annotation-modal .color-column {
    display: flex;
    flex-direction: column;
    gap: 4px;
} */

.image-converter-annotation-tool-image-annotation-modal .section-label {
    font-size: 12px;
    margin-bottom: 4px;
}

.image-converter-annotation-tool-image-annotation-modal .dominant-container {
    display: flex;
    gap: 8px;
}

.image-converter-annotation-tool-image-annotation-modal .color-swatch {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    cursor: pointer;
    transition: transform 0.2s;
}


.image-converter-annotation-tool-image-annotation-modal .color-swatch:hover {
    transform: scale(1.1);
}

.image-converter-annotation-tool-image-annotation-modal .color-separator {
    height: 1px;
    margin: 4px 0;
}





.image-converter-annotation-tool-image-annotation-modal .background-dropdown {
    position: fixed;
    z-index: 1000;
    background-color: var(--background-primary);
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 4px;
    width: 54px;

    .background-option {
        display: flex;
        align-items: center;
        padding: 6px 8px;
        cursor: pointer;
        border-radius: 3px;
        margin-left: 2px;

        &:hover {
            background-color: var(--background-modifier-hover);
        }

        &.is-active {
            background-color: var(--background-modifier-active);
        }

        .option-icon,
        .color-preview {
            width: 20px;
            height: 20px;
            margin-right: 8px;
            border-radius: 3px;
            border: 1px solid var(--background-modifier-border);

            svg {
                width: 100%;
                height: 100%;

                path,
                circle {
                    stroke: var(--text-muted);
                    fill: none;
                }

                circle {
                    fill: var(--text-muted);
                }
            }
        }

        .color-preview {
            border: 1px solid var(--background-modifier-border);
        }
    }
}





.image-converter-annotation-tool-image-annotation-modal .dominant-section {
    display: flex;
    flex-direction: column;
}

.resizable-modal {
    position: relative;
    resize: both;
    overflow: hidden;
}

.image-converter-annotation-tool-image-annotation-modal .modal-resize-handle {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 20px;
    height: 20px;
    cursor: se-resize;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: var(--text-muted);
    user-select: none;
    z-index: 100;
}

.image-converter-annotation-tool-image-annotation-modal .is-resizing {
    user-select: none;
}

.image-converter-annotation-tool-image-annotation-modal .image-converter-annotation-tool-canvas-container {
    transition: width 0.1s ease, height 0.1s ease;
    background: var(--background-secondary);
    overflow: hidden;
    position: relative;
    border: 1px solid var(--background-modifier-border);
}

/* Add checkerboard pattern for transparent areas */
.image-converter-annotation-tool-image-annotation-modal .image-converter-annotation-tool-canvas-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: linear-gradient(45deg, var(--background-modifier-border) 25%, transparent 25%),
        linear-gradient(-45deg, var(--background-modifier-border) 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, var(--background-modifier-border) 75%),
        linear-gradient(-45deg, transparent 75%, var(--background-modifier-border) 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    opacity: 0;
    pointer-events: all;
    z-index: 0;
}

/* Hover states */
.image-converter-annotation-tool-image-annotation-modal .image-converter-annotation-tool-annotation-toolbar button:hover {
    background-color: var(--interactive-hover);
}

/* Active state for buttons */
.image-converter-annotation-tool-image-annotation-modal .image-converter-annotation-tool-annotation-toolbar button.is-active {
    background-color: var(--interactive-accent);
    color: var(--text-on-accent);
}

.image-converter-annotation-tool-image-annotation-modal .image-converter-annotation-tool-annotation-toolbar button svg {
    width: 16px;
    height: 16px;
}

.image-converter-annotation-tool-image-annotation-modal .annotation-toolbar-group {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    padding: 4px;
    min-width: auto;
    /* Remove fixed width */
}

.image-converter-annotation-tool-image-annotation-modal .image-converter-annotation-tool-annotation-toolbar input[type="color"] {
    width: 30px;
    height: 30px;
    padding: 0;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.image-converter-annotation-tool-image-annotation-modal .annotation-toolbar-group:last-child {
    border-right: none;
}


.image-converter-annotation-tool-image-annotation-modal .image-converter-annotation-tool-left-section {
    display: flex;
    /* align-items: center; */
    gap: 8px;
}

.image-converter-annotation-tool-image-annotation-modal .image-converter-annotation-tool-drawing-tools-column {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.image-converter-annotation-tool-image-annotation-modal .toolbar-separator {
    width: 1px;
    height: 100%;
    background-color: var(--background-modifier-border);
    margin: 0 8px;
}


.image-converter-annotation-tool-image-annotation-modal .drawing-group {
    display: flex;
    align-items: center;
}

.image-converter-annotation-tool-image-annotation-modal .image-converter-annotation-tool-color-picker-wrapper {
    display: flex;
    /* align-items: center; */
    padding: 2px;
}

.image-converter-annotation-tool-image-annotation-modal .image-converter-annotation-tool-background-color-wrapper {
    display: flex;
    align-items: center;
}

/* 
.image-converter-annotation-tool-image-annotation-modal .background-alpha-slider {
    width: 60px;
    height: 24px;
    margin: 0;
    padding: 0;
} */

.image-converter-annotation-tool-image-annotation-modal .preset-button .image-converter-annotation-tool-preset-color {
    width: 20px;
    height: 20px;
    border-radius: 10px;
    transition: all 0.2s ease;
}

.image-converter-annotation-tool-image-annotation-modal .preset-button.text-preset .image-converter-annotation-tool-preset-color {
    position: relative;
    border-style: solid;
    border-width: 2px;
}

.image-converter-annotation-tool-image-annotation-modal .color-picker {
    width: 24px;
    height: 24px;
    margin-top: 30px;
    padding: 0;
    border: none;
    border-radius: var(--radius-s);
    cursor: pointer;
}

.image-converter-annotation-tool-image-annotation-modal .color-picker::-webkit-color-swatch-wrapper {
    padding: 0;
}

.image-converter-annotation-tool-image-annotation-modal .color-picker::-webkit-color-swatch {
    border: none;
    border-radius: var(--radius-s);
}

.image-converter-annotation-tool-image-annotation-modal .color-picker::-moz-color-swatch {
    border: 2px solid var(--background-modifier-border);
    border-radius: var(--radius-s);
}

.image-converter-annotation-tool-image-annotation-modal .color-picker:hover {
    opacity: 0.8;
}

.image-converter-annotation-tool-image-annotation-modal .annotation-toolbar-group {
    display: flex;
    gap: 4px;
    padding: 0 8px;
    border-right: 1px solid var(--background-modifier-border);
}

.image-converter-annotation-tool-image-annotation-modal .annotation-toolbar-group:last-child {
    border-right: none;
}

.image-converter-annotation-tool-image-annotation-modal button.is-active {
    background-color: var(--interactive-accent);
    color: var(--text-on-accent);
}

.image-converter-annotation-tool-image-annotation-modal button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.image-converter-annotation-tool-image-annotation-modal .image-converter-annotation-tool-canvas-container {
    position: relative;
}

.image-converter-annotation-tool-image-annotation-modal .image-converter-annotation-tool-canvas-container textarea {
    position: absolute;
    pointer-events: auto;
}


/* Optional: Add hover effect for delete icon */
.image-converter-annotation-tool-image-annotation-modal .image-converter-annotation-tool-canvas-container:hover .delete-icon {
    opacity: 1;
}

/* Save button styling */
.image-converter-annotation-tool-image-annotation-modal .image-converter-annotation-tool-annotation-toolbar button.mod-cta {
    background-color: var(--interactive-accent);
    color: var(--text-on-accent);
}

.image-converter-annotation-tool-image-annotation-modal .image-converter-annotation-tool-annotation-toolbar button.mod-cta:hover {
    background-color: var(--interactive-accent-hover);
}

/* Mobile-specific adjustments */
@media (max-width: 768px) {
    .image-converter-annotation-tool-image-annotation-modal .image-converter-annotation-tool-annotation-toolbar {
        padding: 4px;
        gap: 4px;
    }

    .image-converter-annotation-tool-image-annotation-modal .image-converter-annotation-tool-annotation-toolbar button {
        padding: 6px;
    }

    .image-converter-annotation-tool-image-annotation-modal .image-converter-annotation-tool-annotation-toolbar button svg {
        width: 14px;
        height: 14px;
    }

    .image-converter-annotation-tool-image-annotation-modal .slider-container {
        min-width: 100px;
    }

    .image-converter-annotation-tool-image-annotation-modal .brush-controls {
        flex-direction: column;
        padding-left: 4px;
        margin-left: 4px;
    }
}


.image-converter-annotation-tool-image-annotation-modal .image-converter-annotation-tool-control-group {
    display: flex;
    gap: 5px;
    margin-top: 8px;
    margin-bottom: 2px;
    align-items: center;
}

.image-converter-annotation-tool-image-annotation-modal .text-background-controls {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.image-converter-annotation-tool-image-annotation-modal .text-bg-controls {
    display: flex;
    flex-direction: row;
    align-content: center;
    flex-wrap: nowrap;
    align-items: center;
}

.image-converter-annotation-tool-image-annotation-modal .background-color-picker {
    width: 24px;
    height: 24px;
    padding: 0;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    cursor: pointer;
    margin-right: 4px;
}

.image-converter-annotation-tool-image-annotation-modal .layer-controls {
    display: flex;
    flex-direction: row;
    align-content: center;
    flex-wrap: nowrap;
    align-items: center;
}

/* ------------------------------------------------------------------ */
/* ------------------------------------------------------------------ */


/* ------------------------------------------------------------------ */
/* ------------------------------ CROP ------------------------------ */
/* ------------------------------------------------------------------ */

/* CROP */
.crop-tool-modal .modal {
    position: fixed;
    border-radius: var(--radius-m);
    padding: 0;
    overflow: hidden;
    background-color: var(--background-primary);
    box-shadow: var(--shadow-l);
    animation: modalOpen 200ms ease-out;
    /* margin-top: auto; */
}

.crop-tool-modal .crop-modal-wrapper {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: var(--background-primary);
    border-radius: var(--radius-m);
}

.crop-tool-modal .crop-modal-header {
    padding: var(--size-4-4);
    /* background-color: var(--background-secondary);
    border-bottom: 1px solid var(--background-modifier-border); */
}

.crop-tool-modal .crop-modal-header h2 {
    margin: 0;
    font-size: 1.5em;
}

.crop-tool-modal .crop-container {
    position: relative;
    width: 100%;
    height: 100%;
    /* overflow: hidden; Contain the overlay */
}

.crop-tool-modal .crop-original-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    display: block;
    margin: auto;
}

/* Remove the separate overlay div since we're using box-shadow */
.crop-tool-modal .crop-overlay {
    display: none;
}

.crop-tool-modal .selection-area {
    position: absolute;
    border: 2px solid var(--interactive-accent);
    box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5);
    pointer-events: auto;
    cursor: move;
    z-index: 2;
}

.crop-tool-modal .resize-handle {
    position: absolute;
    width: 10px;
    height: 10px;
    background-color: #fff;
    border: 1px solid #00a8ff;
    border-radius: 50%;
}

/* Position handles */
.crop-tool-modal .nw-resize {
    top: -5px;
    left: -5px;
    cursor: nw-resize;
}

.crop-tool-modal .n-resize {
    top: -5px;
    left: 50%;
    transform: translateX(-50%);
    cursor: n-resize;
}

.crop-tool-modal .ne-resize {
    top: -5px;
    right: -5px;
    cursor: ne-resize;
}

.crop-tool-modal .w-resize {
    top: 50%;
    left: -5px;
    transform: translateY(-50%);
    cursor: w-resize;
}

.crop-tool-modal .e-resize {
    top: 50%;
    right: -5px;
    transform: translateY(-50%);
    cursor: e-resize;
}

.crop-tool-modal .sw-resize {
    bottom: -5px;
    left: -5px;
    cursor: sw-resize;
}

.crop-tool-modal .s-resize {
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    cursor: s-resize;
}

.crop-tool-modal .se-resize {
    bottom: -5px;
    right: -5px;
    cursor: se-resize;
}

.crop-tool-modal .crop-modal-buttons {
    padding: var(--size-4-2);
    border-top: 1px solid var(--background-modifier-border);
    display: flex;
    justify-content: flex-end;
    gap: var(--size-2-3);
    background-color: var(--background-secondary);
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 3;
}

.crop-tool-modal .crop-modal-buttons button {
    padding: var(--size-4-1) var(--size-4-3);
    border-radius: var(--radius-s);
    font-size: var(--font-ui-small);
    font-weight: var(--font-medium);
    border: 1px solid var(--background-modifier-border);
    background-color: var(--interactive-normal);
    color: var(--text-normal);
    cursor: pointer;
    min-width: 70px;
}

.crop-tool-modal .crop-modal-buttons button:hover {
    background-color: var(--interactive-hover);
}

/* Make the Save button more prominent */
.crop-tool-modal .crop-modal-buttons button:first-child {
    background-color: var(--interactive-accent);
    color: var(--text-on-accent);
    border-color: var(--interactive-accent);
}

.crop-tool-modal .crop-modal-buttons button:first-child:hover {
    background-color: var(--interactive-accent-hover);
}

.crop-tool-modal .crop-modal-content {
    flex: 1;
    overflow: hidden;
    position: relative;
    padding: 16px;
}

/* Aspect ratio */

.crop-tool-modal .aspect-ratio-controls {
    padding: 8px 0px;
    border-bottom: 1px solid var(--background-modifier-border);
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.crop-tool-modal .ratio-buttons-container {
    display: flex;
    gap: 8px;
}

.crop-tool-modal .custom-ratio-container {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-left: auto;
    margin-right: 0;
}

.crop-tool-modal .aspect-ratio-button {
    padding: 4px 8px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    background: var(--background-secondary);
    cursor: pointer;
}

.crop-tool-modal .aspect-ratio-button:hover {
    background: var(--background-modifier-hover);
}

.crop-tool-modal .aspect-ratio-button.active {
    background: var(--interactive-accent);
    color: var(--text-on-accent);
}

.crop-tool-modal .custom-ratio-input {
    width: 40px;
    height: 24px;
    padding: 2px 4px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    background: var(--background-primary);
    text-align: center;
}

.crop-tool-modal .custom-ratio-input:focus {
    outline: none;
    border-color: var(--interactive-accent);
}

/* Remove spinner buttons from number inputs */
.crop-tool-modal .custom-ratio-input::-webkit-outer-spin-button,
.crop-tool-modal .custom-ratio-input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.crop-tool-modal .custom-ratio-input[type=number] {
    -moz-appearance: textfield;
}

/* Rotate / Flip */
.crop-tool-modal .transform-controls {
    display: flex;
    margin-left: 0;
    margin-right: auto;
}

.crop-tool-modal .rotate-container,
.crop-tool-modal .flip-container {
    display: flex;
    gap: 1px;
}

.crop-tool-modal .transform-button {
    padding: 4px 8px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    background: var(--background-secondary);
    cursor: pointer;
    font-size: 18px;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
}

.crop-tool-modal .transform-button:hover {
    background: var(--background-modifier-hover);
}

/* Rotate / Zoom slider */
.crop-tool-modal .image-controls {
    display: flex;
    gap: 20px;
    padding: 6px 0px;
    border-bottom: 1px solid var(--background-modifier-border);
}

.crop-tool-modal .control-group {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
}

.crop-tool-modal .control-label {
    min-width: 10px;
    font-weight: var(--font-medium);
}

.crop-tool-modal .slider {
    flex: 1;
    height: 6px;
    -webkit-appearance: none;
    background: var(--background-modifier-border);
    border-radius: 3px;
    outline: none;
}

.crop-tool-modal .slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--interactive-accent);
    cursor: pointer;
    border: none;
}

.crop-tool-modal .slider::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--interactive-accent);
    cursor: pointer;
    border: none;
}

.crop-tool-modal .rotation-value,
.crop-tool-modal .zoom-value {
    min-width: 5px;
    text-align: right;
}


/* Mobile-specific styles */
@media screen and (max-width: 768px) {
    .crop-tool-modal .modal {
        width: calc(100% - var(--size-4-4));
        height: calc(100% - var(--size-4-4));
        max-height: 90vh;
        margin: var(--size-4-2);
    }

    .crop-tool-modal .crop-modal-header {
        padding: var(--size-4-2);
    }

    .crop-tool-modal .crop-modal-buttons {
        padding: var(--size-4-2);
    }
}

/* Animation for modal opening */

/* @keyframes modalOpen {
    from {
        opacity: 0;
        transform: translate(-50%, -48%) scale(0.96);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
} */
/* ------------------------------------------------------------------ */
/* ------------------------------------------------------------------ */





/* ------------------------------DRAG-resize----------------------- */

/* Apply vertical-align only when image is aligned */


/* THIS IS NEEDED to PREVENT CONTENT SHIFT WHEN HOVERING OVER IMAGE */
.internal-embed.image-embed img {
    vertical-align: bottom;
}

.image-resize-container {
    position: relative;
    /*  Needed for absolute positioning of the pseudo-element */
    display: inline-block;
    font-size: 0;
    line-height: 0;
    margin: 0px;
    vertical-align: bottom;
    max-width: fit-content;
}

.image-resize-container::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 1px solid var(--background-modifier-hover);
    /* Use Obsidian's hover border color */
    transition: opacity 0.2s ease;
    /* Smooth transition for visibility */
    pointer-events: none;
    /* Ensures the pseudo-element doesn't interfere with interactions */
    margin: 0;
}

.image-resize-container:hover::before {
    opacity: 0.5;
    /* Show border on hover */
}

/* .image-resize-container {
    display: inline-block;
    position: relative;
    margin: 0px;
    border: 1.5px solid rgba(153, 115, 247, 0.507);
    line-height: 0;
} */

/* ------ EXTERNAL   */
/* Style for external images when hovered (updated for transparency) */
.image-resize-border {
    position: relative;
    outline: 2px solid var(--background-modifier-hover);
    resize: both;
    overflow: hidden;
}


.image-resize-border:hover {
    cursor: default;
}

.image-resize-border:hover:has(.resizing) {
    cursor: grabbing !important;
}

/* Style for external images when resizing is active */
.image-resize-border.resizing {
    cursor: grabbing !important;
    user-select: none; /* Prevent accidental text selection */
}

/* Style for internal image container when resizing is active */
.image-resize-container.resizing {
    cursor: grabbing !important;
    user-select: none;
}

/* --------- */


.image-resize-handle {
    position: absolute;
    width: 8px !important;
    height: 8px !important;
    /* padding: 1px;    Create larger invisible hit area */
    background-color: var(--background-secondary);
    border: 1px solid var(--interactive-accent);
    /* Obsidian's UI element border color */
    border-radius: 2px;
    cursor: grab;
    opacity: 0;
    pointer-events: none;
    /* Make handles non-clickable by default */
    /* box-shadow: 0 0 0 1.5px var(--background-modifier-border-hover); */
    transform: translateZ(0);
    transition: opacity 0.2s ease, transform 0.1s ease;
    /* Smooth transitions */
    z-index: 2;
    /* Ensure handles are visible above the image */
}

.image-resize-handle-nw {
    top: 0px;
    left: 0px;
    cursor: nwse-resize;
}

.image-resize-handle-ne {
    top: 0px;
    right: 0px;
    cursor: nesw-resize;
}

.image-resize-handle-sw {
    bottom: 0px;
    left: 0px;
    cursor: nesw-resize;
}

.image-resize-handle-se {
    bottom: 0px;
    right: 0px;
    cursor: nwse-resize;
}

.image-resize-handle-n {
    top: 0px;
    left: 50%;
    margin-left: -6px;
    /* Half of handle width to center */
    cursor: ns-resize;
}

.image-resize-handle-s {
    bottom: 0px;
    left: 50%;
    margin-left: -6px;
    /* Half of handle width to center */
    cursor: ns-resize;
}

.image-resize-handle-e {
    right: 0px;
    top: 50%;
    margin-top: -6px;
    /* Half of handle height to center */
    cursor: ew-resize;
}

.image-resize-handle-w {
    left: 0px;
    top: 50%;
    margin-top: -6px;
    /* Half of handle height to center */
    cursor: ew-resize;
}


/* Make handles visible on container hover */
/* Handle Visibility on Hover */
.image-resize-container:hover .image-resize-handle,
.image-resize-border:hover .image-resize-handle {
    opacity: 1;
    pointer-events: auto;
    /* Clickable on hover */
}

/* Enhance handle hover effects */
.image-resize-handle:hover {
    background-color: var(--interactive-hover);
    transform: scale(1.1);
    /* Subtle scale increase on hover */
}


/* Active resize state */
.resizing {
    cursor: grabbing !important;
    user-select: none;
}

.resizing .image-resize-handle {

    opacity: 1 !important;

}

/* ------------------------------------------------------------------- */
/* ------------------------------------------------------------------- */


/* --- Map View Compatibility --- */
.map-view-main .image-resize-container {
    display: none !important;
}

.map-view-main .image-resize-border {
    outline: none !important;
}


/* ------------------------------------------------------------------- */
/* ------------------------------------------------------------------- */




/* ---------------------- IMAGE ALIGNMENT --------------------------- */
/* ------------------------------------------------------------------ */
/* We could add a border or other visual indicator here?? For now it is just
    generally class to keep for identification purposes.*/
/*.image-converter-aligned {
    border: 1px solid red; 
} */

/* General styles for image positioning (using flexbox) - apply to containers */
.image-position-left {
    justify-content: flex-start; /* Align to the left */
}

.image-position-center {
    justify-content: center; /* Align to the center */
}

.image-position-right {
    justify-content: flex-end; /* Align to the right */
}

/* General styles for wrapping - apply to containers */
.image-wrap {
    float: left; /* Allow text to wrap.  Apply to the image itself. */
    margin-right: 10px; /* Add spacing. Apply to the image. */
    clear: left; /* Ensure it clears previous floats.  Apply to the image. */
}

.image-no-wrap {
    float: none; /* Prevent text wrapping. Apply to the image itself. */
    clear: both; /* Clear any previous floats */
}

/* Styles for internal embeds to control alignment without affecting image size */
.internal-embed.image-embed.image-position-left {
    text-align: left;
}

.internal-embed.image-embed.image-position-center {
    text-align: center;
}

.internal-embed.image-embed.image-position-right {
    text-align: right;
}

/* ===== Live Preview Specific Styles ===== */
.markdown-source-view.mod-cm6 {

    /* Left alignment WITH wrapping */
    .image-wrap.image-position-left
    .image-wrap.image-position-left {
        float: left !important; /* !important to override Obsidian */
        margin: 0 1em 0.5em 0 !important;
        clear: left !important;
        /* position: relative !important;  to allow right-click and allow it to be above any other content
        z-index: 10 !important */
    }

    /* Right alignment WITH wrapping */
    .image-wrap.image-position-right {
        float: right !important;
        margin: 0em 0 0.5em 1em !important;
        clear: right !important;
        /* position: relative; THIS WORKS BUT BREAKS HANDLES to allow right-click and allow it to be above any other content */
        /* z-index: 10; */
    }

    /* Left alignment WITHOUT wrapping */
    .image-no-wrap.image-position-left {
        display: block !important;
        margin: auto !important;
        margin-left: 0 !important;
        clear: both !important;
        float: none !important;
    }

    /* Right alignment WITHOUT wrapping */
    .image-no-wrap.image-position-right {
        display: block !important;
        margin: auto !important;
        margin-right: 0 !important;
        clear: both !important;
        float: none !important;
    }

    /* Center alignment (always no wrap) */
    .image-position-center {
        display: block !important;
        margin: auto !important;
        clear: both !important;
        float: none !important;
        text-align: center !important;
        /* For the image itself */
    }

    .image-captions-figure {
        margin: 0;
        padding: 2px;
        display: inline-table;
        border-radius: 2px;
    }



    /* Center alignment for internal embeds */
    .internal-embed.image-embed.image-position-center {
        text-align: center !important;
    }
}


/* ------------------------ Reading Mode Styles --------------------------------------*/
.markdown-reading-view {

    /* Left alignment WITH wrapping */
    .image-wrap.image-position-left {
        float: left !important;
        margin: 0 1em 0.5em 0 !important;
        clear: left !important;
    }

    /* Right alignment WITH wrapping */
    .image-wrap.image-position-right {
        float: right !important;
        margin: 0em 0 0.5em 1em !important;
        clear: right !important;
    }

    /* Left alignment WITHOUT wrapping */
    .image-no-wrap.image-position-left {
        display: block !important;
        margin: auto !important;
        margin-left: 0 !important;
        clear: both !important;
        float: none !important;
    }

    /* Right alignment WITHOUT wrapping */
    .image-no-wrap.image-position-right {
        display: block !important;
        margin: auto !important;
        margin-right: 0 !important;
        clear: both !important;
        float: none !important;
    }

    /* Center alignment (always no wrap) */
    .image-position-center {
        display: block !important;
        margin: auto !important;
        clear: both !important;
        float: none !important;
        text-align: center !important;
    }

    /* Center alignment for internal embeds */
    .internal-embed.image-embed.image-position-center {
        text-align: center !important;
    }
}

/* --- List Handle Position --- */
/* Reset line-height - probably not needed anymore */
/* .cm-line .image-resize-container li {
    line-height: normal;
} */

/* ------------------------------------------------------------------ */
/* ------------------------------------------------------------------ */



/* ---------------------- MODAL - BATCH PROCESSING ------------------- */

/* Convert current note */
/* //////////////////////////////////////////////// */
.image-convert-modal {
    padding: 1rem;
}

.image-convert-modal .image-counts-display-container {
    margin-top: 20px;
    text-align: center;
    /* Center-align the counts */
}

.image-counts-display {
    width: fit-content;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
}

.image-convert-modal .modal-subtitle {
    font-size: 0.9em;
    color: #666;
}

.image-convert-modal .modal-header {
    text-align: center;
    margin-bottom: 1rem;
}

.image-convert-modal .modal-subtitle {
    margin-top: -18px;
}

.image-convert-modal .modal-warning {
    margin-top: -10px;
    padding: 20px;
    border-radius: 10px;
    background-color: var(--background-secondary);
}

.image-convert-modal .settings-container {
    margin-top: 1rem;
}

.image-convert-modal .button-container {
    margin-top: 1rem;
    text-align: center;
}

.image-convert-modal .resize-input-setting,
.image-convert-modal .enlarge-reduce-setting {
    margin-top: 1em;
}

.image-convert-modal .conditional-settings {
    margin-top: 1em;
    padding: 1em;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
}

.image-convert-modal .image-source-setting-container,
.image-convert-modal .format-quality-container,
.image-convert-modal .resize-container,
.image-convert-modal .skip-container {
    margin-bottom: 20px;
    padding: 10px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 5px;
}