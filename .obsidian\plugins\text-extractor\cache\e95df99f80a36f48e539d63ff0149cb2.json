{"path": "Pasted image 20231221113925.png", "text": "train: weights=yolov5l.pt, cfg=, data=data\\cocol28.yaml, hyp=data\\hyps\\hyp.scratch-low.yaml, epochs=188, batch_size=16, imgsz=648, rect=False, resume=False, I YOLOVS 2823-11-23 Python-3.8.8 torch-1.13.1+culls CUDA:® (Quadro RTX 5000, 16384HiB) hyperparaneters: 1rg=0.61, 1rf=0.61, momentun=0.937, weight_decay=0.8805, warmup_epochs=3.9, warnup_momentun=9.8, warmup_bias_r=.1, box=8.85, cls=0.5, cls_| Comet: run 'pip install comet_ml' to automatically track and visualize Y<PERSON>OVS runs in Comet TensorBoard: Start with 'tensorboard --logdir runs\\train', view at http://localhost:6806/ Overriding model.yaml nc=88 with nc=4 from n parans module argunents [ 108 7848 models. conmon. Conv. [3, 64, 6, 2, 2] 1 10 73984 models. common . Conv [64, 128, 3, 2] et", "libVersion": "0.2.4", "langs": "eng"}