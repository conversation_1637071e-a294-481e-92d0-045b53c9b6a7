2025-07-17 21:52:14.809 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-17 21:52:14.810 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-17 21:52:14.925 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-17 21:52:16.594 [info] components database created cost 4 ms   
2025-07-17 21:52:16.596 [info] components index initializing...   
2025-07-17 21:52:17.821 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-17 21:52:17.926 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-17 21:52:18.183 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-17 21:52:19.464 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-17 21:52:19.513 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-17 21:52:19.663 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-17 21:52:19.679 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-17 21:52:19.689 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-17 21:52:19.696 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-17 21:52:19.698 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-17 21:52:20.607 [info] start to batch put pages: 6   
2025-07-17 21:52:20.609 [info] batch persist cost 6  2 
2025-07-17 21:52:20.638 [info] components index initialized, 988 files cost 4048 ms   
2025-07-17 21:52:20.638 [info] refresh page data from init listeners 0 988   
2025-07-17 21:52:22.764 [debug] ignore file modify evnet 学习库/Anki/Artificial Intelligence/未命名.md   
2025-07-17 21:52:22.821 [info] trigger 学习库/Anki/Artificial Intelligence/未命名.md resolve  [object Object] 
2025-07-17 21:52:22.826 [info] index finished after resolve  [object Object] 
2025-07-17 21:52:22.827 [info] refresh page data from resolve listeners 0 988   
2025-07-17 21:52:23.310 [info] indexing created file Home/components/未命名.components  [object Object] 
2025-07-17 21:52:23.318 [info] refresh page data from created listeners 0 989   
2025-07-17 21:52:24.083 [debug] ignore file modify evnet Home/Home.md   
2025-07-17 21:52:24.118 [info] trigger Home/Home.md resolve  [object Object] 
2025-07-17 21:52:24.119 [info] index finished after resolve  [object Object] 
2025-07-17 21:52:24.120 [info] refresh page data from resolve listeners 0 989   
2025-07-17 21:52:24.696 [debug] ignore file modify evnet 日记库/fleeting_notes/闪念.md   
2025-07-17 21:52:24.710 [info] trigger 日记库/fleeting_notes/闪念.md resolve  [object Object] 
2025-07-17 21:52:24.712 [info] index finished after resolve  [object Object] 
2025-07-17 21:52:24.713 [info] refresh page data from resolve listeners 0 989   
2025-07-17 21:52:25.228 [info] indexing created file 日记库/day/2025-07-14.md  [object Object] 
2025-07-17 21:52:25.228 [info] indexing created ignore file 日记库/day/2025-07-14.md   
2025-07-17 21:52:25.246 [info] trigger 日记库/day/2025-07-14.md resolve  [object Object] 
2025-07-17 21:52:25.247 [info] index finished after resolve  [object Object] 
2025-07-17 21:52:25.248 [info] refresh page data from resolve listeners 0 990   
2025-07-17 21:52:26.026 [debug] ignore file modify evnet 日记库/template/fleeting_note.md   
2025-07-17 21:52:26.034 [info] trigger 日记库/template/fleeting_note.md resolve  [object Object] 
2025-07-17 21:52:26.035 [info] index finished after resolve  [object Object] 
2025-07-17 21:52:26.036 [info] refresh page data from resolve listeners 0 990   
2025-07-17 21:52:27.774 [info] indexing created file 日记库/fleeting_notes/2025-07-14.md  [object Object] 
2025-07-17 21:52:27.774 [info] indexing created ignore file 日记库/fleeting_notes/2025-07-14.md   
2025-07-17 21:52:27.788 [info] trigger 日记库/fleeting_notes/2025-07-14.md resolve  [object Object] 
2025-07-17 21:52:27.789 [info] index finished after resolve  [object Object] 
2025-07-17 21:52:27.791 [info] refresh page data from resolve listeners 0 991   
2025-07-17 21:52:30.174 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-17 21:52:32.085 [info] indexing created file 学习库/Docker/未命名.md  [object Object] 
2025-07-17 21:52:32.085 [info] indexing created ignore file 学习库/Docker/未命名.md   
2025-07-17 21:52:32.095 [info] trigger 工作库/项目/舌诊/人脸识别.md resolve  [object Object] 
2025-07-17 21:52:32.294 [info] trigger 学习库/Docker/未命名.md resolve  [object Object] 
2025-07-17 21:52:32.296 [info] index finished after resolve  [object Object] 
2025-07-17 21:52:32.297 [info] refresh page data from resolve listeners 0 992   
2025-07-17 21:52:32.796 [info] indexing created file 学习库/python笔记/项目管理.md  [object Object] 
2025-07-17 21:52:32.797 [info] indexing created ignore file 学习库/python笔记/项目管理.md   
2025-07-17 21:52:32.836 [info] trigger 学习库/python笔记/项目管理.md resolve  [object Object] 
2025-07-17 21:52:32.838 [info] index finished after resolve  [object Object] 
2025-07-17 21:52:32.839 [info] refresh page data from resolve listeners 0 993   
2025-07-17 21:52:35.268 [info] indexing created file components/logs/2025-07-14.components.log  [object Object] 
2025-07-17 21:52:35.277 [info] refresh page data from created listeners 0 994   
2025-07-17 21:52:36.463 [debug] ignore file modify evnet 学习库/stm32/3 串口.md   
2025-07-17 21:52:36.506 [info] trigger 学习库/stm32/3 串口.md resolve  [object Object] 
2025-07-17 21:52:36.509 [info] index finished after resolve  [object Object] 
2025-07-17 21:52:36.511 [info] refresh page data from resolve listeners 0 994   
2025-07-17 21:52:38.748 [info] indexing created file components/logs/2025-07-15.components.log  [object Object] 
2025-07-17 21:52:38.752 [info] refresh page data from created listeners 0 995   
2025-07-17 21:52:39.368 [info] indexing created file components/logs/2025-07-16.components.log  [object Object] 
2025-07-17 21:52:39.373 [info] refresh page data from created listeners 0 996   
2025-07-17 21:52:40.180 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-17 21:52:40.215 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-17 21:52:40.218 [info] index finished after resolve  [object Object] 
2025-07-17 21:52:40.219 [info] refresh page data from resolve listeners 0 996   
2025-07-17 21:52:41.948 [debug] ignore file modify evnet 学习库/学习库.components   
2025-07-17 21:52:43.142 [debug] ignore file modify evnet 学习库/Deep learning/概念库/交叉验证.md   
2025-07-17 21:52:43.177 [info] trigger 学习库/Deep learning/概念库/交叉验证.md resolve  [object Object] 
2025-07-17 21:52:43.182 [info] index finished after resolve  [object Object] 
2025-07-17 21:52:43.183 [info] refresh page data from resolve listeners 0 996   
