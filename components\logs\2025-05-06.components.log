2025-05-06 15:46:05 [info] indexing created file 学习库/ROS/机器人学/机器视觉/Drawing 2025-05-06 15.46.05.excalidraw.md  [object Object] 
2025-05-06 15:46:05 [info] indexing created ignore file 学习库/ROS/机器人学/机器视觉/Drawing 2025-05-06 15.46.05.excalidraw.md   
2025-05-06 15:46:05 [info] indexing created file components/logs/2025-05-06.components.log  [object Object] 
2025-05-06 15:46:05 [info] refresh page data from created listeners 0 876   
2025-05-06 15:46:05 [info] trigger 学习库/ROS/机器人学/机器视觉/Drawing 2025-05-06 15.46.05.excalidraw.md resolve  [object Object] 
2025-05-06 15:46:05 [info] index finished after resolve  [object Object] 
2025-05-06 15:46:05 [info] refresh page data from resolve listeners 0 877   
2025-05-06 15:46:32 [info] ignore file modify evnet 学习库/ROS/机器人学/机器视觉/Drawing 2025-05-06 15.46.05.excalidraw.md   
2025-05-06 15:46:32 [info] trigger 学习库/ROS/机器人学/机器视觉/Drawing 2025-05-06 15.46.05.excalidraw.md resolve  [object Object] 
2025-05-06 15:46:32 [info] index finished after resolve  [object Object] 
2025-05-06 15:46:32 [info] refresh page data from resolve listeners 0 877   
2025-05-06 15:49:01 [info] ignore file modify evnet 学习库/ROS/机器人学/机器视觉/Drawing 2025-05-06 15.46.05.excalidraw.md   
2025-05-06 15:49:01 [info] trigger 学习库/ROS/机器人学/机器视觉/Drawing 2025-05-06 15.46.05.excalidraw.md resolve  [object Object] 
2025-05-06 15:49:01 [info] index finished after resolve  [object Object] 
2025-05-06 15:49:01 [info] refresh page data from resolve listeners 0 877   
2025-05-06 15:49:11 [info] indexing created file Excalidraw/Drawing 2025-05-06 15.49.11.excalidraw.md  [object Object] 
2025-05-06 15:49:11 [info] indexing created ignore file Excalidraw/Drawing 2025-05-06 15.49.11.excalidraw.md   
2025-05-06 15:49:11 [info] trigger Excalidraw/Drawing 2025-05-06 15.49.11.excalidraw.md resolve  [object Object] 
2025-05-06 15:49:11 [info] index finished after resolve  [object Object] 
2025-05-06 15:49:11 [info] refresh page data from resolve listeners 0 878   
2025-05-06 15:49:44 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-06 15.49.11.excalidraw.md   
2025-05-06 15:49:44 [info] trigger Excalidraw/Drawing 2025-05-06 15.49.11.excalidraw.md resolve  [object Object] 
2025-05-06 15:49:44 [info] index finished after resolve  [object Object] 
2025-05-06 15:49:44 [info] refresh page data from resolve listeners 0 878   
2025-05-06 15:49:54 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-06 15.49.11.excalidraw.md   
2025-05-06 15:49:54 [info] trigger Excalidraw/Drawing 2025-05-06 15.49.11.excalidraw.md resolve  [object Object] 
2025-05-06 15:49:54 [info] index finished after resolve  [object Object] 
2025-05-06 15:49:54 [info] refresh page data from resolve listeners 0 878   
2025-05-06 15:50:36 [info] indexing created file Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md  [object Object] 
2025-05-06 15:50:36 [info] indexing created ignore file Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md   
2025-05-06 15:50:36 [info] trigger Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md resolve  [object Object] 
2025-05-06 15:50:36 [info] index finished after resolve  [object Object] 
2025-05-06 15:50:36 [info] refresh page data from resolve listeners 0 879   
2025-05-06 15:50:37 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-05-06 15:50:37 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-05-06 15:50:37 [info] ignore file modify evnet Home/components/view/remember.components   
2025-05-06 15:50:37 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-05-06 15:50:37 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-05-06 15:50:56 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md   
2025-05-06 15:50:56 [info] trigger Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md resolve  [object Object] 
2025-05-06 15:50:56 [info] index finished after resolve  [object Object] 
2025-05-06 15:50:56 [info] refresh page data from resolve listeners 0 879   
2025-05-06 15:51:13 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md   
2025-05-06 15:51:13 [info] trigger Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md resolve  [object Object] 
2025-05-06 15:51:13 [info] index finished after resolve  [object Object] 
2025-05-06 15:51:13 [info] refresh page data from resolve listeners 0 879   
2025-05-06 15:51:19 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md   
2025-05-06 15:51:19 [info] trigger Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md resolve  [object Object] 
2025-05-06 15:51:19 [info] index finished after resolve  [object Object] 
2025-05-06 15:51:19 [info] refresh page data from resolve listeners 0 879   
2025-05-06 16:03:07 [info] indexing created file Excalidraw/Drawing 2025-05-06 16.03.07.excalidraw.md  [object Object] 
2025-05-06 16:03:07 [info] indexing created ignore file Excalidraw/Drawing 2025-05-06 16.03.07.excalidraw.md   
2025-05-06 16:03:07 [info] trigger Excalidraw/Drawing 2025-05-06 16.03.07.excalidraw.md resolve  [object Object] 
2025-05-06 16:03:07 [info] index finished after resolve  [object Object] 
2025-05-06 16:03:07 [info] refresh page data from resolve listeners 0 880   
2025-05-06 16:03:34 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md   
2025-05-06 16:03:34 [info] trigger Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md resolve  [object Object] 
2025-05-06 16:03:34 [info] index finished after resolve  [object Object] 
2025-05-06 16:03:34 [info] refresh page data from resolve listeners 0 880   
2025-05-06 16:03:54 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md   
2025-05-06 16:03:54 [info] trigger Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md resolve  [object Object] 
2025-05-06 16:03:54 [info] index finished after resolve  [object Object] 
2025-05-06 16:03:54 [info] refresh page data from resolve listeners 0 880   
2025-05-06 16:04:13 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md   
2025-05-06 16:04:13 [info] trigger Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md resolve  [object Object] 
2025-05-06 16:04:13 [info] index finished after resolve  [object Object] 
2025-05-06 16:04:13 [info] refresh page data from resolve listeners 0 880   
2025-05-06 16:04:28 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md   
2025-05-06 16:04:28 [info] trigger Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md resolve  [object Object] 
2025-05-06 16:04:28 [info] index finished after resolve  [object Object] 
2025-05-06 16:04:28 [info] refresh page data from resolve listeners 0 880   
2025-05-06 16:04:50 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md   
2025-05-06 16:04:50 [info] trigger Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md resolve  [object Object] 
2025-05-06 16:04:50 [info] index finished after resolve  [object Object] 
2025-05-06 16:04:50 [info] refresh page data from resolve listeners 0 880   
2025-05-06 16:05:05 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md   
2025-05-06 16:05:05 [info] trigger Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md resolve  [object Object] 
2025-05-06 16:05:05 [info] index finished after resolve  [object Object] 
2025-05-06 16:05:05 [info] refresh page data from resolve listeners 0 880   
2025-05-06 16:05:41 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md   
2025-05-06 16:05:41 [info] trigger Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md resolve  [object Object] 
2025-05-06 16:05:41 [info] index finished after resolve  [object Object] 
2025-05-06 16:05:41 [info] refresh page data from resolve listeners 0 880   
2025-05-06 16:06:37 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md   
2025-05-06 16:06:37 [info] trigger Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md resolve  [object Object] 
2025-05-06 16:06:37 [info] index finished after resolve  [object Object] 
2025-05-06 16:06:37 [info] refresh page data from resolve listeners 0 880   
2025-05-06 16:09:19 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md   
2025-05-06 16:09:19 [info] trigger Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md resolve  [object Object] 
2025-05-06 16:09:19 [info] index finished after resolve  [object Object] 
2025-05-06 16:09:19 [info] refresh page data from resolve listeners 0 880   
2025-05-06 16:09:39 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md   
2025-05-06 16:09:39 [info] trigger Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md resolve  [object Object] 
2025-05-06 16:09:39 [info] index finished after resolve  [object Object] 
2025-05-06 16:09:39 [info] refresh page data from resolve listeners 0 880   
2025-05-06 16:10:22 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md   
2025-05-06 16:10:22 [info] trigger Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md resolve  [object Object] 
2025-05-06 16:10:22 [info] index finished after resolve  [object Object] 
2025-05-06 16:10:22 [info] refresh page data from resolve listeners 0 880   
2025-05-06 16:10:52 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md   
2025-05-06 16:10:52 [info] trigger Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md resolve  [object Object] 
2025-05-06 16:10:52 [info] index finished after resolve  [object Object] 
2025-05-06 16:10:52 [info] refresh page data from resolve listeners 0 880   
2025-05-06 16:13:37 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md   
2025-05-06 16:13:37 [info] trigger Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md resolve  [object Object] 
2025-05-06 16:13:37 [info] index finished after resolve  [object Object] 
2025-05-06 16:13:37 [info] refresh page data from resolve listeners 0 880   
2025-05-06 16:14:03 [info] ignore file modify evnet Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md   
2025-05-06 16:14:03 [info] trigger Excalidraw/Drawing 2025-05-06 15.50.36.excalidraw.md resolve  [object Object] 
2025-05-06 16:14:03 [info] index finished after resolve  [object Object] 
2025-05-06 16:14:03 [info] refresh page data from resolve listeners 0 880   
2025-05-06 22:12:15 [info] components database created cost 2 ms   
2025-05-06 22:12:15 [info] components index initializing...   
2025-05-06 22:12:16 [info] start to batch put pages: 7   
2025-05-06 22:12:17 [info] batch persist cost 7  1417 
2025-05-06 22:12:17 [info] components index initialized, 880 files cost 1597 ms   
2025-05-06 22:12:17 [info] refresh page data from init listeners 0 880   
2025-05-06 22:12:19 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-05-06 22:12:19 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-05-06 22:12:19 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-05-06 22:12:19 [info] ignore file modify evnet Home/components/view/remember.components   
2025-05-06 22:12:19 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-05-06 22:12:19 [info] ignore file modify evnet Home/components/view/文件检索.components   
2025-05-06 22:28:12 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-05-06 22:28:12 [info] ignore file modify evnet Home/components/view/o'clock.components   
2025-05-06 22:28:12 [info] ignore file modify evnet Home/components/view/remember.components   
2025-05-06 22:28:12 [info] ignore file modify evnet Home/components/view/快速导航.components   
2025-05-06 22:28:12 [info] ignore file modify evnet Home/components/view/文件检索.components   
