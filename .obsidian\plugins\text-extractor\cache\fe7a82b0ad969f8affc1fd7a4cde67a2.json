{"path": "Deep learning/yolov5/Pasted image 20240605152225.jpg", "text": "164 64 128 64 64 2 input : output Image —_— . tle ™1™ segmentatior gl e g8 map NI K MR ca gl d 88 2| o % Nl Of ©® ~| | © 5518 ' 128 128 I 256 128 ﬂ aol \"wl HE E %f g ' a0p: 2 512 256 %I &Id —; \"g[l?ml’él = conv 3x3, RelLU S B “TpS 8 = copy and cro ¥ 52 s 1024 512 ' Py sz; & & max sielll-l — [ E-E-E # max poo 5 3§ jou I 4 up-conv 2x2 “:\"-E & = conv 1x1", "libVersion": "0.3.2", "langs": "eng"}