2025-07-26 14:54:40.398 [info] components database created cost 1 ms   
2025-07-26 14:54:40.398 [info] components index initializing...   
2025-07-26 14:54:40.601 [info] start to batch put pages: 6   
2025-07-26 14:54:40.615 [info] batch persist cost 6  14 
2025-07-26 14:54:40.655 [info] components index initialized, 1010 files cost 258 ms   
2025-07-26 14:54:40.655 [info] refresh page data from init listeners 0 1010   
2025-07-26 14:54:42.023 [info] indexing created file components/logs/2025-07-26.components.log  [object Object] 
2025-07-26 14:54:42.025 [info] refresh page data from created listeners 0 1011   
2025-07-26 14:54:42.147 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-26 14:54:42.327 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-26 14:54:42.683 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-26 14:54:42.714 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-26 14:54:42.718 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-26 14:54:42.723 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-26 21:13:15.005 [info] indexing created file 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md  [object Object] 
2025-07-26 21:13:15.005 [info] indexing created ignore file 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md   
2025-07-26 21:13:15.044 [info] trigger 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md resolve  [object Object] 
2025-07-26 21:13:15.051 [info] index finished after resolve  [object Object] 
2025-07-26 21:13:15.051 [info] refresh page data from resolve listeners 0 1012   
2025-07-26 21:13:15.412 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-26 21:13:15.799 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-26 21:13:15.811 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-26 21:13:15.817 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-26 21:13:15.825 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-26 21:13:32.575 [debug] ignore file modify evnet 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md   
2025-07-26 21:13:32.601 [info] trigger 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md resolve  [object Object] 
2025-07-26 21:13:32.602 [info] index finished after resolve  [object Object] 
2025-07-26 21:13:32.602 [info] refresh page data from resolve listeners 0 1012   
2025-07-26 21:13:35.680 [debug] ignore file modify evnet 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md   
2025-07-26 21:13:35.690 [info] trigger 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md resolve  [object Object] 
2025-07-26 21:13:35.691 [info] index finished after resolve  [object Object] 
2025-07-26 21:13:35.692 [info] refresh page data from resolve listeners 0 1012   
2025-07-26 21:13:51.749 [debug] ignore file modify evnet 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md   
2025-07-26 21:13:51.756 [info] trigger 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md resolve  [object Object] 
2025-07-26 21:13:51.758 [info] index finished after resolve  [object Object] 
2025-07-26 21:13:51.758 [info] refresh page data from resolve listeners 0 1012   
2025-07-26 21:14:03.331 [debug] ignore file modify evnet 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md   
2025-07-26 21:14:03.340 [info] trigger 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md resolve  [object Object] 
2025-07-26 21:14:03.341 [info] index finished after resolve  [object Object] 
2025-07-26 21:14:03.342 [info] refresh page data from resolve listeners 0 1012   
2025-07-26 21:14:16.610 [debug] ignore file modify evnet 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md   
2025-07-26 21:14:16.616 [info] trigger 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md resolve  [object Object] 
2025-07-26 21:14:16.617 [info] index finished after resolve  [object Object] 
2025-07-26 21:14:16.618 [info] refresh page data from resolve listeners 0 1012   
2025-07-26 21:14:32.429 [debug] ignore file modify evnet 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md   
2025-07-26 21:14:32.434 [info] trigger 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md resolve  [object Object] 
2025-07-26 21:14:32.435 [info] index finished after resolve  [object Object] 
2025-07-26 21:14:32.436 [info] refresh page data from resolve listeners 0 1012   
2025-07-26 21:14:35.395 [debug] ignore file modify evnet 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md   
2025-07-26 21:14:35.400 [info] trigger 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md resolve  [object Object] 
2025-07-26 21:14:35.401 [info] index finished after resolve  [object Object] 
2025-07-26 21:14:35.401 [info] refresh page data from resolve listeners 0 1012   
2025-07-26 21:14:51.149 [debug] ignore file modify evnet 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md   
2025-07-26 21:14:51.153 [info] trigger 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md resolve  [object Object] 
2025-07-26 21:14:51.153 [info] index finished after resolve  [object Object] 
2025-07-26 21:14:51.154 [info] refresh page data from resolve listeners 0 1012   
2025-07-26 21:16:01.632 [debug] ignore file modify evnet 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md   
2025-07-26 21:16:01.637 [info] trigger 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md resolve  [object Object] 
2025-07-26 21:16:01.638 [info] index finished after resolve  [object Object] 
2025-07-26 21:16:01.639 [info] refresh page data from resolve listeners 0 1012   
2025-07-26 21:16:28.722 [debug] ignore file modify evnet 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md   
2025-07-26 21:16:28.731 [info] trigger 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md resolve  [object Object] 
2025-07-26 21:16:28.732 [info] index finished after resolve  [object Object] 
2025-07-26 21:16:28.733 [info] refresh page data from resolve listeners 0 1012   
2025-07-26 21:16:54.335 [debug] ignore file modify evnet 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md   
2025-07-26 21:16:54.344 [info] trigger 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md resolve  [object Object] 
2025-07-26 21:16:54.346 [info] index finished after resolve  [object Object] 
2025-07-26 21:16:54.347 [info] refresh page data from resolve listeners 0 1012   
2025-07-26 21:17:10.356 [debug] ignore file modify evnet 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md   
2025-07-26 21:17:10.363 [info] trigger 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md resolve  [object Object] 
2025-07-26 21:17:10.365 [info] index finished after resolve  [object Object] 
2025-07-26 21:17:10.366 [info] refresh page data from resolve listeners 0 1012   
2025-07-26 21:17:29.839 [debug] ignore file modify evnet 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md   
2025-07-26 21:17:29.846 [info] trigger 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md resolve  [object Object] 
2025-07-26 21:17:29.848 [info] index finished after resolve  [object Object] 
2025-07-26 21:17:29.849 [info] refresh page data from resolve listeners 0 1012   
2025-07-26 21:19:44.836 [debug] ignore file modify evnet 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md   
2025-07-26 21:19:44.841 [info] trigger 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md resolve  [object Object] 
2025-07-26 21:19:44.843 [info] index finished after resolve  [object Object] 
2025-07-26 21:19:44.844 [info] refresh page data from resolve listeners 0 1012   
2025-07-26 21:19:57.926 [debug] ignore file modify evnet 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md   
2025-07-26 21:19:57.932 [info] trigger 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md resolve  [object Object] 
2025-07-26 21:19:57.935 [info] index finished after resolve  [object Object] 
2025-07-26 21:19:57.935 [info] refresh page data from resolve listeners 0 1012   
2025-07-26 21:20:14.159 [debug] ignore file modify evnet 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md   
2025-07-26 21:20:14.167 [info] trigger 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md resolve  [object Object] 
2025-07-26 21:20:14.169 [info] index finished after resolve  [object Object] 
2025-07-26 21:20:14.170 [info] refresh page data from resolve listeners 0 1012   
2025-07-26 21:20:26.322 [debug] ignore file modify evnet 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md   
2025-07-26 21:20:26.327 [info] trigger 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md resolve  [object Object] 
2025-07-26 21:20:26.330 [info] index finished after resolve  [object Object] 
2025-07-26 21:20:26.331 [info] refresh page data from resolve listeners 0 1012   
2025-07-26 21:20:29.271 [debug] ignore file modify evnet 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md   
2025-07-26 21:20:29.275 [info] trigger 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md resolve  [object Object] 
2025-07-26 21:20:29.277 [info] index finished after resolve  [object Object] 
2025-07-26 21:20:29.278 [info] refresh page data from resolve listeners 0 1012   
2025-07-26 21:20:45.087 [debug] ignore file modify evnet 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md   
2025-07-26 21:20:45.092 [info] trigger 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md resolve  [object Object] 
2025-07-26 21:20:45.094 [info] index finished after resolve  [object Object] 
2025-07-26 21:20:45.095 [info] refresh page data from resolve listeners 0 1012   
2025-07-26 21:21:02.573 [debug] ignore file modify evnet 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md   
2025-07-26 21:21:02.578 [info] trigger 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md resolve  [object Object] 
2025-07-26 21:21:02.580 [info] index finished after resolve  [object Object] 
2025-07-26 21:21:02.581 [info] refresh page data from resolve listeners 0 1012   
2025-07-26 21:21:44.158 [debug] ignore file modify evnet 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md   
2025-07-26 21:21:44.177 [info] trigger 学习库/Latex/Drawing 2025-07-26 21.13.14.excalidraw.md resolve  [object Object] 
2025-07-26 21:21:44.179 [info] index finished after resolve  [object Object] 
2025-07-26 21:21:44.179 [info] refresh page data from resolve listeners 0 1012   
