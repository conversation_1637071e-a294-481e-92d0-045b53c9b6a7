2025-07-16 06:43:58.780 [info] components database created cost 1 ms   
2025-07-16 06:43:58.780 [info] components index initializing...   
2025-07-16 06:43:58.929 [info] start to batch put pages: 7   
2025-07-16 06:43:58.954 [info] batch persist cost 7  25 
2025-07-16 06:43:59.001 [info] components index initialized, 994 files cost 222 ms   
2025-07-16 06:43:59.001 [info] refresh page data from init listeners 0 994   
2025-07-16 06:44:00.325 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-16 06:44:00.451 [info] ignore same query  let keyword1 = '';
let keyword2 = '';
let tag1 = '';
let tag2 = '';
let pageSize = '10';
let pages = dv.pages('!"assets"');
let logi = 'and';

//定义排序字段
let sort1 = 'cday'
let sort2 = 'desc'

// 定义两种筛选函数
const filterByIntersection = p => {
    return (!keyword1 || p.file.name.toLowerCase().includes(keyword1.toLowerCase())) &&
        (!keyword2 || p.file.name.toLowerCase().includes(keyword2.toLowerCase())) &&
        (!tag1 || p.file.tags.includes(tag1)) &&
        (!tag2 || p.file.tags.includes(tag2));
};

const filterByUnion = p => {
    return (keyword1 && p.file.name.toLowerCase().includes(keyword1.toLowerCase())) ||
        (keyword2 && p.file.name.toLowerCase().includes(keyword2.toLowerCase())) ||
        (tag1 && p.file.tags.includes(tag1)) ||
        (tag2 && p.file.tags.includes(tag2));
};

// 使用三元操作符选择筛选函数
pages = pages.filter(logi === 'and' ? filterByIntersection : filterByUnion);

// 排序：这里默认使用的是按照创建日期降序排列，如想要更多的排序，可以使用sortable插件，启用该插件后，每个表头字段都可以通过单击来切换升序、降序排列。发现问题：该插件在markdown中引用的组件中无效，因为components组件禁用了一些点击事件。
if (sort1 === 'name') {
    pages = pages.sort(p => p.file.name, sort2)
} else if (sort1 === 'tags') {
    pages = pages.sort(p => p.file.tags, sort2)
} else if (sort1 === 'inlinks') {
    pages = pages.sort(p => p.file.inlinks, sort2)
} else if (sort1 === 'cday') {
    pages = pages.sort(p => p.file.cday, sort2)
} else if (sort1 === 'mday') {
    pages = pages.sort(p => p.file.mday, sort2)
}

// 表格生成
dv.table(["文件", "所有标签", "入链", "创建日期", "修改日期"],
    pages
        // 所有标签
        .map(p => [p.file.link, p.file.tags.join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅一级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') === -1).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        // 仅二级标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.indexOf('/') > -1 && item.split('/').length <= 2).join(', '), p.file.cday, p.file.mday])
        // 二级及以上标签
        // .map(p => [p.file.link, p.file.tags.filter(item => item.includes('/')).join(', '), p.file.inlinks.join(', '), p.file.cday, p.file.mday])
        .slice(0, pageSize)
) 
2025-07-16 06:44:01.061 [info] indexing created file components/logs/2025-07-16.components.log  [object Object] 
2025-07-16 06:44:01.072 [info] refresh page data from created listeners 0 995   
2025-07-16 06:44:01.109 [debug] ignore file modify evnet Home/components/view/o'clock.components   
2025-07-16 06:44:01.125 [debug] ignore file modify evnet Home/components/view/remember.components   
2025-07-16 06:44:01.136 [debug] ignore file modify evnet Home/components/view/快速导航.components   
2025-07-16 06:44:01.140 [debug] ignore file modify evnet Home/components/view/文件检索.components   
2025-07-16 06:44:02.540 [debug] ignore file modify evnet 学习库/Latex/Latex 从入门到如土.md   
2025-07-16 06:44:02.566 [info] trigger 学习库/Latex/Latex 从入门到如土.md resolve  [object Object] 
2025-07-16 06:44:02.567 [info] index finished after resolve  [object Object] 
2025-07-16 06:44:02.568 [info] refresh page data from resolve listeners 0 995   
2025-07-16 06:44:17.802 [info] query  [object Object] 
2025-07-16 06:44:17.803 [info] query  [object Object] 
2025-07-16 06:44:17.803 [info] query  [object Object] 
2025-07-16 06:44:17.803 [info] query  [object Object] 
2025-07-16 06:44:17.803 [info] query  [object Object] 
2025-07-16 06:44:17.804 [info] query  [object Object] 
2025-07-16 06:44:17.805 [info] query  [object Object] 
2025-07-16 06:44:17.805 [info] query  [object Object] 
2025-07-16 06:44:17.806 [info] query  [object Object] 
2025-07-16 06:44:17.844 [info] query changed, compare cost 0ms, data length diff 0/65   
2025-07-16 06:44:17.867 [info] query changed, compare cost 0ms, data length diff 0/16   
2025-07-16 06:44:17.871 [info] query changed, compare cost 0ms, data length diff 0/9   
2025-07-16 06:44:17.873 [info] query changed, compare cost 0ms, data length diff 0/65   
2025-07-16 08:06:04.494 [info] indexing created file components/logs/2025-07-16.components.log  [object Object] 
2025-07-16 08:06:04.604 [info] refresh page data from created listeners 0 995   
