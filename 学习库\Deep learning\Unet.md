# Unet

Unet 是一个用于分割的神经网络模型，整体流程实际上是一个编码和解码的过程，网络结构如图所示 

````ad-note
collapse: open
title: 网络结构图

![[Pasted image 20240605152225.jpg]]

1. `conv 3x3,ReLu`就是卷积层，其中卷积核大小是3x3，然后经过ReLu激活。
2. `copy and crop` 对于你输出的尺寸，你需要进行复制并进行中心剪裁。方便和后面上采样生成的尺寸进行拼接。
3. `max pool 2x2` 最大池化层，卷积核为2x2
4. `up-conv 2x2`  转置卷积，用来上采样
5. `conv 1x1` 这里就是卷积层，卷积核大小是1x1

````

## 解码过程 (左边部分)

左侧为 contracting path，是由卷积核 max pooling 构成的 [[上采样；下采样#下采样|下采样]]  操作，contracting path 主要由 4 个 block 组成，每一个 block 包含了三层卷积和一层 [[池化层（pooling layers)#最大池化(max pooling)|最大池化]] 操作，并且在每次降采样操作之后都会将 Feature Map 的个数乘二，最终尺寸大小为 32×32 的 Feature Map。

### 第一块内容

```python
# 由572*572*1变成了570*570*64
self.conv1_1 = nn.Conv2d(in_channels=1, out_channels=64, kernel_size=3, stride=1, padding=0)
self.relu1_1 = nn.ReLU(inplace=True)
# 由570*570*64变成了568*568*64
self.conv1_2 = nn.Conv2d(64, 64, kernel_size=3, stride=1, padding=0) 
self.relu1_2 = nn.ReLU(inplace=True)
```

由Unet网络架构图，可以看出输入图像是1x572x572大小，其中的1代表的是通道数（后续可以自己更改成自己想要的通道数）并且通过 conv 3 x 3，得知卷积核为 3 x 3 尺寸，并且由图片中的尺寸变成 570 x 570，因此可以得出相关的参数值 `in_channels=1, out_channels=64, kernel_size=3, stride=1, padding=0`，整个图片的蓝色箭头的卷积操作都是这样，因此 `kernel_size=3, stride=1, padding=0` 可以固定了。只需要更改输入和输出通道数的大小即可。

- **数据维度变化：1x572x572 -> 64x570x570 -> 64x568x568**

### 最大池化层 1

```python
# 采用最大池化进行下采样，图片大小减半，通道数不变，由568*568*64变成284*284*64
self.maxpool_1 = nn.MaxPool2d(kernel_size=2, stride=2)  
```

最大池化的卷积核和步长都设置为2，使得输出尺寸减半，通道数不变。  

- **数据维度变化：64x568x568 -> 64x284x284**

### 第二块内容

```python
# 284*284*64->282*282*128
self.conv2_1 = nn.Conv2d(in_channels=64, out_channels=128, kernel_size=3, stride=1, padding=0)  
self.relu2_1 = nn.ReLU(inplace=True)
# 282*282*128->280*280*128
self.conv2_2 = nn.Conv2d(128, 128, kernel_size=3, stride=1, padding=0)  
self.relu2_2 = nn.ReLU(inplace=True)
```

- **数据维度变化：64x284x284->128x282x282->128x280x280**

### 最大池化层 2

```python
# 采用最大池化进行下采样  280*280*128->140*140*128
self.maxpool_2 = nn.MaxPool2d(kernel_size=2, stride=2)
```

最大池化的卷积核和步长都设置为2，使得输出尺寸减半，通道数不变。

- **数据维度变化：128x280x280 -> 128x140x140**

### 解码部分代码汇总

每经过一次卷积+relu操作，图像尺寸-2，可以得出padding=0（VGG16中padding=1，因此使得图像尺寸不变）；每经过一次最大池化，图像尺寸减半。

```python
class Unet(nn.Module):
    def __init__(self):
        super(Unet, self).__init__()
        self.conv1_1 = nn.Conv2d(in_channels=1, out_channels=64, kernel_size=3, stride=1, padding=0)  # 由572*572*1变成了570*570*64
        self.relu1_1 = nn.ReLU(inplace=True)
        self.conv1_2 = nn.Conv2d(64, 64, kernel_size=3, stride=1, padding=0)  # 由570*570*64变成了568*568*64
        self.relu1_2 = nn.ReLU(inplace=True)

        self.maxpool_1 = nn.MaxPool2d(kernel_size=2, stride=2)  # 采用最大池化进行下采样，图片大小减半，通道数不变，由568*568*64变成284*284*64

        self.conv2_1 = nn.Conv2d(in_channels=64, out_channels=128, kernel_size=3, stride=1, padding=0)  # 284*284*64->282*282*128
        self.relu2_1 = nn.ReLU(inplace=True)
        self.conv2_2 = nn.Conv2d(128, 128, kernel_size=3, stride=1, padding=0)  # 282*282*128->280*280*128
        self.relu2_2 = nn.ReLU(inplace=True)

        self.maxpool_2 = nn.MaxPool2d(kernel_size=2, stride=2)  # 采用最大池化进行下采样  280*280*128->140*140*128

        self.conv3_1 = nn.Conv2d(in_channels=128, out_channels=256, kernel_size=3, stride=1, padding=0)  # 140*140*128->138*138*256
        self.relu3_1 = nn.ReLU(inplace=True)
        self.conv3_2 = nn.Conv2d(256, 256, kernel_size=3, stride=1, padding=0)  # 138*138*256->136*136*256
        self.relu3_2 = nn.ReLU(inplace=True)

        self.maxpool_3 = nn.MaxPool2d(kernel_size=2, stride=2)  # 采用最大池化进行下采样  136*136*256->68*68*256

        self.conv4_1 = nn.Conv2d(in_channels=256, out_channels=512, kernel_size=3, stride=1, padding=0)  # 68*68*256->66*66*512
        self.relu4_1 = nn.ReLU(inplace=True)
        self.conv4_2 = nn.Conv2d(512, 512, kernel_size=3, stride=1, padding=0)  # 66*66*512->64*64*512
        self.relu4_2 = nn.ReLU(inplace=True)

        self.maxpool_4 = nn.MaxPool2d(kernel_size=2, stride=2)  # 采用最大池化进行下采样  64*64*512->32*32*512

        self.conv5_1 = nn.Conv2d(in_channels=512, out_channels=1024, kernel_size=3, stride=1, padding=0)  # 32*32*512->30*30*1024
        self.relu5_1 = nn.ReLU(inplace=True)
        self.conv5_2 = nn.Conv2d(1024, 1024, kernel_size=3, stride=1, padding=0)  # 30*30*1024->28*28*1024
        self.relu5_2 = nn.ReLU(inplace=True)

    def forward(self, x):
        x1 = self.conv1_1(x)
        x1 = self.relu1_1(x1)
        x2 = self.conv1_2(x1)
        x2 = self.relu1_2(x2)  # 这个后续需要使用
        down1 = self.maxpool_1(x2)

        x3 = self.conv2_1(down1)
        x3 = self.relu2_1(x3)
        x4 = self.conv2_2(x3)
        x4 = self.relu2_2(x4)  # 这个后续需要使用
        down2 = self.maxpool_2(x4)

        x5 = self.conv3_1(down2)
        x5 = self.relu3_1(x5)
        x6 = self.conv3_2(x5)
        x6 = self.relu3_2(x6)  # 这个后续需要使用
        down3 = self.maxpool_3(x6)

        x7 = self.conv4_1(down3)
        x7 = self.relu4_1(x7)
        x8 = self.conv4_2(x7)
        x8 = self.relu4_2(x8)  # 这个后续需要使用
        down4 = self.maxpool_4(x8)

        x9 = self.conv5_1(down4)
        x9 = self.relu5_1(x9)
        x10 = self.conv5_2(x9)
        x10 = self.relu5_2(x10)
```

## 编码过程（右边部分）
右侧部分则被称为 expansive path，跟 contracting path 一样，由 4 个 block 组成，每个 block 开始之前通过反卷积操作将特征图尺寸放大两倍，同时将特征通道数减半，然后和左侧对称的 contracting path 的特征图进行合并，由于左侧压缩路径和右侧扩展路径的 Feature Map 的尺寸不一样，U-Net 通过将 contracting path 的 Feature Map 进行<span style="background:#ff4d4f">中心裁剪</span>到和 expansive path相同尺寸（<font color="#7f7f7f">图中的虚线部分就是裁切成相同的尺寸，</font><font color="#c00000">但目前主流的做法不是进行裁切，而是将左边解码中的卷积过程进行一个 padding，从而保证左右两边的 feature map 尺寸相同</font>）的 Feature Map 来进行归一化。
在最后可以看到输出的卷积核的个数是 2，和分类的数量相同。


