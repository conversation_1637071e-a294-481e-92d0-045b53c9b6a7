---
tags:
  - 项目
---
# Kan 原理
![[Efficient KAN-2025-05-19-17-08-30.png]]


$f(x_1, \ldots, x_n) = \sum_{q = 1}^{2n + 1} \Phi_q \left( \sum_{p = 1}^{n} \varphi_{q,p}(x_p) \right)$


# Efficient-KAN 初始化参数详解与调优指南

## 简介

Efficient-KAN 是对原始 Kolmogorov-Arnold Network (KAN) 的内存效率优化实现。本文档详细介绍

`efficient-kan/src/efficient_kan/kan.py`

中 KAN 类的初始化参数，以及如何调整这些参数以优化模型性能。

## KAN 类参数详解

```python
def __init__(
           self,
           layers_hidden,
           grid_size=5,
           spline_order=3,
           scale_noise=0.1,
           scale_base=1.0,
           scale_spline=1.0,
           enable_standalone_scale_spline=True,
           base_activation=torch.nn.SiLU,
           grid_eps=0.02,
           grid_range=[-1, 1],
       ):
```

### 核心参数

|参数|描述|默认值|调优建议|
|---|---|---|---|
|`layers_hidden`|定义网络结构的列表，如 `[2048, 256, 2]`|必需参数|根据任务复杂度调整层数和宽度|
|`grid_size`|B样条网格的大小，控制非线性复杂度|5|复杂任务增大，简单任务减小|
|`spline_order`|B样条的阶数，控制平滑度|3|通常保持默认值|

### 缩放参数

|参数|描述|默认值|调优建议|
|---|---|---|---|
|`scale_noise`|初始化时添加到样条控制点的噪声大小|0.1|减小可提高稳定性，增大可增强初始非线性|
|`scale_base`|基础线性部分权重的初始化缩放|1.0|增大可增强线性部分影响|
|`scale_spline`|样条部分权重的初始化缩放|1.0|减小可减弱非线性部分，防止过拟合|
|`enable_standalone_scale_spline`|是否为每个激活函数使用独立的可学习缩放因子|True|设为False可减少参数量|

### 其他参数

|参数|描述|默认值|调优建议|
|---|---|---|---|
|`base_activation`|基础线性部分使用的激活函数|`torch.nn.SiLU`|可尝试 `GELU` 或 `ReLU`|
|`grid_eps`|控制网格更新时均匀网格和自适应网格的混合比例|0.02|增大使网格更均匀，减小使网格更适应数据分布|
|`grid_range`|定义B样条网格的范围|`[-1, 1]`|通常保持默认值，除非输入数据范围特殊|

## 参数详细说明与调优方向

### 1. `layers_hidden`

**作用**：定义KAN网络的结构，包括输入维度、隐藏层维度和输出维度。

**格式**：列表，如 `[2048, 256, 2]` 表示输入维度为2048，一个256维的隐藏层，输出维度为2。

**调优方向**：

- **增加层数**：增强网络的表达能力，适合复杂任务
    - 例如：`[2048, 1024, 512, 256, 2]`
- **减少层数**：减少过拟合风险，适合简单任务或小数据集
    - 例如：`[2048, 2]`
- **调整层宽**：平衡表达能力和计算效率
    - 宽层（如`[2048, 1024, 2]`）：捕获更多特征
    - 窄层（如`[2048, 128, 2]`）：减少参数量，防止过拟合
- **瓶颈结构**：在中间添加较小的层，如`[2048, 512, 128, 256, 2]`
    - 强制网络学习更紧凑的表示
    - 减少参数量
    - 可能提高泛化能力

### 2. `grid_size`

**作用**：控制B样条网格的大小，即在输入空间中放置多少个控制点。

**调优方向**：

- **增大值**（如7-10）：
    - 提供更细粒度的非线性拟合能力
    - 适合复杂的映射关系
    - 可能导致过拟合
    - 增加内存和计算需求
- **减小值**（如3-4）：
    - 提供更平滑的拟合
    - 减少过拟合风险
    - 适合简单任务或小数据集
    - 减少计算复杂度和内存使用
    - 可能导致欠拟合

**实际影响**：

- 在MNIST数据集上，`grid_size=10`比`grid_size=5`提高了约1-2%的准确率
- 在小数据集上，较小的`grid_size`（3-4）通常表现更好

### 3. `spline_order`

**作用**：定义B样条的阶数，控制样条的平滑度。

**调优方向**：

- **增大值**（如4-5）：
    - 产生更平滑的函数
    - 可能丢失局部细节
    - 增加计算复杂度
- **减小值**（如1-2）：
    - 减少平滑度，增强对局部特征的拟合
    - 可能导致过拟合
    - 减少计算复杂度
- **保持默认值3**：
    - 立方B样条提供良好的平滑性和局部控制平衡
    - 在大多数情况下表现良好

**实际影响**：

- 在大多数任务中，`spline_order=3`（立方B样条）提供了最佳平衡
- 图像分类任务中，有时`spline_order=2`表现更好

### 4. `scale_noise`

**作用**：控制初始化时添加到样条控制点的噪声大小。

**调优方向**：

- **增大值**（如0.2-0.3）：
    - 增加初始非线性性
    - 可能导致训练不稳定
    - 适合复杂任务
    - 可能帮助逃离局部最小值
- **减小值**（如0.01-0.05）：
    - 使初始函数更接近线性
    - 提高训练稳定性
    - 适合简单任务或需要稳定训练的情况

**实际影响**：

- Efficient-KAN的README提到，较小的 `scale_noise`值（如0.05）通常提供更稳定的训练
- 在裂纹舌象分类这样的二分类任务中，建议使用较小的值（0.01-0.05）

### 5.  `scale_base`

**作用**：控制基础线性部分权重的初始化缩放。

**调优方向**：

- **增大值**（如1.2-1.5）：
    - 增强线性部分的影响
    - 提高训练初期的稳定性
    - 适合线性可分或近似线性的问题
- **减小值**（如0.5-0.8）：
    - 减弱线性部分的影响
    - 增强非线性部分的相对重要性
    - 适合高度非线性的问题

**实际影响**：

- 在分类任务中，较大的 `scale_base`值（1.2-1.5）通常表现更好
- 在回归任务中，平衡的值（0.8-1.2）可能更适合

### 6. `scale_spline`

**作用**：控制样条部分权重的初始化缩放。

**调优方向**：

- **增大值**（如1.2-1.5）：
    - 增强非线性部分的影响
    - 提高模型的表达能力
    - 适合复杂的非线性问题
- **减小值**（如0.5-0.8）：
    - 减弱非线性部分的影响
    - 减少过拟合风险
    - 适合简单问题或小数据集

**实际影响**：

- 在复杂任务中，平衡的`scale_spline`值（0.8-1.2）通常表现最佳
- 在小数据集上，较小的值（0.5-0.8）可以减少过拟合

### 7. `enable_standalone_scale_spline`

**作用**：控制是否为每个激活函数使用独立的可学习缩放因子。

**调优方向**：

- **True**：
    - 增加模型灵活性
    - 允许每个特征有不同的非线性强度
    - 增加参数量
    - 可能提高表达能力
- **False**：
    - 减少参数量
    - 提高训练效率
    - 可能减少过拟合
    - 适合小数据集

**实际影响**：

- 在大数据集上， `True` 通常提供更好的性能
- 在小数据集上， `False` 可能有助于防止过拟合

### 8. `base_activation`
**作用**：基础线性部分使用的激活函数。

**调优方向**：

- **SiLU (Swish)**：默认选择，提供平滑的非线性性
- **GELU**：类似于SiLU，在Transformer架构中表现良好
- **ReLU**：简单高效，但不平滑
- **LeakyReLU**：解决ReLU的"死亡"问题
- **Tanh/Sigmoid**：将输出限制在特定范围内

**实际影响**：

- 在Efficient-KAN中，GELU和SiLU通常表现最佳
- 对于裂纹舌象分类任务，GELU可能是一个好的选择

### 9. `grid_eps`

**作用**：控制网格更新时均匀网格和自适应网格的混合比例。

**调优方向**：

- **增大值**（如0.1-0.5）：
    - 使网格更均匀
    - 减少对异常值的敏感性
    - 适合噪声数据
- **减小值**（如0.001-0.01）：
    - 使网格更适应数据分布
    - 增强对数据密集区域的拟合
    - 适合分布不均匀的数据

**实际影响**：

- 在大多数情况下，默认值0.02表现良好
- 对于有噪声的数据，增加到0.05-0.1可能有帮助

### 10. `grid_range`

**作用**：定义B样条网格的范围。

**调优方向**：

- **扩大范围**（如`[-2, 2]`）：
    - 适应更广泛的输入分布
    - 可能导致在数据密集区域的精度降低
- **缩小范围**（如`[-0.5, 0.5]`）：
    - 增强对特定范围内数据的拟合精度
    - 超出范围的数据可能表现不佳
- **保持默认值`[-1, 1]`**：
    - 适合标准化或归一化的输入数据
    - 在大多数情况下表现良好

**实际影响**：

- 如果输入数据已经标准化，默认值`[-1, 1]`通常是最佳选择
- 如果输入数据有特殊分布，可以相应调整

## Efficient-KAN 与原始 KAN 的区别

Efficient-KAN 相比原始 KAN 的主要改进：

1. **内存效率**：重新表述计算方式，避免大型中间张量的创建
2. **正则化方法**：使用权重上的L1正则化替代输入样本上的L1正则化
3. **可选的激活函数缩放**：通过`enable_standalone_scale_spline`参数控制
4. **改进的参数初始化**：使用 `kaiming_uniform_`初始化

这些改进使得 Efficient-KAN 在保持性能的同时，显著减少了内存使用和计算复杂度。

## 针对裂纹舌象分类任务的推荐配置

基于你的二分类任务和数据集规模，以下是几种推荐配置：

### 1. 平衡配置

Copy

KAN(

    layers_hidden=[2048, 256, 2],

    grid_size=4,

    spline_order=3,

    scale_noise=0.05,

    scale_base=1.2,

    scale_spline=0.8,

    enable_standalone_scale_spline=False,

    base_activation=torch.nn.GELU,

    grid_eps=0.05

)

这个配置平衡了表达能力和过拟合风险，适合大多数情况。

### 2. 防过拟合配置

Copy

KAN(

    layers_hidden=[2048, 128, 2],

    grid_size=3,

    spline_order=2,

    scale_noise=0.01,

    scale_base=1.5,

    scale_spline=0.5,

    enable_standalone_scale_spline=False,

    base_activation=torch.nn.ReLU,

    grid_eps=0.1

)

这个配置减少了模型复杂度，增强了正则化效果，适合小数据集。

### 3. 增强表达能力配置

Copy

KAN(

    layers_hidden=[2048, 512, 256, 2],

    grid_size=6,

    spline_order=3,

    scale_noise=0.1,

    scale_base=1.0,

    scale_spline=1.2,

    enable_standalone_scale_spline=True,

    base_activation=torch.nn.SiLU,

    grid_eps=0.02

)

这个配置增加了网络深度和非线性复杂度，适合复杂的分类边界。

### 4. 高效计算配置

Copy

KAN(

    layers_hidden=[2048, 256, 2],

    grid_size=4,

    spline_order=2,  # 降低阶数减少计算

    scale_noise=0.05,

    scale_base=1.0,

    scale_spline=1.0,

    enable_standalone_scale_spline=False,  # 禁用独立缩放减

    少参数

    base_activation=torch.nn.ReLU,  # 使用更高效的激活函数

    grid_eps=0.05

)

这个配置优化了计算效率，适合资源受限的环境。

## 实验建议

1. **从简单开始**：先尝试简单配置，逐步增加复杂度
2. **单参数实验**：每次只改变一个参数，观察其影响
3. **交叉验证**：使用交叉验证评估不同配置
4. **监控过拟合**：观察训练集和验证集性能差距
5. **结合正则化**：配合Dropout和权重衰减使用
6. **学习率调整**：为KAN层使用较小的学习率（通常是基础学习率的0.3-0.5倍）
7. **批量大小实验**：尝试不同的批量大小，KAN对批量大小可能比传统网络更敏感

## 性能监控指标

在调优过程中，除了准确率外，还应关注以下指标：

1. **训练/验证损失差距**：监控过拟合
2. **参数敏感性**：观察小参数变化对性能的影响
3. **收敛速度**：不同配置可能有不同的收敛速率
4. **内存使用**：监控GPU内存使用，特别是对于大
    
     `grid_size`
    
    值
5. **推理时间**：某些配置可能影响推理速度

通过系统地调整这些参数，你可以找到最适合裂纹舌象分类任务的Efficient-KAN配置

