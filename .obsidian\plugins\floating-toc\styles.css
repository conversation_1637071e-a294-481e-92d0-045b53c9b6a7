/* @settings

name: Floating TOC
id: floating-toc-styles
settings:
    - 
        id: floating-toc-font-color-header
        title:  color setting
        title.zh: 颜色相关设置
        type: heading
        level: 2
        collapsed: true
    - 
        id: floating-toc-header-settings
        title: Headers
        title.zh: 大纲标题（H1-H6）
        type: heading
        level: 3
        collapsed: true
    -
        id: floating-toc-remove-heading-indicator
        title: Remove H1-H6 Indicators after Headings 
        title.zh: 移除标题后的H1-H6
        type: class-toggle
        default: false
    -
        id: toggle-floating-toc-header-color 
        title: toggle header color 
        title.zh: 是否开启标题颜色
        type: class-toggle
        default: false
    -
        id: floating-toc-color-h1
        title: Header 1
        type: variable-themed-color
        opacity: true
        format: hex
        default-light: '#'
        default-dark: '#'
    -
        id: floating-toc-color-h2
        title: Header 2
        type: variable-themed-color
        opacity: true
        format: hex
        default-light: '#'
        default-dark: '#'
    -
        id: floating-toc-color-h3
        title: Header 3
        type: variable-themed-color
        opacity: true
        format: hex
        default-light: '#'
        default-dark: '#'
    -
        id: floating-toc-color-h4
        title: Header 4
        type: variable-themed-color
        opacity: true
        format: hex
        default-light: '#'
        default-dark: '#'
    -
      id: floating-toc-color-h5
      title: Header 5
      type: variable-themed-color
      opacity: true
      format: hex
      default-light: '#'
      default-dark: '#'
    -
      id: floating-toc-color-h6
      title: Header 6
      type: variable-themed-color
      opacity: true
      format: hex
      default-light: '#'
      default-dark: '#'
    -
        id: floating-toc-settings
        title:  Other color settings
        title.zh: 其他颜色设置
        type: heading
        level: 3
        collapsed: true
    -
        id: floating-toc-font-color
        title: font color
        title.zh: 目录字体颜色
        type: variable-themed-color
        format: hex
        opacity: false
        default-light: '#f5f6f8'
        default-dark: '#1b1b1b'
    - 
        id: floating-toc-hover-color
        title: hover color
        title.zh: 鼠标聚焦hover色
        description: The header color when the mouse is over the line .
        description.zh: 当鼠标在条目上方时，标题颜色。
        type: variable-themed-color
        format: hex
        opacity: false
        default-light: '#f5f6f8'
        default-dark: '#1b1b1b'
    - 
        id: floating-toc-header-background-color
        title: Header background color
        title.zh: 标题背景色
        type: variable-themed-color
        format: hex
        opacity: true
        default-light: '#ffffff8c'
        default-dark: '#202020c4'
    - 
        id: floating-toc-background-color
        title:  toc background color
        title.zh: 目录背景色
        type: variable-themed-color
        format: hex
        opacity: true
        default-light: '#dddddd4d'
        default-dark: '#20202066'
    -
        id: floating-toc-highlight-color
        title: highlight color
        title.zh: 高亮颜色
        description: Click on the header background color and the indicator color when scrolling .
        description.zh: 点击标题背景色和同步滚动时的指示器颜色
        type: variable-color
        format: hex
        alt-format:
            -
                id: floating-toc-highlight-color-rgb
                format: hsl-values
        default: '#ff8000'
    - 
        id: floating-toc-line-color
        title: Indicator line color
        title.zh: 指示条颜色
        type: variable-themed-color
        format: hex
        opacity: true
        default-light: '#7a9486'
        default-dark: '#84a83a'
    - 
        id: floating-toc-layout-header
        title:  layout settings
        title.zh: 版面相关设置
        type: heading
        level: 2
        collapsed: true
    -  
        id: enable-heading-nowrap
        title:  Header single line display
        title.zh: 目录标题单行显示
        type: class-toggle
        default: false
    -  
        id: floating-toc-font-size
        title: font size
        title.zh: 字体大小
        type: variable-number
        format: rem
        default: 0.7
    - 
        id: floating-toc-line-height
        title: line height
        title.zh: 目录行间距
        type: variable-number
        format: rem
        default: 1.5
    -
        id: floating-toc-position
        title: TOC Position relative to the edge
        title.zh: TOC距离边界的相对位置
        type: variable-number-slider
        default: 0
        format: px
        min: -100
        max: 100
        step: 1
    -
        id: indicator-bar-position
        title: Indicator bar position relative to the edge
        title.zh: 指示条的相对位置
        type: variable-number-slider
        default: 0
        format: em
        min: -3
        max: 3
        step: 0.1
    -  
        id: floating-toc-line-width
        title: Line Width of floating-toc
        title.zh: 目录显示宽度
        description: The maximum line width in rem units (1rem = Body font size)
        type: variable-number
        default: 18
        format: rem
    -
        id: floating-toc-appearance-header
        title:  Appearance settings
        title.zh: 外观设置
        type: heading
        level: 2
        collapsed: true
    -
        id: enable-outline-style
        title: enable outline style
        title.zh: 显示层级线
        type: class-select
        allowEmpty: false
        default: default-outline-style
        options:
          - 
            label: Default
            value: default-outline-style
          - 
            label: curves
            value: float-curves-outline-style
          - 
            label: line
            value: float-outline-style
    -  
        id: enable-bar-icon
        title:  Replace the indicator bar with an icon
        title.zh: 用图标代替指示条
        type: class-toggle
        default: false
    -
        id: enable-bar-heading-text
        title:  Show heading text next to indicator bar (including parent)
        title.zh: 在指示条旁边显示标题上下级
        type: class-toggle
        default: false
    - 
        id: focus-heading
        title: Focus on the current heading
        title.zh: 突出显示当前标题
        description: Focus on  the header corresponding to the current scrollbar, other headers will be faded . Set the fade level here.
        description.zh: 重点突出当前滚动条对应的标题，其他标题将淡化显示。这里调整淡化程度。
        type: variable-number-slider
        default: 0.9
        min: 0
        max: 1
        step: 0.1
    - 
        id: floating-toc-response-time
        title: Response Time
        title.zh: 浮动目录响应速度
        description: Adjust the eject sensitivity. The larger the value, the less sensitive it is
        description.zh: 调整浮动目录弹出灵敏度。值越大越不灵敏
        type: variable-number-slider
        level: 1
        format: s
        default: 0.3
        min: 0
        max: 1
        step: 0.1
    -
        id: floating-background-settings
        title:  Notes background blur effect settings
        title.zh: 笔记背景模糊效果设置
        type: heading
        level: 3
        collapsed: true
    - 
        id: enable-background-blur-dept
        title:  Activate Blurred background 
        title.zh: 开启触发目录后笔记背景模糊效果
        type: class-toggle
        default: false
    - 
        id: background-blur-depth
        title: Blurred background after opening the toc
        title.zh: 触发目录背景模糊程度
        description: To adjust the radius of blur
        description.zh: 调整背景模糊程度
        type: variable-number-slider
        default: 2
        format: px
        min: 0
        max: 30
        step: 1
    -
        id: floating-toc
        title: If you have ideas or feedback,  welcome to commit issues on github 😁
        title.zh:  有任何想法可以跟我交流😁，欢迎来访，QQ群:908688452
        type: heading
        level: 1
        collapsed: true
*/

.theme-light,
.theme-dark {
  --background-blur-depth: 2px;
  --floating-toc-font-color: var(--text-normal);
  --floating-toc-hover-color: var(--text-accent-hover);
  --floating-toc-font-size: 0.7rem;
  --floating-toc-highlight-color: var(--text-accent);
  --floating-toc-highlight-color-rgb: var(--interactive-accent-hsl);
  --floating-toc-line-color: var(--text-accent);
  --floating-toc-line-height: 1.5rem;
  --floating-toc-response-time: 0.3s;
  --floating-toc-position: 0px;
  --focus-heading: 0.9;
  --floating-toc-line-width: 18rem;

}

.theme-light {
  --floating-toc-header-background-color: var(--background-primary);
  --floating-toc-background-color: transparent;
}

.theme-dark {
  --floating-toc-header-background-color: var(--background-primary);
  --floating-toc-background-color: transparent;
}


div.workspace-leaf-content[data-type="style-settings"] div.style-settings-heading[data-id="floating-toc"] {
  margin-top: 10px;
  margin-bottom: 0;
  pointer-events: none;
  cursor: text;
  border-top: 2px solid var(--background-modifier-border);
  pointer-events: none;
}

.style-settings-heading[data-id="floating-toc"] .setting-item-control,
.style-settings-heading[data-id="floating-toc"] .style-settings-collapse-indicator {
  display: none;
}

.style-settings-heading[data-id="floating-toc"] .setting-item-name::before {
  display: inline-block;
  width: 20px;
  height: fit-content;
  padding-right: 8px;
  font-family: "Segoe UI Emoji", "Apple Color Emoji", sans-serif;
  font-weight: initial;
  color: unset;
  content: "❤️";
}


.heading-list-item.located .line-wrap .line {
  background: hsla(var(--floating-toc-highlight-color-rgb), 0.8);
  height: 2.5px;
}

.heading-list-item.located .text-wrap:not(.located) .text {
  color: var(--floating-toc-highlight-color);
}

.heading-list-item .text-wrap.located .text {
  /* background-color: hsla(var(--floating-toc-highlight-color-rgb), 0.4); */
  color:var(--color-accent);
font-weight: bold !important;
}

.heading-list-item {

  z-index: 1;
  font-size: var(--floating-toc-font-size);
  min-height: var(--floating-toc-line-height);
  padding: 0;
  white-space: nowrap;
  position: relative;
}

.heading-list-item .line-wrap {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 1.5rem;
  z-index: -1;
  animation: lineAppear-72a6a55b 250ms cubic-bezier(0.34, 1.56, 0.64, 1) both;
}

@keyframes lineAppear-72a6a55b {
  0% {
    transform: translateX(1rem);
  }

  100% {
    transform: translateX(0rem);
  }
}

.heading-list-item .line-wrap .line {
  position: absolute;
  top: 50%;
  margin-top: calc(2px / -2);
  height: 2px;
  width: 1.5rem;
  background: var(--floating-toc-line-color);
}

.heading-list-item .text-wrap {
  z-index: 9999;
  opacity: 0;
  pointer-events: none;
  height: 100%;
  display: inline-flex;
  align-items: center;
  animation: textAppear-72a6a55b 250ms cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
  text-wrap: inherit;
}

@keyframes textAppear-72a6a55b {
  0% {
    transform: translateX(-1rem);
  }

  100% {
    transform: translateX(0rem);
  }
}

.heading-list-item .text-wrap .text {
  padding: 0.25rem;
  border-radius: 0.5rem;
  text-decoration: none;
  cursor: pointer;
  color: var(--floating-toc-font-color);
  background: var(--floating-toc-header-background-color);
}

body.enable-heading-nowrap .floating-toc-div.pin .floating-toc .heading-list-item>.text-wrap {
  max-width: unset;
}

body.enable-heading-nowrap .heading-list-item .text-wrap .text {
  max-width: calc(var(--floating-toc-line-width) - 3rem);
  overflow: hidden;
  text-overflow: ellipsis;
}

.heading-list-item .text-wrap .text:hover {
  color: var(--floating-toc-hover-color);
  font-weight: bold;
}

.heading-list-item .text-wrap .text:hover::after {
  font-size: 8px;
}


.heading-list-item:hover .line-wrap .line {
  opacity: 0;
  pointer-events: none;
}

.heading-list-item:hover .text-wrap {
  opacity: 1;
  pointer-events: all;
}

.heading-list-item[data-level="2"] .text-wrap {
  margin-left: 0.5rem;

}

.heading-list-item[data-level="3"] .text-wrap {
  margin-left: 1rem;
}

.heading-list-item[data-level="4"] .text-wrap {
  margin-left: 1.5rem;
}

.heading-list-item[data-level="5"] .text-wrap {
  margin-left: 2rem;

}

.heading-list-item[data-level="6"] .text-wrap {
  margin-left: 2.5rem;

}

.heading-list-item[data-level="1"] {
  padding-left: 0rem;
}

.heading-list-item[data-level="1"] .text::after {
  content: "H1";
  font-size: 8px;
  margin-left: 1px;
  color: var(--text-muted);
  font-weight: normal !important;
  padding: 1px 2px 1px 2px;
}

.heading-list-item[data-level="2"] {
  padding-left: 0.5rem;
}

.heading-list-item[data-level="2"] .text::after {
  content: "H2";
  font-size: 8px;
  margin-left: 1px;
  color: var(--text-muted);
  font-weight: normal !important;
  padding: 1px 2px 1px 2px;
}

.heading-list-item[data-level="3"] {
  padding-left: 1rem;
}

.heading-list-item[data-level="3"] .text::after {
  content: "H3";
  font-size: 8px;
  margin-left: 1px;
  color: var(--text-muted);
  font-weight: normal !important;
  padding: 1px 2px 1px 2px;
}

.heading-list-item[data-level="4"] {
  padding-left: 1.5rem;
}

.heading-list-item[data-level="4"] .text::after {
  content: "H4";
  font-size: 8px;
  margin-left: 1px;
  color: var(--text-muted);
  font-weight: normal !important;
  padding: 1px 2px 1px 2px;
}

.heading-list-item[data-level="5"] {
  padding-left: 1.6rem;
}

.heading-list-item[data-level="5"] .text::after {
  content: "H5";
  font-size: 8px;
  margin-left: 1px;
  color: var(--text-muted);
  font-weight: normal !important;
  padding: 1px 2px 1px 2px;
}

.heading-list-item[data-level="6"] {
  padding-left: 1.8rem;
}

.heading-list-item[data-level="6"] .text::after {
  content: "H6";
  font-size: 8px;
  margin-left: 1px;
  color: var(--text-muted);
  font-weight: normal !important;
  padding: 1px 2px 1px 2px;
}

.view-content:has(.markdown-source-view.hide-floating-toc) .floating-toc {
  display: none;
}

.floating-toc {
  overflow: hidden;
  list-style: none;
  margin: 0;
  padding: 0;
  position: absolute;
  top: 100px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  bottom: 40px;
  border-radius:var(--radius-s);
  /* width: 13rem; */
  scroll-behavior: smooth;
  max-height: calc(100% - 135px);
  left: var(--floating-toc-position);
  padding-top: 0.5rem;
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.floating-toc-div.pin .floating-toc,
.floating-toc:hover {
  z-index: 2;
  align-items: flex-start;
  min-width: 6rem;
  max-width: var(--floating-toc-line-width);
  width: auto;
  overflow-y: auto;
  background: var(--floating-toc-background-color);
}

.floating-toc-div.pin .floating-toc::-webkit-scrollbar,
.floating-toc:hover::-webkit-scrollbar {
  display: none;
}


body:not(.enable-heading-nowrap) .floating-toc-div.pin .floating-toc .heading-list-item,
body:not(.enable-heading-nowrap) .floating-toc:hover .heading-list-item {
  min-height: auto;
}

.floating-toc-div:is(.hover, .pin) .floating-toc .heading-list-item {
  padding: 0 1rem;
}

.floating-toc-div.pin .floating-toc .heading-list-item {
  max-width: fit-content;
}

body.enable-heading-nowrap .floating-toc-div.pin .floating-toc .heading-list-item>.text-wrap,
body.enable-heading-nowrap .floating-toc:hover .heading-list-item>.text-wrap {
  white-space: nowrap;
}

.floating-toc-div.pin .floating-toc .heading-list-item>.text-wrap,
.floating-toc:hover .heading-list-item>.text-wrap {
  /* min-width: 12rem; */
  white-space: normal;
  opacity: 1;
  transition-delay: var(--floating-toc-response-time);
  transition-property: opacity;
  transition-timing-function: ease-in-out;
  -webkit-transition-delay: var(--floating-toc-response-time);
  -webkit-transition-property: opacity;
  -webkit-transition-timing-function: ease-in-out;
}


.floating-toc-div.pin .heading-list-item>.line-wrap,
.floating-toc:hover .heading-list-item>.line-wrap {
  display: none;

}

/* .workspace-leaf-content .floating-toc-div.pin:is(.floating-left,.floating-both)+.markdown-source-view .cm-editor {
  padding-left: 2rem;
 
} */



.workspace-leaf-content .floating-toc-div:is(.floating-left, .floating-both)+.markdown-source-view>.cm-editor {
  padding-left: 2rem;
}

.workspace-leaf-content .floating-toc-div.pin:is(.floating-left, .floating-both)+.markdown-source-view>.cm-editor {
  padding-left: var(--floating-toc-line-width);
}

.workspace-leaf-content .floating-toc-div.pin:is(.floating-left, .floating-both)~.markdown-reading-view {
  padding-left: var(--floating-toc-line-width);
}

.workspace-leaf-content .floating-toc-div:is(.floating-left, .floating-both)~.markdown-reading-view {
  padding-left: 4rem;
}

.floating-toc-div:is(.floating-left, .floating-both)+.markdown-source-view .cm-panels {
  margin-left: -2rem;
}

/****right*********/
.floating-toc-div.pin .floating-toc .heading-list-item>.text-wrap a>a,
.floating-toc-div .floating-toc:hover .heading-list-item>.text-wrap a>a {

  pointer-events: none;
}

.floating-toc-div.pin .floating-toc .heading-list-item>.text-wrap a>a.external-link,
.floating-toc-div .floating-toc:hover .heading-list-item>.text-wrap a>a.external-link {

  background-image: none;
}



.workspace-leaf-content .floating-toc-div.floating-right~.markdown-reading-view {
  padding-right: 2rem;

}

.floating-toc-div.floating-right .floating-toc {
  right: calc(0.5rem + var(--floating-toc-position));
  align-items: flex-end;
  left: unset;
}

.floating-toc-div.floating-right .heading-list-item {
  text-align: right;
}

.floating-toc-div.floating-right .heading-list-item .line-wrap {
  right: 0.5rem;
}


.floating-toc-div.floating-right .heading-list-item[data-level="1"] .line-wrap {
  padding-left: 0.5rem;
}

.floating-toc-div.floating-right .heading-list-item[data-level="2"] .line-wrap {
  padding-left: 0.7rem;
}

.floating-toc-div.floating-right .heading-list-item[data-level="3"] .line-wrap {
  padding-left: 0.9rem;
}

.floating-toc-div.floating-right .heading-list-item[data-level="4"] .line-wrap {
  padding-left: 1.1rem;
}

.floating-toc-div.floating-right .heading-list-item[data-level="5"] .line-wrap {
  padding-left: 1.3rem;
}

.floating-toc-div.floating-right .heading-list-item[data-level="6"] .line-wrap {
  padding-left: 1.5rem;
}

.floating-toc-div.floating-right.pin .floating-toc .heading-list-item>.text-wrap,
.floating-toc-div.floating-right .floating-toc:hover .heading-list-item>.text-wrap {
  justify-content: flex-end;
  text-align: right;
  display: inline-flex;
}

/****pin button***/

.floating-right.floating-toc-div .toolbar.pin {
  right: 10px;
 left:unset;
}


.floating-toc-div.pin .toolbar.pin {
  opacity: 1;
  display: block;
}


.floating-toc-div .toolbar.pin:hover {
  opacity: 1;

}

.floating-toc-div .toolbar button:hover {
  box-shadow: none;
  color: var(--interactive-accent-hover);

  transform: scale(1.2);
}

@keyframes shake {

  0%,
  100% {
    -webkit-transform: translateX(0);
  }

  10%,
  30%,
  50%,
  70%,
  90% {
    -webkit-transform: translateX(-0.1px);
  }

  20%,
  40%,
  60%,
  80% {
    -webkit-transform: translateX(0.1px);
  }
}

.floating-toc-div .toolbar.pin.hide {
  display: none;
}

.floating-toc-div .toolbar {
  display: list-item;
  opacity: 0.8;
  z-index: 2;
  position: fixed;
  top: 75px;
  left: 0;
  width: 6rem;
}

.floating-toc-div.pin .toolbar.pin {
  display: block;
}

.floating-toc-div.pin .toolbar.pin button[aria-label="pin"] {
  color: var(--interactive-accent-hover);
  animation: shake 0.5s 2 alternate linear;
  /* 设置动画为2秒钟的时间，并无限循环 */
}

.floating-toc-div .toolbar button {
  margin: 0;
  padding: 0 10px;
  background-color: transparent;
  margin-right: 5px;
  width: 10px;
  display: inline-grid;
  box-shadow: none;
  transition: transform 0.3s;
}

.floating-toc-div .toolbar button svg {
  width: 12px;
  height: 12px;
}



.floating-toc-div.pin .floating-toc .heading-list-item>.text-wrap {
  max-width: calc(var(--floating-toc-line-width) - 1rem);
}


body.enable-background-blur-dept .workspace-leaf-content .floating-toc-div.hover:not(.pin)+.markdown-source-view>.cm-editor {

  filter: blur(var(--background-blur-depth));
  -webkit-filter: blur(var(--background-blur-depth));
  background: var(--floating-toc-background-color);
}

body.enable-background-blur-dept .workspace-leaf-content .floating-toc-div.hover:not(.pin)~.markdown-reading-view .markdown-preview-view {
  filter: blur(var(--background-blur-depth));
  -webkit-filter: blur(var(--background-blur-depth));
  background: var(--floating-toc-background-color);
}

body.enable-background-blur-dept .floating-toc-div.hover:not(.pin) .heading-list-item .text-wrap:not(.located) .text {
  background: transparent;


}

body.enable-background-blur-dept .floating-toc-div.pin .floating-toc,
body.enable-background-blur-dept .floating-toc:hover {
  background: transparent;
}


/****alignLeft**/

.floating-toc-div.floating-right.alignLeft .floating-toc {

  align-items: flex-start;
}

.floating-right.floating-toc-div.alignLeft .toolbar.pin {
  right: 4rem;
}

.floating-toc-div.floating-right.alignLeft.pin .floating-toc .heading-list-item>.text-wrap,
.floating-toc-div.floating-right.alignLeft .floating-toc:hover .heading-list-item>.text-wrap {
  max-width: 12rem;
  text-overflow: ellipsis;
  justify-content: flex-start;

  text-align: left;
}




/* .heading-list-item.located  .text::before
{
  content: "•";
  margin-right: 2px;
  margin-left: -0.5rem;
} */
/* .heading-list-item.focus  .text::before
{
  content: "▾";
  margin-right: 2px;
  margin-left: -1rem;
} */
heading-list-item.focus .text {
  font-weight: bold;
}

.floating-toc-div:not(:is(.hover, .pin)) .heading-list-item {
  width: 2rem;
  overflow: hidden;
  margin-left: var(--indicator-bar-position);
}

.floating-toc-div:hover .heading-list-item {
  width: unset;
}

.floating-toc-div:not(:is(.hover)) .heading-list-item:not(:is(.located, .focus)) {
  opacity: var(--focus-heading);
}



/****enable-bar-heading-text**/

body.enable-bar-heading-text .floating-toc-div:not(:is(.hover, .pin)) .heading-list-item:is(.located, .focus) {
  width: unset;
}

body.enable-bar-heading-text .floating-toc-div:not(:is(.hover, .pin)) .heading-list-item:is(.located, .focus) .text-wrap {
  opacity: 1;
  margin-left: 10px;

}

body.enable-bar-heading-text .floating-toc-div:not(:is(.hover, .pin)) .heading-list-item:is(.located, .focus) .text-wrap .text {
  max-width: 12rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: var(--text-normal);
}

body.enable-bar-heading-text .floating-toc-div:not(:is(.hover, .pin)) .heading-list-item:is(.located, .focus) .text-wrap .text::before {
  content: '';
  margin-left: 0.2rem;
}

.heading-list-item.focus .text-wrap .text {
  font-weight: bolder;
}

/******/
.heading-list-item:not(.focus, .located) .text-wrap .text {
  font-weight: normal;
}

.workspace-tabs.mod-stacked .workspace-tab-header:not(.is-active)+.workspace-leaf .floating-toc-div {
  opacity: 0;
}

/******/


/**第二个tabs右侧*/
.workspace-tabs~.workspace-tabs.mod-top-right-space .floating-toc-div.floating-both~.markdown-reading-view {
  padding-right: 2rem;

}


.workspace-tabs~.workspace-tabs.mod-top-right-space .floating-toc-div.floating-both .floating-toc {
  right: calc(0.5rem + var(--floating-toc-position));
  align-items: flex-end;
  left: unset;
}

.workspace-tabs~.workspace-tabs.mod-top-right-space .floating-toc-div.floating-both .heading-list-item {
  text-align: right;
}

.workspace-tabs~.workspace-tabs.mod-top-right-space .floating-toc-div.floating-both .heading-list-item .line-wrap {
  right: 0.5rem;
}


.workspace-tabs~.workspace-tabs.mod-top-right-space .floating-toc-div.floating-both .heading-list-item[data-level="1"] .line-wrap {
  padding-left: 0.5rem;
}

.workspace-tabs~.workspace-tabs.mod-top-right-space .floating-toc-div.floating-both .heading-list-item[data-level="2"] .line-wrap {
  padding-left: 0.7rem;
}

.workspace-tabs~.workspace-tabs.mod-top-right-space .floating-toc-div.floating-both .heading-list-item[data-level="3"] .line-wrap {
  padding-left: 0.9rem;
}

.workspace-tabs~.workspace-tabs.mod-top-right-space .floating-toc-div.floating-both .heading-list-item[data-level="4"] .line-wrap {
  padding-left: 1.1rem;
}

.workspace-tabs~.workspace-tabs.mod-top-right-space .floating-toc-div.floating-both .heading-list-item[data-level="5"] .line-wrap {
  padding-left: 1.3rem;
}

.workspace-tabs~.workspace-tabs.mod-top-right-space .floating-toc-div.floating-both .heading-list-item[data-level="6"] .line-wrap {
  padding-left: 1.5rem;
}

.workspace-tabs~.workspace-tabs.mod-top-right-space .floating-toc-div.floating-both.pin .floating-toc .heading-list-item>.text-wrap,
.workspace-tabs~.workspace-tabs.mod-top-right-space .floating-toc-div.floating-both .floating-toc:hover .heading-list-item>.text-wrap {
  justify-content: flex-end;
  text-align: right;
  display: inline-flex;
}

.workspace-split.mod-vertical .workspace-tabs~.workspace-tabs .floating-toc-div.floating-both~.markdown-reading-view {
  padding-right: 2rem;

}

/**垂直分割后第二个tabs*/

.workspace-split.mod-vertical>.workspace-tabs~.workspace-tabs .floating-toc-div.floating-both+.markdown-source-view>.cm-editor {
  padding-right: 2rem;
  padding-left: 0;
}

.workspace-split.mod-vertical>.workspace-tabs~.workspace-tabs .floating-toc-div.floating-both~.markdown-reading-view {
  padding-left: 2rem;
  padding-right: 4rem;
}

.workspace-split.mod-vertical>.workspace-tabs~.workspace-tabs .floating-toc-div.floating-both.alignLeft.hover .floating-toc {
  align-items: flex-start;
}

.workspace-split.mod-vertical>.workspace-tabs~.workspace-tabs .floating-toc-div.floating-both .floating-toc {
  right: calc(0.5rem + var(--floating-toc-position));
  align-items: flex-end;
  left: unset;
}

.workspace-split.mod-vertical>.workspace-tabs~.workspace-tabs .floating-toc-div.floating-both .heading-list-item {
  text-align: right;
}

.workspace-split.mod-vertical>.workspace-tabs~.workspace-tabs .floating-toc-div.floating-both .heading-list-item .line-wrap {
  right: 0.5rem;
}



.workspace-split.mod-vertical>.workspace-tabs~.workspace-tabs .floating-toc-div.floating-both.pin .floating-toc .heading-list-item>.text-wrap,
.workspace-split.mod-vertical>.workspace-tabs~.workspace-tabs .floating-toc-div.floating-both .floating-toc:hover .heading-list-item>.text-wrap {
  justify-content: flex-end;
  text-align: right;
  display: inline-flex;
}


/**垂直分割 然后水平分割的tabs*/




.workspace-split.mod-vertical .workspace-tabs~.workspace-split.mod-horizontal>.workspace-tabs .floating-toc-div.floating-both .floating-toc {
  right: calc(0.5rem + var(--floating-toc-position));
  align-items: flex-end;
  left: unset;
}

.workspace-split.mod-vertical .workspace-tabs~.workspace-split.mod-horizontal>.workspace-tabs .floating-toc-div.floating-both .heading-list-item {
  text-align: right;
}

.workspace-split.mod-vertical .workspace-tabs~.workspace-split.mod-horizontal>.workspace-tabs .floating-toc-div.floating-both .heading-list-item .line-wrap {
  right: 0.5rem;
}


.workspace-split.mod-vertical .workspace-tabs~.workspace-split.mod-horizontal>.workspace-tabs .floating-toc-div.floating-both .heading-list-item[data-level="1"] .line-wrap {
  padding-left: 0.5rem;
}

.workspace-split.mod-vertical .workspace-tabs~.workspace-split.mod-horizontal>.workspace-tabs .floating-toc-div.floating-both .heading-list-item[data-level="2"] .line-wrap {
  padding-left: 0.7rem;
}

.workspace-split.mod-vertical .workspace-tabs~.workspace-split.mod-horizontal>.workspace-tabs .floating-toc-div.floating-both .heading-list-item[data-level="3"] .line-wrap {
  padding-left: 0.9rem;
}

.workspace-split.mod-vertical .workspace-tabs~.workspace-split.mod-horizontal>.workspace-tabs .floating-toc-div.floating-both .heading-list-item[data-level="4"] .line-wrap {
  padding-left: 1.1rem;
}

.workspace-split.mod-vertical .workspace-tabs~.workspace-split.mod-horizontal>.workspace-tabs .floating-toc-div.floating-both .heading-list-item[data-level="5"] .line-wrap {
  padding-left: 1.3rem;
}

.workspace-split.mod-vertical .workspace-tabs~.workspace-split.mod-horizontal>.workspace-tabs .floating-toc-div.floating-both .heading-list-item[data-level="6"] .line-wrap {
  padding-left: 1.5rem;
}

.workspace-split.mod-vertical .workspace-tabs~.workspace-split.mod-horizontal>.workspace-tabs .floating-toc-div.floating-both.pin .floating-toc .heading-list-item>.text-wrap,
.workspace-split.mod-vertical .workspace-tabs~.workspace-split.mod-horizontal>.workspace-tabs .floating-toc-div.floating-both .floating-toc:hover .heading-list-item>.text-wrap {
  justify-content: flex-end;
  text-align: right;
  display: inline-flex;
}

/***/

/**两个垂直分割 水平分割的tabs*/

.workspace-split.mod-horizontal~.workspace-split.mod-horizontal>.workspace-tabs .floating-toc-div.floating-both .floating-toc {
  right: calc(0.5rem + var(--floating-toc-position));
  align-items: flex-end;
  left: unset;
}

.workspace-split.mod-horizontal~.workspace-split.mod-horizontal>.workspace-tabs .floating-toc-div.floating-both:is(.pin, .hover) .floating-toc {
  right: calc(-2.5rem + var(--floating-toc-position));
  align-items: unset;
  left: unset;
}

.workspace-split.mod-vertical~.workspace-split.mod-horizontal>.workspace-tabs .floating-toc-div.floating-both .heading-list-item {
  text-align: right;
}

.workspace-split.mod-vertical~.workspace-split.mod-horizontal>.workspace-tabs .floating-toc-div.floating-both .heading-list-item .line-wrap {
  right: 0.5rem;
}


.workspace-split.mod-vertical~.workspace-split.mod-horizontal>.workspace-tabs .floating-toc-div.floating-both .heading-list-item[data-level="1"] .line-wrap {
  padding-left: 0.5rem;
}

.workspace-split.mod-vertical~.workspace-split.mod-horizontal>.workspace-tabs .floating-toc-div.floating-both .heading-list-item[data-level="2"] .line-wrap {
  padding-left: 0.7rem;
}

.workspace-split.mod-vertical~.workspace-split.mod-horizontal>.workspace-tabs .floating-toc-div.floating-both .heading-list-item[data-level="3"] .line-wrap {
  padding-left: 0.9rem;
}

.workspace-split.mod-vertical~.workspace-split.mod-horizontal>.workspace-tabs .floating-toc-div.floating-both .heading-list-item[data-level="4"] .line-wrap {
  padding-left: 1.1rem;
}

.workspace-split.mod-vertical~.workspace-split.mod-horizontal>.workspace-tabs .floating-toc-div.floating-both .heading-list-item[data-level="5"] .line-wrap {
  padding-left: 1.3rem;
}

.workspace-split.mod-vertical~.workspace-split.mod-horizontal>.workspace-tabs .floating-toc-div.floating-both .heading-list-item[data-level="6"] .line-wrap {
  padding-left: 1.5rem;
}

.workspace-split.mod-vertical~.workspace-split.mod-horizontal>.workspace-tabs .floating-toc-div.floating-both.pin .floating-toc .heading-list-item>.text-wrap,
.workspace-split.mod-vertical~.workspace-split.mod-horizontal>.workspace-tabs .floating-toc-div.floating-both .floating-toc:hover .heading-list-item>.text-wrap {
  justify-content: flex-end;
  text-align: right;
  display: inline-flex;
}


/***/

/**第二个tabs右侧*/


.workspace-tabs~.workspace-tabs.mod-top-right-space .floating-toc-div.floating-both.alignLeft .toolbar.pin {
  right: 4rem;
}

.workspace-tabs~.workspace-tabs.mod-top-right-space .floating-toc-div.floating-both.alignLeft.pin .floating-toc .heading-list-item>.text-wrap,
.workspace-tabs~.workspace-tabs.mod-top-right-space .floating-toc-div.floating-both.alignLeft .floating-toc:hover .heading-list-item>.text-wrap {
  max-width: 12rem;
  text-overflow: ellipsis;
  justify-content: flex-start;
  text-align: left;
}




/**垂直分割 然后水平分割的tabs*/

.workspace-split.mod-vertical .workspace-tabs~.workspace-split.mod-horizontal>.workspace-tabs .floating-toc-div.floating-both.alignLeft .toolbar.pin {
  right: 4rem;
}

.workspace-split.mod-vertical .workspace-tabs~.workspace-split.mod-horizontal>.workspace-tabs .floating-toc-div.floating-both.alignLeft.pin .floating-toc .heading-list-item>.text-wrap,
.workspace-split.mod-vertical .workspace-tabs~.workspace-split.mod-horizontal>.workspace-tabs .floating-toc-div.floating-both.alignLeft .floating-toc:hover .heading-list-item>.text-wrap {
  max-width: 12rem;
  text-overflow: ellipsis;
  justify-content: flex-start;
  text-align: left;
}


/***floating-both.alignLeft***/


.workspace-split.mod-vertical>.workspace-tabs~.workspace-tabs .floating-toc-div.floating-both.alignLeft .toolbar.pin {
  right: 4rem;
}

.workspace-split.mod-vertical>.workspace-tabs~.workspace-tabs .floating-toc-div.floating-both.alignLeft.pin .floating-toc .heading-list-item>.text-wrap,
.workspace-split.mod-vertical>.workspace-tabs~.workspace-tabs .floating-toc-div.floating-both.alignLeft .floating-toc:hover .heading-list-item>.text-wrap {
  max-width: 12rem;
  text-overflow: ellipsis;
  justify-content: flex-start;
  text-align: left;
}







/*****/
.setting-item-heading.float-cta:after {
  content: "";
  position: absolute;
  top: -10%;
  width: 104%;
  left: -2%;
  height: 120%;
  outline: 2px solid var(--text-accent);
  border-radius: 1em;
  pointer-events: none;
}

.setting-item-heading.float-cta {
  position: relative;
}


.floating-toc-remove-heading-indicator .heading-list-item[data-level] .text::after {
  display: none;
}

body:not(.toggle-floating-toc-header-color) .setting-item[data-id^="floating-toc-color-"] {
  display: none;
}

body.toggle-floating-toc-header-color .floating-toc-div .floating-toc .heading-list-item[data-level="1"] a.text {
  color: var(--floating-toc-color-h1, var(--h1-color));
}

body.toggle-floating-toc-header-color .floating-toc-div .floating-toc .heading-list-item[data-level="2"] a.text {
  color: var(--floating-toc-color-h2, var(--h2-color));
}

body.toggle-floating-toc-header-color .floating-toc-div .floating-toc .heading-list-item[data-level="3"] a.text {
  color: var(--floating-toc-color-h3, var(--h3-color));
}

body.toggle-floating-toc-header-color .floating-toc-div .floating-toc .heading-list-item[data-level="4"] a.text {
  color: var(--floating-toc-color-h4, var(--h4-color));
}

body.toggle-floating-toc-header-color .floating-toc-div .floating-toc .heading-list-item[data-level="5"] a.text {
  color: var(--floating-toc-color-h5, var(--h5-color));
}

body.toggle-floating-toc-header-color .floating-toc-div .floating-toc .heading-list-item[data-level="6"] a.text {
  color: var(--floating-toc-color-h6, var(--h6-color));
}

/**************/

.enable-bar-icon .floating-toc-div:not(.hover) .floating-toc:before {
  content: '';
  height: 12px;
  z-index: 10;
  background-repeat: no-repeat;
  cursor: var(--cursor);
  text-align: right;
  padding: 4px 20px;
  margin-bottom: 2px;
  border-radius: 5px;
  font-weight: 500;
  font-size: var(--font-adaptive-small);
}

.enable-bar-icon .floating-toc-div:not(.hover) .floating-toc:before {
  opacity: .35;
  background-position: center center;
  background-size: 18px;
  background-image: url("data:image/svg+xml,%3Csvg t='1671519666455' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='11772' width='16' height='16'%3E%3Cpath d='M887.212102 264.644379 320.083527 264.644379c-29.826306 0-54.012148-23.896246-54.012148-53.723575s24.185842-53.723575 54.012148-53.723575l567.127552 0c29.827329 0 54.012148 23.896246 54.012148 53.723575S917.039431 264.644379 887.212102 264.644379zM887.212102 562.426483 320.083527 562.426483c-29.826306 0-54.012148-24.408923-54.012148-54.235229 0-29.828352 24.185842-54.235229 54.012148-54.235229l567.127552 0c29.827329 0 54.012148 24.406876 54.012148 54.235229C941.22425 538.018583 917.039431 562.426483 887.212102 562.426483zM887.212102 859.18528 320.083527 859.18528c-29.826306 0-54.012148-24.407899-54.012148-54.235229s24.185842-54.235229 54.012148-54.235229l567.127552 0c29.827329 0 54.012148 24.407899 54.012148 54.235229S917.039431 859.18528 887.212102 859.18528zM153.565003 257.94377l-47.261397 0c-13.05534 0-23.630187-10.601452-23.630187-23.655769l0-47.234791c0-13.080923 10.574846-23.630187 23.630187-23.630187l47.261397 0c13.054317 0 23.630187 10.549264 23.630187 23.630187l0 47.234791C177.19519 247.341295 166.61932 257.94377 153.565003 257.94377L153.565003 257.94377zM153.565003 852.076372l-47.261397 0c-13.05534 0-23.630187-10.600429-23.630187-23.655769l0-47.233768c0-13.080923 10.574846-23.63121 23.630187-23.63121l47.261397 0c13.054317 0 23.630187 10.550287 23.630187 23.63121l0 47.233768C177.19519 841.475943 166.61932 852.076372 153.565003 852.076372L153.565003 852.076372zM153.565003 541.507034l-47.261397 0c-13.05534 0-23.630187-10.601452-23.630187-23.655769l0-47.234791c0-13.080923 10.574846-23.630187 23.630187-23.630187l47.261397 0c13.054317 0 23.630187 10.549264 23.630187 23.630187l0 47.234791C177.19519 530.905582 166.61932 541.507034 153.565003 541.507034L153.565003 541.507034z' fill='white' p-id='11773'%3E%3C/path%3E%3C/svg%3E");
}

.theme-light.enable-bar-icon .floating-toc-div:not(.hover) .floating-toc:before {
  background-image: url("data:image/svg+xml,%3Csvg t='1671519666455' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='11772' width='16' height='16'%3E%3Cpath d='M887.212102 264.644379 320.083527 264.644379c-29.826306 0-54.012148-23.896246-54.012148-53.723575s24.185842-53.723575 54.012148-53.723575l567.127552 0c29.827329 0 54.012148 23.896246 54.012148 53.723575S917.039431 264.644379 887.212102 264.644379zM887.212102 562.426483 320.083527 562.426483c-29.826306 0-54.012148-24.408923-54.012148-54.235229 0-29.828352 24.185842-54.235229 54.012148-54.235229l567.127552 0c29.827329 0 54.012148 24.406876 54.012148 54.235229C941.22425 538.018583 917.039431 562.426483 887.212102 562.426483zM887.212102 859.18528 320.083527 859.18528c-29.826306 0-54.012148-24.407899-54.012148-54.235229s24.185842-54.235229 54.012148-54.235229l567.127552 0c29.827329 0 54.012148 24.407899 54.012148 54.235229S917.039431 859.18528 887.212102 859.18528zM153.565003 257.94377l-47.261397 0c-13.05534 0-23.630187-10.601452-23.630187-23.655769l0-47.234791c0-13.080923 10.574846-23.630187 23.630187-23.630187l47.261397 0c13.054317 0 23.630187 10.549264 23.630187 23.630187l0 47.234791C177.19519 247.341295 166.61932 257.94377 153.565003 257.94377L153.565003 257.94377zM153.565003 852.076372l-47.261397 0c-13.05534 0-23.630187-10.600429-23.630187-23.655769l0-47.233768c0-13.080923 10.574846-23.63121 23.630187-23.63121l47.261397 0c13.054317 0 23.630187 10.550287 23.630187 23.63121l0 47.233768C177.19519 841.475943 166.61932 852.076372 153.565003 852.076372L153.565003 852.076372zM153.565003 541.507034l-47.261397 0c-13.05534 0-23.630187-10.601452-23.630187-23.655769l0-47.234791c0-13.080923 10.574846-23.630187 23.630187-23.630187l47.261397 0c13.054317 0 23.630187 10.549264 23.630187 23.630187l0 47.234791C177.19519 530.905582 166.61932 541.507034 153.565003 541.507034L153.565003 541.507034z' fill='%23272636' p-id='11773'%3E%3C/path%3E%3C/svg%3E");
}

.enable-bar-icon .floating-toc-div:not(.pin) .floating-toc:not(:hover) li {
  display: none;
}

.enable-bar-icon .floating-toc-div.pin .floating-toc:before {
  display: none;
}

/******/



body.float-curves-outline-style .floating-toc-div:is(.hover, .pin) .heading-list-item[data-level='1']+li.heading-list-item[data-level='2']::before,
body.float-curves-outline-style .floating-toc-div:is(.hover, .pin) .heading-list-item[data-level='2']+li.heading-list-item[data-level='3']::before,
body.float-curves-outline-style .floating-toc-div:is(.hover, .pin) .heading-list-item[data-level='3']+li.heading-list-item[data-level='4']::before,
body.float-curves-outline-style .floating-toc-div:is(.hover, .pin) .heading-list-item[data-level='4']+li.heading-list-item[data-level='5']::before,
body.float-curves-outline-style .floating-toc-div:is(.hover, .pin) .heading-list-item[data-level='5']+li.heading-list-item[data-level='6']::before {
  top: calc(calc(var(--nav-item-size) * 1.8) / 2 * -0.8) !important;

}

body.float-curves-outline-style .floating-toc-div:is(.hover, .pin) .heading-list-item[data-level='1']~.heading-list-item[data-level='2']:not([iscollapsed])::before,
body.float-curves-outline-style .floating-toc-div:is(.hover, .pin) .heading-list-item[data-level='2']~.heading-list-item[data-level='3']:not([iscollapsed])::before,
body.float-curves-outline-style .floating-toc-div:is(.hover, .pin) .heading-list-item[data-level='3']~.heading-list-item[data-level='4']:not([iscollapsed])::before,
body.float-curves-outline-style .floating-toc-div:is(.hover, .pin) .heading-list-item[data-level='4']~.heading-list-item[data-level='5']:not([iscollapsed])::before,
body.float-curves-outline-style .floating-toc-div:is(.hover, .pin) .heading-list-item[data-level='5']~.heading-list-item[data-level='6']:not([iscollapsed])::before {
  content: " ";
  position: absolute;
  top: calc(calc(var(--nav-item-size) * 1.8) / 2 * -1.6);
  /* left: -10px; */
  bottom: calc(100% - (calc(var(--nav-item-size) * 1.8) - 4px) / 2);
  width: 18px;
  border-bottom-left-radius: var(--radius-m);
  border-bottom: 1.5px solid hsla(var(--color-accent-hsl), 0.9);
  border-left: 2px solid hsla(var(--color-accent-hsl), 0.9);
  pointer-events: none;
}

.heading-list-item[data-level='2']::before {
  left: 0.7rem;
}

.heading-list-item[data-level='3']::before {
  left: 1.2rem;
}

.heading-list-item[data-level='4']::before {
  left: 1.7rem;
}

.heading-list-item[data-level='5']::before {
  left: 2.2rem;
}

.heading-list-item[data-level='6']::before {
  left: 2.7rem;
}


body.float-outline-style .heading-list-item[data-level='2']+.heading-list-item[data-level='2']::before,
body.float-outline-style .heading-list-item[data-level='3']+.heading-list-item[data-level='3']::before,
body.float-outline-style .heading-list-item[data-level='4']+.heading-list-item[data-level='4']::before,
body.float-outline-style .heading-list-item[data-level='5']+.heading-list-item[data-level='5']::before,
body.float-outline-style .heading-list-item[data-level='6']+.heading-list-item[data-level='6']::before {
  content: " ";
  position: absolute;
  display: block;

  top: -2.6em;
  bottom: 100%;
  border-right: 1px solid var(--text-muted);
  pointer-events: none;

}

/*********/


.floating-toc-div.hide {
  display: none;
}



/******/
/* 默认不显示伪元素 */
/* .heading-list-item::before {
    content: '';
} */

/* 对于折叠状态 */
.floating-toc-div.pin .heading-list-item[isCollapsed="true"] .heading-rendered::before,
.floating-toc-div.hover .heading-list-item[isCollapsed="true"] .heading-rendered::before {
  /* content: '▶';  */
  /* 源文件是src/resources/右键头，需要调整颜色和大小后转化成 URL encoded */
  content: url("data:image/svg+xml,%3C%3Fxml version='1.0' standalone='no'%3F%3E%3C!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3E%3Csvg t='1701099392052' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='4217' xmlns:xlink='http://www.w3.org/1999/xlink' width='0.65em' height='0.65em'%3E%3Cpath d='M757.792745 435.407215L419.597482 96.904967c-40.010393-40.010393-104.886579-40.010393-144.896972 0-40.010393 40.010393-40.010393 104.988908 0 144.9993L540.344959 507.855701 274.70051 773.807135c-40.010393 40.112721-40.010393 104.988908 0 144.9993 40.010393 40.010393 104.886579 40.010393 144.896972 0l338.092935-338.39992c40.112721-40.010393 40.112721-104.988908 0.102328-144.9993z' fill='%23d3c6aa' p-id='4218'%3E%3C/path%3E%3C/svg%3E%0A");
  position: absolute;
  width: 1em;
  margin-left: -0.9em;
  /* 向左偏移自身宽度，使伪元素不占用.text-wrap的空间 */
  padding-right: 0.5rem;
  z-index: 2;
  left: 0;
  /* 使伪元素位于li的最左边 */
  top: 50%;
  /* 垂直居中 */
  transform: translateY(-50%);
  /* 偏移自身高度的一半以实现精确的垂直居中 */
  text-align: center;
  /* 确保字符水平居中 */
}

/* 对于展开状态 */
.floating-toc-div.pin .heading-list-item[isCollapsed="false"] .heading-rendered::before,
.floating-toc-div.hover .heading-list-item[isCollapsed="false"] .heading-rendered::before {
  /* content: '▼';  */
  content: url("data:image/svg+xml,%3C%3Fxml version='1.0' standalone='no'%3F%3E%3C!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3E%3Csvg t='1701099490786' class='icon' viewBox='0 0 1026 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='7616' xmlns:xlink='http://www.w3.org/1999/xlink' width='0.65em' height='0.65em'%3E%3Cpath d='M857.088 224.256q28.672-28.672 69.12-28.672t69.12 28.672q29.696 28.672 29.696 68.608t-29.696 68.608l-382.976 380.928q-12.288 14.336-30.72 19.968t-38.912 4.608-40.448-8.704-34.304-22.016l-376.832-374.784q-29.696-28.672-29.696-68.608t29.696-68.608q14.336-14.336 32.256-21.504t36.864-7.168 37.376 7.168 32.768 21.504l313.344 309.248z' fill='%23d3c6aa' p-id='7617'%3E%3C/path%3E%3C/svg%3E%0A");
  position: absolute;
  width: 1em;
  margin-left: -0.9em;
  padding-right: 0.5rem;
  z-index: 2;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  text-align: center;
}

.check-list {
  gap: 0.2em;
  display: flex;
  padding: 6px;
}

.check-item {
  display: flex;
  padding: 3px 8px 3px 8px;
  align-items: center;
}

.floating-toc>.toolbar+.heading-list-item {
  padding-top: 0.5rem;
}